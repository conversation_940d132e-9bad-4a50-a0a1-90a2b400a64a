# FieldOps Mobile App – Project Overview

This repository powers the FieldOps mobile application for project execution monitoring in infrastructure projects. The app supports multi-role workflows including Site Engineers, Approvers, and managerial roles. It works offline, syncs when online, and integrates GIS mapping for spatial insights.

---

## 👥 User Roles & Responsibilities

| Role              | Key Features |
|-------------------|--------------|
| **Site Engineer** | - Full dashboard access<br>- Can submit: Progress Update, Hindrance, Asset Request, Day Planning<br>- Has approval rights<br>- Access to GIS map features |
| **Approver**      | - Can review & approve submissions from Site Engineers |
| **Quality Incharge, P&M Incharge, Section Incharge, Project Manager, Accountant** | - Have read or action rights based on organizational rules (customized per instance) |

---

## 🚀 Key Functional Modules

- **Download WBS Data** for job site work plans
- **Progress Updates** with daily status via map-connected nodes
- **Hindrance Reporting**
- **Bookmarking** critical items
- **Asset Allocation/Requests**
- **Day Planning & Scheduling**
- **Offline-First Sync Engine**
- **User Manual Access**
- **GIS Map Viewer**: daily visual overlays of coverage and pending work

---

## 📂 Folder Structure

src/
├── @types/ # Global TypeScript types and mocks
│ └── *.d.ts
├── config/ # App configuration files
│ └── Config.ts
├── database/ # Local SQLite DB & WatermelonDB setup
│ ├── schema.ts
│ ├── migrations.ts
│ ├── model/ # Domain models (Progress, WBS, etc.)
│ ├── helper/ # DB interaction helpers
│ ├── WBSDataInsert/ # Data insert handlers
│ └── WBSDataDelete/ # Data delete handlers
├── navigation/ # Navigation stack and routing logic
│ └── AppNavigator.tsx
├── translation/ # Multi-language support (EN, FR)
│ └── en.json
├── utils/ # Common utilities (Crypto, String helpers)
│ ├── Crypto/
│ └── Strings/
└── ...

yaml

---

## 🧭 Map-Connected Workflow (Flow Diagram)

```text
[Download WBS] → [Progress Update] → [Map View]
                             ↓
                       [Hindrance Report]
                             ↓
                      [Day Planning Request]
                             ↓
                     [Asset Request / Allocation]
                             ↓
                        [Approver Approval]
                             ↓
                      [Sync with Server]

Local Database Layer

Uses WatermelonDB for structured, sync-capable local persistence.
Model folders: ProgressUpdate/, BookmarkList/, WBSDetails/, etc.
Common patterns:
StoreWbsDetailsData.ts — Insert WBS metadata
ProgressUpdateEngineer.ts — Capture & sync site reports
DeleteWBSData.ts — Cleanup stale records

Navigation & Multilingual Support

AppNavigator.tsx — React Navigation stack
translation/ — Supports English and French with dynamic switching

Offline & Sync Strategy

Data is captured offline and marked pendingSync
Background service checks network availability
Once online, all requests (progress, hindrance, asset) sync to server
Status updates reflected via local DB

GIS Map Integration

If GIS access is enabled (via config flag), user can launch GeoSpatial external app
Returned geometrical data is persisted and rendered inside app maps

Environment Notes

Built with React Native 0.78.2
Database: WatermelonDB with SQLite backend
State management: Redux or internal controller logic (based on final structure)
Native bridges: GIS launcher, offline sync engine, file access

Contribution Tips

Follow singular folder names and lower_snake_case
Keep business logic out of views
Prefer type-safe models and constants over inline strings
Feature folders should include: screen, controller, model, AP