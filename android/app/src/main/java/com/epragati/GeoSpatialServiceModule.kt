package com.obs.epragatiRN

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.util.Log
import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule

class GeoSpatialServiceModule(private val reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext), LifecycleEventListener, ActivityEventListener {

    private var receiver: BroadcastReceiver? = null
    private var syncStatus: String = "default"

    override fun getName(): String {
        return "GeoSpatialService"
    }

    init {
        reactContext.addLifecycleEventListener(this)
        reactContext.addActivityEventListener(this)
        registerGeoSyncReceiver()
    }

    @ReactMethod
    fun startGeospatialService(data: ReadableMap) {
        try {
            val jobCode = data.getString("jobCode") ?: ""
            val intent = Intent().apply {
                putExtra("Job_Codes", jobCode)
                putExtra("Auth_Key", "PjfeOlbSnKDi8AD2tCSY.")
                component = ComponentName(
                    "lnt.geospatial.egeospatial",
                    "lnt.geospatial.egeospatial.services.SyncGeospatialDBService"
                )
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                reactContext.startForegroundService(intent)
            } else {
                reactContext.startService(intent)
            }

        } catch (e: Exception) {
            Log.e("GeoSpatialService", "Failed to start service", e)
        }
    }

    @ReactMethod
    fun openGISApp(jobCode: String, boqCode: String, deliverableCode: String) {
        try {
            Log.d("GeoSpatialService", "=== OPENING GIS APP ===")
            Log.d("GeoSpatialService", "Job_Code: $jobCode")
            Log.d("GeoSpatialService", "BOQ_Code: $boqCode") 
            Log.d("GeoSpatialService", "Deliverable_Code: $deliverableCode")
            
            val packageName = "lnt.geospatial.egeospatial"
            Log.d("GeoSpatialService", "Target package: $packageName")
            
            // Method 1: Check if package is installed using PackageManager
            Log.d("GeoSpatialService", "=== CHECKING PACKAGE INSTALLATION ===")
            var isPackageInstalled = false
            try {
                val packageInfo = reactContext.packageManager.getPackageInfo(packageName, 0)
                isPackageInstalled = true
                Log.d("GeoSpatialService", "✅ Package found via getPackageInfo!")
                Log.d("GeoSpatialService", "Package version: ${packageInfo.versionName}")
                Log.d("GeoSpatialService", "Package code: ${packageInfo.versionCode}")
            } catch (e: Exception) {
                Log.e("GeoSpatialService", "❌ Package not found via getPackageInfo: ${e.message}")
            }
            
            // Method 2: Try to get launch intent
            Log.d("GeoSpatialService", "=== CHECKING LAUNCH INTENT ===")
            val launchIntent = reactContext.packageManager.getLaunchIntentForPackage(packageName)
            
            if (launchIntent != null) {
                Log.d("GeoSpatialService", "✅ Launch intent found!")
                Log.d("GeoSpatialService", "Launch intent action: ${launchIntent.action}")
                Log.d("GeoSpatialService", "Launch intent component: ${launchIntent.component}")
                
                // Set extras exactly as in C# code
                launchIntent.putExtra("Job_Code", jobCode)
                launchIntent.putExtra("BOQ_Code", boqCode)
                launchIntent.putExtra("Auth_Key", "PjfeOlbSnKDi8AD2tCSY.")
                launchIntent.putExtra("Deliverable_Code", deliverableCode)
                
                // Set ActivityFlags.MultipleTask flag as in C# code
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_MULTIPLE_TASK)
                
                Log.d("GeoSpatialService", "Intent configured with all extras and flags")

                // Get current activity and start for result
                val currentActivity = reactContext.currentActivity
                if (currentActivity != null) {
                    Log.d("GeoSpatialService", "Starting GIS app with startActivityForResult, request code: $GIS_REQUEST_CODE")
                    currentActivity.startActivityForResult(launchIntent, GIS_REQUEST_CODE)
                    sendEventToJS("GeoSpatialLaunch", "GIS app launched successfully")
                } else {
                    Log.e("GeoSpatialService", "No current activity available - trying fallback")
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    reactContext.startActivity(launchIntent)
                    sendEventToJS("GeoSpatialError", "Launched without activity context - may not return results")
                }
            } else {
                Log.e("GeoSpatialService", "❌ LaunchIntent is null")
                
                if (isPackageInstalled) {
                    Log.e("GeoSpatialService", "Package is installed but has no launch intent!")
                    
                    // Method 3: Try alternative approach - create intent manually
                    Log.d("GeoSpatialService", "=== TRYING MANUAL INTENT CREATION ===")
                    try {
                        val manualIntent = Intent(Intent.ACTION_MAIN)
                        manualIntent.addCategory(Intent.CATEGORY_LAUNCHER)
                        manualIntent.setPackage(packageName)
                        
                        val resolveInfos = reactContext.packageManager.queryIntentActivities(manualIntent, 0)
                        if (resolveInfos.isNotEmpty()) {
                            val resolveInfo = resolveInfos[0]
                            Log.d("GeoSpatialService", "Found launchable activity: ${resolveInfo.activityInfo.name}")
                            
                            val componentName = ComponentName(packageName, resolveInfo.activityInfo.name)
                            val alternateIntent = Intent(Intent.ACTION_MAIN)
                            alternateIntent.addCategory(Intent.CATEGORY_LAUNCHER)
                            alternateIntent.component = componentName
                            
                            // Add extras
                            alternateIntent.putExtra("Job_Code", jobCode)
                            alternateIntent.putExtra("BOQ_Code", boqCode)
                            alternateIntent.putExtra("Auth_Key", "PjfeOlbSnKDi8AD2tCSY.")
                            alternateIntent.putExtra("Deliverable_Code", deliverableCode)
                            alternateIntent.addFlags(Intent.FLAG_ACTIVITY_MULTIPLE_TASK)
                            
                            val currentActivity = reactContext.currentActivity
                            if (currentActivity != null) {
                                Log.d("GeoSpatialService", "Launching with manual intent via startActivityForResult")
                                currentActivity.startActivityForResult(alternateIntent, GIS_REQUEST_CODE)
                                sendEventToJS("GeoSpatialLaunch", "GIS app launched via manual intent")
                            } else {
                                alternateIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                reactContext.startActivity(alternateIntent)
                                sendEventToJS("GeoSpatialError", "Launched via manual intent without activity context")
                            }
                        } else {
                            Log.e("GeoSpatialService", "No launchable activities found for package")
                            sendEventToJS("GeoSpatialError", "GIS App installed but no launchable activities found")
                        }
                    } catch (e: Exception) {
                        Log.e("GeoSpatialService", "Manual intent creation failed: ${e.message}")
                        sendEventToJS("GeoSpatialError", "Failed to create manual intent: ${e.message}")
                    }
                } else {
                    Log.e("GeoSpatialService", "Package not found at all")
                    sendEventToJS("GeoSpatialError", "GIS App not installed")
                }
            }

        } catch (e: Exception) {
            Log.e("GeoSpatialService", "Critical error in openGISApp: ${e.message}", e)
            sendEventToJS("GeoSpatialError", "Error opening GIS App: ${e.message}")
        }
    }

    companion object {
        const val GIS_REQUEST_CODE = 1  // Same as C# Xamarin requestCode == 1
    }

    @ReactMethod
    fun getSyncStatus(promise: Promise) {
        try {
            promise.resolve(syncStatus)
        } catch (e: Exception) {
            promise.reject("ERROR", "Failed to get sync status", e)
        }
    }

    @ReactMethod
    fun debugListInstalledApps(promise: Promise) {
        try {
            Log.d("GeoSpatialService", "=== LISTING ALL INSTALLED PACKAGES ===")
            
            val packageManager = reactContext.packageManager
            val installedPackages = packageManager.getInstalledPackages(0)
            
            val gisRelatedPackages = mutableListOf<String>()
            val allPackages = mutableListOf<String>()
            
            for (packageInfo in installedPackages) {
                val packageName = packageInfo.packageName
                allPackages.add(packageName)
                
                // Look for any packages that might be related to GIS
                if (packageName.contains("geospatial", ignoreCase = true) ||
                    packageName.contains("lnt", ignoreCase = true) ||
                    packageName.contains("gis", ignoreCase = true)) {
                    
                    gisRelatedPackages.add(packageName)
                    Log.d("GeoSpatialService", "🎯 Found GIS-related package: $packageName")
                    Log.d("GeoSpatialService", "   Version: ${packageInfo.versionName}")
                    
                    // Check if this package has a launch intent
                    val launchIntent = packageManager.getLaunchIntentForPackage(packageName)
                    Log.d("GeoSpatialService", "   Has launch intent: ${launchIntent != null}")
                    
                    if (launchIntent != null) {
                        Log.d("GeoSpatialService", "   Launch component: ${launchIntent.component}")
                    }
                }
            }
            
            Log.d("GeoSpatialService", "Total packages found: ${installedPackages.size}")
            Log.d("GeoSpatialService", "GIS-related packages: ${gisRelatedPackages.size}")
            
            val result = mapOf(
                "totalPackages" to installedPackages.size,
                "gisRelatedPackages" to gisRelatedPackages,
                "targetPackageFound" to allPackages.contains("lnt.geospatial.egeospatial")
            )
            
            val resultMap = Arguments.createMap()
            resultMap.putInt("totalPackages", installedPackages.size)
            resultMap.putArray("gisRelatedPackages", Arguments.createArray().apply {
                gisRelatedPackages.forEach { pushString(it) }
            })
            resultMap.putBoolean("targetPackageFound", allPackages.contains("lnt.geospatial.egeospatial"))
            promise.resolve(resultMap)
            
        } catch (e: Exception) {
            Log.e("GeoSpatialService", "Error listing packages: ${e.message}", e)
            promise.reject("ERROR", "Failed to list packages", e)
        }
    }

    // 📡 Register the BroadcastReceiver
    private fun registerGeoSyncReceiver() {
        if (receiver != null) return

        receiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                if (intent?.action.equals("com.lnt.Pragati.GISCallback", ignoreCase = true)) {
                    val extras = intent?.extras
                    syncStatus = if (extras == null) {
                        "default"
                    } else {
                        if (extras.getBoolean("syncStatus", false)) {
                            // Stop the service when sync is complete
                            val stopIntent = Intent().apply {
                                putExtra("Auth_Key", "PjfeOlbSnKDi8AD2tCSY.")
                                component = ComponentName(
                                    "lnt.geospatial.egeospatial",
                                    "lnt.geospatial.egeospatial.services.SyncGeospatialDBService"
                                )
                            }
                            try {
                                val result = reactContext.stopService(stopIntent)
                                Log.d("GeoSpatialService", "Service stop result: $result")
                            } catch (e: Exception) {
                                Log.e("GeoSpatialService", "Failed to stop service", e)
                            }
                            "Synced"
                        } else {
                            "Sync Failed"
                        }
                    }

                    // Send the sync status to JavaScript
                    sendEventToJS("GeoSyncStatus", syncStatus)
                    
                    // Send success/failure event for popup handling
                    val params = Arguments.createMap().apply {
                        putString("status", syncStatus)
                        putBoolean("success", syncStatus == "Synced")
                    }
                    sendEventToJS("GeoSyncCallback", params)
                }
            }
        }

        val filter = IntentFilter("com.lnt.Pragati.GISCallback")
        reactContext.registerReceiver(receiver, filter)
    }

    private fun sendEventToJS(eventName: String, data: String) {
        reactContext
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
            .emit(eventName, data)
    }

    private fun sendEventToJS(eventName: String, data: WritableMap) {
        reactContext
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
            .emit(eventName, data)
    }

    override fun onHostResume() {}
    override fun onHostPause() {}

    override fun onHostDestroy() {
        if (receiver != null) {
            try {
                reactContext.unregisterReceiver(receiver)
                receiver = null
            } catch (e: Exception) {
                Log.e("GeoSpatialService", "Error unregistering receiver", e)
            }
        }
    }

    // ActivityEventListener implementation (matches C# Xamarin OnActivityResult)
    override fun onActivityResult(activity: Activity?, requestCode: Int, resultCode: Int, data: Intent?) {
        Log.d("GeoSpatialService", "onActivityResult called - requestCode: $requestCode, resultCode: $resultCode, hasData: ${data != null}")
        
        // Match C# Xamarin: else if (requestCode == 1)
        if (requestCode == GIS_REQUEST_CODE) {
            Log.d("GeoSpatialService", "GIS request code matched! Processing result...")
            
            // Match C# Xamarin: if (resultCode == Android.App.Result.Ok)
            if (resultCode == Activity.RESULT_OK) {
                Log.d("GeoSpatialService", "Result OK received from GIS app")
                
                // Match C# Xamarin: if (data != null)
                if (data != null) {
                    try {
                        // Match C# Xamarin: Bundle bundle = data.Extras;
                        val bundle = data.extras
                        if (bundle != null) {
                            Log.d("GeoSpatialService", "Bundle received with ${bundle.size()} items")
                            
                            // Match C# Xamarin: ICollection<string> response = bundle.KeySet();
                            val response = bundle.keySet()
                            val responseValues = Arguments.createMap()
                            
                            Log.d("GeoSpatialService", "Bundle keys: ${response.joinToString(", ")}")
                            
                            // Match C# Xamarin: foreach (var key in response)
                            for (key in response) {
                                val value = bundle.get(key)
                                val valueString = value?.toString() ?: ""
                                
                                Log.d("GeoSpatialService", "Bundle key: '$key' = '$valueString'")
                                
                                // Match C# Xamarin: responseValues.Add(key, value.ToString());
                                when (value) {
                                    is String -> responseValues.putString(key, value)
                                    is Int -> {
                                        responseValues.putInt(key, value)
                                        responseValues.putString(key + "_string", value.toString())
                                    }
                                    is Boolean -> {
                                        responseValues.putBoolean(key, value)
                                        responseValues.putString(key + "_string", value.toString())
                                    }
                                    is Double -> {
                                        responseValues.putDouble(key, value)
                                        responseValues.putString(key + "_string", value.toString())
                                    }
                                    else -> responseValues.putString(key, valueString)
                                }
                                
                                // Special handling for "Response" key (contains JSON data)
                                if (key == "Response") {
                                    Log.d("GeoSpatialService", "Found Response key with JSON data: $valueString")
                                }
                            }
                            
                            // Send the response data to JavaScript (matches C# calling SyncGeoSpatialData)
                            Log.d("GeoSpatialService", "Sending GeoSpatialResponse event to JavaScript...")
                            sendEventToJS("GeoSpatialResponse", responseValues)
                            
                            Log.d("GeoSpatialService", "✅ GeoSpatial response data sent to JS successfully")
                        } else {
                            Log.w("GeoSpatialService", "Bundle is null - no data received from GIS app")
                            sendEventToJS("GeoSpatialError", "No bundle data received from GIS app")
                        }
                    } catch (e: Exception) {
                        Log.e("GeoSpatialService", "❌ Error processing GIS response", e)
                        sendEventToJS("GeoSpatialError", "Error processing GIS response: ${e.message}")
                    }
                } else {
                    Log.w("GeoSpatialService", "Intent data is null")
                    sendEventToJS("GeoSpatialError", "No intent data received from GIS app")
                }
            } else {
                Log.w("GeoSpatialService", "GIS app returned unsuccessful result code: $resultCode")
                sendEventToJS("GeoSpatialError", "GIS app returned error result: $resultCode")
            }
        } else {
            Log.d("GeoSpatialService", "Request code $requestCode does not match GIS_REQUEST_CODE ($GIS_REQUEST_CODE)")
        }
    }

    override fun onNewIntent(intent: Intent?) {
        // Not used for this implementation
    }

    @ReactMethod
    fun testGISResponse(promise: Promise) {
        try {
            Log.d("GeoSpatialService", "🧪 Testing GIS response flow...")
            
            // Create a test response that simulates what a GIS app would return
            val testResponse = Arguments.createMap().apply {
                // Add test JSON data in the Response key (typical GIS app format)
                val testJsonData = """
                [
                    {
                        "Pipe_ID": "TEST_PIPE_001",
                        "Length": "150",
                        "From_Length": "75",
                        "Design_Length": "200",
                        "Zone": "Zone_A",
                        "Design_Dia": "300",
                        "Manpower": "8",
                        "Date_Edited": "15-01-24",
                        "Direction": "Left",
                        "Remarks": "Test pipe installation completed",
                        "Start_Node": "NODE_001",
                        "Stop_Node": "NODE_002",
                        "Image_Path": "/storage/gis/test_image.jpg",
                        "Node_Status": "Completed"
                    },
                    {
                        "Pipe_ID": "TEST_PIPE_002", 
                        "Length": "100",
                        "From_Length": "50",
                        "Design_Length": "120",
                        "Zone": "Zone_B",
                        "Design_Dia": "250",
                        "Manpower": "5",
                        "Date_Edited": "16-01-24",
                        "Direction": "Right",
                        "Remarks": "Second test pipe",
                        "Start_Node": "NODE_002",
                        "Stop_Node": "NODE_003", 
                        "Image_Path": "",
                        "Node_Status": "In Progress"
                    }
                ]
                """.trimIndent()
                
                putString("Response", testJsonData)
                putString("Job_Code", "TEST_JOB_123")
                putString("BOQ_Code", "1074")
                putString("Status", "Success")
                putInt("RecordCount", 2)
                putBoolean("HasImages", true)
            }
            
            Log.d("GeoSpatialService", "🧪 Sending test GeoSpatialResponse event...")
            sendEventToJS("GeoSpatialResponse", testResponse)
            
            promise.resolve("Test GIS response sent successfully")
            
        } catch (e: Exception) {
            Log.e("GeoSpatialService", "Error in test GIS response", e)
            promise.reject("ERROR", "Test failed: ${e.message}", e)
        }
    }

    @ReactMethod 
    fun validateGISIntegration(promise: Promise) {
        try {
            val validationResult = Arguments.createMap()
            
            // Check if GIS package is installed
            val packageName = "lnt.geospatial.egeospatial"
            var isInstalled = false
            var hasLaunchIntent = false
            var packageVersion = "Unknown"
            
            try {
                val packageInfo = reactContext.packageManager.getPackageInfo(packageName, 0)
                isInstalled = true
                packageVersion = packageInfo.versionName ?: "Unknown"
                
                val launchIntent = reactContext.packageManager.getLaunchIntentForPackage(packageName)
                hasLaunchIntent = launchIntent != null
                
            } catch (e: Exception) {
                Log.d("GeoSpatialService", "Package validation: ${e.message}")
            }
            
            // Check if current activity is available for startActivityForResult
            val hasCurrentActivity = reactContext.currentActivity != null
            
            validationResult.apply {
                putBoolean("isGISAppInstalled", isInstalled)
                putBoolean("hasLaunchIntent", hasLaunchIntent)
                putBoolean("hasCurrentActivity", hasCurrentActivity)
                putString("packageVersion", packageVersion)
                putString("targetPackage", packageName)
                putBoolean("readyForIntegration", isInstalled && hasLaunchIntent && hasCurrentActivity)
            }
            
            promise.resolve(validationResult)
            
        } catch (e: Exception) {
            promise.reject("ERROR", "Validation failed: ${e.message}", e)
        }
    }
}
