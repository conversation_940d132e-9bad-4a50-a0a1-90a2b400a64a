# must be unique in a given SonarQube instance

sonar.projectKey=e<PERSON>ragathi

# this is the name and version displayed in the SonarQube UI. Was mandatory prior to SonarQube 6.1.

sonar.projectName=ePragathi

sonar.projectVersion=1.0

# sonar.host.url=https://sonarqube.optisolbusiness.com
sonar.host.url=http://localhost:9000

sonar.sources=src

sonar.exclusions=**/node_modules/**, babel.config.js, src/config/config.tsx, src/unit_testing/**/*, **/*.test.ts, **/*.test.tsx, **/*.spec.ts, **/*.spec.tsx, **/__tests__/**

# sonar.tests=src/unit_testing

sonar.test.inclusions=**/*.test.tsx

sonar.typescript.lcov.reportPaths=coverage/lcov.info
sonar.issue.ignore.multicriteria=e1,e2
# ignore the nested inline condition
sonar.issue.ignore.multicriteria.e1.ruleKey=typescript:S3358
sonar.issue.ignore.multicriteria.e1.resourceKey=**/*.tsx
# ignore the congintive condition
sonar.issue.ignore.multicriteria.e2.ruleKey=typescript:S3776
sonar.issue.ignore.multicriteria.e2.resourceKey=**/*.tsx

# sonar.coverage.exclusions=**/database/**/*.*

sonar.cpd.inclusions=src

# sonar.cpd.exclusions=**/database/**/*.*

sonar.login=squ_15811b6a144937e42a341f054ba4d07b3cc2ab72

# Path is relative to the sonar-project.properties file. Replace "\" by "/" on Windows.

# This property is optional if sonar.modules is set.

# sonar.sources=src
# sonar.exclusions=src/redux/**/*
# sonar.cpd.exclusions=src/utils/**/*

# Encoding of the source code. Default is default system encoding

sonar.sourceEncoding=UTF-8

# Authentication credentials

#sonar.login=admin  

#sonar.password=opti818