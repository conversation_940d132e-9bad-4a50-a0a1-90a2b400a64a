import axios, {AxiosRequestConfig} from 'axios';
import PrintLog from '../utils/Logger/PrintLog';
import Config from 'react-native-config';

export async function AxiosRequestForAuth(apiParams: AxiosRequestConfig) {
  axios.defaults.baseURL = Config.BASE_URL_FOR_AUTH;
  try {
    const response = await axios({
      timeout: 5000,
      ...apiParams,
    });
    return response;
  } catch (error: any) {
    PrintLog.error('AxiosRequestForAuth', {'Catch Error: ': error});
    return error.response;
  }
}

export async function AxiosRequestForDownload(apiParams: AxiosRequestConfig) {
  axios.defaults.baseURL = Config.BASE_URL_FOR_DOWNLOAD;
  try {
    const response = await axios({
      timeout: 5000,
      ...apiParams,
    });
    return response;
  } catch (error: any) {
    PrintLog.error('AxiosRequestForDownload', {'Catch Error: ': error});
    return error.response;
  }
}

export async function AxiosRequest(baseUrl: string, apiParams: AxiosRequestConfig) {
  try {
    const response = await axios({
      baseURL: baseUrl, // Pass inline instead of relying on global defaults
      timeout: 5000,
      ...apiParams,
    });
    return response;
  } catch (error: any) {
    PrintLog.error('AxiosRequest', {'Catch Error: ': error});
    return error.response;
  }
}
