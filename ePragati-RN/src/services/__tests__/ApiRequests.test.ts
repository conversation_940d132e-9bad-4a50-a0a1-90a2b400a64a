import Config from 'react-native-config';
import * as Strings from '../../utils/Strings/Strings';
import PrintLog from '../../utils/Logger/PrintLog';

// ↓ mock all three request fns
jest.mock('../AxiosService', () => ({
  AxiosRequest:    jest.fn(),
  AxiosRequestForAuth:     jest.fn(),
  AxiosRequestForDownload: jest.fn(),
}));
import {
  AxiosRequest,
  AxiosRequestForAuth,
  AxiosRequestForDownload,
} from '../AxiosService';

// re‐import the module under test
import * as Api from '../ApiRequests';

// For testing getHeaders we re‐export it:
const { getHeaders } = Api as unknown as { getHeaders: typeof Api['getHeaders'] };

describe('getHeaders()', () => {
  beforeAll(() => {
    // set up Config and strings
    Config.OCP_APIM_SUBSCRIPTION_KEY_COMMON = 'COMMON_KEY';
    Config.OCP_APIM_SUBSCRIPTION_KEY        = 'KEY';
    (Strings as any).apiHeaderStrings = { PLAIN: 'PLAIN' } as any;
  });

  it('uses application/json for PLAIN and common key when url contains Pragatiext', () => {
    const hdr = getHeaders('PLAIN', 'https://ltceip4dev.azure-api.net/Pragati/mobile/Pragatiext/endpoint');
    expect(hdr['Content-Type']).toBe('application/json');
    expect(hdr['Ocp-Apim-Subscription-Key']).toBe('COMMON_KEY');
    expect(hdr.Authorization).toBeUndefined();
  });

  it('uses multipart/form-data for non-PLAIN and branch key when url contains Pragati', () => {
    const hdr = getHeaders('OTHER', 'https://ltceip4dev.azure-api.net/Pragati/mobile/Pragati/foo');
    expect(hdr['Content-Type']).toBe('multipart/form-data');
    expect(hdr['Ocp-Apim-Subscription-Key']).toBe('KEY');
  });

  it('adds raw token header when token is provided and not “GetActualsForApproval”', () => {
    const hdr = getHeaders('PLAIN', 'https://ltceip4dev.azure-api.net/Pragati/mobile/any', 'my-token');
    expect(hdr.Authorization).toBe('my-token');
  });

  it('prefixes Bearer when url includes GetActualsForApproval', () => {
    const hdr = getHeaders('PLAIN', 'https://ltceip4dev.azure-api.net/Pragati/mobile/GetActualsForApproval', 'xyz');
    expect(hdr.Authorization).toBe('Bearer xyz');
  });
});

describe('getData / getDataWithParams', () => {
  const success = { foo: 'bar' };
  const error   = { message: 'nope' };

  beforeEach(() => {
    (PrintLog.debug as jest.Mock) = jest.fn();
    (PrintLog.error as jest.Mock) = jest.fn();
  });

  it('getData calls onSuccess when data present', async () => {
    (AxiosRequestForDownload as jest.Mock).mockResolvedValue({ data: success });

    const onSuccess = jest.fn();
    const onError   = jest.fn();

    await Api.getData('fn', '/url', onSuccess, onError);

    expect(AxiosRequestForDownload).toHaveBeenCalledWith({
      url: '/url', method: 'GET'
    });
    expect(onSuccess).toHaveBeenCalledWith(success);
    expect(onError).not.toHaveBeenCalled();
  });

  it('getData calls onError when data missing', async () => {
    (AxiosRequestForDownload as jest.Mock).mockResolvedValue({ });

    const onSuccess = jest.fn();
    const onError   = jest.fn();

    await Api.getData('fn', '/url', onSuccess, onError);

    expect(onError).toHaveBeenCalledWith({});
  });

  it('getData catches and calls onError on exception', async () => {
    (AxiosRequestForDownload as jest.Mock).mockRejectedValue({ response: error });

    const onSuccess = jest.fn();
    const onError   = jest.fn();

    await Api.getData('fn', '/url', onSuccess, onError);

    expect(onError).toHaveBeenCalledWith(error);
  });

  it('getDataWithParams passes params through', async () => {
    (AxiosRequestForDownload as jest.Mock).mockResolvedValue({ data: success });
    const onSuccess = jest.fn();
    await Api.getDataWithParams('f2', '/u2', { q: 1 }, onSuccess, jest.fn());
    expect(AxiosRequestForDownload).toHaveBeenCalledWith({
      url: '/u2', method: 'GET', params: { q: 1 }
    });
    expect(onSuccess).toHaveBeenCalledWith(success);
  });
});

describe('postDataWithBodyForAuth / postDataWithBodyForDownload / postDataWithBodyToken / putDataWithBody', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('postDataWithBodyForAuth → success', async () => {
    (AxiosRequestForAuth as jest.Mock).mockResolvedValue({ data: 'ok' });
    const onSuccess = jest.fn(), onError = jest.fn();
    await Api.postDataWithBodyForAuth('/auth', { a: 1 }, onSuccess, onError);
    expect(AxiosRequestForAuth).toHaveBeenCalled();
    expect(onSuccess).toHaveBeenCalledWith('ok');
    expect(onError).not.toHaveBeenCalled();
  });

  it('postDataWithBodyForAuth → no data calls onError', async () => {
    (AxiosRequestForAuth as jest.Mock).mockResolvedValue({ });
    const onError = jest.fn(), onSuccess = jest.fn();
    await Api.postDataWithBodyForAuth('/auth', {}, onSuccess, onError);
    expect(onError).toHaveBeenCalled();
  });

  it('postDataWithBodyForAuth → exception calls onError', async () => {
    (AxiosRequestForAuth as jest.Mock).mockRejectedValue({ response: 'err' });
    const onError = jest.fn(), onSuccess = jest.fn();
    await Api.postDataWithBodyForAuth('/auth', {}, onSuccess, onError);
    expect(onError).toHaveBeenCalledWith('err');
  });

  it('postDataWithBodyForDownload → success and error', async () => {
    (AxiosRequestForDownload as jest.Mock).mockResolvedValue({ data: 42 });
    const ok = jest.fn(), nok = jest.fn();
    await Api.postDataWithBodyForDownload('/dl', { x: 1 }, ok, nok);
    expect(ok).toHaveBeenCalledWith(42);

    (AxiosRequestForDownload as jest.Mock).mockResolvedValue({});
    await Api.postDataWithBodyForDownload('/dl', { x: 1 }, ok, nok);
    expect(nok).toHaveBeenCalled();
  });

  it('postDataWithBodyToken → success and error', async () => {
    (AxiosRequest as jest.Mock).mockResolvedValue({ data: 'T' });
    const ok = jest.fn(), nok = jest.fn();
    await Api.postDataWithBodyToken('/tok', { }, ok, nok);
    expect(ok).toHaveBeenCalledWith('T');

    (AxiosRequest as jest.Mock).mockResolvedValue({});
    await Api.postDataWithBodyToken('/tok', { }, ok, nok);
    expect(nok).toHaveBeenCalled();
  });

  it('putDataWithBody → success and error', async () => {
    (AxiosRequestForDownload as jest.Mock).mockResolvedValue({ data: 7 });
    const ok = jest.fn(), nok = jest.fn();
    await Api.putDataWithBody('/put', {}, ok, nok);
    expect(ok).toHaveBeenCalledWith(7);

    (AxiosRequestForDownload as jest.Mock).mockResolvedValue({});
    await Api.putDataWithBody('/put', {}, ok, nok);
    expect(nok).toHaveBeenCalled();
  });
});
