import axios from 'axios';
import { AxiosRequestForAuth, AxiosRequestForDownload, AxiosRequest } from '../AxiosService';
import PrintLog from '../../utils/Logger/PrintLog';

jest.mock('axios');

jest.mock('react-native-config', () => ({
  BASE_URL_FOR_AUTH: 'https://auth.example.com',
  BASE_URL_FOR_DOWNLOAD: 'https://download.example.com',
}));
jest.mock('../../utils/Logger/PrintLog', () => ({
  error: jest.fn(),
}));

describe('AxiosRequest functions', () => {
  const mockedAxios = axios as jest.MockedFunction<typeof axios>;
  const mockResponse = { data: 'mockData', status: 200 };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('AxiosRequestForAuth', () => {
    it('should return data when request succeeds', async () => {
      mockedAxios.mockResolvedValueOnce(mockResponse);

      const result = await AxiosRequestForAuth({ url: '/login', method: 'GET' });

      expect(mockedAxios).toHaveBeenCalledWith(expect.objectContaining({
        timeout: 5000,
        url: '/login',
        method: 'GET',
      }));
      expect(result).toEqual(mockResponse);
      expect(PrintLog.error).not.toHaveBeenCalled();
    });

    it('should log error and return error response when request fails', async () => {
      const error = { response: { status: 401 }, message: 'Unauthorized' };
      mockedAxios.mockRejectedValueOnce(error);

      const result = await AxiosRequestForAuth({ url: '/login', method: 'POST' });

      expect(PrintLog.error).toHaveBeenCalledWith('AxiosRequestForAuth', { 'Catch Error: ': error });
      expect(result).toEqual(error.response);
    });
  });

  describe('AxiosRequestForDownload', () => {
    it('should return data on success', async () => {
      mockedAxios.mockResolvedValueOnce(mockResponse);

      const result = await AxiosRequestForDownload({ url: '/file', method: 'GET' });

      expect(mockedAxios).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });

    it('should return error response on failure', async () => {
      const error = { response: { status: 404 }, message: 'Not Found' };
      mockedAxios.mockRejectedValueOnce(error);

      const result = await AxiosRequestForDownload({ url: '/missing-file', method: 'GET' });

      expect(PrintLog.error).toHaveBeenCalledWith('AxiosRequestForDownload', { 'Catch Error: ': error });
      expect(result).toEqual(error.response);
    });
  });

  describe('AxiosRequest (generic)', () => {
    it('should use provided base URL and succeed', async () => {
      mockedAxios.mockResolvedValueOnce(mockResponse);

      const result = await AxiosRequest('https://custom.api', {
        url: '/custom-endpoint',
        method: 'GET',
      });

      expect(mockedAxios).toHaveBeenCalledWith(expect.objectContaining({
        baseURL: 'https://custom.api',
        url: '/custom-endpoint',
        method: 'GET',
      }));
      expect(result).toEqual(mockResponse);
    });

    it('should handle error and log it', async () => {
      const error = { response: { status: 500 }, message: 'Server Error' };
      mockedAxios.mockRejectedValueOnce(error);

      const result = await AxiosRequest('https://custom.api', {
        url: '/error-endpoint',
        method: 'POST',
      });

      expect(PrintLog.error).toHaveBeenCalledWith('AxiosRequest', { 'Catch Error: ': error });
      expect(result).toEqual(error.response);
    });
  });
});
