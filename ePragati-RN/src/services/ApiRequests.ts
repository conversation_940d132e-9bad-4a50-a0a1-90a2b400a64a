import BaseApiConstants from '../utils/Constants/CommonConstant';
import PrintLog from '../utils/Logger/PrintLog';
import Strings from '../utils/Strings/Strings';
import Config from 'react-native-config';
import {
  AxiosRequest,
  AxiosRequestForAuth,
  AxiosRequestForDownload,
} from './AxiosService';

const getHeaders = (
  type: string,
  url: string,
  token?: string
): Record<string, string> => {
  const isPlain = type.toUpperCase() === Strings.apiHeaderStrings.PLAIN.toUpperCase();

  const isPragatiext = url.toLowerCase().includes('pragatiext');

  const contentType =
    isPlain && isPragatiext
      ? 'application/json'
      : 'multipart/form-data';

  const subscriptionKey = isPragatiext
    ? Config.OCP_APIM_SUBSCRIPTION_KEY_COMMON!
    : Config.OCP_APIM_SUBSCRIPTION_KEY!;

  const headers: Record<string, string> = {
    'Content-Type': contentType,
    'Ocp-Apim-Subscription-Key': subscriptionKey,
  };

  if (token) {
    headers.Authorization = url.includes('GetActualsForApproval')
      ? `Bearer ${token}`
      : token;
  }

  return headers;
};

const getData = async <response = any>(
  callingFunction: string,
  urlSuffix: string,
  onSuccess: (res: response) => void,
  onError: (err: any) => void,
): Promise<void> => {
  PrintLog.debug('getData', {
    'CallingFunction: ': callingFunction,
    ' -- urlSuffix: ': urlSuffix,
  });
  try {
    const result = await AxiosRequestForDownload({
      url: urlSuffix,
      method: 'GET',
    });
    PrintLog.debug('getData', {
      'CallingFunction: ': callingFunction,
      ' -- result: ': result,
    });
    if (result?.data) {
      onSuccess(result.data as response);
    } else {
      onError(result);
    }
  } catch (error: any) {
    PrintLog.error('getData', {
      'CallingFunction: ': callingFunction,
      ' catch Error: ': error,
    });
    onError(error?.response ?? error);
  }
};

const getDataWithParams = async <params = any, response = any>(
  callingFunction: string,
  urlSuffix: string,
  queryParams: params,
  onSuccess: (res: response) => void,
  onError: (err: any) => void,
): Promise<void> => {
  PrintLog.debug('getDataWithParams', {
    'CallingFunction: ': callingFunction,
    ' -- urlSuffix: ': urlSuffix,
    ' -- queryParams: ': queryParams,
  });
  try {
    const result = await AxiosRequestForDownload({
      url: urlSuffix,
      method: 'GET',
      params: queryParams,
    });
    PrintLog.debug('getDataWithParams', {
      'CallingFunction: ': callingFunction,
      ' -- result: ': result,
    });
    if (result?.data) {
      onSuccess(result.data as response);
    } else {
      onError(result);
    }
  } catch (error: any) {
    PrintLog.error('getDataWithParams', {
      'CallingFunction: ': callingFunction,
      ' catch Error: ': error,
    });
    onError(error?.response ?? error);
  }
};

const postDataWithBodyForAuth = async <data = any, response = any>(
  urlSuffix: string,
  body: data,
  onSuccess: (res: response) => void,
  onError: (err: any) => void,
): Promise<void> => {
  try {
    const result = await AxiosRequestForAuth({
      url: urlSuffix,
      method: 'POST',
      data: body,
      headers: getHeaders(
        Strings.apiHeaderStrings.PLAIN,
        Config.BASE_URL_FOR_DOWNLOAD + urlSuffix,
        '',
      ),
    });
    PrintLog.debug('postDataWithBodyForAuth', {'result: ': result});
    if (result?.data) {
      onSuccess?.(result.data as response);
    } else {
      onError?.(result);
      PrintLog.debug('postDataWithBodyForAuth', {'Error: ': result});
    }
  } catch (error: any) {
    onError?.(error?.response ?? error);
    PrintLog.debug('postDataWithBodyForAuth', {'Catch Error: ': error});
  }
};

const postDataWithBodyForDownload = async <data = any, response = any>(
  urlSuffix: string,
  body: data,
  onSuccess: (res: response) => void,
  onError: (err: any) => void,
): Promise<void> => {
  try {
    const result = await AxiosRequestForDownload({
      url: urlSuffix,
      method: 'POST',
      data: body,
      headers: getHeaders(
        Strings.apiHeaderStrings.PLAIN,
        Config.BASE_URL_FOR_DOWNLOAD + urlSuffix,
        '',
      ),
    });

    if (result?.data) {
      onSuccess?.(result.data as response);
    } else {
      onError?.(result);
    }
  } catch (error: any) {
    onError?.(error?.response ?? error);
  }
};

const postDataWithBodyToken = async <data = any, response = any>(
  urlSuffix: string,
  body: data,
  onSuccess: (res: response) => void,
  onError: (err: any) => void,
): Promise<void> => {
  try {
    const result = await AxiosRequest(BaseApiConstants.defaultGenerateToken, {
      url: urlSuffix,
      method: 'POST',
      data: body,
      headers: getHeaders(
        Strings.apiHeaderStrings.PLAIN,
        BaseApiConstants.defaultGenerateToken + urlSuffix,
        '',
      ),
    });

    if (result?.data) {
      onSuccess?.(result.data as response);
    } else {
      onError?.(result);
    }
  } catch (error: any) {
    onError?.(error?.response ?? error);
  }
};

const putDataWithBody = async <data = any, response = any>(
  urlSuffix: string,
  body: data,
  onSuccess: (res: response) => void,
  onError: (err: any) => void,
): Promise<void> => {
  try {
    const result = await AxiosRequestForDownload({
      url: urlSuffix,
      method: 'PUT',
      data: body,
    });

    if (result?.data) {
      onSuccess?.(result.data as response);
    } else {
      onError?.(result);
    }
  } catch (error: any) {
    onError?.(error?.response ?? error);
  }
};

export {
  getHeaders,
  getData,
  getDataWithParams,
  postDataWithBodyForAuth,
  postDataWithBodyForDownload,
  postDataWithBodyToken,
  putDataWithBody,
};
