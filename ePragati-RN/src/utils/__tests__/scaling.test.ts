import {
    scale,
    verticalScale,
    ms as moderateScale,
    moderateVerticalScale,
    width,
    height,
  } from '../Scale/Scaling';
  
  describe('Scaling Utilities', () => {
    const baseWidth = 375;
    const baseHeight = 812;
  
    test('should correctly get screen dimensions', () => {
      expect(typeof width).toBe('number');
      expect(typeof height).toBe('number');
      expect(width).toBeGreaterThan(0);
      expect(height).toBeGreaterThan(0);
    });
  
    test('scale should correctly scale width-based values', () => {
      const value = 100;
      const expected = (width / baseWidth) * value;
      expect(scale(value)).toBeCloseTo(expected);
    });
  
    test('verticalScale should correctly scale height-based values', () => {
      const value = 100;
      const expected = (height / baseHeight) * value;
      expect(verticalScale(value)).toBeCloseTo(expected);
    });
  
    test('moderateScale should scale width moderately based on factor', () => {
      const value = 100;
      const factor = 0.5;
      const scaled = scale(value);
      const expected = value + (scaled - value) * factor;
      expect(moderateScale(value, factor)).toBeCloseTo(expected);
    });
  
    test('moderateVerticalScale should scale height moderately based on factor', () => {
      const value = 100;
      const factor = 0.5;
      const scaled = verticalScale(value);
      const expected = value + (scaled - value) * factor;
      expect(moderateVerticalScale(value, factor)).toBeCloseTo(expected);
    });
  
    test('moderateScale should use default factor when not provided', () => {
      const value = 100;
      const defaultFactor = 0.5;
      const scaled = scale(value);
      const expected = value + (scaled - value) * defaultFactor;
      expect(moderateScale(value)).toBeCloseTo(expected);
    });
  
    test('moderateVerticalScale should use default factor when not provided', () => {
      const value = 100;
      const defaultFactor = 0.5;
      const scaled = verticalScale(value);
      const expected = value + (scaled - value) * defaultFactor;
      expect(moderateVerticalScale(value)).toBeCloseTo(expected);
    });
  });
  