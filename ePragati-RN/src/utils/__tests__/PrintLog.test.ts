import PrintLog from '../Logger/PrintLog'; // Adjust the path as needed

describe('PrintLog', () => {
  const originalDebug = console.debug;
  const originalError = console.error;
  const originalDev = (global as any).__DEV__;

  beforeEach(() => {
    console.debug = jest.fn();
    console.error = jest.fn();
  });

  afterEach(() => {
    console.debug = originalDebug;
    console.error = originalError;
    (global as any).__DEV__ = originalDev;
  });

  test('PrintLog.debug should log message when __DEV__ is true', () => {
    (global as any).__DEV__ = true;

    PrintLog.debug('TestTag', 'This is a debug message', { test: true });

    expect(console.debug).toHaveBeenCalledWith(
      '[DEBUG] TestTag:',
      'This is a debug message',
      { test: true }
    );
  });

  test('PrintLog.debug should not log message when __DEV__ is false', () => {
    (global as any).__DEV__ = false;

    PrintLog.debug('TestTag', 'This message should not appear');

    expect(console.debug).not.toHaveBeenCalled();
  });

  test('PrintLog.error should always log error', () => {
    PrintLog.error('ErrorTag', 'Something went wrong', new Error('Boom'));

    expect(console.error).toHaveBeenCalledWith(
      '[ERROR] ErrorTag:',
      'Something went wrong',
      expect.any(Error)
    );
  });
});
