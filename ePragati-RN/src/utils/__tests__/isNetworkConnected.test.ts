import NetInfo from '@react-native-community/netinfo';
import { isNetworkConnected } from '../Network/NetworkConnection';
import { customAlertWithOK } from '../../components/CustomAlert';
import Strings from '../Strings/Strings';
import { t } from 'i18next';

// Mock the NetInfo module
jest.mock('@react-native-community/netinfo', () => ({
  fetch: jest.fn(),
}));

// Mock the alert
jest.mock('../../components/CustomAlert', () => ({
  customAlertWithOK: jest.fn(),
}));

describe('isNetworkConnected', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return true when network is connected', async () => {
    (NetInfo.fetch as jest.Mock).mockResolvedValue({ isConnected: true });

    const result = await isNetworkConnected();
    expect(result).toBe(true);
    expect(customAlertWithOK).not.toHaveBeenCalled();
  });

  it('should return false and show alert when not connected', async () => {
    (NetInfo.fetch as jest.Mock).mockResolvedValue({ isConnected: false });

    const result = await isNetworkConnected(true);
    expect(result).toBe(false);
    expect(customAlertWithOK).toHaveBeenCalledWith(
      t('alertMessageStrings.networkAlertTitle'),
      t('alertMessageStrings.noNetworkConnection'),
      [{ text: t('commonStrings.ok')}],
      false
    );
  });

  it('should return false and not show alert when not connected and showAlert is false', async () => {
    (NetInfo.fetch as jest.Mock).mockResolvedValue({ isConnected: false });

    const result = await isNetworkConnected(false);
    expect(result).toBe(false);
    expect(customAlertWithOK).not.toHaveBeenCalled();
  });
});
