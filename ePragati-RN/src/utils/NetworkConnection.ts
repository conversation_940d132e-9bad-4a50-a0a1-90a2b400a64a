import NetInfo from '@react-native-community/netinfo';
import { Alert } from 'react-native';
import Strings from './Strings/Strings';

export const checkNetworkConnection = async (showAlert = true): Promise<boolean> => {
  const state = await NetInfo.fetch();

  if (!state.isConnected) {
    if (showAlert) {
      Alert.alert(
        Strings.alertMessageStrings.networkAlertTitle,
        Strings.alertMessageStrings.noNetworkConnection,
        [{ text: Strings.commonStrings.ok }],
        { cancelable: false }
      );
    }
    return false;
  }
  return true;
};