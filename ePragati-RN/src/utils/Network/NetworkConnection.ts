import NetInfo from '@react-native-community/netinfo';
import Strings from '../Strings/Strings';
import { customAlertWithOK } from '../../components/CustomAlert';
import { t } from 'i18next';

export const isNetworkConnected = async (
  showAlert = true,
): Promise<boolean> => {
  const state = await NetInfo.fetch();

  if (!state.isConnected) {
    if (showAlert) {
      customAlertWithOK(
        t('alertMessageStrings.networkAlertTitle'),
        t('alertMessageStrings.noNetworkConnection'),
        [{ text: t('commonStrings.ok')}],
        false,
      );
    }
    return false;
  }
  return true;
};
