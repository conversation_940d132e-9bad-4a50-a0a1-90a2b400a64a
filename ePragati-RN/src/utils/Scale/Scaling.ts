import { Dimensions } from 'react-native';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Base dimensions (iPhone X as reference)
const guidelineBaseWidth = 375;
const guidelineBaseHeight = 812;

// Basic scaling functions
const widthScale = (size: number): number => (SCREEN_WIDTH / guidelineBaseWidth) * size;
const heightScale = (size: number): number => (SCREEN_HEIGHT / guidelineBaseHeight) * size;

// Moderate scaling with factor
const moderateScale = (size: number, factor: number = 0.5): number => {
    const scaledSize = widthScale(size);
    return size + (scaledSize - size) * factor;
};

// Moderate vertical scaling
const moderateVerticalScale = (size: number, factor: number = 0.5): number => {
    const scaledSize = heightScale(size);
    return size + (scaledSize - size) * factor;
};

export {
    SCREEN_WIDTH as width,
    SCREEN_HEIGHT as height,
    widthScale as scale,
    heightScale as verticalScale,
    moderateScale as ms,
    moderateVerticalScale,
};