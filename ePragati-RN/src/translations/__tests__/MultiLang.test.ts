import i18n from '../index'; // Update the path as needed

describe('i18n configuration', () => {
    beforeAll(async () => {
      await i18n.init({
        resources: {
          en: { translation: { welcome: 'Welcome' } },
          fr: { translation: { welcome: 'Bienvenue' } },
        },
        lng: 'en',
        fallbackLng: 'en',
        interpolation: { escapeValue: false },
      });
    });
  
    it('should return correct English translation', () => {
      const translated = i18n.t('welcome');
      expect(translated).toBe('Welcome');
    });
  
    it('should return correct French translation when language is changed', async () => {
      await i18n.changeLanguage('fr');
      const translated = i18n.t('welcome');
      expect(translated).toBe('Bienvenue');
    });
  });
