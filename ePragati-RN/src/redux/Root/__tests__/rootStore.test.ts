import { configureStore } from '@reduxjs/toolkit';
import createSagaMiddleware from 'redux-saga';
import { LoginReducer } from '../../AuthRedux/Login/LoginReducer';
import { loginSuccess, loginFailure } from '../../AuthRedux/Login/LoginActions';
import rootSaga from '../rootSaga';

const createTestStore = () => {
  const sagaMiddleware = createSagaMiddleware();
  const store = configureStore({
    reducer: {
      auth: LoginReducer,
    },
    middleware: getDefaultMiddleware =>
      getDefaultMiddleware({
        serializableCheck: false,
        thunk: false,
      }).concat(sagaMiddleware),
  });

  sagaMiddleware.run(rootSaga);
  return store;
};

describe('Redux Store', () => {
  let store: ReturnType<typeof createTestStore>;

  beforeEach(() => {
    store = createTestStore();
  });

  it('should dispatch loginSuccess action and update state', () => {
    const mockPayload = {
      StatusCode: 200,
      Message: 'Success',
      Token: 'mockToken',
    };

    store.dispatch(loginSuccess(mockPayload));
    const state = store.getState();

    expect(state.auth).toMatchObject({
      loading: false,
      user: mockPayload,
      error: null,
    });
  });

  it('should dispatch loginFailure action and update state', () => {
    const mockError = 'Invalid credentials';

    store.dispatch(loginFailure(mockError));
    const state = store.getState();

    expect(state.auth).toMatchObject({
      loading: false,
      user: null,
      error: mockError,
    });
  });
});
