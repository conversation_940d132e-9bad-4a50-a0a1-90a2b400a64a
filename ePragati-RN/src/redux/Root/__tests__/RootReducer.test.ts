import { RootReducer } from '../RootReducer';
import { LOGIN_REQUEST, LOGIN_SUCCESS, LOGIN_FAILURE } from '../../AuthRedux/Login/LoginActionTypes';
import { LoginResponseData } from '../../../model/Auth/LoginData';

describe('RootReducer', () => {
    const initialState = {
        loginReducer: {
            user: null,
            loading: false,
            error: null,
        },
    };

    it('should return initial state', () => {
        const state = RootReducer(undefined, { type: 'INIT' });
        expect(state).toEqual(initialState);
    });

    it('should handle LOGIN_REQUEST', () => {
        const action = { type: LOGIN_REQUEST };
        const state = RootReducer(initialState, action);

        expect(state.loginReducer).toEqual({
            user: null,
            loading: true,
            error: null,
        });
    });

    it('should handle LOGIN_SUCCESS', () => {
        const mockUserData: LoginResponseData = {
            Message: 'Login successful',
            Status: 'Success',
            StatusCode: 200,
            token: 'test-token',
        };

        const action = { type: LOGIN_SUCCESS, payload: mockUserData };
        const state = RootReducer(initialState, action);

        expect(state.loginReducer).toEqual({
            user: mockUserData,
            loading: false,
            error: null,
        });
    });

    it('should handle LOGIN_FAILURE', () => {
        const errorMessage = 'Invalid credentials';
        const action = { type: LOGIN_FAILURE, payload: errorMessage };
        const state = RootReducer(initialState, action);

        expect(state.loginReducer).toEqual({
            user: null,
            loading: false,
            error: errorMessage,
        });
    });

    it('should maintain state immutability', () => {
        const action = { type: LOGIN_REQUEST };
        const state = RootReducer(initialState, action);

        // Original state should remain unchanged
        expect(initialState).toEqual({
            loginReducer: {
                user: null,
                loading: false,
                error: null,
            },
        });

        // New state should be different object
        expect(state).not.toBe(initialState);
        expect(state.loginReducer).not.toBe(initialState.loginReducer);
    });

    it('should handle unknown action', () => {
        const action = { type: 'UNKNOWN_ACTION' };
        const state = RootReducer(initialState, action);

        expect(state).toEqual(initialState);
    });

    it('should handle action sequence', () => {
        // Start with initial state
        let state = RootReducer(undefined, { type: 'INIT' });
        expect(state).toEqual(initialState);

        // Request login
        state = RootReducer(state, { type: LOGIN_REQUEST });
        expect(state.loginReducer.loading).toBe(true);
        expect(state.loginReducer.error).toBeNull();

        // Login success
        const mockUserData: LoginResponseData = {
            Message: 'Login successful',
            Status: 'Success',
            StatusCode: 200,
            token: 'test-token',
        };
        state = RootReducer(state, { type: LOGIN_SUCCESS, payload: mockUserData });
        expect(state.loginReducer.loading).toBe(false);
        expect(state.loginReducer.user).toEqual(mockUserData);
        expect(state.loginReducer.error).toBeNull();

        // Login failure
        const errorMessage = 'Invalid credentials';
        state = RootReducer(state, { type: LOGIN_FAILURE, payload: errorMessage });
        expect(state.loginReducer.loading).toBe(false);
        expect(state.loginReducer.user).toEqual(mockUserData);
        expect(state.loginReducer.error).toBe(errorMessage);
    });
}); 