import { all, fork } from 'redux-saga/effects';
import rootSaga from '../rootSaga';
import loginSaga from '../../AuthRedux/Login/LoginSaga';
import PrintLog from '../../../utils/Logger/PrintLog';

// Mock dependencies
jest.mock('../../../utils/Logger/PrintLog');
jest.mock('../../AuthRedux/Login/LoginSaga');

describe('RootSaga', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should fork all sagas', () => {
        const generator = rootSaga();

        // First yield should be the all effect with forked sagas
        const allEffect = generator.next().value;
        expect(allEffect).toEqual(all([fork(loginSaga)]));

        // Second yield should be the debug log
        expect(generator.next().value).toBeUndefined();
        expect(PrintLog.debug).toHaveBeenCalledWith('rootSaga', 'Running root saga');

        // Saga should be done
        expect(generator.next().done).toBe(true);
    });

    it('should handle errors', () => {
        const mockError = new Error('Test error');
        const generator = rootSaga();

        // Move past the all effect
        generator.next();

        // Simulate an error
        const errorResult = generator.throw(mockError);

        // Should log the error
        expect(PrintLog.debug).toHaveBeenCalledWith('Saga error:', mockError);

        // Saga should be done after error
        expect(errorResult.done).toBe(true);
    });

    it('should include all required sagas', () => {
        const generator = rootSaga();

        // Get the all effect
        const allEffect = generator.next().value;

        // Check that loginSaga is included
        expect(allEffect).toEqual(all([fork(loginSaga)]));
    });

    it('should maintain saga composition', () => {
        const generator = rootSaga();

        // Get the all effect
        const allEffect = generator.next().value;

        // Verify the structure of the all effect
        const expectedEffect = all([fork(loginSaga)]);
        expect(allEffect).toEqual(expectedEffect);
    });
}); 