import {
    LOGIN_REQUEST,
    LOGIN_SUCCESS,
    LOGIN_FAILURE,
} from '../LoginActionTypes';

describe('LoginActionTypes', () => {
    it('should have the correct action type for LOGIN_REQUEST', () => {
        expect(LOGIN_REQUEST).toBe('LOGIN_REQUEST');
    });

    it('should have the correct action type for LOGIN_SUCCESS', () => {
        expect(LOGIN_SUCCESS).toBe('LOGIN_SUCCESS');
    });

    it('should have the correct action type for LOGIN_FAILURE', () => {
        expect(LOGIN_FAILURE).toBe('LOGIN_FAILURE');
    });
}); 