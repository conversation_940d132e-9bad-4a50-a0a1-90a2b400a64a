import {
    loginRequest,
    loginSuccess,
    loginFailure,
} from '../LoginActions';
import {
    LOGIN_REQUEST,
    LOGIN_SUCCESS,
    LOGIN_FAILURE,
} from '../LoginActionTypes';
import { LoginRequestData, LoginResponseData } from '../../../../model/Auth/LoginData';
import { TEST_CREDENTIALS } from '../../../../config/testConfig';

describe('LoginActions', () => {
    describe('loginRequest', () => {
        it('should create an action to request login', () => {
            const mockLoginData: LoginRequestData = {
                userId: TEST_CREDENTIALS.userId,
                password: TEST_CREDENTIALS.password,
            };

            const expectedAction = {
                type: LOGIN_REQUEST,
                payload: mockLoginData,
            };

            expect(loginRequest(mockLoginData)).toEqual(expectedAction);
        });
    });

    describe('loginSuccess', () => {
        it('should create an action for successful login', () => {
            const mockResponseData: LoginResponseData = {
                Message: 'Login successful',
                Status: 'Success',
                StatusCode: 200,
                token: 'mock-token',
            };

            const expectedAction = {
                type: LOGIN_SUCCESS,
                payload: mockResponseData,
            };

            expect(loginSuccess(mockResponseData)).toEqual(expectedAction);
        });
    });

    describe('loginFailure', () => {
        it('should create an action for failed login', () => {
            const errorMessage = 'Invalid credentials';

            const expectedAction = {
                type: LOGIN_FAILURE,
                payload: errorMessage,
            };

            expect(loginFailure(errorMessage)).toEqual(expectedAction);
        });
    });
}); 