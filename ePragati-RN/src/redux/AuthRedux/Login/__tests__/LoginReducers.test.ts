import {
    LOGIN_REQUEST,
    LOGIN_SUCCESS,
    LOGIN_FAILURE,
} from '../LoginActionTypes';
import { LoginRequestData, LoginResponseData } from '../../../../model/Auth/LoginData';
import { TEST_CREDENTIALS } from '../../../../config/testConfig';

interface LoginState {
    loading: boolean;
    error: string | null;
    data: LoginResponseData | null;
}

const initialState: LoginState = {
    loading: false,
    error: null,
    data: null,
};

const loginReducer = (state = initialState, action: any): LoginState => {
    switch (action.type) {
        case LOGIN_REQUEST:
            return {
                ...state,
                loading: true,
                error: null,
            };
        case LOGIN_SUCCESS:
            return {
                ...state,
                loading: false,
                data: action.payload,
                error: null,
            };
        case LOGIN_FAILURE:
            return {
                ...state,
                loading: false,
                error: action.payload,
            };
        default:
            return state;
    }
};

describe('LoginReducer', () => {
    it('should return the initial state', () => {
        expect(loginReducer(undefined, { type: 'UNKNOWN_ACTION' })).toEqual(initialState);
    });

    it('should handle LOGIN_REQUEST', () => {
        const mockLoginData: LoginRequestData = {
            userId: TEST_CREDENTIALS.userId,
            password: TEST_CREDENTIALS.password,
        };

        const expectedState = {
            ...initialState,
            loading: true,
            error: null,
        };

        expect(loginReducer(initialState, { type: LOGIN_REQUEST, payload: mockLoginData })).toEqual(expectedState);
    });

    it('should handle LOGIN_SUCCESS', () => {
        const mockResponseData: LoginResponseData = {
            Message: 'Login successful',
            Status: 'Success',
            StatusCode: 200,
            token: 'mock-token',
        };

        const expectedState = {
            ...initialState,
            loading: false,
            data: mockResponseData,
            error: null,
        };

        expect(loginReducer(initialState, { type: LOGIN_SUCCESS, payload: mockResponseData })).toEqual(expectedState);
    });

    it('should handle LOGIN_FAILURE', () => {
        const errorMessage = 'Invalid credentials';

        const expectedState = {
            ...initialState,
            loading: false,
            error: errorMessage,
        };

        expect(loginReducer(initialState, { type: LOGIN_FAILURE, payload: errorMessage })).toEqual(expectedState);
    });
}); 