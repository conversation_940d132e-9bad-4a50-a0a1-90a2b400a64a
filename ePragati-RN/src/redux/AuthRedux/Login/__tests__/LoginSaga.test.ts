import { runSaga } from 'redux-saga';
import { call, put } from 'redux-saga/effects';
import { loginFailure, loginSuccess } from '../LoginActions';
import { API } from '../../../../utils/Constants/ApiConstants';
import * as Api from '../../../../services/ApiRequests';
import { handleLogin } from '../LoginSaga';
import Strings from '../../../../utils/Strings/Strings';
import { t } from 'i18next';

// Mock imports
jest.mock('../../../../components/CustomAlert', () => ({
  customAlertWithOK: jest.fn(),
}));

jest.mock('../../../../utils/Logger/PrintLog', () => ({
  error: jest.fn(),
}));

describe('handleLogin Saga', () => {
  const loginParams = {
    username: 'testuser',
    password: 'testpass',
  };

  const action = {
    type: 'LOGIN_REQUEST',
    payload: loginParams,
  };

  it('should handle successful login', async () => {
    const fakeResponse = {
      StatusCode: 200,
      Message: 'Success',
      Token: 'test-token',
      UserDetails: {},
    };

    const postDataSpy = jest
      .spyOn(Api, 'postDataWithBodyForAuth')
      .mockImplementation((url, body, resolve, reject) => {
        resolve(fakeResponse);
      });

    const dispatched: any[] = [];

    await runSaga(
      {
        dispatch: (action) => dispatched.push(action),
      },
      handleLogin,
      action
    ).toPromise();

    expect(dispatched).toContainEqual(loginSuccess(fakeResponse));
    expect(postDataSpy).toHaveBeenCalledWith(API.login, loginParams, expect.any(Function), expect.any(Function));
  });

  it('should handle failed login with invalid token', async () => {
    const fakeResponse = {
      StatusCode: 401,
      Message: 'Invalid credentials',
    };

    const postDataSpy = jest
      .spyOn(Api, 'postDataWithBodyForAuth')
      .mockImplementation((url, body, resolve, reject) => {
        resolve(fakeResponse);
      });

    const dispatched: any[] = [];
    const { customAlertWithOK } = require('../../../../components/CustomAlert');

    await runSaga(
      {
        dispatch: (action) => dispatched.push(action),
      },
      handleLogin,
      action
    ).toPromise();

    expect(dispatched).toContainEqual(loginFailure(fakeResponse.Message));
    expect(customAlertWithOK).toHaveBeenCalledWith(
      t('commonStrings.alertTitle'),
      t('commonStrings.invalidToken'),
      [{ text: t('commonStrings.ok')}],
      false
    );
  });

  it('should handle login error', async () => {
    const postDataSpy = jest
      .spyOn(Api, 'postDataWithBodyForAuth')
      .mockImplementation((url, body, resolve, reject) => {
        reject(new Error('Network Error'));
      });

    const dispatched: any[] = [];
    const PrintLog = require('../../../../utils/Logger/PrintLog');

    await runSaga(
      {
        dispatch: (action) => dispatched.push(action),
      },
      handleLogin,
      action
    ).toPromise();

    expect(dispatched).toContainEqual(loginFailure('Login failed'));
    expect(PrintLog.error).toHaveBeenCalledWith(
        'handleLogin catch',
        expect.objectContaining({
          error: expect.any(Error),
        })
      );

});
});
