import { call, put, takeLatest } from 'redux-saga/effects';
import { loginFailure, loginSuccess } from './LoginActions';
import { LOGIN_REQUEST } from './LoginActionTypes';
import { postDataWithBodyForAuth } from '../../../services/ApiRequests';
import { API } from '../../../utils/Constants/ApiConstants';
import PrintLog from '../../../utils/Logger/PrintLog';
import { customAlertWithOK } from '../../../components/CustomAlert'
import Strings from '../../../utils/Strings/Strings';
import {
  LoginRequestData,
  LoginResponseData,
} from '../../../model/Auth/LoginData';
import { t } from 'i18next';

interface LoginAction {
  type: typeof LOGIN_REQUEST;
  payload: LoginRequestData;
}

export function* handleLogin(action: LoginAction) {
  try {
    const params = action.payload;
    const response: LoginResponseData = yield call(
      () =>
        new Promise((resolve, reject) =>
          postDataWithBodyForAuth<LoginRequestData, LoginResponseData>(
            API.login,
            params,
            res => resolve(res),
            err => reject(new Error(typeof err === 'string' ? err : JSON.stringify(err))),
          ),
        ),
    );

    if (response && response.StatusCode === 200) {
      yield put(loginSuccess(response));
    } else {
      yield put(loginFailure(response.Message));
      customAlertWithOK(
        t('commonStrings.alertTitle'),
        t('commonStrings.invalidToken'),
        [{ text: t('commonStrings.ok')}],
        false,
      );
    }
  } catch (error: any) {
    PrintLog.error('handleLogin catch', { error });
    yield put(loginFailure('Login failed'));
  }
}

export default function* loginSaga() {
  yield takeLatest(LOGIN_REQUEST, handleLogin);
}
