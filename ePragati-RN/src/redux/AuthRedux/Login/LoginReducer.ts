import {LoginResponseData} from '../../../model/Auth/LoginData';
import {LOGIN_REQUEST, LOGIN_SUCCESS, LOGIN_FAILURE} from './LoginActionTypes';

interface AuthState {
  user: LoginResponseData | null;
  loading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  loading: false,
  error: null,
};

export const LoginReducer = (state = initialState, action: any): AuthState => {
  switch (action.type) {
    case LOGIN_REQUEST:
      return {...state, loading: true, error: null};

    case LOGIN_SUCCESS:
      return {...state, loading: false, user: action.payload};

    case LOGIN_FAILURE:
      return {...state, loading: false, error: action.payload};

    default:
      return state;
  }
};
