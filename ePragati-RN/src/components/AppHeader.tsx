import React from 'react';
import { View, Text, StyleSheet, Pressable } from 'react-native';
import Colors from '../utils/Colors/Colors';
import Bookmark from '../assets/svg/Bookmark.svg'
import Icon from '@react-native-vector-icons/ionicons';
import { ms } from '../utils/Scale/Scaling';
import { NavigationProp, useNavigation } from '@react-navigation/native';

interface AppHeaderProps {
    title: string;
    onBookmarkPress: () => void;
}

const AppHeader: React.FC<AppHeaderProps> = ({ title, onBookmarkPress }) => {
    const navigation = useNavigation<NavigationProp<any>>();

    return (
        <View style={styles.container}>
            <View style={styles.leftContent}>
                <Pressable
                    onPress={() => navigation.goBack()}
                    testID="back-button"
                >
                    <Icon name='chevron-back-outline' size={24} color={Colors.black} />
                </Pressable>
                <Text style={styles.title}>{title}</Text>
            </View>
            <View style={styles.rightIcons}>
                <Pressable
                    onPress={onBookmarkPress}
                    testID="bookmark-button"
                >
                    <Bookmark />
                </Pressable>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: ms(14),
        paddingVertical: ms(16),
        elevation: 10,
        backgroundColor: Colors.white,
    },
    title: {
        fontFamily: 'MNSemiBold',
        fontSize: ms(16),
        color: Colors.primary,
        marginHorizontal: ms(15),
        marginBottom: ms(2),
    },
    leftContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    rightIcons: {
        flexDirection: 'row',
    },
});

export default AppHeader;