import {Alert, Platform} from 'react-native';

type AlertButton = {
  text: string;
  onPress?: () => void;
  style?: 'default' | 'cancel' | 'destructive';
};

export const customAlertWithOK = (
  title: string,
  message: string,
  buttons: AlertButton[] = [{text: 'OK'}],
  cancelable: boolean = true,
) => {
  if (Platform.OS === 'android') {
    Alert.alert(title, message, buttons, {cancelable});
  } else {
    Alert.alert(title, message, buttons);
  }
};
