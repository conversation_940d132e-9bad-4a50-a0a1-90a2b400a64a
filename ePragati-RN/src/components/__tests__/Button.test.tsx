import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import Button from '../Button';

// Mock react-native-svg components
jest.mock('react-native-svg', () => {
    const React = require('react');
    return {
        Svg: (props: any) => React.createElement('Svg', props),
        Rect: (props: any) => React.createElement('Rect', props),
        Defs: (props: any) => React.createElement('Defs', props),
        LinearGradient: (props: any) => React.createElement('LinearGradient', props),
        Stop: (props: any) => React.createElement('Stop', props),
    };
});

describe('Button', () => {
    const mockOnPress = jest.fn();

    beforeEach(() => {
        mockOnPress.mockClear();
    });

    it('should render with title', () => {
        const { getByText } = render(
            <Button title="Test Button" onPress={mockOnPress} />
        );
        expect(getByText('Test Button')).toBeTruthy();
    });

    it('should call onPress when pressed', () => {
        const { getByText } = render(
            <Button title="Test Button" onPress={mockOnPress} />
        );
        const button = getByText('Test Button');
        fireEvent.press(button);
        expect(mockOnPress).toHaveBeenCalledTimes(1);
    });

    it('should render with correct text styles', () => {
        const { getByText } = render(
            <Button title="Test Button" onPress={mockOnPress} />
        );
        const buttonText = getByText('Test Button');
        expect(buttonText.props.style).toMatchObject({
            color: expect.any(String),
            fontFamily: 'MNBold',
            fontSize: expect.any(Number),
        });
    });

    it('should render with correct button styles', () => {
        const { getByTestId } = render(
            <Button title="Test Button" onPress={mockOnPress} testID="button" />
        );
        const button = getByTestId('button');
        expect(button.props.style).toMatchObject({
            width: '90%',
            height: expect.any(Number),
            borderRadius: 8,
            justifyContent: 'center',
            alignItems: 'center',
            overflow: 'hidden',
        });
    });

    it('should render with empty title', () => {
        const { getByText } = render(
            <Button title="" onPress={mockOnPress} />
        );
        expect(getByText('')).toBeTruthy();
    });
}); 