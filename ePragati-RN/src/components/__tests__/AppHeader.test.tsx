import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import AppHeader from '../AppHeader';
import { useNavigation } from '@react-navigation/native';

// Mock the navigation hook
jest.mock('@react-navigation/native', () => ({
    useNavigation: jest.fn(),
}));

// Mock the Bookmark SVG component
jest.mock('../../assets/svg/Bookmark.svg', () => 'Bookmark');

// Mock the vector icons
jest.mock('@react-native-vector-icons/ionicons', () => 'Icon');

describe('AppHeader', () => {
    const mockGoBack = jest.fn();
    const mockOnBookmarkPress = jest.fn();

    beforeEach(() => {
        (useNavigation as jest.Mock).mockReturnValue({
            goBack: mockGoBack,
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should render with title', () => {
        const { getByText } = render(
            <AppHeader title="Test Title" onBookmarkPress={mockOnBookmarkPress} />
        );
        expect(getByText('Test Title')).toBeTruthy();
    });

    it('should call onBookmarkPress when bookmark is pressed', () => {
        const { getByTestId } = render(
            <AppHeader title="Test Title" onBookmarkPress={mockOnBookmarkPress} />
        );
        const bookmarkButton = getByTestId('bookmark-button');
        fireEvent.press(bookmarkButton);
        expect(mockOnBookmarkPress).toHaveBeenCalledTimes(1);
    });

    it('should call navigation.goBack when back button is pressed', () => {
        const { getByTestId } = render(
            <AppHeader title="Test Title" onBookmarkPress={mockOnBookmarkPress} />
        );
        const backButton = getByTestId('back-button');
        fireEvent.press(backButton);
        expect(mockGoBack).toHaveBeenCalledTimes(1);
    });

    it('should render with empty title', () => {
        const { getByText } = render(
            <AppHeader title="" onBookmarkPress={mockOnBookmarkPress} />
        );
        expect(getByText('')).toBeTruthy();
    });
});
