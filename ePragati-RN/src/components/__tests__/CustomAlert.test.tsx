// __tests__/CustomAlert.test.tsx
import { Alert, Platform } from 'react-native';
import { customAlertWithOK } from '../CustomAlert';

describe('customAlertWithOK', () => {
  const realOS = Platform.OS;

  beforeEach(() => {
    // mock out Alert.alert
    jest.spyOn(Alert, 'alert').mockImplementation(() => {});
  });

  afterEach(() => {
    // restore Alert.alert
    jest.resetAllMocks();
    // restore Platform.OS
    Object.defineProperty(Platform, 'OS', { value: realOS });
  });

  it('calls Alert.alert with cancelable option on Android', () => {
    // override to Android
    Object.defineProperty(Platform, 'OS', { value: 'android' });

    const buttons = [
      { text: 'Yes', onPress: jest.fn(), style: 'destructive' as const },
      { text: 'No', style: 'cancel' as const },
    ];

    customAlertWithOK('Warning', 'Are you sure?', buttons, false);

    expect(Alert.alert).toHaveBeenCalledWith(
      'Warning',
      'Are you sure?',
      buttons,
      { cancelable: false }
    );
  });

  it('defaults buttons to [{ text: "OK" }] and omits cancelable on iOS', () => {
    // override to iOS
    Object.defineProperty(Platform, 'OS', { value: 'ios' });

    customAlertWithOK('Hello', 'This is a test');

    expect(Alert.alert).toHaveBeenCalledWith(
      'Hello',
      'This is a test',
      [{ text: 'OK' }]
    );
  });
});
