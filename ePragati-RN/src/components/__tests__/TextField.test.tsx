import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import TextField from '../TextField';

// Mock the vector icons
jest.mock('@react-native-vector-icons/ionicons', () => 'Icon');

describe('TextField', () => {
    it('should render with label', () => {
        const { getByText } = render(
            <TextField label="Test Label" />
        );
        expect(getByText('Test Label')).toBeTruthy();
    });

    it('should render with required asterisk', () => {
        const { getByText } = render(
            <TextField label="Test Label" required />
        );
        expect(getByText('*')).toBeTruthy();
    });

    it('should render with placeholder', () => {
        const { getByPlaceholderText } = render(
            <TextField label="Test Label" placeholder="Enter text" />
        );
        expect(getByPlaceholderText('Enter text')).toBeTruthy();
    });

    it('should render with icon', () => {
        const { UNSAFE_getByType } = render(
            <TextField label="Test Label" icon="search" />
        );
        expect(UNSAFE_getByType('Icon')).toBeTruthy();
    });

    it('should render without icon when not provided', () => {
        const { queryByTestId } = render(
            <TextField label="Test Label" />
        );
        expect(queryByTestId('icon')).toBeNull();
    });

    it('should handle text input', () => {
        const onChangeText = jest.fn();
        const { getByPlaceholderText } = render(
            <TextField
                label="Test Label"
                placeholder="Enter text"
                value=""
                onChangeText={onChangeText}
            />
        );
        const input = getByPlaceholderText('Enter text');
        fireEvent.changeText(input, 'Test Input');
        expect(onChangeText).toHaveBeenCalledWith('Test Input');
    });

    it('should display the provided value', () => {
        const { getByDisplayValue } = render(
            <TextField
                label="Test Label"
                value="Initial Value"
                onChangeText={() => { }}
            />
        );
        expect(getByDisplayValue('Initial Value')).toBeTruthy();
    });
}); 