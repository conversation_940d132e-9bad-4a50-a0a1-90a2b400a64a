import React from 'react';
import { View, TextInput, StyleSheet, Text } from 'react-native';
import Icon from '@react-native-vector-icons/ionicons';
import Colors from '../utils/Colors/Colors';
import { ms } from '../utils/Scale/Scaling';

interface TextInputProps {
    label: string;
    placeholder?: string;
    required?: boolean;
    icon?: string;
    value?: string;
    onChangeText?: (text: string) => void;
}

const TextField: React.FC<TextInputProps> = ({
    label,
    placeholder,
    required,
    icon,
    value = '',
    onChangeText
}) => {
    return (
        <View style={styles.container}>
            <Text style={styles.label}>
                {label}
                {required && <Text style={styles.asterisk}> *</Text>}
            </Text>
            <View style={styles.innerContainer}>
                <View style={styles.inputContainer}>
                    <TextInput
                        style={styles.input}
                        value={value}
                        onChangeText={onChangeText}
                        placeholder={placeholder}
                        placeholderTextColor={Colors.primary}
                    />
                    {icon && <Icon name={icon} size={24} color={Colors.black} style={styles.icon} />}
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        marginHorizontal: ms(16),
    },
    innerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.lightBlue,
        borderWidth: 1,
        borderColor: Colors.grey,
        borderRadius: 6,
        padding: ms(4),
    },
    asterisk: {
        color: Colors.labelBlue,
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    label: {
        fontFamily: 'MNMedium',
        fontSize: ms(14),
        color: Colors.labelBlue,
    },
    input: {
        flex: 1,
        fontFamily: 'MNMedium',
        fontSize: ms(14),
        color: Colors.primary,
    },
    icon: {
        paddingHorizontal: ms(5)
    }
});

export default TextField;