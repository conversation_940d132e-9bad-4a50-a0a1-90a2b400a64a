import { Pressable, Text, StyleSheet } from 'react-native'
import React from 'react'
import { Svg, Rect, Defs, LinearGradient, Stop } from 'react-native-svg'
import { ms } from '../utils/Scale/Scaling';
import Colors from '../utils/Colors/Colors';

interface ButtonProps {
    onPress: () => void;
    title: string;
}

const Button: React.FC<ButtonProps> = ({ onPress, title }) => {
    return (
        <Pressable
            style={styles.button}
            onPress={onPress}
            testID={"button"}
        >
            <Svg height={44} width="100%" style={StyleSheet.absoluteFill}>
                <Defs>
                    <LinearGradient id="grad" x1="0" y1="0" x2="0" y2="1">
                        <Stop offset="0" stopColor={Colors.deepBlue} stopOpacity="1" />
                        <Stop offset="1" stopColor={Colors.blue} stopOpacity="1" />
                    </LinearGradient>
                </Defs>
                <Rect x="0" y="0" width="100%" height={44} rx={8} fill="url(#grad)" />
            </Svg>
            <Text style={styles.text}>{title}</Text>
        </Pressable>
    )
}

const styles = StyleSheet.create({
    button: {
        width: '90%',
        height: ms(44),
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden',
    },
    text: {
        color: Colors.white,
        fontFamily: 'MNBold',
        fontSize: ms(16),
        zIndex: 1,
    },
})

export default Button;