import React from 'react';
import { Text, TouchableOpacity, View, ActivityIndicator, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/Root/rootStore';
import Config from 'react-native-config';
import Strings from '../../utils/Strings/Strings';
import Colors from '../../utils/Colors/Colors';
import { t } from 'i18next';


export const LoginView = () => {
  const {loading, error } = useSelector((state: RootState) => state.auth);

  return (
    <View style={styles.overallContainer}>
      {loading ? (
        <ActivityIndicator size="large" color={Colors.bgColor} 
        testID="activity-indicator"/>
      ) : (
        <View style={styles.screenView}>

          <Text style={styles.textStyle}>
            {Config.ENV}
          </Text>

          <Text style={styles.textStyle}>
            {Config.OCP_APIM_SUBSCRIPTION_KEY}
          </Text>

          <TouchableOpacity
            style={styles.buttonStyle}>
            <Text style={styles.textStyle}>
              {t('loginStrings.login')}
            </Text>
          </TouchableOpacity>
        </View>
      )}
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  overallContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  screenView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  textStyle: {
    color: 'black',
    fontSize: 18,
    fontWeight: 'bold'
  },
  buttonStyle: {
    backgroundColor: Colors.bgColor,
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
  },
  errorText: {
    color: Colors.red,
    marginTop: 10
  }
});

export default LoginView;
