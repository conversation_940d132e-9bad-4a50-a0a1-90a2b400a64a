import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Strings from '../../utils/Strings/Strings';
import { t } from 'i18next';

const HomeScreen = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('welcomeMessageStrings.welcome')}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
});

export default HomeScreen;
