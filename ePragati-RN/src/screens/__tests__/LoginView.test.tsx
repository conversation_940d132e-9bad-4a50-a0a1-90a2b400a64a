import React from 'react';
import { render } from '@testing-library/react-native';
import { useSelector } from 'react-redux';
import { LoginView } from '../Auth/LoginScreen'; // adjust the path accordingly

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
}));

jest.mock('react-native-config', () => ({
  ENV: 'development',
  OCP_APIM_SUBSCRIPTION_KEY: '12345-key',
}));

jest.mock('../../utils/Strings/Strings', () => ({
  loginStrings: {
    login: 'Login',
  },
}));

jest.mock('../../utils/Colors/Colors', () => ({
  bgColor: '#0000FF',
  red: '#FF0000',
}));

describe('LoginView', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should show loading indicator when loading is true', () => {
    ((useSelector as unknown) as jest.Mock).mockReturnValue({ loading: true, error: null });

    const { getByTestId } = render(<LoginView />);
    expect(getByTestId('activity-indicator')).toBeTruthy();
  });

  it('should show ENV, API Key, and Login button when not loading', () => {
    ((useSelector as unknown) as jest.Mock).mockReturnValue({ loading: false, error: null });

    const { getByText } = render(<LoginView />);
    expect(getByText('development')).toBeTruthy();
    expect(getByText('12345-key')).toBeTruthy();
    expect(getByText('Login')).toBeTruthy();
  });

  it('should show error message when error exists', () => {
    ((useSelector as unknown) as jest.Mock).mockReturnValue({
      loading: false,
      error: 'Login failed',
    });

    const { getByText } = render(<LoginView />);
    expect(getByText('Login failed')).toBeTruthy();
  });
});
