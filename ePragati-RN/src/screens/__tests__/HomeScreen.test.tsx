import React from 'react';
import { render } from '@testing-library/react-native';
import HomeScreen from '../Home/HomeScreen'; // Adjust path as needed
import Strings from '../../utils/Strings/Strings';
import { t } from 'i18next';

// Mock the Strings module to control the returned value of welcome()
jest.mock('../../utils/Strings/Strings', () => ({
  welcomeMessageStrings: {
    welcome: jest.fn(() => 'Welcome to the app!'),
  },
}));

describe('HomeScreen', () => {
  test('renders welcome message correctly', () => {
    const { getByText } = render(<HomeScreen />);

    // The welcome message should be rendered
    expect(getByText('Welcome to the app!')).toBeTruthy();

    // Optionally verify the mock was called
    expect(t('welcomeMessageStrings.welcome')).toHaveBeenCalled();
  });
});
