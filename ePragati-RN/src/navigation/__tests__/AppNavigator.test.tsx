import React from 'react';
import { render } from '@testing-library/react-native';
import AppNavigator from '../AppNavigator';

// Mock screen components
jest.mock('../../screens/Auth/LoginScreen', () => () => <></>);
jest.mock('../../screens/Home/HomeScreen', () => () => <></>);

describe('AppNavigator', () => {
  it('renders without crashing and contains Login and Home screens', () => {
    const { getByText, queryByText } = render(<AppNavigator />);
    expect(queryByText('Login')).toBeNull(); // Login screen is mocked empty, so no text
  });
});
