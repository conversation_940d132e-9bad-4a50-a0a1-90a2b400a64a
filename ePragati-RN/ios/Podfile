envfile_mapping = {
  'Dev' => '.env.dev',
  'Qa' => '.env.qa',
  'Prod' => '.env.prod'
}

ENV['ENVFILE'] ||= envfile_mapping[ENV['CONFIGURATION']] || '.env.dev'

project 'ePragathi', {
  'Dev' => :debug,
  'Qa' => :debug,
  'Prod' => :release
}

# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, min_ios_version_supported
prepare_react_native_project!

# Enable the New Architecture
ENV['RCT_NEW_ARCH_ENABLED'] = '1'

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'ePragathi' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/..",
    # Enable the New Architecture
    :fabric_enabled => true,
    :hermes_enabled => true
  )
  
  target 'ePragathiQa' do
    inherit! :complete
  end

  target 'ePragathiProd' do
    inherit! :complete
  end
  
  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )

    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['ENVFILE'] = ENV['ENVFILE'] || '.env'
      end
    end
  end
end
