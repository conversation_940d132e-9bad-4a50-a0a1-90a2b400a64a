// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		191E4D3A6A34EAC42DE828BD /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */; };
		2E3C2FD24765EFD5F1498D56 /* libPods-ePragathi-ePragathiQa.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B30DEC16D9D0C8126F65BCC4 /* libPods-ePragathi-ePragathiQa.a */; };
		6B04D6302DDDA1D200726E3A /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 761780EC2CA45674006654EE /* AppDelegate.swift */; };
		6B04D6332DDDA1D200726E3A /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		6B04D6342DDDA1D200726E3A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		6B04D6352DDDA1D200726E3A /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */; };
		6B04D6422DDDA1EA00726E3A /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 761780EC2CA45674006654EE /* AppDelegate.swift */; };
		6B04D6452DDDA1EA00726E3A /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		6B04D6462DDDA1EA00726E3A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		6B04D6472DDDA1EA00726E3A /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */; };
		761780ED2CA45674006654EE /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 761780EC2CA45674006654EE /* AppDelegate.swift */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		AA24F58136C51272D3507B7C /* libPods-ePragathi-ePragathiProd.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B126FD34D44B44384154EE6E /* libPods-ePragathi-ePragathiProd.a */; };
		AB9DC1AA191F53603101364C /* libPods-ePragathi.a in Frameworks */ = {isa = PBXBuildFile; fileRef = D979AC2D12E8B2F252E63D8B /* libPods-ePragathi.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = ePragathi;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0C2B8712C1085C0F97D67677 /* Pods-ePragathi-ePragathiProd.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi-ePragathiProd.debug.xcconfig"; path = "Target Support Files/Pods-ePragathi-ePragathiProd/Pods-ePragathi-ePragathiProd.debug.xcconfig"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* ePragathi.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ePragathi.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = ePragathi/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = ePragathi/Info.plist; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = ePragathi/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		2449CD48F7FE60239EDA8BA1 /* Pods-ePragathi-ePragathiProd.qa.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi-ePragathiProd.qa.xcconfig"; path = "Target Support Files/Pods-ePragathi-ePragathiProd/Pods-ePragathi-ePragathiProd.qa.xcconfig"; sourceTree = "<group>"; };
		31BA10A1DF687E10D347EF7B /* Pods-ePragathi-ePragathiQa.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi-ePragathiQa.release.xcconfig"; path = "Target Support Files/Pods-ePragathi-ePragathiQa/Pods-ePragathi-ePragathiQa.release.xcconfig"; sourceTree = "<group>"; };
		5171B26385914058E14DFBCD /* Pods-ePragathi.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi.release.xcconfig"; path = "Target Support Files/Pods-ePragathi/Pods-ePragathi.release.xcconfig"; sourceTree = "<group>"; };
		61234BE53D3963CF1F3EDB3E /* Pods-ePragathi-ePragathiQa.qa.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi-ePragathiQa.qa.xcconfig"; path = "Target Support Files/Pods-ePragathi-ePragathiQa/Pods-ePragathi-ePragathiQa.qa.xcconfig"; sourceTree = "<group>"; };
		6B04D63E2DDDA1D200726E3A /* ePragathiQa.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ePragathiQa.app; sourceTree = BUILT_PRODUCTS_DIR; };
		6B04D63F2DDDA1D200726E3A /* ePragathiQa-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "ePragathiQa-Info.plist"; path = "/Users/<USER>/RN_Projects/L-T---RN1/ePragathi/ios/ePragathiQa-Info.plist"; sourceTree = "<absolute>"; };
		6B04D6502DDDA1EA00726E3A /* ePragathiProd.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ePragathiProd.app; sourceTree = BUILT_PRODUCTS_DIR; };
		6B04D6512DDDA1EA00726E3A /* ePragathiProd-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "ePragathiProd-Info.plist"; path = "/Users/<USER>/RN_Projects/L-T---RN1/ePragathi/ios/ePragathiProd-Info.plist"; sourceTree = "<absolute>"; };
		761780EC2CA45674006654EE /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = AppDelegate.swift; path = ePragathi/AppDelegate.swift; sourceTree = "<group>"; };
		7BD50A572C6E3657AB765E1B /* Pods-ePragathi.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi.debug.xcconfig"; path = "Target Support Files/Pods-ePragathi/Pods-ePragathi.debug.xcconfig"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = ePragathi/LaunchScreen.storyboard; sourceTree = "<group>"; };
		85395EF5F9E4054B0C81C640 /* Pods-ePragathi-ePragathiProd.dev.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi-ePragathiProd.dev.xcconfig"; path = "Target Support Files/Pods-ePragathi-ePragathiProd/Pods-ePragathi-ePragathiProd.dev.xcconfig"; sourceTree = "<group>"; };
		8C7257BC527CFFA171A52D9B /* Pods-ePragathi-ePragathiQa.prod.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi-ePragathiQa.prod.xcconfig"; path = "Target Support Files/Pods-ePragathi-ePragathiQa/Pods-ePragathi-ePragathiQa.prod.xcconfig"; sourceTree = "<group>"; };
		93A6BCF4DFBC1C7BD6240851 /* Pods-ePragathi-ePragathiQa.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi-ePragathiQa.debug.xcconfig"; path = "Target Support Files/Pods-ePragathi-ePragathiQa/Pods-ePragathi-ePragathiQa.debug.xcconfig"; sourceTree = "<group>"; };
		A4AA15CFB7EB3DF3335CBBE7 /* Pods-ePragathi-ePragathiQa.dev.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi-ePragathiQa.dev.xcconfig"; path = "Target Support Files/Pods-ePragathi-ePragathiQa/Pods-ePragathi-ePragathiQa.dev.xcconfig"; sourceTree = "<group>"; };
		AEC7257755161028DD224384 /* Pods-ePragathi-ePragathiProd.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi-ePragathiProd.release.xcconfig"; path = "Target Support Files/Pods-ePragathi-ePragathiProd/Pods-ePragathi-ePragathiProd.release.xcconfig"; sourceTree = "<group>"; };
		B126FD34D44B44384154EE6E /* libPods-ePragathi-ePragathiProd.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-ePragathi-ePragathiProd.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		B30DEC16D9D0C8126F65BCC4 /* libPods-ePragathi-ePragathiQa.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-ePragathi-ePragathiQa.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		B9161BCA77C4F5DB5AF5FDCF /* Pods-ePragathi.qa.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi.qa.xcconfig"; path = "Target Support Files/Pods-ePragathi/Pods-ePragathi.qa.xcconfig"; sourceTree = "<group>"; };
		BF9EE0DEAECE5B4440E0003C /* Pods-ePragathi.prod.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi.prod.xcconfig"; path = "Target Support Files/Pods-ePragathi/Pods-ePragathi.prod.xcconfig"; sourceTree = "<group>"; };
		D979AC2D12E8B2F252E63D8B /* libPods-ePragathi.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-ePragathi.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		EF9DDBF74C415349595164C5 /* Pods-ePragathi-ePragathiProd.prod.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi-ePragathiProd.prod.xcconfig"; path = "Target Support Files/Pods-ePragathi-ePragathiProd/Pods-ePragathi-ePragathiProd.prod.xcconfig"; sourceTree = "<group>"; };
		F7213369377B352D902BE44C /* Pods-ePragathi.dev.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ePragathi.dev.xcconfig"; path = "Target Support Files/Pods-ePragathi/Pods-ePragathi.dev.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AB9DC1AA191F53603101364C /* libPods-ePragathi.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6B04D6312DDDA1D200726E3A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2E3C2FD24765EFD5F1498D56 /* libPods-ePragathi-ePragathiQa.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6B04D6432DDDA1EA00726E3A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AA24F58136C51272D3507B7C /* libPods-ePragathi-ePragathiProd.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* ePragathi */ = {
			isa = PBXGroup;
			children = (
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				761780EC2CA45674006654EE /* AppDelegate.swift */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
			);
			name = ePragathi;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				D979AC2D12E8B2F252E63D8B /* libPods-ePragathi.a */,
				B126FD34D44B44384154EE6E /* libPods-ePragathi-ePragathiProd.a */,
				B30DEC16D9D0C8126F65BCC4 /* libPods-ePragathi-ePragathiQa.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* ePragathi */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				6B04D63F2DDDA1D200726E3A /* ePragathiQa-Info.plist */,
				6B04D6512DDDA1EA00726E3A /* ePragathiProd-Info.plist */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* ePragathi.app */,
				6B04D63E2DDDA1D200726E3A /* ePragathiQa.app */,
				6B04D6502DDDA1EA00726E3A /* ePragathiProd.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				7BD50A572C6E3657AB765E1B /* Pods-ePragathi.debug.xcconfig */,
				F7213369377B352D902BE44C /* Pods-ePragathi.dev.xcconfig */,
				5171B26385914058E14DFBCD /* Pods-ePragathi.release.xcconfig */,
				BF9EE0DEAECE5B4440E0003C /* Pods-ePragathi.prod.xcconfig */,
				B9161BCA77C4F5DB5AF5FDCF /* Pods-ePragathi.qa.xcconfig */,
				0C2B8712C1085C0F97D67677 /* Pods-ePragathi-ePragathiProd.debug.xcconfig */,
				85395EF5F9E4054B0C81C640 /* Pods-ePragathi-ePragathiProd.dev.xcconfig */,
				AEC7257755161028DD224384 /* Pods-ePragathi-ePragathiProd.release.xcconfig */,
				EF9DDBF74C415349595164C5 /* Pods-ePragathi-ePragathiProd.prod.xcconfig */,
				2449CD48F7FE60239EDA8BA1 /* Pods-ePragathi-ePragathiProd.qa.xcconfig */,
				93A6BCF4DFBC1C7BD6240851 /* Pods-ePragathi-ePragathiQa.debug.xcconfig */,
				A4AA15CFB7EB3DF3335CBBE7 /* Pods-ePragathi-ePragathiQa.dev.xcconfig */,
				31BA10A1DF687E10D347EF7B /* Pods-ePragathi-ePragathiQa.release.xcconfig */,
				8C7257BC527CFFA171A52D9B /* Pods-ePragathi-ePragathiQa.prod.xcconfig */,
				61234BE53D3963CF1F3EDB3E /* Pods-ePragathi-ePragathiQa.qa.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* ePragathi */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "ePragathi" */;
			buildPhases = (
				6EA9885EB0005019557D8177 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				6B048DEC2DD58C9100E868AD /* Generate react-native-config xcconfig */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				0AB115EFE71E4163F58D3E35 /* [CP] Embed Pods Frameworks */,
				BEAD0DDDFFFD14DB8FA2A894 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ePragathi;
			productName = ePragathi;
			productReference = 13B07F961A680F5B00A75B9A /* ePragathi.app */;
			productType = "com.apple.product-type.application";
		};
		6B04D62E2DDDA1D200726E3A /* ePragathiQa */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6B04D6382DDDA1D200726E3A /* Build configuration list for PBXNativeTarget "ePragathiQa" */;
			buildPhases = (
				90DD0C433A50E292A8E03B54 /* [CP] Check Pods Manifest.lock */,
				6B04D62F2DDDA1D200726E3A /* Sources */,
				6B04D6312DDDA1D200726E3A /* Frameworks */,
				6B04D6322DDDA1D200726E3A /* Resources */,
				6B04D6362DDDA1D200726E3A /* Generate react-native-config xcconfig */,
				6B04D6372DDDA1D200726E3A /* Bundle React Native code and images */,
				9F30898F1424E21135E217E6 /* [CP] Embed Pods Frameworks */,
				1D40AF1D314BB1C7E2310F3E /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ePragathiQa;
			productName = ePragathi;
			productReference = 6B04D63E2DDDA1D200726E3A /* ePragathiQa.app */;
			productType = "com.apple.product-type.application";
		};
		6B04D6402DDDA1EA00726E3A /* ePragathiProd */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6B04D64A2DDDA1EA00726E3A /* Build configuration list for PBXNativeTarget "ePragathiProd" */;
			buildPhases = (
				BCA3E1D2C6D82DED5E6E2290 /* [CP] Check Pods Manifest.lock */,
				6B04D6412DDDA1EA00726E3A /* Sources */,
				6B04D6432DDDA1EA00726E3A /* Frameworks */,
				6B04D6442DDDA1EA00726E3A /* Resources */,
				6B04D6482DDDA1EA00726E3A /* Generate react-native-config xcconfig */,
				6B04D6492DDDA1EA00726E3A /* Bundle React Native code and images */,
				4D2BFA14DF3FBB714D3594C6 /* [CP] Embed Pods Frameworks */,
				90571AE5D0CE3F800525D6F3 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ePragathiProd;
			productName = ePragathi;
			productReference = 6B04D6502DDDA1EA00726E3A /* ePragathiProd.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "ePragathi" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* ePragathi */,
				6B04D62E2DDDA1D200726E3A /* ePragathiQa */,
				6B04D6402DDDA1EA00726E3A /* ePragathiProd */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6B04D6322DDDA1D200726E3A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6B04D6332DDDA1D200726E3A /* LaunchScreen.storyboard in Resources */,
				6B04D6342DDDA1D200726E3A /* Images.xcassets in Resources */,
				6B04D6352DDDA1D200726E3A /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6B04D6442DDDA1EA00726E3A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6B04D6452DDDA1EA00726E3A /* LaunchScreen.storyboard in Resources */,
				6B04D6462DDDA1EA00726E3A /* Images.xcassets in Resources */,
				6B04D6472DDDA1EA00726E3A /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/bash\n\necho \"Configuration: $CONFIGURATION\"\n\n# Determine ENVFILE based on configuration\ncase \"$CONFIGURATION\" in\n  Dev)\n    ENVFILE=\".env.dev\"\n    ;;\n  Qa)\n    ENVFILE=\".env.qa\"\n    ;;\n  Prod)\n    ENVFILE=\".env.prod\"\n    ;;\n  *)\n    ENVFILE=\".env\"\n    ;;\nesac\n\necho \"Using ENVFILE: $ENVFILE\"\n\n# Ensure PROJECT_DIR points to the project root\nPROJECT_DIR=\"$SRCROOT/..\"\n\n# Resolve the ENVFILE path (optional, not always required!)\nENVFILE_PATH=\"$PROJECT_DIR/$ENVFILE\"\necho \"Resolved ENVFILE_PATH: $ENVFILE_PATH\"\n\n#Export required variables\nexport PROJECT_DIR\nexport ENVFILE=\"$ENVFILE\"  # keep it just the filename as required by react-native-config\n# export ENVFILE=\"$ENVFILE_PATH\"  Do NOT do this unless you're patching the Ruby script to accept full paths\n\n#  Call the react-native-config script\n\"${SRCROOT}/../node_modules/react-native-config/ios/ReactNativeConfig/BuildDotenvConfig.rb\"\n\n# Continue with JS bundling\nexport NODE_BINARY=node\n\"${SRCROOT}/../node_modules/react-native/scripts/react-native-xcode.sh\"\n";
		};
		0AB115EFE71E4163F58D3E35 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ePragathi/Pods-ePragathi-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ePragathi/Pods-ePragathi-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ePragathi/Pods-ePragathi-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		1D40AF1D314BB1C7E2310F3E /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ePragathi-ePragathiQa/Pods-ePragathi-ePragathiQa-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ePragathi-ePragathiQa/Pods-ePragathi-ePragathiQa-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ePragathi-ePragathiQa/Pods-ePragathi-ePragathiQa-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		4D2BFA14DF3FBB714D3594C6 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ePragathi-ePragathiProd/Pods-ePragathi-ePragathiProd-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ePragathi-ePragathiProd/Pods-ePragathi-ePragathiProd-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ePragathi-ePragathiProd/Pods-ePragathi-ePragathiProd-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		6B048DEC2DD58C9100E868AD /* Generate react-native-config xcconfig */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Generate react-native-config xcconfig";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/bash\n\necho \"Configuration: $CONFIGURATION\"\n\n# Determine ENVFILE based on configuration\ncase \"$CONFIGURATION\" in\n  Dev)\n    ENVFILE=\".env.dev\"\n    ;;\n  Qa)\n    ENVFILE=\".env.qa\"\n    ;;\n  Prod)\n    ENVFILE=\".env.prod\"\n    ;;\n  *)\n    ENVFILE=\".env\"\n    ;;\nesac\n\necho \"Using ENVFILE: $ENVFILE\"\n\n# Project root\nPROJECT_DIR=\"$SRCROOT/..\"\n\n# ✅ Export ONLY the filename, NOT full path\nexport PROJECT_DIR\nexport ENVFILE  # this must be .env.prod, not a path\n\n# Debug print\necho \">> PROJECT_DIR from ENV: $PROJECT_DIR\"\necho \">> ENVFILE from ENV: $ENVFILE\"\n\n# Run the dotenv config\n\"${SRCROOT}/../node_modules/react-native-config/ios/ReactNativeConfig/BuildDotenvConfig.rb\"\n\n# Continue with JS bundling\nexport NODE_BINARY=node\n\"${SRCROOT}/../node_modules/react-native/scripts/react-native-xcode.sh\"\n";
		};
		6B04D6362DDDA1D200726E3A /* Generate react-native-config xcconfig */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Generate react-native-config xcconfig";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/bash\n\necho \"Configuration: $CONFIGURATION\"\n\n# Determine ENVFILE based on configuration\ncase \"$CONFIGURATION\" in\n  Dev)\n    ENVFILE=\".env.dev\"\n    ;;\n  Qa)\n    ENVFILE=\".env.qa\"\n    ;;\n  Prod)\n    ENVFILE=\".env.prod\"\n    ;;\n  *)\n    ENVFILE=\".env\"\n    ;;\nesac\n\necho \"Using ENVFILE: $ENVFILE\"\n\n# Project root\nPROJECT_DIR=\"$SRCROOT/..\"\n\n# ✅ Export ONLY the filename, NOT full path\nexport PROJECT_DIR\nexport ENVFILE  # this must be .env.prod, not a path\n\n# Debug print\necho \">> PROJECT_DIR from ENV: $PROJECT_DIR\"\necho \">> ENVFILE from ENV: $ENVFILE\"\n\n# Run the dotenv config\n\"${SRCROOT}/../node_modules/react-native-config/ios/ReactNativeConfig/BuildDotenvConfig.rb\"\n\n# Continue with JS bundling\nexport NODE_BINARY=node\n\"${SRCROOT}/../node_modules/react-native/scripts/react-native-xcode.sh\"\n";
		};
		6B04D6372DDDA1D200726E3A /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/bash\n\necho \"Configuration: $CONFIGURATION\"\n\n# Determine ENVFILE based on configuration\ncase \"$CONFIGURATION\" in\n  Dev)\n    ENVFILE=\".env.dev\"\n    ;;\n  Qa)\n    ENVFILE=\".env.qa\"\n    ;;\n  Prod)\n    ENVFILE=\".env.prod\"\n    ;;\n  *)\n    ENVFILE=\".env\"\n    ;;\nesac\n\necho \"Using ENVFILE: $ENVFILE\"\n\n# Ensure PROJECT_DIR points to the project root\nPROJECT_DIR=\"$SRCROOT/..\"\n\n# Resolve the ENVFILE path (optional, not always required!)\nENVFILE_PATH=\"$PROJECT_DIR/$ENVFILE\"\necho \"Resolved ENVFILE_PATH: $ENVFILE_PATH\"\n\n#Export required variables\nexport PROJECT_DIR\nexport ENVFILE=\"$ENVFILE\"  # keep it just the filename as required by react-native-config\n# export ENVFILE=\"$ENVFILE_PATH\"  Do NOT do this unless you're patching the Ruby script to accept full paths\n\n#  Call the react-native-config script\n\"${SRCROOT}/../node_modules/react-native-config/ios/ReactNativeConfig/BuildDotenvConfig.rb\"\n\n# Continue with JS bundling\nexport NODE_BINARY=node\n\"${SRCROOT}/../node_modules/react-native/scripts/react-native-xcode.sh\"\n";
		};
		6B04D6482DDDA1EA00726E3A /* Generate react-native-config xcconfig */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Generate react-native-config xcconfig";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/bash\n\necho \"Configuration: $CONFIGURATION\"\n\n# Determine ENVFILE based on configuration\ncase \"$CONFIGURATION\" in\n  Dev)\n    ENVFILE=\".env.dev\"\n    ;;\n  Qa)\n    ENVFILE=\".env.qa\"\n    ;;\n  Prod)\n    ENVFILE=\".env.prod\"\n    ;;\n  *)\n    ENVFILE=\".env\"\n    ;;\nesac\n\necho \"Using ENVFILE: $ENVFILE\"\n\n# Project root\nPROJECT_DIR=\"$SRCROOT/..\"\n\n# ✅ Export ONLY the filename, NOT full path\nexport PROJECT_DIR\nexport ENVFILE  # this must be .env.prod, not a path\n\n# Debug print\necho \">> PROJECT_DIR from ENV: $PROJECT_DIR\"\necho \">> ENVFILE from ENV: $ENVFILE\"\n\n# Run the dotenv config\n\"${SRCROOT}/../node_modules/react-native-config/ios/ReactNativeConfig/BuildDotenvConfig.rb\"\n\n# Continue with JS bundling\nexport NODE_BINARY=node\n\"${SRCROOT}/../node_modules/react-native/scripts/react-native-xcode.sh\"\n";
		};
		6B04D6492DDDA1EA00726E3A /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/bash\n\necho \"Configuration: $CONFIGURATION\"\n\n# Determine ENVFILE based on configuration\ncase \"$CONFIGURATION\" in\n  Dev)\n    ENVFILE=\".env.dev\"\n    ;;\n  Qa)\n    ENVFILE=\".env.qa\"\n    ;;\n  Prod)\n    ENVFILE=\".env.prod\"\n    ;;\n  *)\n    ENVFILE=\".env\"\n    ;;\nesac\n\necho \"Using ENVFILE: $ENVFILE\"\n\n# Ensure PROJECT_DIR points to the project root\nPROJECT_DIR=\"$SRCROOT/..\"\n\n# Resolve the ENVFILE path (optional, not always required!)\nENVFILE_PATH=\"$PROJECT_DIR/$ENVFILE\"\necho \"Resolved ENVFILE_PATH: $ENVFILE_PATH\"\n\n#Export required variables\nexport PROJECT_DIR\nexport ENVFILE=\"$ENVFILE\"  # keep it just the filename as required by react-native-config\n# export ENVFILE=\"$ENVFILE_PATH\"  Do NOT do this unless you're patching the Ruby script to accept full paths\n\n#  Call the react-native-config script\n\"${SRCROOT}/../node_modules/react-native-config/ios/ReactNativeConfig/BuildDotenvConfig.rb\"\n\n# Continue with JS bundling\nexport NODE_BINARY=node\n\"${SRCROOT}/../node_modules/react-native/scripts/react-native-xcode.sh\"\n";
		};
		6EA9885EB0005019557D8177 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ePragathi-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		90571AE5D0CE3F800525D6F3 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ePragathi-ePragathiProd/Pods-ePragathi-ePragathiProd-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ePragathi-ePragathiProd/Pods-ePragathi-ePragathiProd-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ePragathi-ePragathiProd/Pods-ePragathi-ePragathiProd-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		90DD0C433A50E292A8E03B54 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ePragathi-ePragathiQa-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9F30898F1424E21135E217E6 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ePragathi-ePragathiQa/Pods-ePragathi-ePragathiQa-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ePragathi-ePragathiQa/Pods-ePragathi-ePragathiQa-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ePragathi-ePragathiQa/Pods-ePragathi-ePragathiQa-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		BCA3E1D2C6D82DED5E6E2290 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ePragathi-ePragathiProd-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		BEAD0DDDFFFD14DB8FA2A894 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ePragathi/Pods-ePragathi-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ePragathi/Pods-ePragathi-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ePragathi/Pods-ePragathi-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				761780ED2CA45674006654EE /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6B04D62F2DDDA1D200726E3A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6B04D6302DDDA1D200726E3A /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6B04D6412DDDA1EA00726E3A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6B04D6422DDDA1EA00726E3A /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* ePragathi */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7BD50A572C6E3657AB765E1B /* Pods-ePragathi.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = ePragathi/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = ePragathi;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5171B26385914058E14DFBCD /* Pods-ePragathi.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = ePragathi/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = ePragathi;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		6B04D6392DDDA1D200726E3A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 93A6BCF4DFBC1C7BD6240851 /* Pods-ePragathi-ePragathiQa.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "ePragathiQa-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		6B04D63A2DDDA1D200726E3A /* Dev */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A4AA15CFB7EB3DF3335CBBE7 /* Pods-ePragathi-ePragathiQa.dev.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "ePragathiQa-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Dev;
		};
		6B04D63B2DDDA1D200726E3A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 31BA10A1DF687E10D347EF7B /* Pods-ePragathi-ePragathiQa.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = "ePragathiQa-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		6B04D63C2DDDA1D200726E3A /* Prod */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8C7257BC527CFFA171A52D9B /* Pods-ePragathi-ePragathiQa.prod.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = "ePragathiQa-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Prod;
		};
		6B04D63D2DDDA1D200726E3A /* Qa */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 61234BE53D3963CF1F3EDB3E /* Pods-ePragathi-ePragathiQa.qa.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = "ePragathiQa-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Qa;
		};
		6B04D64B2DDDA1EA00726E3A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0C2B8712C1085C0F97D67677 /* Pods-ePragathi-ePragathiProd.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "ePragathiProd-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		6B04D64C2DDDA1EA00726E3A /* Dev */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 85395EF5F9E4054B0C81C640 /* Pods-ePragathi-ePragathiProd.dev.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "ePragathiProd-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Dev;
		};
		6B04D64D2DDDA1EA00726E3A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AEC7257755161028DD224384 /* Pods-ePragathi-ePragathiProd.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = "ePragathiProd-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		6B04D64E2DDDA1EA00726E3A /* Prod */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EF9DDBF74C415349595164C5 /* Pods-ePragathi-ePragathiProd.prod.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = "ePragathiProd-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Prod;
		};
		6B04D64F2DDDA1EA00726E3A /* Qa */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2449CD48F7FE60239EDA8BA1 /* Pods-ePragathi-ePragathiProd.qa.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = "ePragathiProd-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Qa;
		};
		6BB37D0A2DD4E34000ED5C49 /* Dev */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Dev;
		};
		6BB37D0B2DD4E34000ED5C49 /* Dev */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F7213369377B352D902BE44C /* Pods-ePragathi.dev.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = ePragathi/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = ePragathi;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Dev;
		};
		6BB37D0C2DD4E35C00ED5C49 /* Qa */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Qa;
		};
		6BB37D0D2DD4E35C00ED5C49 /* Qa */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B9161BCA77C4F5DB5AF5FDCF /* Pods-ePragathi.qa.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = ePragathi/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = ePragathi;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Qa;
		};
		6BB37D0E2DD4E37600ED5C49 /* Prod */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Prod;
		};
		6BB37D0F2DD4E37600ED5C49 /* Prod */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BF9EE0DEAECE5B4440E0003C /* Pods-ePragathi.prod.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = ePragathi/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = ePragathi;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Prod;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "ePragathi" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				6BB37D0B2DD4E34000ED5C49 /* Dev */,
				13B07F951A680F5B00A75B9A /* Release */,
				6BB37D0F2DD4E37600ED5C49 /* Prod */,
				6BB37D0D2DD4E35C00ED5C49 /* Qa */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6B04D6382DDDA1D200726E3A /* Build configuration list for PBXNativeTarget "ePragathiQa" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6B04D6392DDDA1D200726E3A /* Debug */,
				6B04D63A2DDDA1D200726E3A /* Dev */,
				6B04D63B2DDDA1D200726E3A /* Release */,
				6B04D63C2DDDA1D200726E3A /* Prod */,
				6B04D63D2DDDA1D200726E3A /* Qa */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6B04D64A2DDDA1EA00726E3A /* Build configuration list for PBXNativeTarget "ePragathiProd" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6B04D64B2DDDA1EA00726E3A /* Debug */,
				6B04D64C2DDDA1EA00726E3A /* Dev */,
				6B04D64D2DDDA1EA00726E3A /* Release */,
				6B04D64E2DDDA1EA00726E3A /* Prod */,
				6B04D64F2DDDA1EA00726E3A /* Qa */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "ePragathi" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				6BB37D0A2DD4E34000ED5C49 /* Dev */,
				83CBBA211A601CBA00E9B192 /* Release */,
				6BB37D0E2DD4E37600ED5C49 /* Prod */,
				6BB37D0C2DD4E35C00ED5C49 /* Qa */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
