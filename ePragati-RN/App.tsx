import i18n from './src/translations/index';
import React from 'react';
import { Provider } from 'react-redux';
import store from './src/redux/Root/rootStore';
import AppNavigator from './src/navigation/AppNavigator';
import { SafeAreaView, StatusBar, StyleSheet } from 'react-native';
import Colors from './src/utils/Colors/Colors';

i18n.changeLanguage('en');

export default function App() {
  return (
    <Provider store={store}>
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={Colors.white} />
        <AppNavigator />
      </SafeAreaView>
    </Provider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  }
})
