{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android --mode=debug", "ios": "react-native run-ios", "lint": "eslint '**/*.{ts,tsx}' --fix", "start": "react-native start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "sonar": "sonar-scanner", "postinstall": "patch-package", "android_dev": "react-native run-android --mode=debug", "android_qa": "react-native run-android --mode=qa", "android_prod": "react-native run-android --mode=release", "clean": "watchman watch-del-all &&  rm -rf node_modules android/app/build ios/Pods ios/Podfile.lock ~/Library/Developer/Xcode/DerivedData"}, "dependencies": {"@react-native-community/netinfo": "^11.4.1", "@react-native-vector-icons/fontawesome6": "^12.0.0", "@react-native-vector-icons/ionicons": "^12.0.0", "@react-navigation/native": "^7.1.8", "@react-navigation/native-stack": "^7.3.12", "@reduxjs/toolkit": "^2.8.1", "axios": "^1.9.0", "i18next": "^25.0.1", "react": "19.0.0", "react-i18next": "^15.4.0", "react-native": "0.78.0", "react-native-config": "^1.5.5", "react-native-localize": "^3.4.1", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.12.0", "react-native-svg-transformer": "^1.5.1", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-saga": "^1.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "^15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.0", "@react-native/eslint-config": "0.78.0", "@react-native/metro-config": "0.78.0", "@react-native/typescript-config": "0.78.0", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.14", "@types/react": "^19.1.4", "@types/react-native": "^0.72.8", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "jest": "^29.7.0", "patch-package": "^8.0.0", "prettier": "^2.8.8", "react-test-renderer": "19.0.0", "redux-saga-test-plan": "^4.0.6", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}