// module.exports = {
//   root: true,
//   extends: '@react-native',
// };

module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
    project: './tsconfig.json',
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-native/all',
    'plugin:prettier/recommended',
  ],
  plugins: ['@typescript-eslint', 'react', 'react-native'],
  env: {
    'react-native/react-native': true,
    es6: true,
    node: true,
  },
  rules: {
    'react-native/no-inline-styles': 'off',
    '@typescript-eslint/semi': ['error', 'always'],
    'no-unexpected-multiline': 'error',
    'no-extra-semi': 'error',
  },
  settings: {
    react: {
      version: 'detect',
    },
  },
};
