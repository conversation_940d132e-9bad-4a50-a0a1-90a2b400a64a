# Node modules
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock
*.lock


# Metro bundler cache
.expo/
.expo-shared/
.expo-dev-client/
.expo-*

# macOS system files
.DS_Store

# Watchman
.watchmanconfig

# TypeScript cache
*.tsbuildinfo

# Logs
*.log
logs/

# iOS
ios/Pods/
ios/Podfile.lock
ios/build/
ios/DerivedData/
ios/.xcode.env.local
ios/.xcode.env
ios/*.xcworkspace
ios/*.xcuserdata
ios/*.xcuserstate
ios/.idea/

# Xcode
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
xcuserdata/

# Android
android/.gradle/
android/build/
android/local.properties
android/.idea/
android/captures/
android/.cxx/
android/app/build/

# Buck
buck-out/
.buckconfig.local
.buckd/

# Kotlin
*.iml
.gradle/
build/
.idea/

# Fastlane
fastlane/

# React Native specific
*.keystore
*.jks
debug.keystore
*.p8
*.pem
*.mobileprovision

# CodePush
CodePush/

# Testing
coverage/
jest-cache/
jest-test-results.json
__tests__/output/
.nyc_output/

# Firebase
firebase-debug.log

# Misc
*.swp
*.swo
*.tmp
tmp/
temp/
*.bak
*.orig

# Android NDK / CMake
android/app/.cxx/
android/.cxx/
android/app/cmake-build-debug/
android/.idea/
*.iml

# Ninja, CMake, and build artifacts
*.ninja
*.cmake
*.bin
*.txt
*.log
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3

# Ignore native build and CMake artifacts
android/app/.cxx/
android/.cxx/
*.ninja
*.cmake
*.log
*.bin
*.txt

# Ruby & CocoaPods
.bundle/
vendor/
Gemfile.lock
.scannerwork/

# Additional entries
ios/main.jsbundle
ios/tmp.xcconfig
.vscode/