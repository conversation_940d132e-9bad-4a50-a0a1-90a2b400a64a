{"commonStrings": {"ok": "OK", "cancel": "Cancel", "apply": "Apply", "done": "Done", "yes": "Yes", "no": "No", "select": "Select", "loadingData": "Loading data...", "alertTitle": "<PERSON><PERSON>", "invalidToken": "Invalid <PERSON>", "internalServerError": "Internal Server Error", "invalidCredentials": "Invalid Credentials", "loginSuccess": "Logged In Succesfully", "noResponseReceived": "No Response Received", "noRolesFound": "No Roles Found", "downloadWbs": "Download Master WBS", "makeCurrentJob": "Make", "currentWBSJobSetMSg": "Current Job or Delete", "WBSDeleteConfirmMsg": "Are you Sure want to delete", "job": "Job", "setCurrentJob": "Make Current Job", "delete": "Delete", "download": "Download", "downloadAsPrimary": "Download & Set as a Primary", "search": "Search", "all": "All", "downloaded": "Downloaded", "allKey": "all", "downloadedKey": "downloaded", "currentJob": "Current Job", "otherJob": "Other job", "roles": "Roles", "somethingWentWrong": "Something went wrong. Please try again.", "wbsDownloadError": "WBS Download Error", "wbsDownloading": "WBS Downloading...", "pleaseWait": "Please Wait...", "wbsDownloadSuccess": "Download has been completed successfully", "wbsDownloadFailed": "Download Failed", "wbsDeleteSuccess": "Deletion has been completed successfully", "wbsResponse": "Please contact Planning in-charge to get WBS access", "NoDetailsFound": "WBS Master No Details found", "NetworkError": "Network Error Occured", "invalidWbsResponse": "Invalid WBS response"}, "alertMessageStrings": {"networkAlertTitle": "Network Alert", "noNetworkConnection": "Please check your internet connection and try again."}, "welcomeMessageStrings": {"welcome": "Welcome"}, "loginStrings": {"login": "<PERSON><PERSON>"}, "hindranceStrings": {"createHindrance": "Create Hindrance", "singlePointMsg": "Select Single Map Location", "multiplePointMsg": "Place the Location one and two to create hindrance", "updateHindrance": "Update Hindrance", "select": "Select", "pipeMaterialDia": "Select Pipe Material and Dia", "reasonForGap": "Select Reason for Gap", "hindrance": "Hindrance", "create": "Create", "viewRemarks": "View Remarks", "balance": "Balance", "update": "Update", "adminApproval": "Yet to approve by <PERSON><PERSON>", "add": "Add"}, "SyncData": {"syncDataHeader": "Sync Data", "progressUpdate": "Progress Update", "hindrance": "Hindrance", "dayPlan": "Day Plan", "assets": "Assets", "sync": "Sync", "date": "Date", "gapLength": "Gap Length", "lat": "Latitude", "long": "Longitude", "mandays": "Man Days", "viewlastupdate": "View Last Update", "progressquantity": "Progress Qty", "progresstilldate": "Progress Till date", "plannedqunatity": "planned Qty", "totalquantity": "Total Qty", "update": "Update", "remarks": "Remarks", "enter": "Enter", "pipemeterailanddia": "Pipe Material and Dia", "reasonforgap": "Reason for Gap", "gaplength": "Gap Length", "startandendnode": "Start and End Node", "progresslength": "Progress Length", "viewother": "View Other", "balance": "Balance", "dayplanning": "Day Planning", "manpower": "Man Power", "material": "Material", "pm": "P&M", "safety": "Safety", "available": "Available", "required": "Required", "requestnewasset": "Request New Asset", "time": "Time", "usage": "Usage", "reason": "Reason", "fromdate": "From Date", "fromtime": "From Time", "totime": "To Time", "todate": "To Date", "editasset": "Edit Asset", "asset": "<PERSON><PERSON>", "requiredquantity": "Required <PERSON><PERSON><PERSON><PERSON>", "skills": "Skills", "helperrequiredqunatity": "Helper Required <PERSON><PERSON><PERSON><PERSON>", "editrequirementtitle": "Edit Requirement for", "addsubactivity": "Add Sub - Activity", "vendorcode": "Vendor Code - Des", "focusarea": "Focus Area", "risk": "Risk Potential", "riskzoning": "Risk Zoning", "ocp": "OCP"}, "PendingForApprovalStrings": {"pendingForApproval": "Pending", "selectPath": "Select Path"}, "BookmarkStrings": {"bookmarks": "Book Mark"}, "RecentStrings": {"recentList": "Recent"}, "ProgressUpdate": {"update": "Progress Update", "updateError": "Progress Update Error", "updateSuccess": "Progress Updated Successfully", "storedOffline": "Network Error.Progress update has been stored in Sync.", "selectWbs": "Select WBS"}}