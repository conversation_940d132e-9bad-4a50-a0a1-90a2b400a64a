import axios, { AxiosRequestConfig } from 'axios';
import PrintLog from '../utils/Logger/PrintLog';
import Config from '../config/Config';
import BaseApiConstants from '../utils/Constants/CommonConstant';

export async function AxiosRequestForAuth(apiParams: AxiosRequestConfig) {
  axios.defaults.baseURL = Config.BASE_URL_FOR_AUTH;
  PrintLog.debug('AxiosRequestForAuth', ' -- baseUrl: ', Config.BASE_URL_FOR_AUTH, ' -- apiParams: ', apiParams);
  try {
    const response = await axios({
      timeout: 5000,
      ...apiParams,
    });
    return response;
  } catch (error: any) {
    PrintLog.error('AxiosRequestForAuth', { 'Catch Error: ': error });
    return error.response;
  }
}

export async function AxiosRequestForDownload(apiParams: AxiosRequestConfig) {
  axios.defaults.baseURL = Config.BASE_URL_FOR_DOWNLOAD;
  PrintLog.debug('AxiosRequestForDownload', ' -- baseUrl: ', Config.BASE_URL_FOR_DOWNLOAD, ' -- apiParams: ', apiParams);
  try {
    const response = await axios({
      timeout: 15000,
      ...apiParams,
    });
    return response;
  } catch (error: any) {
    PrintLog.error('AxiosRequestForDownload', { 'Catch Error: ': error });
    return error.response;
  }
}

export async function AxiosRequest(baseUrl: string, apiParams: AxiosRequestConfig) {
  PrintLog.debug('AxiosRequest', ' -- baseUrl: ', baseUrl, ' -- apiParams: ', apiParams);
  try {
    const response = await axios({
      baseURL: baseUrl, // Pass inline instead of relying on global defaults
      timeout: 5000,
      ...apiParams,
    });
    return response;
  } catch (error: any) {
    PrintLog.error('AxiosRequest', { 'Catch Error: ': error });
    return error.response;
  }
}

export async function AxiosRequestForNode(apiParams: AxiosRequestConfig) {
  axios.defaults.baseURL = BaseApiConstants.nodeList;
  PrintLog.debug('AxiosRequestForNode', ' -- baseUrl: ', BaseApiConstants.nodeList, ' -- apiParams: ', apiParams);
  try {
    const response = await axios({
      timeout: 15000,
      ...apiParams,
    });
    return response;
  } catch (error: any) {
    PrintLog.error('AxiosRequestForNode', { 'Catch Error: ': error });
    return error.response;
  }
}

export async function AxiosRequestForImageDownload(apiParams: AxiosRequestConfig) {
  axios.defaults.baseURL = BaseApiConstants.imageUploadDownloadUrl;
  PrintLog.debug('AxiosRequestForDownload', ' -- baseUrl: ', Config.BASE_URL_FOR_DOWNLOAD, ' -- apiParams: ', apiParams);
  try {
    const response = await axios({
      timeout: 15000,
      ...apiParams,
    });
    return response;
  } catch (error: any) {
    PrintLog.error('AxiosRequestForDownload', { 'Catch Error: ': error });
    return error.response;
  }
}
