import BaseApiConstants from '../utils/Constants/CommonConstant';
import PrintLog from '../utils/Logger/PrintLog';
import Strings from '../utils/Strings/Strings';
import Config from '../config/Config';
import {
  AxiosRequest,
  AxiosRequestForAuth,
  AxiosRequestForDownload,
} from './AxiosService';

const getHeaders = (
  type: string,
  url: string,
  token?: string
): Record<string, string> => {
  const isPlain = type.toUpperCase() === Strings.apiHeaderStrings.PLAIN.toUpperCase();

  PrintLog.debug('getHeaders', ' -- isPlain: ', isPlain, ' -- url: ', url);
  const contentType =
    isPlain ? 'application/json'
      : 'multipart/form-data';

  let subscriptionKey = Config.OCP_APIM_SUBSCRIPTION_KEY!;

  if (Config.ENV !== 'prod') {
    if (!url.includes('Pragati')) {
      PrintLog.debug('NOt Includes -- getHeaders', ' -- Config.OCP_APIM_SUBSCRIPTION_KEY_COMMON: ', Config.OCP_APIM_SUBSCRIPTION_KEY_COMMON);
      subscriptionKey = Config.OCP_APIM_SUBSCRIPTION_KEY_COMMON!;
    }
  }

  const headers: Record<string, string> = {
    'Content-Type': contentType,
    'Ocp-Apim-Subscription-Key': subscriptionKey,
  };

  if (token) {
    headers.Authorization = url.includes('GetActualsForApproval')
      ? `Bearer ${token}`
      : token;
  }
  PrintLog.debug('headers: ', headers);
  return headers;
};

const getData = async <response = any>(
  callingFunction: string,
  urlSuffix: string,
  onSuccess: (res: response) => void,
  onError: (err: any) => void,
): Promise<void> => {
  PrintLog.debug('getData', {
    'CallingFunction: ': callingFunction,
    ' -- urlSuffix: ': urlSuffix,
  });
  try {
    const result = await AxiosRequestForDownload({
      url: urlSuffix,
      method: 'GET',
    });
    PrintLog.debug('getData', {
      'CallingFunction: ': callingFunction,
      ' -- result: ': result,
    });
    if (result?.data) {
      onSuccess(result.data as response);
    } else {
      onError(result);
    }
  } catch (error: any) {
    PrintLog.error('getData', {
      'CallingFunction: ': callingFunction,
      ' catch Error: ': error,
    });
    onError(error?.response ?? error);
  }
};

const getDataWithParams = async <params = any, response = any>(
  callingFunction: string,
  urlSuffix: string,
  queryParams: params,
  onSuccess: (res: response) => void,
  onError: (err: any) => void,
): Promise<void> => {
  PrintLog.debug('getDataWithParams', {
    'CallingFunction: ': callingFunction,
    ' -- urlSuffix: ': urlSuffix,
    ' -- queryParams: ': queryParams,
  });
  try {
    const result = await AxiosRequestForDownload({
      url: urlSuffix,
      method: 'GET',
      params: queryParams,
    });
    PrintLog.debug('getDataWithParams', {
      'CallingFunction: ': callingFunction,
      ' -- result: ': result,
    });
    if (result?.data) {
      onSuccess(result.data as response);
    } else {
      onError(result);
    }
  } catch (error: any) {
    PrintLog.error('getDataWithParams', {
      'CallingFunction: ': callingFunction,
      ' catch Error: ': error,
    });
    onError(error?.response ?? error);
  }
};

const postDataWithBodyForAuth = async <data = any, response = any>(
  urlSuffix: string,
  body: data,
  onSuccess: (res: response) => void,
  onError: (err: any) => void,
): Promise<void> => {
  try {
    const result = await AxiosRequestForAuth({
      url: urlSuffix,
      method: 'POST',
      data: body,
      headers: getHeaders(
        Strings.apiHeaderStrings.PLAIN,
        Config.BASE_URL_FOR_AUTH + urlSuffix,
        '',
      ),
    });
    PrintLog.debug('postDataWithBodyForAuth', { 'result: ': result });
    if (result?.data) {
      onSuccess?.(result.data as response);
    } else {
      onError?.(result);
      PrintLog.debug('postDataWithBodyForAuth', { 'Error: ': result });
    }
  } catch (error: any) {
    onError?.(error?.response ?? error);
    PrintLog.debug('postDataWithBodyForAuth', { 'Catch Error: ': error });
  }
};

const postDataWithBodyForDownload = async <data = any, response = any>(
  urlSuffix: string,
  body: data,
  onSuccess: (res: response) => void,
  onError: (err: any) => void,
): Promise<void> => {
  try {
    const headers = getHeaders(
      Strings.apiHeaderStrings.PLAIN,
      Config.BASE_URL_FOR_DOWNLOAD + urlSuffix,
      '',
    );
    const result = await AxiosRequestForDownload({
      url: urlSuffix,
      method: 'POST',
      data: body,
      headers,
    });
    try {
      PrintLog.debug('[API RESPONSE] POST', {
        url: Config.BASE_URL_FOR_DOWNLOAD + urlSuffix,
        result: JSON.stringify(result, null, 2),
      });
    } catch (e) {
      PrintLog.debug('[API RESPONSE] Logging failed:', e);
    }
    if (result?.data) {
      onSuccess?.(result.data as response);
    } else {
      onError?.(result);
    }
  } catch (error: any) {
    // Detailed error logging
    try {
      PrintLog.error('[API ERROR] POST', {
        url: Config.BASE_URL_FOR_DOWNLOAD + urlSuffix,
        error: typeof error === 'object' ? JSON.stringify(error, null, 2) : error,
      });
    } catch (e) {
      PrintLog.error('[API ERROR] Logging failed:', e);
    }
    onError?.(error?.response ?? error);
  }
};

const postDataWithBodyToken = async <data = any, response = any>(
  urlSuffix: string,
  body: data,
  onSuccess: (res: response) => void,
  onError: (err: any) => void,
): Promise<void> => {
  try {
    const result = await AxiosRequest(BaseApiConstants.defaultGenerateToken, {
      url: urlSuffix,
      method: 'POST',
      data: body,
      headers: getHeaders(
        Strings.apiHeaderStrings.PLAIN,
        BaseApiConstants.defaultGenerateToken + urlSuffix,
        '',
      ),
    });

    if (result?.data) {
      onSuccess?.(result.data as response);
    } else {
      onError?.(result);
    }
  } catch (error: any) {
    onError?.(error?.response ?? error);
  }
};

const putDataWithBody = async <data = any, response = any>(
  urlSuffix: string,
  body: data,
  onSuccess: (res: response) => void,
  onError: (err: any) => void,
): Promise<void> => {
  try {
    const result = await AxiosRequestForDownload({
      url: urlSuffix,
      method: 'PUT',
      data: body,
    });

    if (result?.data) {
      onSuccess?.(result.data as response);
    } else {
      onError?.(result);
    }
  } catch (error: any) {
    onError?.(error?.response ?? error);
  }
};

export {
  getHeaders,
  getData,
  getDataWithParams,
  postDataWithBodyForAuth,
  postDataWithBodyForDownload,
  postDataWithBodyToken,
  putDataWithBody,
};
