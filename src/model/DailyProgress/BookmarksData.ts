export interface BookmarksRequestData {
    Uid: string;
    Type: string;
    JobWorkLists: JobWorkListsData[];
}

export interface JobWorkListsData {
    jobcode: string;
    wpcode: string;
  }

export interface BookmarksResponseData {
    BookMarkUnlinkedOutput: {
        Message: string;
    };
    bookMarklinkedOutput: {
        Message: string;
    };
    MasterList: {
        BookMarkLinked: null;
        LatLongHierarchy: null;
        UserMannual: null;
        WBS_Description: null;
    };
}

