export type StatusCallback<T = any> = (params: { success: boolean; data?: T; statusCode?: number }) => void;

export interface WBSItem {
    id: string;
    entity_Code: string;
    job_Code: string;
    entity_Type: string;
    leaf_Node_Tag: string;
    entity_Description: string;
    parent_WBS_Code: string | null;
    parent_entity_code?: string;
    parent_Task?: string | null;
    gis_Tag?: string;
    isChild?: boolean;
    et_Code?: string;
    fullPath: string;
}

export interface WBSJobRecord {
    WBS_Code: string;
    Job_Code: string;
    Leaf_Node_Tag: string;
    WBS_Description: string;
    Parent_WBS_Code: string | null;
    ET_Code: string;
    GIS_Tag: string;
    Parent_Entity_Code: string
}

export interface PendingForApprovalBQITRecord {
    ActualQuantity: number | null;
    Alignment: string | null;
    Distance_From_Center: number | null;
    From_Length: number | null;
    IS_Completed: string | null;
    Image_ID: string | null;
    Image_URL: string | null;
    Is_Hindrance: string | null;
    JOB: string;
    Latitude: number;
    Longitude: number;
    Manpower: number;
    NodeId: string | null;
    Progress_Length: number | null;
    Quantity: number;
    ReferanceNodeId: string | null;
    Remarks: string | null;
    TDate: string;
    Task: string;
    TaskType: string;
    Task_Custom_Description: string;
    Task_Description: string;
    TotalQuantity: number | null;
    Total_Length: number | null;
    UOM: string | null;
    UserID: number;
    UserName: string;
    WBS: string;
    WBS_Custom_Description: string;
    WBS_Description: string;
    Leaf_Node_Tag: string;
    GIS_Tag: string;
    Parent_Entity_Code: string;
}

export interface ActualList {
    WBS: string;
    TaskCode: string;
    ADate: string;
    Quantity: string;
    Manpower: string;
    Remarks: string;
    Tasktype: string;
    Is_Approved: string;
    Tag: string;
    Latitude: number;
    Longitude: number;
}

export interface ProgressUpdateRequestBody {
    jobCode: string;
    UID: string;
    Type: string;
    Notification_Desc: string;
    Quantity: number;
    uOM: string;
    manPower: number;
    ActualList: ActualList[];
    Attachments: any | null;
    cb: StatusCallback;
}

// Progress Update Response Types
export interface ProgressUpdateResponseData {
    success: boolean;
    message: string;
    data?: any;
}

export type FilterSource = 'Book Mark' | 'Recent' | 'Pending';


// export interface WBSTaskRecord {
//     Task_Code: string;
//     Job_Code: string;
//     WBS_Code: string;
//     Parent_WBS_Code: string | null;
//     Parent_Task: string | null;
//     Is_Active: string;
// }

// export interface GISNodeRecord {
//     entity_Code: string;
//     job_Code: string;
//     description: string;
//     Parent_Entity_Code: string;
//     Parent_Task: string;
// }

// Constants to match EntityConstants
export const EntityConstants = {
    WBSJob: 'WBSJob',
    WBSTask: 'WBSTask',
    WBSDetails: 'WBSDetails',
    GISNode: 'GISNode',
    Y: 'Y'
};