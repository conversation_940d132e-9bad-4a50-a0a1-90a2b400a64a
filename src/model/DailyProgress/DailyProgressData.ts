export type StatusCallback<T = any> = (params: { success: boolean; data?: T; statusCode?: number }) => void;

export interface WBSItem {
    id: string;
    entity_Code: string;
    job_Code: string;  // refers to Job in pending  list and same for Recentslist
    entity_Type: string; // refers to TaskType in pending  list and same for Recentslist
    leaf_Node_Tag: string;
    entity_Description: string; // refers to WBS_Description in pending  list and same for Recentslist
    parent_WBS_Code: string | null;  // refers to wbs for pending  list and same for Recentslist
    parent_entity_code?: string;
    parent_Task?: string | null;
    gis_Tag?: string;
    isChild?: boolean;
    et_Code?: string; // refers to TaskType in pending  list and same for Recentslist
    fullPath?: string;
    fullDescriptionForTooltip?: string;
    taskDescription?: string; // refers to Task_Description in pending  list and NOT in Recentslist

// Newly added to include Pending list now
    ActualQuantity?: number | null;
    Alignment?: string | null;
    Distance_From_Center?: number | null;
    From_Length?: number | null;
    IS_Completed?: string | null;
    Image_ID?: string | null;
    Image_URL?: string | null;
    Is_Hindrance?: string | null;
    JOB?: string;
    Latitude?: number;
    Longitude?: number;
    Manpower?: number;
    NodeId?: string | null;
    Progress_Length?: number | null;
    Quantity?: number;
    ReferanceNodeId?: string | null;
    Remarks?: string | null;
    TDate?: string;
    Task?: string;
    // TaskType?: string;
    // Task_Custom_Description?: string;
    // Task_Description?: string;
    TotalQuantity?: number | null;
    Total_Length?: number | null;
    UOM?: string | null;
    UserID?: number;
    UserName?: string;
    // WBS?: string;
    // WBS_Custom_Description?: string;
    // WBS_Description?: string;
    Leaf_Node_Tag?: string;
    GIS_Tag?: string;
    Parent_Entity_Code?: string;
    Job_Description?: string;

    hierarchyLevel?: string,
    isActive?: string,
    wpCode?: string,
    wpDescription?: string,
    childWorkCode?: string,
    childWorkDescription?: string,
    deliverableCode?: number,
    deliverableCodeDesc?: string,                                       
};


export interface WBSJobRecord {
    WBS_Code: string;
    Job_Code: string;
    Leaf_Node_Tag: string;
    WBS_Description: string;
    Parent_WBS_Code: string | null;
    ET_Code: string;
    GIS_Tag: string;
    Parent_Entity_Code: string
}

export interface PendingForApprovalBQITRecord {
    ActualQuantity: number | null;
    Alignment: string | null;
    Distance_From_Center: number | null;
    From_Length: number | null;
    IS_Completed: string | null;
    Image_ID: string | null;
    Image_URL: string | null;
    Is_Hindrance: string | null;
    JOB: string;
    Latitude: number;
    Longitude: number;
    Manpower: number;
    NodeId: string | null;
    Progress_Length: number | null;
    Quantity: number;
    ReferanceNodeId: string | null;
    Remarks: string | null;
    TDate: string;
    Task: string;
    TaskType: string;
    Task_Custom_Description: string;
    Task_Description: string;
    TotalQuantity: number | null;
    Total_Length: number | null;
    UOM: string | null;
    UserID: number;
    UserName: string;
    WBS: string;
    WBS_Custom_Description: string;
    WBS_Description: string;
    Leaf_Node_Tag: string;
    GIS_Tag: string;
    Parent_Entity_Code: string;
    Job_Description: string;
}

export interface ActualList {
    WBS: string;
    TaskCode: string;
    ADate: string;
    Quantity: string;
    Manpower: string;
    Remarks: string;
    Tasktype: string;
    Is_Approved: string;
    // Tag: string;
    Latitude: number;
    Longitude: number;
}
export interface Attachment {
     WBS: string;
     TaskCode: string;
     ADate: string;
     Tasktype: string;
     SiteUrl: string;
     Unique: string;
}

export interface ProgressUpdateRequestBody {
    jobCode: string;
    UID: string;
    Type: string;
    Notification_Desc: string;
    Quantity: number;
    uOM: string;
    manPower: number;
    ActualList: ActualList[];
    Attachments: Attachment[];
    cb: StatusCallback;
}

export interface UploadImageResponse{
        UniqueID: string;
        SiteUrl: string;
        LibraryName: string;
        FileSize: string;
}

export interface DownloadImageRequest {
    ModuleName: string;
    Unique: string;
    SiteUrl: string;
}

export interface DownloadImageResponse {
    success: boolean;
    data?: string; // Base64 encoded image data
    message?: string;
}

// Progress Update Response Types
export interface ProgressUpdateResponseData {
    success: boolean;
    message: string;
    data?: any;
}

export type FilterSource = 'Book Mark' | 'Recent' | 'Pending';


// export interface WBSTaskRecord {
//     Task_Code: string;
//     Job_Code: string;
//     WBS_Code: string;
//     Parent_WBS_Code: string | null;
//     Parent_Task: string | null;
//     Is_Active: string;
// }

// export interface GISNodeRecord {
//     entity_Code: string;
//     job_Code: string;
//     description: string;
//     Parent_Entity_Code: string;
//     Parent_Task: string;
// }

// Constants to match EntityConstants
export const EntityConstants = {
    WBSJob: 'WBSJob',
    WBSTask: 'WBSTask',
    WBSDetails: 'WBSDetails',
    GISNode: 'GISNode',
    Y: 'Y'
};