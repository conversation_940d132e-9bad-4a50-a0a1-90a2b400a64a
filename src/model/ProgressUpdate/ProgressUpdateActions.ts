import { ProgressUpdateRequestBody, ProgressUpdateResponseData } from "../DailyProgress/DailyProgressData";
import { PROGRESS_UPDATE_FAILURE, PROGRESS_UPDATE_REQUEST, PROGRESS_UPDATE_SUCCESS } from "./ProgressUpdateActionTypes";

// Action Creators
export const ProgressUpdateRequest = (payload: ProgressUpdateRequestBody) => ({
    type: PROGRESS_UPDATE_REQUEST,
    payload
});

export const ProgressUpdateSuccess = (payload: ProgressUpdateResponseData) => ({
    type: PROGRESS_UPDATE_SUCCESS,
    payload
});

export const ProgressUpdateFailure = (payload: string) => ({
    type: PROGRESS_UPDATE_FAILURE,
    payload
}); 