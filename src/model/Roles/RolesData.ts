import { StatusCallback } from "../Wbs/WbsData";

export interface RoleItem {
    User_ID: number;
    User_Full_Name: string;
    Login_Name: string;
    Jobcode: string;
    JobDesc: string;
    FRCode: number;
    Functional_ROLE: string;
}

export interface RolesResponse {
    RolesList: RoleItem[];
}

export interface RolesRequest {
    User_ID: string;
    RoleType: string;
    cb: StatusCallback;
}