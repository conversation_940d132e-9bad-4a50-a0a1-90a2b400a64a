import { Attachment } from "../DailyProgress/DailyProgressData";

export interface ApproveRequestData {
  jobCode: string;
  UID: string;
  Type: 'approve' | 'ADeletion';
  Notification_Desc: string;
  Quantity: number;
  uOM: string;
  manPower: number;
  ActualList: Array<{
    WBS: string;
    TaskCode: string;
    ADate: string;
    Quantity: number;
    Manpower: number;
    Remarks: string;
    Tasktype: string;
    Is_Approved: string;
    Tag: string;
    Latitude: number;
    Longitude: number;
  }>;
  Attachments: Attachment[];
}

export interface ApproveResponseData {
  // StatusCode: number;
  // message?: string;
  // data?: any;

  success: boolean;
  message: string;
  data?: any;
} 