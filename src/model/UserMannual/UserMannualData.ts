export interface UserMannual {
    PRGDTA_Document_Number: string;
    PRGDTA_DT_Code: number;
    PRGDTA_Unique_ID: number;
}
export interface MasterList {
    BookMarkLinked: any;
    LatLongHierarchy: any;
    UserMannual: UserMannual[];
    WBS_Description: any;
}
export interface UserMannualResponse {
    BookMarkUnlinkedOutput: any;
    bookMarklinkedOutput: any;
    MasterList: MasterList;
}

export interface UserMannualDownloadRequestData {
    ModuleName: string,
    Unique: number,
    SiteUrl: string
}
