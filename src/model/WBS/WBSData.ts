export interface WbsJobItem {
    jobCode: string;
    jobName?: string;
}

export type WbsRequestType =
    | 'WBS'
    | 'Task'
    | 'Details'
    | 'GISDetails'
    | 'ProgressDetails_Enginner'
    | 'ProgressConsolidated'
    | 'ViewLastUpdateBQITFromTodateOutput'
    | 'ViewLastUpdateGISFromTodateOutput'
    | 'BookMarkList'
    | 'LatLongHierarchy';

export type StatusCallback<T = any> = (params: { success: boolean; data?: T }) => void;
export interface WbsRequestData {
    userId: string;
    type?: WbsRequestType;
    objJoblist: WbsJobItem[];
    Fromdate?: string;
    Todate?: string;
    selectedJob?: Array<{ id: string, name: string, role: string }>;
    isLogin?: boolean;
    cb: StatusCallback;
}

export interface GetWbsApproverProps {
    jobCode: string;
    type: string;
    selectedJob: any;
    objJoblist: any;
    UID?: number;
    cb: StatusCallback;
}

export interface PendingApprovalRequestBody {
    jobCode: string;
    type: string;
    UID?: number;
}

export interface WbsHierarchyItem {
    Job_Code: string;
    WBS_Code: string;
    WBS_Description: string;
    Parent_WBS_Code: string;
    ET_Code: string;
    Leaf_Node_Tag: string | null;
    Sort_Order: number;
    Is_Active: string;
    entity_type: string;
}

export interface WbsTaskItem {
    Job_Code: string;
    Task_Code: string;
    Task_Description: string;
    Parent_Task_Code: string;
    ET_Code: string;
    UOM_Symbol: string;
    Is_Active: string;
    Gis_Tag: string;
    Parent_WBS: string;
    Parent_Task: string;
    entity_Type: string;
    // Parent_WBS_Code: string;
}

export interface WbsDetailsItem {
    JobCode: string;
    WBS: string;
    Task: string;
    PlanedQty: number;
    PlanedSDate: string;
    PlanedEDate: string;
    PlanedLabour: number;
    ActualQty: number;
    ActualSDate: string | null;
    ActualEDate: string | null;
    ActualLabour: number;
    LastUpdatedDate: string;
    LastUpdatedQty: number;
    LastUpdatedLabour: number;
    Remarks: string;
    TaskType: string;
    Min_Productivity_Range: number;
    Max_Productivity_Range: number;
    Scope: number;
    MonthwisePlanQty: number;
    Et_Code: number;
    DeliverableType_Et_Code: string;
    DET_Assigned_Scope_Quantity: number;
    DET_Year_Month: number;
    TDD_Is_Engineer_Target_Mandatory: string;
}

export interface WbsGISDetailsItem {
    JobCode: string;
    WBS: string;
    Task: string;
    Deliverable: string | null;
    Remarks: string | null;
    Boq: string | null;
    ActualDate: string;
    NodeId: string;
    NodeRefCode: number;
    FromLength: number;
    ToLength: number;
    TotalLength: number;
    Manpower: number;
    DistanceFCenter: number;
    Alignment: string;
    TaskType: string;
    Scope: number;
    MonthwisePlanQty: number;
    Et_Code: number;
    DeliverableType_Et_Code: string;
    Is_Hindrance: string;
    DET_Assigned_Scope_Quantity: number;
    DET_Year_Month: number | null;
    TDD_Is_Engineer_Target_Mandatory: string;
    PlanedSDate: string;
    PlanedEDate: string;
    ActualSDate: string;
    ActualEDate: string;
}

export interface DeliverableProgressDetailsEngineerItem {
    TDD_Job_Code: string;
    TDD_WBS_Code: string;
    TDD_Deliverable_Code: number;
    Delivscope: number;
    CumProg: number;
    CumManday: number;
    FTMProgress: number;
    FTMManday: number;
    CumTargetPlanQty: number;
    FTMCumTargetPlanQty: number;
    TargetScope: number;
}

export interface ProgressConsolidatedItem {
    TDD_Job_Code: string;
    TDD_WBS_Code: string;
    TDD_Deliverable_Code: number;
    Delivscope: number;
    CumProg: number;
    CumManday: number;
    FTMProgress: number;
    FTMManday: number;
    ScScopeE: number;
    ScProgress: number;
    ScManday: number;
    CumPlanningQuantity: number;
    FTMPlanningQuantity: number;
}

export interface ViewLastUpdateBQITItem {
    JOB: string;
    TDP_WBS_CODE: string;
    TDP_DELIVERABLE_CODE: number;
    INSERTED_BY: number;
    Inserted_On: string;
    QTY: number;
    MAN: number;
    MUSGD_User_ID: number;
    Full_Name: string;
    Reference_ID: string;
    MUOM_Short_Description: string;
}

export interface ViewLastUpdateGISItem {
    JOB: string;
    TDP_WBS_CODE: string;
    TDP_DELIVERABLE_CODE: number;
    NodeID: string;
    INSERTED_BY: number;
    Inserted_On: string;
    QTY: number;
    MAN: number;
    MUSGD_User_ID: number;
    Full_Name: string;
    Reference_ID: string;
    MUOM_Short_Description: string;
}

export interface WbsJobResponseData {
    WbsHierarchyOutput: WbsHierarchyItem[] | null;
    WbsTaskOutput: WbsTaskItem[] | null;
    WbsDetailsOutput: WbsDetailsItem[] | null;
    WbsGISDetailsOutput: WbsGISDetailsItem[] | null;
    ProgressConsolidatedOutput: ProgressConsolidatedItem[] | null;
    DeliverableProgressDetailsEnginneerOutput: DeliverableProgressDetailsEngineerItem[] | null;
    ViewLastUpdateBQITFromTodateOutput: ViewLastUpdateBQITItem | null;
    ViewLastUpdateGISFromTodateOutput: ViewLastUpdateGISItem | null;
    GisPragatiItemOutput: GisPragatiItem[] | null;
}

export interface WbsResponseData {
    StatusCode: number;
    Message: string;
    Data?: WbsJobResponseData;
}

export interface WbsPendingApprovalModal {
    JOB: string;
    WBS: string;
    Task: string;
    TDate: string;
    UserID: number;
    UserName: string;
    Quantity: number;
    Manpower: number;
    Remarks: string;
    TaskType: string;
    Latitude: number;
    Longitude: number;
    TotalQuantity: number;
    ActualQuantity: number;
    WBS_Description: string;
    WBS_Custom_Description: string;
    Task_Description: string;
    Task_Custom_Description: string;
    Image_ID: string;
    Image_URL: string;
    UOM: string;
    NodeId: string;
    ReferanceNodeId: string;
    From_Length: number;
    Progress_Length: number;
    Total_Length: number;
    Distance_From_Center: number;
    Alignment: string;
    IS_Completed: string;
    Is_Hindrance: string;
};

export interface WBSPendingApprovalProps {
    Table: WbsPendingApprovalModal[];
};

export interface GisPragatiItem {
    ZONE: string;
    DMA: number;
    Sl_No: number;
    PRAGATI_WBS_Dia: number;
    Design_Dia: number;
    Material: string;
    Pipe_ID: string;
    Start_Node_ID: string;
    Stop_Node_ID: string;
    Design_Length: number;
    Deliverable_Code: number;
    Boq_Code: string;
}