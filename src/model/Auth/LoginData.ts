export interface LoginRequestData {
  UserName: string;
  Password: string;
  Token: string;
  CompanyCode: number;
  intAppCode: number;
  isipcheck: string;
  strDeviceUniqueCode: string;
}

export interface LoginResponseItem {
  Status: string;
  StatusCode: number;
  Message: string;
  UID: number;
  intAppCode: number;
  strDeviceUniqueCode: string;
  CompanyCode: number;
  PSNumber: string;
  UserName: string;
  Department: string;
  EMailid: string;
  MobileNo: string;
  ISDCode: number;
  RegTag: string;
  EmpDetails: any;
}

export type LoginResponseData = LoginResponseItem[];

