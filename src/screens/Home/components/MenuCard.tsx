import React from 'react';
import { View, Text, StyleSheet, Pressable, ScrollView, TouchableOpacity } from 'react-native';
import Colors from '../../../utils/Colors/Colors';
import { ms } from '../../../utils/Scale/Scaling';
import Icon from 'react-native-vector-icons/Ionicons';
import MapView from '../../../assets/svg/MapView.svg';
import MenuCardButton from './MenuCardButton';
import Strings from '../../../utils/Strings/Strings';

interface MenuCardProps {
    testID?: string;
    icon: React.ReactNode;
    label: string;
    onPress: () => void;
    badge?: string;
    rightIcon: boolean;
    isShowMap?: boolean;
}

const assetAllocation = ['Request Approval', 'Asset Status', 'Direct Allocation'];

const MenuCard: React.FC<MenuCardProps> = ({ testID, icon, label, onPress, badge, rightIcon, isShowMap }) => {
    const isPipeTracking = label === Strings.homeScreen.pipeTracking;
    const isAssetAllocation = label === Strings.homeScreen.assetAllocation;
    return (
        <Pressable
            testID={testID}
            style={styles.card} onPress={onPress}>
            <View style={styles.row}>
                {icon}
                <Text style={styles.label}>{label}</Text>
                {rightIcon && <Icon name='log-out-outline' size={24} color={Colors.red} style={styles.rightIcon} />}
            </View>

            {badge && (
                <View style={styles.badgeContainer}>
                    <Text style={styles.badgeText}>{badge}</Text>
                </View>
            )}

            {
                (isShowMap && isPipeTracking) && (
                    <>
                        {/* Map SVG view */}
                        <View style={styles.mapPlaceholder}>
                            <MapView width="100%" height="120%" />
                        </View>
                        {/* Action buttons */}
                        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.actionsRow}>
                            <TouchableOpacity style={[styles.actionButton, styles.padddingLeft]}>
                                <Text style={styles.actionText}>Unloading</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={styles.actionButton}>
                                <Text style={styles.actionText}>Shifting</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={styles.actionButton}>
                                <Text style={styles.actionText}>Physical Verification</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={[styles.actionButton, styles.paddingRight]}>
                                <Icon name="add" size={16} color={Colors.white} />
                            </TouchableOpacity>
                        </ScrollView>
                    </>
                )
            }
            {/* Asset Allocation */}
            {
                isAssetAllocation && (
                    <View style={styles.assetButtonsContainer}>
                        {
                            assetAllocation.map(item => <MenuCardButton
                                key={item}
                                title={item}
                                onPress={onPress}
                            />)
                        }
                    </View>
                )
            }
        </Pressable >
    );
};

const styles = StyleSheet.create({
    card: {
        backgroundColor: Colors.white,
        borderRadius: ms(8),
        marginHorizontal: ms(20),
        marginVertical: ms(6),
        paddingVertical: ms(16),
        elevation: 10,
        shadowColor: Colors.black,
        shadowOpacity: 0.05,
        shadowRadius: 8,
        shadowOffset: {
            width: 0,
            height: 2
        },
    },
    row: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: ms(16),
        width: '100%'
    },
    label: {
        fontFamily: 'MNMedium',
        fontSize: ms(14),
        color: Colors.textPrimary,
        marginLeft: ms(15),
    },
    badgeContainer: {
        backgroundColor: Colors.badgeContainerColor,
        borderRadius: ms(30),
        width: '45%',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: ms(5),
        marginLeft: ms(50),
        padding: ms(5)
    },
    badgeText: {
        fontFamily: 'MNSemiBold',
        fontSize: ms(11),
        color: Colors.textPrimary,
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: ms(4)
    },
    rightIcon: {
        marginLeft: 'auto'
    },
    mapPlaceholder: {
        width: '100%',
        height: ms(120),
        marginTop: ms(10),
        overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center',
    },
    actionsRow: {
        flexDirection: 'row',
        paddingTop: ms(12),
    },
    actionButton: {
        backgroundColor: Colors.blue,
        borderRadius: ms(20),
        paddingHorizontal: ms(10),
        paddingVertical: ms(8),
        marginHorizontal: ms(5),
        justifyContent: 'center',
        alignItems: 'center',
    },
    padddingLeft: {
        marginLeft: ms(15)
    },
    paddingRight: {
        marginRight: ms(15)
    },
    actionText: {
        color: Colors.white,
        fontFamily: 'MNMedium',
        fontSize: ms(11),
    },
    assetButtonsContainer: {
        marginTop: ms(12),
        marginHorizontal: ms(8),
    },
});

export default MenuCard; 