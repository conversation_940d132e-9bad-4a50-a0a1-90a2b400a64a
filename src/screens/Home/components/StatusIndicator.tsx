import React from 'react';
import { View, Text, StyleSheet, StyleProp, ViewStyle } from 'react-native';
import Colors from '../../../utils/Colors/Colors';
import { ms } from '../../../utils/Scale/Scaling';
import Icon from 'react-native-vector-icons/Ionicons';

interface StatusIndicatorProps {
    isOnline: boolean;
    style?: StyleProp<ViewStyle>;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({ isOnline, style }) => {
    const statusText = isOnline ? 'Online' : 'Offline';
    const dotColor = isOnline ? Colors.onlineGreen : Colors.offlineRed;
    const backgroundColor = isOnline ? Colors.onlineBackground : Colors.offlineBackground;

    return (
        <View style={[styles.container, { backgroundColor }, style]}>
            <Icon name='ellipse' size={10} color={dotColor} />
            <Text style={[styles.statusText, { color: dotColor }]}>{statusText}</Text>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: ms(8),
        paddingVertical: ms(2),
        paddingHorizontal: ms(10),
    },
    statusText: {
        fontSize: ms(11),
        fontFamily: 'MNMedium',
        marginLeft: ms(5)
    },
});

export default StatusIndicator; 