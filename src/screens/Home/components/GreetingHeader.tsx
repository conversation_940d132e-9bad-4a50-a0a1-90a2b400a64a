import React from 'react';
import { View, Text, StyleSheet, Pressable, Image } from 'react-native';
import { ms } from '../../../utils/Scale/Scaling';
import Colors from '../../../utils/Colors/Colors';
import NotificationBell from '../../../assets/svg/NotificationBell.svg';

const GreetingHeader = ({ name }: { name: string }) => (
    <View style={styles.header}>
        <Image source={require('../../../assets/png/greetings_bg.png')}
            resizeMode="cover"
            style={styles.bgImage} />
        <View style={styles.row}>
            <Text style={styles.greeting}>Hi {name}</Text>
            <View style={styles.headerIcons}>
                {/* <Pressable >
                    <StatusIndicator isOnline={false} />
                </Pressable> */}
                <Pressable>
                    <NotificationBell width={28} height={28} />
                </Pressable>
            </View>
        </View>
    </View>
);

const styles = StyleSheet.create({
    header: {
        width: '100%',
        height: '25%',
    },
    bgImage: {
        width: '100%',
        height: '100%',
        position: 'absolute'
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        zIndex: 1,
        marginVertical: ms(25),
        marginHorizontal: ms(20),
    },
    greeting: {
        color: Colors.white,
        fontFamily: 'MNSemiBold',
        fontSize: ms(24),
        maxWidth: ms(210),
    },
    icon: {
        marginLeft: ms(12),
    },
    headerIcons: {
        flexDirection: 'row',
    }
});

export default GreetingHeader; 