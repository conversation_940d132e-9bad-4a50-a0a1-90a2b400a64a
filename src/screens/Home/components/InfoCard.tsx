import React from 'react';
import { View, Text, StyleSheet, Pressable } from 'react-native';
import { ms } from '../../../utils/Scale/Scaling';
import Colors from '../../../utils/Colors/Colors';
import Strings from '../../../utils/Strings/Strings';
import CircularProgress from './CircularProgress';
import { Dispatch } from 'redux';
import { getUserRolesInfo } from '../../../utils/DataStorage/Storage';
import { NavigationProp } from '@react-navigation/native';
import ButtonComponent from '../../../components/ButtonComponent';

const roles = getUserRolesInfo();
const userRole: string | null = roles?.RolesList?.[0]?.Functional_ROLE ?? null;

interface ProgressItem {
    label: string;
    value: number;
}

interface InfoCardProps {
    mDispatch: Dispatch<any>;
    userId: string;
    location: string;
    progressItems: ProgressItem[];
    isWbsDownloaded: boolean;
    availableJobsCount: number;
    navigation: NavigationProp<any>;
}

const InfoCard: React.FC<InfoCardProps> = ({
    mDispatch,
    userId,
    location,
    progressItems,
    isWbsDownloaded,
    availableJobsCount,
    navigation
}) => (
    <View style={styles.card}>
        {
            (availableJobsCount >= 1 && !isWbsDownloaded) ? (
                <View style={styles.downloadWBS}>
                    <Text style={styles.title}>{Strings.homeScreen.downloadWBSMsg}</Text>
                        <ButtonComponent title={Strings.homeScreen.viewAndDownloadWBS} 
                        mainContainerStyle={styles.wbsBtn}
                        onPress={() => {
                            navigation.navigate('DownloadWBS')
                        }} />
                </View>
            ) : (
                <>
                    <View style={styles.details}>
                        <Text style={styles.title}>{userId} - {location}</Text>
                        {userRole === 'Site Engineer' && (<Pressable style={styles.rank}>
                            <Text style={styles.rankText}>R - 10</Text>
                        </Pressable>
                        )}
                    </View>

                    <View style={styles.progressRow}>
                        {progressItems.map((item, index) => (
                            <CircularProgress
                                label={item.label}
                                fill={item.value}
                                key={item.label}
                            />
                        ))}
                    </View>
                    <Pressable>
                        <Text style={styles.link}>{Strings.homeScreen.viewMore}</Text>
                    </Pressable>
                </>
            )
        }
    </View >
);

const styles = StyleSheet.create({
    card: {
        width: '92%',
        height: ms(180),
        backgroundColor: Colors.white,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: ms(12),
        elevation: 10,
        position: 'absolute',
        zIndex: 1,
        top: '13%',
    },
    downloadWBS: {
        width: "100%",
        alignItems: 'center',
    },
    wbsBtn: {
        width: '100%',
        alignItems: 'center',
        marginTop: ms(20),
        elevation: 0,
        backgroundColor: 'transparent',
        borderTopWidth: 0
    },
    details: {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    title: {
        fontFamily: 'MNMedium',
        fontSize: ms(15),
        color: Colors.textPrimary,
    },
    rank: {
        backgroundColor: Colors.secondary,
        borderRadius: 4,
    },
    rankText: {
        fontFamily: 'MNMedium',
        fontSize: ms(14),
        color: Colors.white,
        paddingVertical: ms(3),
        paddingHorizontal: ms(10)
    },
    progressRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        marginVertical: ms(10),
    },
    progressItem: {
        alignItems: 'center',
        flex: 1,
    },
    link: {
        color: Colors.blue,
        fontFamily: 'MNMedium',
        fontSize: ms(12),
        textAlign: 'center',
        textDecorationLine: 'underline',
    },
});

export default InfoCard; 