import React from 'react';
import { View, Text, Image, StyleSheet, Dimensions } from 'react-native';
import { ms } from '../../../utils/Scale/Scaling';
import Colors from '../../../utils/Colors/Colors';
import Strings from '../../../utils/Strings/Strings';
import StatusIndicator from './StatusIndicator';

const screenWidth = Dimensions.get('window').width;

interface UserCardProps {
    name: string;
    userId: string;
    location: string;
    profileImage: string;
}

const UserCard: React.FC<UserCardProps> = ({ name, userId, location, profileImage }) => (

    <View style={styles.bgWrapper}>
        <Image source={require('../../../assets/png/greetings_bg.png')}
            resizeMode="cover"
            style={styles.bgImage} />
        <View style={styles.container}>
            <View>
                <Text style={styles.greeting}>{Strings.homeScreen.hello} {name}</Text>
                <Text style={styles.subText}>{userId} - {location}</Text>
            </View>
            <View style={styles.rightContent}>
                <Image source={{ uri: profileImage }} style={styles.avatar} />
            </View>
            <StatusIndicator isOnline={true} style={styles.statusIndicator} />
        </View>
    </View>
);

const styles = StyleSheet.create({
    bgWrapper: {
        borderRadius: ms(8),
        overflow: 'hidden',
        marginHorizontal: ms(20),
        marginTop: ms(30),
        marginBottom: ms(15),
        height: ms(95),
        width: screenWidth - ms(40),
        position: 'relative',
    },
    bgImage: {
        width: '100%',
        height: '100%',
        position: 'absolute'
    },
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: ms(15),
        height: '100%',
    },
    greeting: {
        color: Colors.white,
        fontFamily: 'MNBold',
        fontSize: ms(18),
    },
    subText: {
        color: Colors.white,
        fontFamily: 'MNRegular',
        fontSize: ms(13),
        marginTop: ms(4),
        opacity: 0.7,
    },
    rightContent: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: ms(-10)
    },
    statusIndicator: {
        position: 'absolute',
        right: ms(14),
        top: ms(65),
        zIndex: 1,
    },
    avatar: {
        width: ms(50),
        height: ms(50),
        borderRadius: ms(28),
        bottom: ms(8),
        marginRight: ms(6)
    },
});

export default UserCard; 