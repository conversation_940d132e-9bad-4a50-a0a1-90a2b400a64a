import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import Colors from '../../../utils/Colors/Colors';
import { ms } from '../../../utils/Scale/Scaling';

interface MenuCardButtonProps {
    title: string;
    onPress?: () => void;
}

const MenuCardButton: React.FC<MenuCardButtonProps> = ({ title, onPress }) => (
    <TouchableOpacity style={styles.button} onPress={onPress}>
        <Text style={styles.text}>{title}</Text>
    </TouchableOpacity>
);

const styles = StyleSheet.create({
    button: {
        width: "83%",
        alignSelf: 'flex-end',
        borderWidth: ms(1),
        borderColor: Colors.blue,
        borderRadius: ms(4),
        paddingVertical: ms(10),
        alignItems: 'center',
        marginVertical: ms(6),
        marginRight: ms(15),
        backgroundColor: Colors.white,
    },
    text: {
        color: Colors.blue,
        fontFamily: 'MNSemiBold',
        fontSize: ms(14),
    },
});

export default MenuCardButton; 