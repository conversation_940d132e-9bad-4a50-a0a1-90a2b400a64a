import React from 'react';
import { Text, StyleSheet, View } from 'react-native';
import { AnimatedCircularProgress } from 'react-native-circular-progress';
import Colors from '../../../utils/Colors/Colors';
import { ms } from '../../../utils/Scale/Scaling';

interface CircularProgressProps {
    size?: number;
    width?: number;
    fill: number;
    label: string;
}

const CircularProgress: React.FC<CircularProgressProps> = ({
    size = 63,
    width = 4,
    fill,
    label
}) => (
    <View style={styles.progressItem}>
        <Text style={styles.progressLabel}>{label}</Text>
        <AnimatedCircularProgress
            size={size}
            width={width}
            fill={fill}
            tintColor={fill <= 25 ? Colors.blue : (fill <= 50 ? Colors.pipeIdTextBlack : Colors.secondary)}
            backgroundColor={Colors.progressAsh}
        >
            {(fill: number) => (
                <Text style={[styles.progressValue, { color: fill <= 25 ? Colors.blue : (fill <= 50 ? Colors.pipeIdTextBlack : Colors.secondary) }]}>
                    {fill.toFixed(0)}%
                </Text>
            )}
        </AnimatedCircularProgress>
    </View>
);

const styles = StyleSheet.create({
    progressItem: {
        alignItems: 'center',
        flex: 1,
    },
    progressLabel: {
        color: Colors.textInputBlack,
        fontSize: ms(12),
        marginBottom: ms(5),
    },
    progressValue: {
        fontSize: ms(12),
        fontWeight: 'bold',
    },
});

export default CircularProgress;