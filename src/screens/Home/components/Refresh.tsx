import React, { useState } from 'react';
import { View, Text, StyleSheet, Pressable, StyleProp, ViewStyle } from 'react-native';
import Colors from '../../../utils/Colors/Colors';
import { ms } from '../../../utils/Scale/Scaling';
import Icon from '../../../assets/svg/Refresh.svg';
// import { ProgressView } from "@react-native-community/progress-view";
import Close from '../../../assets/svg/Close.svg';
import Strings from '../../../utils/Strings/Strings';

interface RefreshProps {
    lastUpdated: string;
    progressVal?: number;
    customStyle?: StyleProp<ViewStyle>;
}

const Refresh: React.FC<RefreshProps> = ({ lastUpdated, progressVal = 0.7, customStyle }) => {
    const [showProgress, setShowProgress] = useState(false);

    const handleRefreshPress = () => {
        setShowProgress(true);
    };

    const handleClosePress = () => {
        setShowProgress(false);
    };

    return (
        <View style={[styles.container, customStyle]}>
            {!showProgress ? (
                // Initial state: Refresh button with last update time
                <Pressable style={styles.refreshButton} onPress={handleRefreshPress}>
                    <View style={styles.innerContainer}>
                        <Icon width={24} height={24} />
                        <Text style={styles.titleText}>{Strings.homeScreen.refresh}</Text>
                    </View>
                    <View style={styles.innerContainer}>
                        <Text style={styles.updateText}>{Strings.homeScreen.lastUpdated}{lastUpdated}</Text>
                    </View>
                </Pressable>
            ) : (
                // Progress state: Progress bar with close button
                <View style={styles.progressContainer}>
                    {/* <ProgressView
                        progressTintColor={Colors.blue}
                        trackTintColor={Colors.progressAsh}
                        progress={progressVal}
                        progressViewStyle={'bar'}
                        style={styles.progress}
                    /> */}
                    <Pressable onPress={handleClosePress}>
                        <Close width={22} height={22} />
                    </Pressable>
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.white,
        borderRadius: ms(10),
        margin: ms(10),
        paddingVertical: ms(10),
        paddingHorizontal: ms(16),
        elevation: 10,
        shadowColor: Colors.black,
        shadowOpacity: 0.05,
        shadowRadius: 8,
        shadowOffset: {
            width: 0,
            height: 2
        },
    },
    refreshButton: {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    innerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    titleText: {
        fontFamily: 'MNMedium',
        fontSize: ms(14),
        color: Colors.textPrimary,
        marginLeft: ms(15),
    },
    updateText: {
        fontFamily: 'MNMedium',
        fontSize: ms(10),
        color: Colors.textInputBlack,
    },
    progressContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
    },
    progress: {
        width: '80%'
    }
});

export default Refresh;