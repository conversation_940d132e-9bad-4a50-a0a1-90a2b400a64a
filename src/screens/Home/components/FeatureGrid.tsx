import React from 'react';
import { View, StyleSheet, Pressable, Text } from 'react-native';
import { ms } from '../../../utils/Scale/Scaling';
import Colors from '../../../utils/Colors/Colors';
import Icon from 'react-native-vector-icons/Ionicons';

interface FeatureeGridProps {
    testID?: string
    icon: React.ReactNode;
    rightIcon: boolean;
    label: string;
    onPress: () => void;
}

const FeatureGrid: React.FC<FeatureeGridProps> = ({ testID, icon, rightIcon, label, onPress }) => (
    <Pressable
        testID={testID}
        style={styles.card} onPress={onPress}>
        <View style={styles.icon}>
            {icon}
            {rightIcon && <Icon name='log-out-outline' size={24} color={Colors.red} />}
        </View>
        <Text style={styles.label}>{label}</Text>
    </Pressable>
);

const styles = StyleSheet.create({
    card: {
        width: '46%',
        backgroundColor: Colors.white,
        borderRadius: 10,
        padding: ms(14),
        marginVertical: ms(8),
        marginHorizontal: '2%',
        elevation: 7,
        shadowColor: Colors.black,
        shadowOpacity: 0.05,
        shadowRadius: 8,
        shadowOffset: { width: 0, height: 2 },
    },
    icon: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%'
    },
    label: {
        fontFamily: 'MNMedium',
        fontSize: ms(14),
        color: Colors.textPrimary,
        marginTop: ms(10)
    },
});

export default FeatureGrid; 