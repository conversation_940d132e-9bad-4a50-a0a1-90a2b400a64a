import React, { useCallback, useEffect, useState } from 'react';
import { View, ScrollView, StyleSheet, Modal, ActivityIndicator, Pressable, Alert } from 'react-native';
import Colors from '../../utils/Colors/Colors';
import { ms } from '../../utils/Scale/Scaling';
import { CommonActions, NavigationProp, useNavigation } from '@react-navigation/native';
import Logout from '../../components/LogOut';
import { clearAllData, getUserInfo, getUserRolesInfo } from '../../utils/DataStorage/Storage';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../redux/Root/rootStore';
import { USER_FEATURES } from '../../utils/Constants/UserFeature/userFeature';
import Strings from '../../utils/Strings/Strings';
import { logoutRequest } from '../../redux/AuthRedux/Logout/LogoutAction';
import FeatureGrid from './components/FeatureGrid';
import GreetingHeader from './components/GreetingHeader';
import InfoCard from './components/InfoCard';
import MenuCard from './components/MenuCard';
import UserCard from './components/UserCard';
import { getSelectedJobs } from '../../utils/Storage/Storage';
import { setJobsDownloaded, setSelectedCurrentJobId } from '../../redux/HomeRedux/HomeActions';
import { useTranslation } from 'react-i18next';

const HomeScreen = () => {
  const navigation = useNavigation<NavigationProp<any>>();
  const dispatch = useDispatch();
  const {t} = useTranslation();
  const [isLogoutClicked, setisLogoutClicked] = useState<boolean>(false);
  const [isShowLoader, setIsShowLoader] = useState<boolean>(false);
  const { isJobsDownloaded, currentJobId } = useSelector((state: RootState) => state.home);
  const user = getUserInfo();
  const roles = getUserRolesInfo();
  const userRole: string | null = roles?.RolesList?.[0]?.Functional_ROLE ?? null;

  const features = (userRole ? (USER_FEATURES as any)[userRole] : null) ?? [];

  useEffect(() => {
    const selectedJobs = getSelectedJobs();
    const hasSelectedJobs = selectedJobs && selectedJobs.length > 0;
    dispatch(setJobsDownloaded(hasSelectedJobs));
    dispatch(setSelectedCurrentJobId(selectedJobs[0]?.id));
  }, [])


  const callLogoutFunction = () => {
    clearAllData();
    dispatch(logoutRequest());
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      })
    );
    setIsShowLoader(false);
    setisLogoutClicked(false);
  };

  const menuNavigation = useCallback((screen: string) => () => {
    switch (screen) {
      case Strings.homeScreen.downloadWBS:
        navigation.navigate('DownloadWBS');
        break;

      case Strings.homeScreen.progressUpdate:
        if(userRole == "Site Engineer") {
          navigation.navigate('DailyProgressView');
        }else{
          navigation.navigate('DailyProgressApprove');
        }
        break;

      case Strings.homeScreen.syncData:
        navigation.navigate('SyncData');
        break;
      
      case Strings.homeScreen.hindrance:
        navigation.navigate('HindranceMapView');
        break;

      case Strings.homeScreen.userManual:
        navigation.navigate('UserMannualScreen');
        break;
      // case Strings.homeScreen.pipeStockYard:
      //   navigation.navigate('DailyProgressApprove');
      //   break;
      default:
        break;

    }

  }, []);

  return (
    <View style={styles.container}>
      {
        userRole == "Approver" || userRole == "Site Engineer" || userRole == "Master Admin" || userRole == "Project Manager" ? (
          <>
            {user && <GreetingHeader name={user.UserName} />}
            <InfoCard
              mDispatch={dispatch}
              userId="LE1f0880"
              location="Pune ESR and GSR"
              progressItems={[
                { label: 'Pipe Laying', value: 80 },
                { label: 'Concrete', value: 20 },
                { label: 'Productivity', value: 50 }
              ]}
              isWbsDownloaded={isJobsDownloaded}
              availableJobsCount={roles?.RolesList.length ?? 0}
              navigation={navigation}
            />
            <View style={styles.assetButtonsContainer} >
              <ScrollView style={styles.innerContainer}>
                <View style={styles.featureContainer}>
                  {/* <Refresh lastUpdated="Today: 10:00 AM" /> */}
                  {features.map((feature, index) => {
                    const IconComponent = feature.icon;
                    return (
                      <FeatureGrid
                        testID={`feature-${feature.label}`}
                        icon={<IconComponent width={24} height={24} />}
                        rightIcon={feature.label == "Profile"}
                        label={feature.label}
                        onPress={() => {
                          if(feature.label === Strings.homeScreen.hindrance && getSelectedJobs()[0]?.id ) {
                            menuNavigation(feature.label)();
                            return
                          } else if(feature.label !== Strings.homeScreen.hindrance) {
                            feature.label == "Profile" ? setisLogoutClicked(true) : menuNavigation(feature.label)();
                          } else {
                            Alert.alert('Please download and set the current.');
                          }
                        }
                        }
                        key={feature.label}
                      />
                    )
                  })}
                  {/*  */}
                </View>
              </ScrollView>
              {(isShowLoader) && (
                <ActivityIndicator style={styles.activityIndicatorStyle} size="large" color={Colors.primary}
                  testID="activity-indicator" />
              )}
            </View>
          </>
        ) : (
          <>
            <UserCard
              name="Vignesh"
              userId="LE1f0880"
              location="Pune ESR and GSR"
              profileImage="https://randomuser.me/api/portraits/men/1.jpg"
            />
            <ScrollView>
              {features.map((feature, index) => {
                const IconComponent = feature.icon;
                const MapComponent = feature.mapComponent;
                const isShowMapView = userRole === 'Store Incharge'
                return (
                  <MenuCard
                    testID={`menu-${feature.label}`}
                    key={feature.label}
                    icon={<IconComponent width={24} height={24} />}
                    label={feature.label}
                    badge={feature.badge}
                    isShowMap={isShowMapView}
                    map={feature.map}
                    mapComponent={MapComponent ? <MapComponent width="100%" height="120%" /> : undefined}
                    assetButtons={feature.assetButtons}
                    rightIcon={feature.label == "Profile"}
                    onPress={() => feature.label == "Profile" && setisLogoutClicked(true)}
                  />
                );
              })}
            </ScrollView>
          </>
        )
      }
      <Logout
        visible={isLogoutClicked}
        onClose={() => setisLogoutClicked(false)}
        testIdConfirm="logout-confirm"
        testIdCancel="logout-cancel"
        onCancel={() => setisLogoutClicked(false)}
        onConfirm={() => {
          setIsShowLoader(true);
          callLogoutFunction();
        }}
      />
    </View>

  )
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flex: 1,
    backgroundColor: Colors.containerligetBlue
  },
  assetButtonsContainer: {
    borderTopLeftRadius: ms(20),
    borderTopRightRadius: ms(20),
    marginTop: -ms(20),
    backgroundColor: Colors.containerligetBlue,
    width: '100%',
    flex: 1,
    overflow: 'hidden',
  },
  activityIndicatorStyle: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center'
  },
  innerContainer: {
    backgroundColor: Colors.containerligetBlue,
    borderTopLeftRadius: ms(20),
    borderTopRightRadius: ms(20),
    marginTop: ms(40),
  },
  featureContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: ms(80),
    marginBottom: ms(20),
    marginHorizontal: ms(10),
  },
  refreshButton: {
    marginHorizontal: ms(20)
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: Colors.shadow,
    justifyContent: 'flex-end',
  },
});

export default HomeScreen;
