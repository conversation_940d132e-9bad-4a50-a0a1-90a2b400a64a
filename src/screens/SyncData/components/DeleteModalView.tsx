import * as React from 'react';
import BottomPopup from '../../../components/BottomPopup';
import { Alert, Pressable, Text, View } from 'react-native';
import { ms } from '../../../utils/Scale/Scaling';
import Colors from '../../../utils/Colors/Colors';
import { AppFonts } from '../../../components/Fonts';
import { useTranslation } from 'react-i18next';
import ButtonComponent from '../../../components/ButtonComponent';
import { SVG } from '../../../utils/ImagePath';

interface DeleteModalProps {
    visible: boolean; 
    content: string;
    onCancelPress: () => void;
    onChange: (data?: any) => void;
}

const DeleteModal = ({
    visible, 
    content,
    onCancelPress, 
    onChange
}: DeleteModalProps) => {
    const {t} = useTranslation();

    return (
        <BottomPopup visible={visible} onCancelPress={onCancelPress}>
                   <View style={{
                    alignItems: 'center',
                    justifyContent: 'center'
                   }}>
                   <SVG.DeleteCross width={ms(100)} height={ms(100)} style={{marginVertical: ms(20)}} />
                    <View style={{
                        paddingBottom: ms(30),
                        paddingHorizontal: ms(25)
                    }}>
                        <Text style={{
                            fontFamily: AppFonts.Bold,
                            fontSize: ms(18),
                            color: Colors.black,
                            textAlign: 'center'
                        }}>{`${t('commonStrings.WBSDeleteConfirmMsg')} ${content}?`}</Text>
                    </View>
                   </View>
                    <ButtonComponent 
                    showSecondaryButton
                    secondaryButtonTitle={t('commonStrings.no')}
                    onSecondaryPress={onCancelPress}
                    mainContainerStyle={{
                        paddingBottom: ms(30)
                    }}
                    title={t('commonStrings.yes')} 
                    onPress={onChange} />
                </BottomPopup>
    )
};
export default DeleteModal;