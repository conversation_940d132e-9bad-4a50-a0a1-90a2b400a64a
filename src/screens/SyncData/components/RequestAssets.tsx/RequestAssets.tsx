import * as React from 'react';
import { View, Text, Pressable, FlatList, ListRenderItemInfo, SafeAreaView, StyleSheet } from 'react-native';
import { AssetsDetails } from '../../../../utils/Constants/SyncDataConstants';
import CardView from '../../../../components/CardView';
import { AssetDetailsProps } from '../../../../utils/types/SyncData';
import { ms } from '../../../../utils/Scale/Scaling';
import { AppFonts } from '../../../../components/Fonts';
import Colors from '../../../../utils/Colors/Colors';
import { SVG } from '../../../../utils/ImagePath';
import { Icon } from '../../../../components/Icons';
import AppHeader from '../../../../components/AppHeader';
import { useTranslation } from 'react-i18next';
import { FormatDate } from '../../../../utils/helpers';
import ButtonComponent from '../../../../components/ButtonComponent';
import BottomPopup from '../../../../components/BottomPopup';
import EditAssetModal from './EditAssetsModal/EditAssetsModalView';
import DeleteModal from '../DeleteModalView';

const RequestAssetsScreen = () => {
    const { t } = useTranslation();
    const [showEdit, setShowEdit] = React.useState(false);
    const [assetRequirement, setAssetRequirement] = React.useState(0);
    const [deleteModal, setDeleteModal] = React.useState(false);
    const [deleteContent, setDeleteContent] = React.useState('');
    
    const renderAssetsDetails = React.useCallback(() => ({ item, index }: ListRenderItemInfo<AssetDetailsProps>) => {
        return (
            <>
                {/* Card view */}
                <CardView containerStyle={{
                    marginBottom: index === 0 ? ms(7.5) : 0,
                    marginTop: index === 0 ? ms(15) : ms(7.5),
                    borderWidth:ms(0.8)
                }}>
                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center'
                    }}>
                        <Text style={{
                            fontFamily: AppFonts.SemiBold,
                            fontSize: ms(16),
                            color: Colors.secondary,
                            flex: 1
                        }}>{item.name}</Text>
                        <Pressable style={{
                            backgroundColor: Colors.bgSecondaryLightBlue,
                            paddingVertical: ms(3),
                            paddingHorizontal: ms(3),
                            borderRadius: ms(5),
                            marginHorizontal: ms(5)
                        }}
                            onPress={() => setShowEdit(true)}>
                            <SVG.EditPen width={ms(19)} height={ms(19)} />
                        </Pressable>

                        <Pressable style={{
                            backgroundColor: Colors.bgLightRed,
                            paddingVertical: ms(2),
                            paddingHorizontal: ms(2),
                            borderRadius: ms(5),
                            marginHorizontal: ms(5)
                        }} onPress={() => {
                            setDeleteModal(true);
                            setDeleteContent(item.name)
                            }}>
                            <Icon type={'Ionicons'} name={'trash-outline'} size={ms(17)} color={Colors.red} style={{ opacity: 0.6 }} />
                        </Pressable>
                    </View>

                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center'
                    }}>
                        <Text style={{
                            flex: 0.5,
                            fontFamily: AppFonts.Medium,
                            fontSize: ms(12),
                            color: Colors.textInputBlack,
                            marginRight: ms(20)
                        }}>{`${t('SyncData.fromdate')} & ${t('SyncData.time')}`}</Text>

                        <Text style={{
                            flex: 1,
                            fontFamily: AppFonts.SemiBold,
                            fontSize: ms(13),
                            color: Colors.black
                        }}>{`${FormatDate(item.fromDate).shortDate} (${FormatDate(item.fromDate).time})`}</Text>
                    </View>

                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center'
                    }}>
                        <Text style={{
                            flex: 0.5,
                            fontFamily: AppFonts.Medium,
                            fontSize: ms(12),
                            color: Colors.textInputBlack,
                            marginRight: ms(20)
                        }}>{`${t('SyncData.todate')} & ${t('SyncData.time')}`}</Text>

                        <Text style={{
                            flex: 1,
                            fontFamily: AppFonts.SemiBold,
                            fontSize: ms(13),
                            color: Colors.black
                        }}>{`${FormatDate(item.toDate).shortDate} (${FormatDate(item.toDate).time})`}</Text>
                    </View>
                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center'
                    }}>
                        <Text style={{
                            flex: 0.5,
                            fontFamily: AppFonts.Medium,
                            fontSize: ms(12),
                            color: Colors.textInputBlack,
                            marginRight: ms(20)
                        }}>{`${t('SyncData.usage')}`}</Text>

                        <Text style={{
                            flex: 1,
                            fontFamily: AppFonts.SemiBold,
                            fontSize: ms(13),
                            color: Colors.black
                        }}>{`${item.usage}`}</Text>
                    </View>

                    {/* Reason */}
                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center'
                    }}>
                        <Text style={{
                            flex: 0.5,
                            fontFamily: AppFonts.Medium,
                            fontSize: ms(12),
                            color: Colors.textInputBlack,
                            marginRight: ms(20)
                        }}>{`${t('SyncData.reason')}`}</Text>

                        <Text style={{
                            flex: 1,
                            fontFamily: AppFonts.SemiBold,
                            fontSize: ms(13),
                            color: Colors.black
                        }}>{`${item.reason}`}</Text>
                    </View>
                </CardView>
            </>
        )
    }, []);
    return (
        <SafeAreaView style={styles.container}>
            <AppHeader
                title={t('SyncData.requestnewasset')}
            />
            <View style={{
                flex: 1,
                padding: ms(15),
            }}>
                <FlatList
                    nestedScrollEnabled
                    showsVerticalScrollIndicator={false}
                    data={AssetsDetails}
                    renderItem={renderAssetsDetails()}
                    keyExtractor={(item) => `${item.id}`}
                />
                <EditAssetModal
                visible={showEdit}
                onChange={(data: any) => setAssetRequirement(data)}
                onCancelPress={() => setShowEdit(false)}
                 />
                 <DeleteModal 
                 visible={deleteModal}
                 content={deleteContent}
                 onChange={(data: any) => setDeleteModal(data)}
                 onCancelPress={() => setDeleteModal(false)}
                 />
            </View>
            <ButtonComponent
                title={t('SyncData.sync')}
                onPress={() => console.log('update')} />
        </SafeAreaView>

    )
};
export default RequestAssetsScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.containerligetBlue,
    },
    tabContainer: {
        alignItems: 'center',
    },
});