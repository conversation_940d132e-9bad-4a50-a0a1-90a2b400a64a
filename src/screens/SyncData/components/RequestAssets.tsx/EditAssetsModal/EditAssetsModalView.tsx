import * as React from 'react';
import BottomPopup from '../../../../../components/BottomPopup';
import { Alert, Pressable, Text, View } from 'react-native';
import { Icon } from '../../../../../components/Icons';
import { ms } from '../../../../../utils/Scale/Scaling';
import Colors from '../../../../../utils/Colors/Colors';
import { AppFonts } from '../../../../../components/Fonts';
import { useTranslation } from 'react-i18next';
import ButtonComponent from '../../../../../components/ButtonComponent';
import { useFocusEffect } from '@react-navigation/native';
import CustomDropdownPicker from '../../../../../components/DropDownPicker';
import { constructionAssets } from '../../../../../utils/Constants/SyncDataConstants';
import BottomCalendarModal from '../../../../../components/CalendarPicker/BottomPopupCalendar';

interface EditAssetModalProps {
    visible: boolean; 
    onCancelPress: () => void;
    onChange: (data: any) => void;
}

const EditAssetModal = ({
    visible, 
    onCancelPress, 
    onChange
}: EditAssetModalProps) => {
    const {t} = useTranslation();
    const [requiredQuantity, setRequiredQunatity] = React.useState(0);
    const [formatDropDownData, setFormatDropdownData] = React.useState<{ label: string; value: string }[]>([]);
    const [assetName, setAssetName] = React.useState('');
    const [showCalendar, setShowCalendar] = React.useState(false);

    useFocusEffect(
        React.useCallback(() => {
            onChange(requiredQuantity);
        }, [requiredQuantity])
    );

    React.useEffect( () => {
        const dropdownFormatted = constructionAssets.map(asset => ({
            label: asset.name,
            value: asset.name,
          }));
        
          setFormatDropdownData(dropdownFormatted);
    }, []);

    const incrementQunatity = React.useCallback(() => {
        setRequiredQunatity(prev => prev +1);
    }, []);

    const decrementQunatity = React.useCallback(() => {
        setRequiredQunatity(prev => prev === 0 ? prev : prev - 1);
    }, []);

    return (
        <BottomPopup visible={visible} onCancelPress={onCancelPress}>
                    <View style={{
                        paddingHorizontal: ms(25),
                        paddingVertical: ms(15)
                    }}>
                        <View style={{
                            paddingVertical: ms(20),
                            borderBottomColor: Colors.progressAsh,
                            borderBottomWidth: ms(0.5)
                        }}>
                            <Text style={{
                                fontFamily: AppFonts.Bold,
                                fontSize: ms(16),
                                color: Colors.black
                            }}>{t('SyncData.editasset')}</Text>
                        </View>
                        <CustomDropdownPicker 
                        title={'SyncData.asset'}
                        defaultValue={assetName}
                        data={formatDropDownData} 
                        onSelect={(data: any) => setAssetName(data)}
                        />

                        {/* from date and time */}
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center'
                        }}>
                            <View style={{
                                marginVertical: ms(5),
                                flex: 1,
                                marginRight: ms(10)
                            }}>
                                <Text style={{
                                    fontFamily: AppFonts.Medium,
                                    fontSize: ms(13),
                                    color: Colors.textGrey,
                                    marginBottom: ms(5)
                                }}>{`${t('SyncData.fromdate')}`}</Text>
                                <Pressable style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    borderWidth: ms(0.9),
                                    borderColor: Colors.inputBorder,
                                    borderRadius: ms(5),
                                    backgroundColor: Colors.textInputLightBlueBg,
                                    height: ms(40),
                                    paddingHorizontal: ms(10)
                                }} onPress={() => setShowCalendar(true)}>
                                    <View style={{ flex: 1 }}>
                                        <Text style={{
                                            fontFamily: AppFonts.Medium,
                                            fontSize: ms(13),
                                            color: Colors.primary,
                                            marginBottom: ms(5),
                                            opacity: 0.4
                                        }}>{'Select'}</Text>
                                    </View>
                                    <View>
                                        <Icon type={'Ionicons'} name={'calendar-outline'} size={ms(18)} />
                                    </View>
                                </Pressable>
                            </View>

                            <View style={{
                                marginVertical: ms(5),
                                flex: 1,
                                marginLeft: ms(10)
                            }}>
                                <Text style={{
                                    fontFamily: AppFonts.Medium,
                                    fontSize: ms(13),
                                    color: Colors.textGrey,
                                    marginBottom: ms(5)
                                }}>{`${t('SyncData.time')}`}</Text>
                                <Pressable style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    borderWidth: ms(0.9),
                                    borderColor: Colors.inputBorder,
                                    borderRadius: ms(5),
                                    backgroundColor: Colors.textInputLightBlueBg,
                                    height: ms(40),
                                    paddingHorizontal: ms(10)
                                }}>
                                    <View style={{ flex: 1 }}>
                                        <Text style={{
                                            fontFamily: AppFonts.Medium,
                                            fontSize: ms(13),
                                            color: Colors.primary,
                                            marginBottom: ms(5),
                                            opacity: 0.4
                                        }}>{'Select'}</Text>
                                    </View>
                                    <View>
                                        <Icon type={'Ionicons'} name={'time-outline'} size={ms(20)} />
                                    </View>
                                </Pressable>
                            </View>
                        </View>

                        {/* Todate and time */}
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center'
                        }}>
                            <View style={{
                                marginVertical: ms(5),
                                flex: 1,
                                marginRight: ms(10)
                            }}>
                                <Text style={{
                                    fontFamily: AppFonts.Medium,
                                    fontSize: ms(13),
                                    color: Colors.textGrey,
                                    marginBottom: ms(5)
                                }}>{`${t('SyncData.todate')}`}</Text>
                                <Pressable style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    borderWidth: ms(0.9),
                                    borderColor: Colors.inputBorder,
                                    borderRadius: ms(5),
                                    backgroundColor: Colors.textInputLightBlueBg,
                                    height: ms(40),
                                    paddingHorizontal: ms(10)
                                }}
                                onPress={() => setShowCalendar(true)}>
                                    <View style={{ flex: 1 }}>
                                        <Text style={{
                                            fontFamily: AppFonts.Medium,
                                            fontSize: ms(13),
                                            color: Colors.primary,
                                            marginBottom: ms(5),
                                            opacity: 0.4
                                        }}>{'Select'}</Text>
                                    </View>
                                    <View>
                                        <Icon type={'Ionicons'} name={'calendar-outline'} size={ms(18)} />
                                    </View>
                                </Pressable>
                            </View>

                            <View style={{
                                marginVertical: ms(5),
                                flex: 1,
                                marginLeft: ms(10)
                            }}>
                                <Text style={{
                                    fontFamily: AppFonts.Medium,
                                    fontSize: ms(13),
                                    color: Colors.textGrey,
                                    marginBottom: ms(5)
                                }}>{`${t('SyncData.time')}`}</Text>
                                <Pressable style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    borderWidth: ms(0.9),
                                    borderColor: Colors.inputBorder,
                                    borderRadius: ms(5),
                                    backgroundColor: Colors.textInputLightBlueBg,
                                    height: ms(40),
                                    paddingHorizontal: ms(10)
                                }}>
                                    <View style={{ flex: 1 }}>
                                        <Text style={{
                                            fontFamily: AppFonts.Medium,
                                            fontSize: ms(13),
                                            color: Colors.primary,
                                            marginBottom: ms(5),
                                            opacity: 0.4
                                        }}>{'Select'}</Text>
                                    </View>
                                    <View>
                                        <Icon type={'Ionicons'} name={'time-outline'} size={ms(20)} />
                                    </View>
                                </Pressable>
                            </View>
                        </View>

                        <View style={{ 
                            marginVertical: ms(5), 
                            marginBottom: ms(35)
                         }}>
                            <Text style={{
                                fontFamily: AppFonts.Medium,
                                fontSize: ms(13),
                                color: Colors.textGrey,
                                marginBottom: ms(5)
                            }}>{`${t('SyncData.requiredquantity')}`}</Text>
                            <Pressable style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                borderWidth: ms(0.9),
                                borderColor: Colors.inputBorder,
                                borderRadius: ms(8),
                                backgroundColor: Colors.textInputLightBlueBg,
                                height: ms(40),
                                paddingHorizontal: ms(10)
                            }}>
                                <View style={{ flex: 1 }}>
                                    <Text style={{
                                        fontFamily: AppFonts.Medium,
                                        fontSize: ms(13),
                                        color: Colors.black,
                                        marginBottom: ms(5),
                                    }}>{requiredQuantity}</Text>
                                </View>

                                <Pressable style={{
                                    backgroundColor: Colors.iconBg,
                                    paddingVertical: ms(5),
                                    paddingHorizontal: ms(5),
                                    borderRadius: ms(5),
                                    marginRight: ms(3)
                                }}
                                onPress={() => decrementQunatity()}>
                                    <Icon type={'Ionicons'} color={Colors.textGrey} name={'remove-outline'} size={ms(18)} />
                                </Pressable>

                                <Pressable style={{
                                    backgroundColor: Colors.iconBg,
                                    paddingVertical: ms(5),
                                    paddingHorizontal: ms(5),
                                    borderRadius: ms(5),
                                    marginLeft: ms(3)
                                }} onPress={() => incrementQunatity()}>
                                    <Icon type={'Ionicons'} color={Colors.textGrey} name={'add-outline'} size={ms(18)} />
                                </Pressable>
                            </Pressable>
                        </View>
                    </View>
                    <ButtonComponent 
                    showSecondaryButton
                    secondaryButtonTitle={t('commonStrings.cancel')}
                    mainContainerStyle={{
                        paddingBottom: ms(30)
                    }}
                    title={t('SyncData.update')} 
                    onPress={() => console.log('update')} />

                <BottomCalendarModal 
                        visible={showCalendar}
                        fromDateTitle={'SyncData.fromdate'}
                        initialDate={new Date()}
                        onApply={(from: string, to: string) => {
                            console.log('from-----', from);
                            console.log('to------', to);
                        }}
                        onClose={() => setShowCalendar(false)}
                    />
                </BottomPopup>
    )
};
export default EditAssetModal;