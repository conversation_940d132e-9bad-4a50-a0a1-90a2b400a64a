import * as React from 'react';
import { FlatList, ListRenderItemInfo, Pressable, SafeAreaView, ScrollView, StyleSheet, Text, View } from 'react-native';
import Colors from '../../../../utils/Colors/Colors';
import AppHeader from '../../../../components/AppHeader';
import ButtonComponent from '../../../../components/ButtonComponent';
import { useTranslation } from 'react-i18next';
import { AppFonts } from '../../../../components/Fonts';
import { ms } from '../../../../utils/Scale/Scaling';
import CardView from '../../../../components/CardView';
import TextInputComponent from '../../../../components/TextInputComponent';
import { Icon } from '../../../../components/Icons';
import AttachmentComponent from '../../../../components/Attachment';
import { FormatDate } from '../../../../utils/helpers';
import { ProgressUpdateDetailProps } from '../../../../utils/Constants/SyncDataConstants';
import { ProgressUpdateData } from '../../../../utils/types/SyncData';
import BottomCalendarModal from '../../../../components/CalendarPicker/BottomPopupCalendar';
import TimePickerModal from '../../../../components/TimePicker';
import { styles } from './Styles';


const SyncProgressUpdateDetails: React.FC<any> = () => {
    const {t} = useTranslation();
    const [progressQty, setProgressQty] = React.useState('');
    const [manDays, setMandays] = React.useState('');
    const [date, setDate] = React.useState('');
    const [remarks, setRemarks] = React.useState('');
    const [showCalendar, setShowCalendar] = React.useState(false);

    return (
        <SafeAreaView style={styles.container}>
            <AppHeader title={'Pipeline / HDPE / Cluster Dist. HDPE / Row / m'} />
            <View style={styles.mainContainer}>
                <ScrollView showsVerticalScrollIndicator={false}>
                <View style={styles.contentContainer}>
                    <Text style={styles.titleText}>{t('SyncData.progressUpdate')}</Text>

                    <Pressable>
                        <Text style={styles.lastHistoryText}>{t('SyncData.viewlastupdate')}</Text>
                    </Pressable>
                </View>

                <CardView 
                disabled={true}
                containerStyle={styles.cardViewContainer}>
                   <FlatList 
                    data={ProgressUpdateDetailProps}
                    scrollEnabled={false}
                    keyExtractor={(item, index) => `${item.name}${index}`}
                    renderItem={({item, index}: ListRenderItemInfo<ProgressUpdateData>) => {
                        return (
                            <View style={styles.progressContainer(index)}>
                                <Text style={styles.progressTitleText}>{t(item.name)}</Text>
                                <Text style={styles.progressDataText}>{item.data}</Text>
                            </View>
                        )
                    }}
                    key={4}
                    numColumns={4}
                   />
                </CardView>


                <TextInputComponent
                    label={t('SyncData.progressquantity')} 
                    placeholder={t('SyncData.enter')}
                    value={progressQty}
                    containerStyle={styles.mv5}
                    onChangeText={(text: string) => setProgressQty(text)}
                />

                <TextInputComponent
                    label={`${t('SyncData.mandays')}*`}
                    placeholder={t('SyncData.enter')}
                    value={progressQty}
                    containerStyle={styles.mv5}
                    onChangeText={(text: string) => setProgressQty(text)}
                />

                <View style={styles.mv5}>
                    <Text style={styles.dateTitle}>{`${t('SyncData.date')}*`}</Text>
                    <Pressable style={styles.dateButton}
                    onPress={() => setShowCalendar(true)}>
                        <View style={styles.flex1}>
                            <Text style={styles.dateText}>{date ? date : FormatDate(new Date().toString()).fullDate}</Text>
                        </View>
                        <View>
                            <Icon type={'Ionicons'} name={'calendar-outline'} size={ms(18)} />
                        </View>
                    </Pressable>
                </View>

                <TextInputComponent
                    label={`${t('SyncData.remarks')}*`}
                    placeholder={t('SyncData.enter')}
                    value={progressQty}
                    containerStyle={styles.mv5}
                    multiline
                    inputContainerStyle={styles.remarkInputContainer}
                    inputStyle={styles.inputStyle}
                    onChangeText={(text: string) => setProgressQty(text)}
                />

                <AttachmentComponent />
                </ScrollView>
                {showCalendar && <BottomCalendarModal 
                        visible={showCalendar}
                        fromDateTitle={'SyncData.fromdate'}
                        selectedFromDate={date}
                        onApply={(from: string, to: string) => {
                            console.log('fromdate', from)
                            setDate(from);
                            // setShowCalendar(false);
                            console.log('to------', to);
                        }}
                        onClose={() => setShowCalendar(false)}
                    />}
                {/* <TimePickerModal 
                    visible={showCalendar}
                    onTimeSelect={(time: string) => {
                        console.log('from-----', time);
                    }}
                    onClose={() => setShowCalendar(false)}
                /> */}
            </View>
            <ButtonComponent 
            title={t('SyncData.update')} 
            onPress={() => console.log('update')} />
        </SafeAreaView>
    )
};
export default SyncProgressUpdateDetails;