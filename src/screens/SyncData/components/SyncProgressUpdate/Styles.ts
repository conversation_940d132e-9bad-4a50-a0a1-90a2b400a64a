import { StyleSheet } from "react-native";
import Colors from "../../../../utils/Colors/Colors";
import { ms } from "../../../../utils/Scale/Scaling";
import { AppFonts } from "../../../../components/Fonts";

export const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.white,
    },
    card: {
      paddingHorizontal: 0,
      paddingVertical: 0
    },
    content: {
      gap: ms(12),
    },
    pathContainer: {
      backgroundColor: Colors.tabBgColoor,
      borderTopLeftRadius: ms(10),
      borderTopRightRadius: ms(10),
      paddingHorizontal: ms(17),
      paddingVertical: ms(12)
    },
    packageName: {
      fontFamily: AppFonts.SemiBold,
      fontSize: ms(14),
      color: Colors.textPrimary
    },
    detailsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: ms(15),
      paddingBottom: ms(10)
    },
    detailItem: {
      flex: 1,
    },
    label: {
      opacity: 0.6,
      fontFamily: AppFonts.Medium,
      fontSize: ms(11.5),
      color: Colors.textLightGray,
      marginBottom: ms(4),
    },
    value: {
      fontFamily: AppFonts.SemiBold,
      fontSize: ms(13),
      fontWeight: '500',
      color: Colors.textPrimary,
    },
    mainContainer: {
        flex: 1,
        paddingHorizontal: ms(15),
        paddingVertical: ms(15)
    },
    contentContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    titleText: {
        flex: 1,
        fontFamily: AppFonts.Bold,
        fontSize: ms(14),
        color: Colors.black
    },
    lastHistoryText: {
        fontFamily: AppFonts.Medium,
        fontSize: ms(12),
        color: Colors.blue,
        textDecorationStyle: 'solid',
        textDecorationLine: 'underline',
        textDecorationColor: Colors.blue
    }, 
    cardViewContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginVertical: ms(10),
        paddingVertical: ms(8)
    },
    progressContainer: (index: number) => {
        return {
                paddingLeft: index === 0 ? ms(5) : ms(10), 
                paddingRight: ms(10) 
        }
    },
    progressTitleText: {
        fontFamily: AppFonts.Medium,
        fontSize: ms(11.5),
        color: Colors.textLightGray,
        opacity: 0.6,
        marginBottom: ms(5)
    }, 
    progressDataText: {
        fontFamily: AppFonts.SemiBold,
        fontSize: ms(12),
        color: Colors.black,
    },
    mv5: {
        marginVertical: ms(5)
    },
    dateTitle: {
        fontFamily: AppFonts.Medium,
        fontSize: ms(12),
        color: Colors.pipeIdTextBlack,
        marginBottom: ms(5)
    },
    dateButton: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: ms(0.9),
        borderColor: Colors.inputBorder,
        borderRadius: ms(8),
        backgroundColor: Colors.containerligetBlue,
        height: ms(40),
        paddingHorizontal: ms(10)
    },
    flex1: {flex: 1},
    dateText: {
        fontFamily: AppFonts.Medium,
        fontSize: ms(13),
        color: Colors.primary,
        marginBottom: ms(5),
        opacity: 0.4
    },
    remarkInputContainer: {
      minHeight: ms(90),
      maxHeight: ms(90)
  },
  inputStyle: {
    textAlignVertical: 'top',
    minHeight: ms(80),
    maxHeight: ms(80)
}
  });