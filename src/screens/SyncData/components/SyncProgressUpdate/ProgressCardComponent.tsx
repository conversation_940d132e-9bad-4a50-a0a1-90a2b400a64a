import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { ProgressCardProps } from '../../../../@types/sync.types';
import { ms } from '../../../../utils/Scale/Scaling';
import Colors from '../../../../utils/Colors/Colors';
import { AppFonts } from '../../../../components/Fonts';
import { Icon } from '../../../../components/Icons';
import CardView from '../../../../components/CardView';
import { styles } from './Styles';

const ProgressCard: React.FC<ProgressCardProps> = ({ item, onPress }) => {
  return (
    <CardView
    containerStyle={styles.card} 
    onPress={onPress}>
      <View style={styles.content}>

        <View style={styles.pathContainer}>
        <Text style={styles.packageName}>{item.packageName}</Text>
        </View>

        <View style={styles.detailsContainer}>
          <View style={styles.detailItem}>
            <Text style={styles.label}>Progress Qty</Text>
            <Text style={styles.value}>{item.progressQty}</Text>
          </View>
          
          <View style={styles.detailItem}>
            <Text style={styles.label}>Man Days</Text>
            <Text style={styles.value}>{item.manDays}</Text>
          </View>
          
          <View style={styles.detailItem}>
            <Text style={styles.label}>Date</Text>
            <Text style={styles.value}>{item.date}</Text>
          </View>
          
          <Icon type={'Ionicons'} name="arrow-forward-outline" size={ms(18)} color={Colors.blue} />
        </View>
      </View>
    </CardView>
  );
};
export default ProgressCard;