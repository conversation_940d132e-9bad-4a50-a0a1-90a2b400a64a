import * as React from 'react';
import { FlatList, ListRenderItemInfo, Pressable, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Colors from '../../../../utils/Colors/Colors';
import { ms } from '../../../../utils/Scale/Scaling';
import ButtonComponent from '../../../../components/ButtonComponent';
import CardView from '../../../../components/CardView';
import { Text } from 'react-native';
import { AppFonts } from '../../../../components/Fonts';
import { useTranslation } from 'react-i18next';
import AppHeader from '../../../../components/AppHeader';
import { DayPlanningCategory, ManPowerCategory, MaterialCategory, PMCategory, SafetyCategory } from '../../../../utils/Constants/SyncDataConstants';
import { DayPlanningCategoryDetails, SyncDataHeaderProps } from '../../../../utils/types/SyncData';
import { Icon } from '../../../../components/Icons';
import { SVG } from '../../../../utils/ImagePath';
import EditDayPlanModal from './EditModals/EditPopupView';
import FloatingButton from '../../../../components/FloatingButton';


const SyncDayPlanListScreen = () => {
    const {t} = useTranslation();
    const [activeTab, setActiveTab] = React.useState(0);
    const [showEdit, setShowEdit] = React.useState(false);


    const renderCategory = React.useMemo(() => {
        return (
        <FlatList
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          data={DayPlanningCategory}
          style={{}}
          contentContainerStyle={styles.tabContainer}
          renderItem={({ item, index }: ListRenderItemInfo<SyncDataHeaderProps>) => {
            return (
              <TouchableOpacity 
              onPress={() => {
                item.onPress();
                setActiveTab(index);
              }}
              style={{
                backgroundColor: activeTab === index ? Colors.white : 'transparent',
                paddingVertical: ms(5),
                paddingHorizontal: ms(15),
                marginHorizontal: ms(5),
                marginVertical: ms(7),
                borderRadius: ms(8),
                borderBottomWidth: ms(1.2),
                borderBottomColor: activeTab === index ? Colors.borderColor : 'transparent',
              }}>
                  <Text style={{
                    fontFamily: activeTab === index ? AppFonts.Bold : AppFonts.Medium,
                    fontSize: ms(13),
                    color: activeTab === index ? Colors.secondary : Colors.textSecondary
                  }}>{`${t(item.tabName)}`}</Text>
              </TouchableOpacity>
            )
          }}
        />
      )
    }, [activeTab]);

    const renderManPowerItems = React.useCallback(() => ({item, index}: ListRenderItemInfo<DayPlanningCategoryDetails>) => {
        return (
            <>
               {/* Card view */}
               <CardView containerStyle={{
                        marginBottom: index === 0 ? ms(7.5) : 0,
                        marginTop: index === 0 ? ms(15) : ms(7.5)
                    }}>
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center'
                        }}>
                            <Text style={{
                                fontFamily: AppFonts.SemiBold,
                                fontSize: ms(16),
                                color: Colors.secondary,
                                flex: 1
                            }}>{item.name}</Text>
                            <Pressable style={{
                                backgroundColor: Colors.bgSecondaryLightBlue,
                                paddingVertical: ms(3),
                                paddingHorizontal: ms(3),
                                borderRadius: ms(5),
                                marginHorizontal: ms(5)
                            }}
                            onPress={() => setShowEdit(true)}>
                                <SVG.EditPen width={ms(19)} height={ms(19)} />
                            </Pressable>

                            <Pressable style={{
                                backgroundColor: Colors.bgLightRed,
                                paddingVertical: ms(2),
                                paddingHorizontal: ms(2),
                                borderRadius: ms(5),
                                marginHorizontal: ms(5)
                            }}>
                                <Icon type={'Ionicons'} name={'trash-outline'} size={ms(17)} color={Colors.red} style={{opacity: 0.6}} />
                            </Pressable>
                        </View>

                    {!!item.subCategory?.length && <FlatList
                        scrollEnabled={false}
                        data={item.subCategory}
                        renderItem={({ item }) => (
                            <>
                                <View style={{paddingRight: ms(20)}}>
                                    <Text style={{
                                        fontFamily: AppFonts.Medium,
                                        fontSize: ms(13),
                                        color: Colors.textLightGray,
                                        opacity: 0.6,
                                        marginBottom: ms(8)
                                    }}>{item.catergory}</Text>

                                    <Text style={{
                                        fontFamily: AppFonts.Bold,
                                        fontSize: ms(13),
                                        color: Colors.black,
                                    }}>{item.data}</Text>
                                </View>
                            </>
                        )}
                        key={4}
                        numColumns={4}
                    />}
                    </CardView>
            </>
        )
    }, []);

    return (
        <SafeAreaView style={styles.container}>
            <AppHeader 
            title={t('SyncData.dayplanning')} 
            isRightIconVisible
            rightContent={
                <View style={{
                    flexDirection: 'row',
                    alignItems: 'center'
                }}>
                    <Icon type={'Ionicons'} name={'calendar-outline'} size={ms(18)} />
                    <Text style={{
                        fontFamily: AppFonts.Medium,
                        fontSize: ms(12),
                        color: Colors.black,
                        marginLeft: ms(2)
                    }}>{'11/01/25'}</Text>
                </View>
            }
            />
            <View style={{
                flex: 1,
                paddingTop: ms(15),
            }}>
                    <CardView containerStyle={{
                        alignItems: 'center',
                        backgroundColor: Colors.mediumBlueBg,
                        borderColor: Colors.borderColor,
                        marginHorizontal: ms(15),
                        marginBottom: ms(15),
                        elevation: 2
                    }}>
                        <Text style={{
                            fontFamily: AppFonts.SemiBold,
                            fontSize: ms(14),
                            color: Colors.black
                        }}>{'ESR | Shardpur - 200KL - 10m | RCC upto GL(m3)'}</Text>
                        <Text style={{
                            fontFamily: AppFonts.Medium,
                            fontSize: ms(14),
                            color: Colors.textLightGray,
                            opacity: 0.6
                        }}>{'FTM Progress: 12 | FTM Plan: 30 | FTD Plans: 4.5'}</Text>
                    </CardView>

                    <View style={{
                        flex: 1,
                        backgroundColor: Colors.white,
                        paddingHorizontal: ms(15),
                        paddingTop: ms(15),
                        paddingBottom: ms(10),
                    }}>
                        <View style={{
                            backgroundColor: Colors.tabBgColoor,
                            paddingHorizontal: ms(3),
                            marginVertical: ms(5),
                            borderRadius: ms(10),
                            alignItems: 'center'
                        }}>
                            {renderCategory}
                        </View>

                       { activeTab === 0 && <View style={{
                            marginVertical: ms(15)
                        }}>
                        <Text style={{
                            fontFamily: AppFonts.Bold,
                            fontSize: ms(16),
                            color: Colors.black
                        }}>{'Plan productivity w.r.t Norm - 80%'}</Text>
                        </View>}

                     <FlatList 
                        nestedScrollEnabled
                        showsVerticalScrollIndicator={false}
                        data={activeTab === 0 ? ManPowerCategory : activeTab === 1 ? MaterialCategory : activeTab === 2 ? PMCategory : SafetyCategory}
                        renderItem={renderManPowerItems()}
                        keyExtractor={(item) => `${item.id}`}
                     />
                    </View>

                    <EditDayPlanModal 
                        activeTab={activeTab}
                        visible={showEdit}
                        onChange={(data: any) => console.log('data-----', data)}
                        onCancelPress={() => setShowEdit(false)}
                    />
                    <FloatingButton
                     onAddPress={() => console.log('Add new pressed')} 
                    />
            </View>

            <ButtonComponent
                title={t('SyncData.sync')}
                onPress={() => console.log('update')} />
        </SafeAreaView>
    )
};
export default SyncDayPlanListScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.containerligetBlue,
    },
    tabContainer: {
        alignItems: 'center',
      },
});