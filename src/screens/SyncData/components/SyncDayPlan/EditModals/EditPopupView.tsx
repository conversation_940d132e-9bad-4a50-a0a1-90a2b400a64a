import * as React from 'react';
import BottomPopup from '../../../../../components/BottomPopup';
import { Pressable, Text, View } from 'react-native';
import { Icon } from '../../../../../components/Icons';
import { ms } from '../../../../../utils/Scale/Scaling';
import Colors from '../../../../../utils/Colors/Colors';
import { AppFonts } from '../../../../../components/Fonts';
import { useTranslation } from 'react-i18next';
import ButtonComponent from '../../../../../components/ButtonComponent';
import { useFocusEffect } from '@react-navigation/native';
import CustomDropdownPicker from '../../../../../components/DropDownPicker';
import { ManPowerCategory, MaterialCategory, PMCategory } from '../../../../../utils/Constants/SyncDataConstants';

interface EditDayPlanModalProps {
    activeTab: number;
    visible: boolean;
    onCancelPress: () => void;
    onChange: (data: any) => void;
}

const EditDayPlanModal = ({
    activeTab,
    visible,
    onCancelPress,
    onChange
}: EditDayPlanModalProps) => {
    const { t } = useTranslation();
    const [requiredQuantity, setRequiredQunatity] = React.useState(0);
    const [formatDropDownData, setFormatDropdownData] = React.useState<{ label: string; value: string }[]>([]);

    useFocusEffect(
        React.useCallback(() => {
            onChange(requiredQuantity);
        }, [requiredQuantity, onChange])
    );

    React.useEffect(() => {
        const arrayValue = activeTab === 0 ? ManPowerCategory : activeTab === 1 ? MaterialCategory : activeTab === 2 ? PMCategory : []
        const formatterValue = arrayValue.map((x) => ({
            label: x.name,
            value: x.name
        }));
        setFormatDropdownData(formatterValue);
    }, [activeTab]);

    const incrementQunatity = React.useCallback(() => {
        setRequiredQunatity(prev => prev + 1);
    }, []);

    const decrementQunatity = React.useCallback(() => {
        setRequiredQunatity(prev => prev === 0 ? prev : prev - 1);
    }, []);

    return (
        <BottomPopup visible={visible} onCancelPress={onCancelPress}>
            <View style={{
                paddingHorizontal: ms(25),
                paddingVertical: ms(15)
            }}>
                <View style={{
                    paddingTop: ms(20),
                    paddingBottom: ms(10),
                    borderBottomColor: Colors.progressAsh,
                    borderBottomWidth: ms(0.5)
                }}>
                    <Text style={{
                        fontFamily: AppFonts.Bold,
                        fontSize: ms(16),
                        color: Colors.black
                    }}>{activeTab !== 3 ? `${t('SyncData.editrequirementtitle')} ${activeTab === 0 ? 'Man Power' : activeTab === 1 ? 'Material' : 'P&M'}` : 'Edit Sub - Activity for Safety'}</Text>
                </View>


                {(activeTab === 0 || activeTab === 1) && (
                    <>
                        <CustomDropdownPicker
                            defaultValue=''
                            title={'SyncData.skills'}
                            data={formatDropDownData}
                            onSelect={(data: any) => console.log(data)}
                        />

                        <View style={{
                            marginVertical: ms(10),
                            marginBottom: ms(20)
                        }}>
                            <Text style={{
                                fontFamily: AppFonts.Medium,
                                fontSize: ms(13),
                                color: Colors.pipeIdTextBlack,
                                marginBottom: ms(5)
                            }}>{`${activeTab as number === 0 ? t('SyncData.helperrequiredqunatity') : t('SyncData.requiredquantity')}`}</Text>
                            <Pressable style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                borderWidth: ms(0.9),
                                borderColor: Colors.inputBorder,
                                borderRadius: ms(8),
                                backgroundColor: Colors.containerligetBlue,
                                height: ms(40),
                                paddingHorizontal: ms(10)
                            }}>
                                <View style={{ flex: 1 }}>
                                    <Text style={{
                                        fontFamily: AppFonts.Medium,
                                        fontSize: ms(13),
                                        color: Colors.black,
                                        marginBottom: ms(5),
                                    }}>{requiredQuantity}</Text>
                                </View>

                                <Pressable style={{
                                    backgroundColor: Colors.iconBg,
                                    paddingVertical: ms(5),
                                    paddingHorizontal: ms(5),
                                    borderRadius: ms(5),
                                    marginRight: ms(3)
                                }}
                                    onPress={() => decrementQunatity()}>
                                    <Icon type={'Ionicons'} color={Colors.pipeIdTextBlack} name={'remove-outline'} size={ms(18)} />
                                </Pressable>

                                <Pressable style={{
                                    backgroundColor: Colors.iconBg,
                                    paddingVertical: ms(5),
                                    paddingHorizontal: ms(5),
                                    borderRadius: ms(5),
                                    marginLeft: ms(3)
                                }} onPress={() => incrementQunatity()}>
                                    <Icon type={'Ionicons'} color={Colors.pipeIdTextBlack} name={'add-outline'} size={ms(18)} />
                                </Pressable>
                            </Pressable>
                        </View>
                    </>
                )}
                {
                    activeTab === 2 && (
                        <>
                            <CustomDropdownPicker
                                defaultValue=''
                                title={'SyncData.material'}
                                data={formatDropDownData}
                                mainContainerStyle={{marginTop: ms(15)}}
                                onSelect={(data: any) => console.log(data)}
                            />

                            <View style={{
                                marginVertical: ms(10),
                            }}>
                                <Text style={{
                                    fontFamily: AppFonts.Medium,
                                    fontSize: ms(13),
                                    color: Colors.pipeIdTextBlack,
                                    marginBottom: ms(5)
                                }}>{`${t('SyncData.fromtime')}`}</Text>
                                <Pressable style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    borderWidth: ms(0.9),
                                    borderColor: Colors.inputBorder,
                                    borderRadius: ms(5),
                                    backgroundColor: Colors.containerligetBlue,
                                    height: ms(40),
                                    paddingHorizontal: ms(10)
                                }}>
                                    <View style={{ flex: 1 }}>
                                        <Text style={{
                                            fontFamily: AppFonts.Medium,
                                            fontSize: ms(13),
                                            color: Colors.primary,
                                            marginBottom: ms(5),
                                            opacity: 0.4
                                        }}>{'Select'}</Text>
                                    </View>
                                    <View>
                                        <Icon type={'Ionicons'} name={'time-outline'} size={ms(20)} />
                                    </View>
                                </Pressable>
                            </View>

                            {/* To time */}
                            <View style={{
                                marginVertical: ms(10),
                            }}>
                                <Text style={{
                                    fontFamily: AppFonts.Medium,
                                    fontSize: ms(13),
                                    color: Colors.pipeIdTextBlack,
                                    marginBottom: ms(5)
                                }}>{`${t('SyncData.totime')}`}</Text>
                                <Pressable style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    borderWidth: ms(0.9),
                                    borderColor: Colors.inputBorder,
                                    borderRadius: ms(5),
                                    backgroundColor: Colors.containerligetBlue,
                                    height: ms(40),
                                    paddingHorizontal: ms(10)
                                }}>
                                    <View style={{ flex: 1 }}>
                                        <Text style={{
                                            fontFamily: AppFonts.Medium,
                                            fontSize: ms(13),
                                            color: Colors.primary,
                                            marginBottom: ms(5),
                                            opacity: 0.4
                                        }}>{'Select'}</Text>
                                    </View>
                                    <View>
                                        <Icon type={'Ionicons'} name={'time-outline'} size={ms(20)} />
                                    </View>
                                </Pressable>
                            </View>

                            <View style={{
                            marginVertical: ms(10),
                        }}>
                            <Text style={{
                                fontFamily: AppFonts.Medium,
                                fontSize: ms(13),
                                color: Colors.pipeIdTextBlack,
                                marginBottom: ms(5)
                            }}>{`${t('SyncData.requiredquantity')}`}</Text>
                            <Pressable style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                borderWidth: ms(0.9),
                                borderColor: Colors.inputBorder,
                                borderRadius: ms(8),
                                backgroundColor: Colors.containerligetBlue,
                                height: ms(40),
                                paddingHorizontal: ms(10)
                            }}>
                                <View style={{ flex: 1 }}>
                                    <Text style={{
                                        fontFamily: AppFonts.Medium,
                                        fontSize: ms(13),
                                        color: Colors.black,
                                        marginBottom: ms(5),
                                    }}>{requiredQuantity}</Text>
                                </View>

                                <Pressable style={{
                                    backgroundColor: Colors.iconBg,
                                    paddingVertical: ms(5),
                                    paddingHorizontal: ms(5),
                                    borderRadius: ms(5),
                                    marginRight: ms(3)
                                }}
                                    onPress={() => decrementQunatity()}>
                                    <Icon type={'Ionicons'} color={Colors.pipeIdTextBlack} name={'remove-outline'} size={ms(18)} />
                                </Pressable>

                                <Pressable style={{
                                    backgroundColor: Colors.iconBg,
                                    paddingVertical: ms(5),
                                    paddingHorizontal: ms(5),
                                    borderRadius: ms(5),
                                    marginLeft: ms(3)
                                }} onPress={() => incrementQunatity()}>
                                    <Icon type={'Ionicons'} color={Colors.pipeIdTextBlack} name={'add-outline'} size={ms(18)} />
                                </Pressable>
                            </Pressable>
                        </View>
                        </>
                    )}

                    {
                        activeTab === 3 && (
                            <>
                            <CustomDropdownPicker
                                defaultValue=''
                                title={'SyncData.addsubactivity'}
                                data={formatDropDownData}
                                mainContainerStyle={{marginTop: ms(10), marginBottom: ms(5)}}
                                onSelect={(data: any) => console.log(data)}
                            />

                            <CustomDropdownPicker
                                defaultValue=''
                                title={'SyncData.vendorcode'}
                                data={formatDropDownData}
                                mainContainerStyle={{marginVertical: ms(5)}}
                                onSelect={(data: any) => console.log(data)}
                            />

                            <CustomDropdownPicker
                                defaultValue=''
                                title={'SyncData.focusarea'}
                                data={formatDropDownData}
                                mainContainerStyle={{marginVertical: ms(5)}}
                                onSelect={(data: any) => console.log(data)}
                            />

                            <CustomDropdownPicker
                                defaultValue=''
                                title={'SyncData.risk'}
                                data={formatDropDownData}
                                mainContainerStyle={{marginVertical: ms(5)}}
                                onSelect={(data: any) => console.log(data)}
                            />

                            <CustomDropdownPicker
                                defaultValue=''
                                title={'SyncData.riskzoning'}
                                data={formatDropDownData}
                                mainContainerStyle={{marginVertical: ms(5)}}
                                onSelect={(data: any) => console.log(data)}
                            />

                            <CustomDropdownPicker
                                defaultValue=''
                                title={'SyncData.ocp'}
                                data={formatDropDownData}
                                mainContainerStyle={{marginVertical: ms(5)}}
                                onSelect={(data: any) => console.log(data)}
                            />
                            </>
                        )
                    }
            </View>
            <ButtonComponent
                showSecondaryButton
                secondaryButtonTitle={t('commonStrings.cancel')}
                mainContainerStyle={{
                    paddingBottom: ms(30)
                }}
                title={t('SyncData.update')}
                onPress={() => console.log('update')} />
        </BottomPopup>
    )
};
export default EditDayPlanModal;