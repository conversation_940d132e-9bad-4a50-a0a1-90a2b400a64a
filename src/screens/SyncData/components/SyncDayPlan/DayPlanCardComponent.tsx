import * as React from 'react';
import { Text, View } from 'react-native';
import { AppFonts } from '../../../../components/Fonts';
import { ms } from '../../../../utils/Scale/Scaling';
import Colors from '../../../../utils/Colors/Colors';
import { SVG } from '../../../../utils/ImagePath';
import CardView from '../../../../components/CardView';

interface hindranceCardProps {
    item: any;
    onPress: (data?: any) => void;
}

const DayPlanCard: React.FC<hindranceCardProps> = ({
    item, onPress
}) => {
    return (
        <CardView 
        containerStyle={{
            paddingHorizontal: ms(12),
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center'
        }}
        onPress={onPress}>
           <View style={{flex: 1}}>
           <Text style={{
            flex: 1,
            fontFamily: AppFonts.SemiBold,
            fontSize: ms(14),
            color: Colors.black
           }}>{'Pipeline / HDPE/ Cluster Dist. HDPE 200 mm / Row / m'}</Text> 
           </View>
           <View  style={{top: ms(15), left: -ms(5)}}>
            <SVG.ArrowRight />
           </View>
        </CardView>
    )
};
export default DayPlanCard;