import * as React from 'react';
import { Text, View } from 'react-native';
import Strings from '../../../../utils/Strings/Strings';
import { AppFonts } from '../../../../components/Fonts';
import { ms } from '../../../../utils/Scale/Scaling';
import Colors from '../../../../utils/Colors/Colors';
import { SVG } from '../../../../utils/ImagePath';
import CardView from '../../../../components/CardView';

interface hindranceCardProps {
    item: any;
    onPress: (data?: any) => void;
}

const HindranceCard: React.FC<hindranceCardProps> = ({
    item, onPress
}) => {
    return (
        <CardView onPress={onPress}>
            <View style={{
            flex: 1,
            flexDirection: 'row',
        }}>
            <View style={{flex: 1}}>
                <Text style={{
                    opacity: 0.6,
                    fontFamily: AppFonts.Medium,
                    fontSize: ms(12),
                    color: Colors.textLightGray
                }}>{Strings.SyncData.date}</Text>
                <Text style={{
                    fontFamily: AppFonts.SemiBold,
                    fontSize: ms(13),
                    color: Colors.black
                }}>{'DD/MM/YYYY'}</Text>
            </View>
            <View style={{flex: 1}}>
                <Text style={{
                    opacity: 0.6,
                    fontFamily: AppFonts.Medium,
                    fontSize: ms(12),
                    color: Colors.textLightGray,
                }}>{Strings.SyncData.gapLength}</Text>
                <Text style={{
                    fontFamily: AppFonts.SemiBold,
                    fontSize: ms(13),
                    color: Colors.black
                }}>{'5'}</Text>
            </View>
            <View>
                <SVG.ArrowRight width={ms(18)} height={ms(18)} />
            </View>
        </View>

        <View style={{
            flex: 1,
            flexDirection: 'row',
        }}>
            <View style={{flex: 1}}>
                <Text style={{
                    opacity: 0.6,
                    fontFamily: AppFonts.Medium,
                    fontSize: ms(12),
                    color: Colors.textLightGray
                }}>{Strings.SyncData.lat}</Text>
                <Text style={{
                    fontFamily: AppFonts.SemiBold,
                    fontSize: ms(13),
                    color: Colors.black
                }}>{'1.93839389309483'}</Text>
            </View>
            <View style={{flex: 1}}>
                <Text style={{
                    opacity: 0.6,
                    fontFamily: AppFonts.Medium,
                    fontSize: ms(12),
                    color: Colors.textLightGray
                }}>{Strings.SyncData.long}</Text>
                <Text style={{
                    fontFamily: AppFonts.SemiBold,
                    fontSize: ms(13),
                    color: Colors.black
                }}>{'1.9030939898'}</Text>
            </View>
        </View>
        </CardView>
    )
};
export default HindranceCard;