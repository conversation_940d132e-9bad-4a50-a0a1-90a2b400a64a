import * as React from 'react';
import { Pressable, SafeAreaView, ScrollView, StyleSheet, Text, View } from 'react-native';
import Colors from '../../../../utils/Colors/Colors';
import AppHeader from '../../../../components/AppHeader';
import ButtonComponent from '../../../../components/ButtonComponent';
import { useTranslation } from 'react-i18next';
import { AppFonts } from '../../../../components/Fonts';
import { ms } from '../../../../utils/Scale/Scaling';
import CardView from '../../../../components/CardView';
import TextInputComponent from '../../../../components/TextInputComponent';
import { SVG } from '../../../../utils/ImagePath';
import AttachmentComponent from '../../../../components/Attachment';
import { FormatDate } from '../../../../utils/helpers';
 
 
const SyncHindranceUpdateDetails: React.FC<any> = () => {
    const { t } = useTranslation();
    const [progresslength, setProgressLength] = React.useState('');
    const [remarks, setRemarks] = React.useState('');
    const [expand, setExpand] = React.useState(false);
 
 
    return (
        <SafeAreaView style={styles.container}>
            <AppHeader title={'Update Hindrance'} />
            <View style={{
                flex: 1,
                paddingHorizontal: ms(15),
                paddingVertical: ms(15)
            }}>
                <ScrollView showsVerticalScrollIndicator={false}>
                    {/* <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                    }}> */}
                        {/* <Text style={{
                            flex: 1,
                            fontFamily: AppFonts.Bold,
                            fontSize: ms(14),
                            color: Colors.black
                        }}>{t('SyncData.progressUpdate')}</Text> */}
 
                        {/* <Pressable>
                            <Text style={{
                                fontFamily: AppFonts.Medium,
                                fontSize: ms(12),
                                color: Colors.blue,
                                textDecorationStyle: 'solid',
                                textDecorationLine: 'underline',
                                textDecorationColor: Colors.blue
                            }}>{t('SyncData.viewlastupdate')}</Text>
                        </Pressable> */}
                    {/* </View> */}
 
                    <CardView
                        disabled={true}
                        containerStyle={{
                            marginVertical: ms(10),
                            paddingVertical: ms(8),
                            backgroundColor: Colors.containerligetBlue,
                            height: expand ? 'auto' : ms(40),
                            borderColor:Colors.searchBorderGrey,
                            borderWidth:ms(1)
                        }}>
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                        }}>
                            <View style={{ flex: 0.7 }}>
                                <Text style={{
                                    fontFamily: AppFonts.Medium,
                                    fontSize: ms(12),
                                    color: Colors.textLightGray,
                                    opacity: 0.6
                                }}>{t('SyncData.date')}</Text>
                            </View>
                            <View style={{ flex: 0.6 }}>
                                <Text style={{
                                    fontFamily: AppFonts.SemiBold,
                                    fontSize: ms(13),
                                    color: Colors.black,
                                }}>{FormatDate(new Date().toDateString()).fullDate}</Text>
                            </View>
                            <Pressable style={{
                                flex: 0.1,
                                paddingHorizontal: ms(5),
                            }}
                            onPress={() => setExpand(!expand)}>
                                {
                                    expand ? <SVG.ChevronUp width={ms(17)} height={ms(17)} /> : <SVG.ChevronDown width={ms(17)} height={ms(17)} />
                                }
                            </Pressable>
                        </View>
 
                        {expand && (
                            <>
                            <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                        }}>
                            <View style={{ flex: 1 }}>
                                <Text style={{
                                    fontFamily: AppFonts.Medium,
                                    fontSize: ms(12),
                                    color: Colors.textLightGray,
                                    opacity: 0.6
                                }}>{t('SyncData.pipemeterailanddia')}</Text>
                            </View>
                            <View style={{ flex: 1 }}>
                                <Text style={{
                                    fontFamily: AppFonts.SemiBold,
                                    fontSize: ms(13),
                                    color: Colors.black,
                                }}>{'DI 500'}</Text>
                            </View>
                        </View>
 
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                        }}>
                            <View style={{ flex: 1 }}>
                                <Text style={{
                                    fontFamily: AppFonts.Medium,
                                    fontSize: ms(12),
                                    color: Colors.textLightGray,
                                    opacity: 0.6
                                }}>{t('SyncData.reasonforgap')}</Text>
                            </View>
                            <View style={{ flex: 1 }}>
                                <Text style={{
                                    fontFamily: AppFonts.SemiBold,
                                    fontSize: ms(13),
                                    color: Colors.black,
                                }}>{'Non-availability of Pipe/Specials/financial'}</Text>
                            </View>
                        </View>
 
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                        }}>
                            <View style={{ flex: 1 }}>
                                <Text style={{
                                    fontFamily: AppFonts.Medium,
                                    fontSize: ms(12),
                                    color: Colors.textLightGray,
                                    opacity: 0.6
                                }}>{t('SyncData.gaplength')}</Text>
                            </View>
                            <View style={{ flex: 1 }}>
                                <Text style={{
                                    fontFamily: AppFonts.SemiBold,
                                    fontSize: ms(13),
                                    color: Colors.black,
                                }}>{'5'}</Text>
                            </View>
                        </View>
 
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                        }}>
                            <View style={{ flex: 1 }}>
                                <Text style={{
                                    fontFamily: AppFonts.Medium,
                                    fontSize: ms(12),
                                    color: Colors.textLightGray,
                                    opacity: 0.6
                                }}>{t('SyncData.lat')}</Text>
                            </View>
                            <View style={{ flex: 1 }}>
                                <Text style={{
                                    fontFamily: AppFonts.SemiBold,
                                    fontSize: ms(13),
                                    color: Colors.black,
                                }}>{'13.05682387564873'}</Text>
                            </View>
                        </View>
 
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                        }}>
                            <View style={{ flex: 1 }}>
                                <Text style={{
                                    fontFamily: AppFonts.Medium,
                                    fontSize: ms(12),
                                    color: Colors.textLightGray,
                                    opacity: 0.6
                                }}>{t('SyncData.long')}</Text>
                            </View>
                            <View style={{ flex: 1 }}>
                                <Text style={{
                                    fontFamily: AppFonts.SemiBold,
                                    fontSize: ms(13),
                                    color: Colors.black,
                                }}>{'80.05682387564873'}</Text>
                            </View>
                        </View>
 
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                        }}>
                            <View style={{ flex: 1 }}>
                                <Text style={{
                                    fontFamily: AppFonts.Medium,
                                    fontSize: ms(12),
                                    color: Colors.textLightGray,
                                    opacity: 0.6
                                }}>{t('SyncData.startandendnode')}</Text>
                            </View>
                            <View style={{ flex: 1 }}>
                                <Text style={{
                                    fontFamily: AppFonts.SemiBold,
                                    fontSize: ms(13),
                                    color: Colors.black,
                                }}>{'-'}</Text>
                            </View>
                        </View>
                        </>
                    )}
 
                    </CardView>
 
                    <TextInputComponent
                        label={`${t('SyncData.progresslength')}*`}
                        placeholder={t('SyncData.enter')}
                        value={progresslength}
                        showSecondaryText
                        secondaryText={`${t('SyncData.balance')}: 2`}
                        onChangeText={(text: string) => setRemarks(text)}
                    />
 
                    <TextInputComponent
                        label={`${t('SyncData.remarks')}*`}
                        placeholder={t('SyncData.enter')}
                        value={progresslength}
                        secondaryLabel={`${t('SyncData.viewother')} 3 ${t('SyncData.remarks')}`}
                        secondaryLabelStyle={{
                            textDecorationLine: 'underline',
                            textDecorationStyle: 'solid'
                        }}
                        containerStyle={{
                            marginVertical: ms(5)
                        }}
                        multiline
                        inputContainerStyle={{
                            minHeight: ms(90),
                            maxHeight: ms(90)
                        }}
                        inputStyle={{
                            textAlignVertical: 'top',
                            minHeight: ms(80),
                            maxHeight: ms(80)
                        }}
                        onChangeText={(text: string) => setRemarks(text)}
                    />
                    <AttachmentComponent />
                </ScrollView>
            </View>
            <ButtonComponent
                title={t('update')}
                onPress={() => console.log('update')} />
        </SafeAreaView>
    )
};
export default SyncHindranceUpdateDetails;
 
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.white,
    },
});