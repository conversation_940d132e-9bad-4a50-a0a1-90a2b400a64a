import React, { useMemo, useState } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  SafeAreaView,
  TouchableOpacity,
  Text,
  ListRenderItemInfo,
} from 'react-native';
import { ProgressItem } from '../../@types/sync.types';
import ProgressCard from './components/SyncProgressUpdate/ProgressCardComponent';
import { NavigationProp, useNavigation, CommonActions } from '@react-navigation/native';
import { ms } from '../../utils/Scale/Scaling';
import Colors from '../../utils/Colors/Colors';
import AppHeader from '../../components/AppHeader';
import Strings from '../../utils/Strings/Strings';
import { AppFonts } from '../../components/Fonts';
import { tabs } from '../../utils/Constants/SyncDataConstants';
import { SyncDataHeaderProps } from '../../utils/types/SyncData';
import HindranceCard from './components/SyncHindrance/HindaranceCardComponent';
import ButtonComponent from '../../components/ButtonComponent';
import { useTranslation } from 'react-i18next';
import DayPlanCard from './components/SyncDayPlan/DayPlanCardComponent';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { styles } from './Styles';



const SyncDataScreen: React.FC = () => {
  const navigation: NavigationProp<RootStackParamList> = useNavigation();
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState(0);
  const flatListRef = React.useRef<FlatList>(null);
  const [progressData] = useState<ProgressItem[]>([
    {
      packageName: 'Package1 → OHSR → 100KL → 18M → Jawada → Plinth Protection',
      progressQty: 19,
      manDays: 15,
      date: '11/05/2025',
    },
    // Add more items as needed
  ]);

  const handleTabPress = React.useCallback((index: number, onPress: () => void) => {
    setActiveTab(index);
    onPress?.();

    flatListRef.current?.scrollToIndex({
      index,
      animated: true,
      viewPosition: 0.5, // Center align. Use 1 for right-align or 0 for left.
    });
  }, [setActiveTab]);

  const renderTabBasedComponent = useMemo(() => {
    switch (activeTab) {
      case 0: {
        return (
          <FlatList
            data={progressData}
            renderItem={({ item }) => (
              <ProgressCard
                item={item}
                onPress={() => navigation.navigate('SyncProgressUpdateScreen')}
              />
            )}
            keyExtractor={(item) => item.packageName}
            contentContainerStyle={styles.listContent}
          />
        );
      }
      case 1: {
        return (
          <FlatList
            data={progressData}
            renderItem={({ item }) => (
              <HindranceCard
                item={item}
                onPress={() => navigation.navigate('SyncHindranceUpdateScreen')}
              />
            )}
            keyExtractor={(item) => item.packageName}
            contentContainerStyle={styles.listContent}
          />
        )
      }
      case 2:
      case 3: {
        return (
          <FlatList
            data={progressData}
            renderItem={({ item }) => (
              <DayPlanCard
                item={item}
                onPress={() => navigation.navigate(activeTab === 2 ? 'SyncDayPlanListScreen' : 'RequestAssetsScreen')}
              />
            )}
            keyExtractor={(item) => item.packageName}
            contentContainerStyle={styles.listContent}
          />
        )
      }
    }
  }, [activeTab]);


  const renderHeader = useMemo(() => {
    return (
      <FlatList
        ref={flatListRef}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        data={tabs}
        style={{}}
        contentContainerStyle={styles.tabContainer}
        renderItem={({ item, index }: ListRenderItemInfo<SyncDataHeaderProps>) => {
          return (
            <TouchableOpacity
              onPress={() => handleTabPress(index, item.onPress)}
              style={{
                backgroundColor: activeTab === index ? Colors.white : 'transparent',
                paddingVertical: ms(5),
                paddingHorizontal: ms(12),
                marginHorizontal: ms(5),
                marginVertical: ms(7),
                borderRadius: ms(8),
                borderBottomWidth: ms(1.2),
                borderBottomColor: activeTab === index ? Colors.borderColor : 'transparent',
              }}>
              <Text style={{
                fontFamily: activeTab === index ? AppFonts.Bold : AppFonts.Medium,
                fontSize: ms(13),
                color: activeTab === index ? Colors.secondary : Colors.textSecondary
              }}>{item.tabName}</Text>
            </TouchableOpacity>
          )
        }}
      />
    )
  }, [activeTab]);

  const handleSync = () => {
    // Implement sync logic here
    console.log('Syncing data...');
  };

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader title={Strings.SyncData.syncDataHeader} onBackPress={() => navigation.dispatch(CommonActions.reset({
        index: 0,
        routes: [{ name: 'Home' }],
      }))} />

      <View style={styles.header}>
        {renderHeader}
      </View>
      <View style={styles.contentContainer}>
        {renderTabBasedComponent}
      </View>
      <ButtonComponent
        title={t('SyncData.sync')}
        onPress={handleSync}
      />
    </SafeAreaView>
  );
};

export default SyncDataScreen;