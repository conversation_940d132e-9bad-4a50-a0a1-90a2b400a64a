import React, { useEffect, useRef } from 'react';
import { Dimensions, SafeAreaView, StyleSheet, ActivityIndicator, View } from 'react-native';
import AppHeader from '../../components/AppHeader';
import Strings from '../../utils/Strings/Strings';
import { useDispatch, useSelector } from 'react-redux';
import { userMannualImageRequest, userMannualRequest } from '../../redux/UserMannualRedux/UserMannualActions';
import { getUserRolesInfo } from '../../utils/DataStorage/Storage';
import { RootState } from '../../redux/Root/rootStore';
import { UserMannual, UserMannualDownloadRequestData } from '../../model/UserMannual/UserMannualData';
import BaseApiConstants from '../../utils/Constants/CommonConstant';
import Pdf from 'react-native-pdf';
import Colors from '../../utils/Colors/Colors';
import { ms } from '../../utils/Scale/Scaling';

const UserMannualScreen = () => {
    const dispatch = useDispatch();
    const roles = getUserRolesInfo();
    const userRole = roles?.RolesList?.[0];
    const { userMannualData, userMannualDownloadData, userMannualLoading, userMannualDownloading } = useSelector((state: RootState) => state.userMannual);
    const source = userMannualDownloadData
        ? { uri: `data:application/pdf;base64,${userMannualDownloadData}` }
        : null;

    useEffect(() => {
        dispatch(userMannualRequest());
    }, []);

    useEffect(() => {
        if (userMannualData?.MasterList?.UserMannual && userRole?.FRCode) {
            console.log('usermannual List: ', userMannualData?.MasterList?.UserMannual);
            const matchedManual = userMannualData.MasterList.UserMannual.find(
                (manual: UserMannual) => manual.PRGDTA_DT_Code === userRole.FRCode
            );
            if (matchedManual) {
                const params: UserMannualDownloadRequestData = {
                    ModuleName: BaseApiConstants.userMannualModuleName,
                    Unique: matchedManual.PRGDTA_Unique_ID,
                    SiteUrl: BaseApiConstants.userMannualSiteUrl,
                }
                dispatch(userMannualImageRequest(params));
            }
        }
    }, [userMannualData, userRole?.FRCode, dispatch]);

    return (
        <SafeAreaView style={styles.container}>
            <AppHeader
                title={Strings.homeScreen.userManual}
                onBookmarkPress={() => { }}
            />
            {/* Loader always shows until PDF is ready */}
            {( userMannualLoading || userMannualDownloading) && (
                <View style={styles.loaderContainer}>
                    <ActivityIndicator size="large" color={Colors.forgotPinBlue} />
                </View>
            )}
            {/* PDF only shows when ready */}
            {source && (
                <Pdf
                    source={source}
                    style={styles.pdf}
                    onError={(error: any) => {
                        console.log('PDF load error:', error);
                    }}
                />
            )}
        </SafeAreaView>
    );
};

export default UserMannualScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    loaderContainer: {
        ...StyleSheet.absoluteFillObject,
        justifyContent: 'center',
        alignItems: 'center',

    },
    pdf: {
        flex: 1,
        margin: ms(10),
    },
}); 