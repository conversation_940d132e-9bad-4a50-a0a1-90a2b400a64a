## 🚀 Feature Overview

### 🔹 Module Purpose -  UserMannual Module

The **User Manual Module** allows users across various roles within the app to access a role-specific user manual (PDF). Each user can view a manual tailored to their responsibilities, ensuring they always have the right guidance at their fingertips.

---

## 👥 Supported User Roles

The following roles are supported and each will have access to their respective manuals:

- Site Engineer  
- Quality Incharge  
- Store Incharge  
- Section Incharge  
- Account  
- P&M Incharge  
- Approver  
- Project Manager  
- Master Admin  

---

## 🚀 Features

### ✅ Role-Based Manual Access
- User manual PDFs are **fetched dynamically** based on the **logged-in user's role**.
- Ensures that users only access documents relevant to their position.

### ✅ In-App PDF Viewing
- The manual is rendered **within the app**, using an embedded PDF viewer.
- No need for external downloads or third-party applications.

### ✅ Viewer Capabilities
- **Scroll**, **zoom**, and **navigate** through the document effortlessly.
- Smooth PDF rendering for better readability.

### ✅ Secure Access
- Only **authenticated users** can access the PDF viewer.
- Role-based URLs are **protected** and **not publicly exposed**.

### ✅ Error Handling
- If a manual fails to load or an invalid URL is detected, the viewer displays:
  > **"User manual is currently unavailable. Please try again later."**

### ✅ Performance
- PDF is expected to load in **under 5 seconds**, depending on network strength.

---

## 🛠️ Technical Implementation

### 📄 PDF URL Fetching
- On successful login, the app identifies the user's role.
- A **role-based PDF URL** is fetched from the backend or configuration.

### 🔐 Security
- URLs are **not hardcoded** or publicly accessible.
- Backend ensures that only authenticated users receive the correct PDF link.

### 🧪 Error States
- Failed requests or load errors trigger fallback messaging in the viewer.
- Proper logging can be added for debugging and backend alerting.

---

### 📍 Screens
| File | Purpose |
|------|---------|
| `UserManualScreen.tsx` | Landing screen when user clicks USerMannual in Home Screen and displays the UserMannual PDF" |


---

### 🔁 Redux (State Management)

#### UserMannualRedux
| File | Description |
|------|-------------|
| `UserMannualActions.ts` | Action creators for UserMannual operations |
| `UserMannualActionTypes.ts` | Enum of UserMannual action types |
| `UserMannualReducer.ts` | State handling logic for UserMannual state |
| `UserMannualSaga.ts` | Handles side effects like async API calls for fetching UserMannual pdf data |


### 🗃️ Services

| File | Purpose |
|------|--------|
| `ApiRequests.ts` | Includes the common methods to make API call |
| `AxiosService.ts`| Contains the code to do Axios setup and to fetch data from backend |

---

