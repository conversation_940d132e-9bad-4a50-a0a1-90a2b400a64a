import React, { useCallback } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import CHECKED from '../../../assets/svg/checked_grey.svg';
import UNCHECKED from '../../../assets/svg/unchecked_grey.svg';
import PrintLog from '../../../utils/Logger/PrintLog';
import Colors from '../../../utils/Colors/Colors';
import { ms } from '../../../utils/Scale/Scaling';
import BottomPopup from '../../../components/BottomPopup';

export type DeliveryType = 'Non-Billable' | 'Billable' | 'GIS Node';

interface deliveryTypeProps {
    type: DeliveryType;
    isSelected: boolean;
    onSelect: (type: DeliveryType) => void;
}

interface BottomPopupDeliverablesProps {
    visible: boolean;
    onClose: () => void;
    selectedType: DeliveryType;
    onSelectType: (type: DeliveryType) => void;
}

const DeliveryTypeOption: React.FC<deliveryTypeProps> = ({ type, isSelected, onSelect }) => {
    const handlePress = useCallback(() => {
        onSelect(type);
    }, [onSelect, type]);

    return (
        <TouchableOpacity style={styles.optionContainer} onPress={handlePress}>
            <Text style={styles.optionText}>{type}</Text>
            {isSelected ? (
                <CHECKED />
            ) : (
                <UNCHECKED />
            )}
        </TouchableOpacity>
    );
};

const BottomPopupDeliverables: React.FC<BottomPopupDeliverablesProps> = ({ selectedType, onSelectType, visible,
    onClose })=> {

    const handleSelectType = useCallback(
        (type: DeliveryType) => {
            PrintLog.debug('BottomPopupDeliverables', { 'handleSelectType - Selected type: ': type });
            onSelectType(type);
        },
        [onSelectType]
    );

    return (
        <View style={styles.container}>
            <BottomPopup visible={visible} onCancelPress={onClose}>
                <Text style={styles.title}>Select Delivery Type</Text>
                <View style={styles.dividerView} />
                {/* <DeliveryTypeOption
                    type="SWP"
                    isSelected={selectedType === 'SWP'}
                    onSelect={handleSelectType}
                /> */}
                <DeliveryTypeOption
                    type="Non-Billable"
                    isSelected={selectedType === 'Non-Billable'}
                    onSelect={handleSelectType}
                />
                <DeliveryTypeOption
                    type="Billable"
                    isSelected={selectedType === 'Billable'}
                    onSelect={handleSelectType}
                />
                <DeliveryTypeOption
                    type="GIS Node"
                    isSelected={selectedType === 'GIS Node'}
                    onSelect={handleSelectType}
                />
            </BottomPopup>
        </View>
    );
}

export default BottomPopupDeliverables;

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.white,
    },
    header: {
        alignItems: 'center',
    },
    handle: {
        width: ms(150),
        height: ms(10),
        backgroundColor: Colors.darkBlue,
        borderBottomEndRadius: ms(8),
        borderBottomStartRadius: ms(8),
    },
    title: {
        fontSize: ms(16),
        fontWeight: 'bold',
        marginTop: ms(30),
        marginBottom: ms(5),
        marginHorizontal: ms(12),
        color: Colors.darkBlue,
    },
    dividerView: {
        height: ms(1),
        backgroundColor: Colors.dailyProgressItemBg,
        marginHorizontal: ms(12),
        marginVertical: ms(10)
    },
    optionContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: ms(15),
        paddingHorizontal: ms(12),
        borderBottomWidth: ms(3),
        borderBottomColor: Colors.dailyProgressItemBg,
    },
    optionText: {
        fontSize: ms(16),
        color: Colors.darkBlue,
    },
});

