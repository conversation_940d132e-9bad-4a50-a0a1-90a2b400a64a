import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet } from 'react-native';
import Colors from '../../../utils/Colors/Colors';
import Strings from '../../../utils/Strings/Strings';
import CalenderIcon from '../../../assets/svg/calendar.svg';
import BottomPopup from '../../../components/BottomPopup';
import TopTabNavigator from '../../../components/TopTabNavigator';
import TextWithLabel from '../../../components/TextWithLabel';
import { ms } from '../../../utils/Scale/Scaling';


const GisDetailsPipeHistoryBottomPopup: React.FC = () => {
    const [date, setDate] = useState('07/4/2025');
    const [fromLength, setFromLength] = useState('');
    const [actualLength, setActualLength] = useState('');
    const [manDays, setManDays] = useState('');
    const [tab, setTab] = useState<'dailyProgress' | 'hindrance'>('dailyProgress');

    const handleDateChange = (newDate: string) => {
        setDate(newDate);
    };

    return (
        <View>
            <BottomPopup>
                <Text style={styles.title}>{Strings.DailyProgress.gisNodeDetails}</Text>
                <View style={styles.dividerView} />

                <View style={styles.tabContainer}>
                    <TopTabNavigator
                        tabs={[
                            { key: 'dailyProgress', label: Strings.DailyProgress.dailyProgress},
                            { key: 'hindrance', label: Strings.DailyProgress.hindarnce }
                        ]}
                        activeTab={tab}
                        onTabChange={(key) => setTab(key as 'dailyProgress' | 'hindrance')}
                    />
                </View>
                <View style={styles.formContainer}>
                    <View style={[styles.inputContainer, { marginTop: 5, }]}>
                        <Text style={styles.label}>{Strings.DailyProgress.date}</Text>
                        <View style={styles.dateInputContainer}>
                            <Text style={styles.dateInput}>{Strings.DailyProgress.dummyDate}</Text>
                            <TouchableOpacity style={styles.calendarIcon}>
                                <CalenderIcon />
                            </TouchableOpacity>
                        </View>
                    </View>
                    <TextWithLabel
                        containerStyle={styles.inputContainer}
                        label={Strings.DailyProgress.fromLength}
                        labelStyle={styles.label}
                        inputvalue={Strings.DailyProgress.fromLength}
                        inputStyle={styles.input}
                    />
                    <TextWithLabel
                        containerStyle={styles.inputContainer}
                        label={Strings.DailyProgress.actualLength}
                        labelStyle={styles.label}
                        inputvalue={Strings.DailyProgress.actualLength}
                        inputStyle={styles.input}
                    />
                    <TextWithLabel
                        containerStyle={[styles.inputContainer, { marginBottom: 30 }]}
                        label={Strings.DailyProgress.manDays}
                        labelStyle={styles.label}
                        inputvalue={Strings.DailyProgress.manDays}
                        inputStyle={styles.input}
                    />
                </View>
            </BottomPopup>
        </View>
    );
};

export default GisDetailsPipeHistoryBottomPopup;

const styles = StyleSheet.create({
    title: {
        fontSize: ms(16),
        fontWeight: '700',
        marginTop: ms(30),
        marginBottom: ms(5),
        marginHorizontal: ms(12),
        color: Colors.textPrimary,
    },
    dividerView: {
        height: ms(2),
        backgroundColor: Colors.dailyProgressItemBg,
        marginHorizontal: ms(12),
        marginVertical: ms(10)
    },
    formContainer: {
        margin: ms(12),
    },
    inputContainer: {
        marginBottom: ms(15),
    },
    label: {
        fontSize: ms(14),
        fontWeight: '500',
        color: Colors.pipeIdTextBlack,
        marginBottom: ms(5),
    },
    input: {
        borderWidth: ms(1),
        borderColor: Colors.searchBorderGrey,
        backgroundColor: Colors.containerligetBlue,
        borderRadius: ms(5),
        padding: ms(10),
        fontSize: ms(14),
        color: Colors.textPrimary,
    },
    dateInputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: ms(1),
        borderColor: Colors.searchBorderGrey,
        backgroundColor: Colors.containerligetBlue,
        borderRadius: ms(5),
    },
    dateInput: {
        flex: 1,
        padding: ms(10),
        fontSize: ms(14),
        color: Colors.textPrimary,
    },
    calendarIcon: {
        marginHorizontal: ms(10),
    },
    tabRow: {
        flexDirection: 'row',
        margin: ms(12),
        backgroundColor: Colors.tabBgColoor,
        borderRadius: ms(8),
        borderColor: Colors.tabBgColoor,
        paddingHorizontal: ms(5),
        paddingVertical: ms(5),
    },
    tabContainer: {
        backgroundColor: Colors.tabBgColoor,
        borderRadius: ms(8),
        margin: ms(12),
    },
});

