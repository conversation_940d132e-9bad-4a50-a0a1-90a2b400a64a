import React, { useMemo, useState } from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import Colors from '../../../utils/Colors/Colors';
import Strings from '../../../utils/Strings/Strings';
import TopTabNavigator from '../../../components/TopTabNavigator';
import BottomPopup from '../../../components/BottomPopup';
import ProgressDetailsConsolidated from '../../../database/model/ProgressDetailsConsolidated';
import ProgressUpdateEngineer from '../../../database/model/ProgressUpdateEngineer';
import { ms } from '../../../utils/Scale/Scaling';

interface JobDetail {
    isShowScTab?: Boolean;
    progressConsolidatedData?: ProgressDetailsConsolidated | null;
    progressEngineerData?: ProgressUpdateEngineer | null;
    visible: boolean;
    onClose: () => void;
}

interface DetailRowProps {
    label: string;
    value: string | number | null | undefined;
    isPercentage?: boolean;
}

const { width } = Dimensions.get('window');

type TabType = 'del' | 'engineer' | 'sc';

const DelEngineerScBottomPopup: React.FC<JobDetail> = ({ isShowScTab,
    progressConsolidatedData,
    progressEngineerData,
    visible,
    onClose,
}) => {
    const [tab, setTab] = useState<TabType>('del');

    const tabs = useMemo(() => [
        { key: 'del', label: Strings.DailyProgress.del },
        { key: 'engineer', label: Strings.DailyProgress.engineer },
        ...(isShowScTab ? [{ key: 'sc', label: Strings.DailyProgress.sc }] : [])
    ], [isShowScTab]);

    const DetailRow: React.FC<DetailRowProps> = ({ label, value, isPercentage = false }) => (
        <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>{label}</Text>
            <Text style={styles.detailValue}>
                {value ? (isPercentage ? `${Number(value).toFixed(2)}%` : value) : '-'}
            </Text>
        </View>
    );

    const renderJobTypeButtons = () => (
        <View style={styles.tabContainer}>
            <TopTabNavigator
                tabs={tabs}
                activeTab={tab}
                onTabChange={(key) => setTab(key as TabType)}
            />
        </View>
    );

    const renderDelDetails = () => (
        <View style={styles.jobDetailsWrapper}>
            <View style={styles.jobDetailsContainer}>
                <DetailRow
                    label={Strings.DailyProgress.scope}
                    value={progressConsolidatedData?.delivscope}
                />
                <DetailRow
                    label={Strings.DailyProgress.cumPlanQty}
                    value={progressConsolidatedData?.cumPlanningQuantity}
                />
                <DetailRow
                    label={Strings.DailyProgress.cumProgress}
                    value={progressConsolidatedData?.cumProg}
                    isPercentage
                />
                <DetailRow
                    label={Strings.DailyProgress.cumManday}
                    value={progressConsolidatedData?.cumManday}
                />
                <View style={styles.dividerView} />
                <DetailRow
                    label={Strings.DailyProgress.ftmPlanQty}
                    value={progressConsolidatedData?.ftmPlanningQuantity}
                />
                <DetailRow
                    label={Strings.DailyProgress.ftmProgress}
                    value={progressConsolidatedData?.ftmProgress}
                    isPercentage
                />
                <DetailRow
                    label={Strings.DailyProgress.ftmManday}
                    value={progressConsolidatedData?.ftmManday}
                />
            </View>
        </View>
    );

    const renderEngineerDetails = () => (
        <View style={styles.jobDetailsWrapper}>
            <View style={styles.jobDetailsContainer}>
                <DetailRow
                    label={Strings.DailyProgress.scope}
                    value={progressEngineerData?.delivscope}
                />
                <DetailRow
                    label={Strings.DailyProgress.cumPlanQty}
                    value={progressEngineerData?.cumTargetPlanQty}
                />
                <DetailRow
                    label={Strings.DailyProgress.cumProgress}
                    value={progressEngineerData?.cumProg}
                    isPercentage
                />
                <DetailRow
                    label={Strings.DailyProgress.cumManday}
                    value={progressEngineerData?.cumManday}
                />
                <View style={styles.dividerView} />
                <DetailRow
                    label={Strings.DailyProgress.ftmPlanQty}
                    value={progressEngineerData?.ftmCumTargetPlanQty}
                />
                <DetailRow
                    label={Strings.DailyProgress.ftmProgress}
                    value={progressEngineerData?.ftmProgress}
                    isPercentage
                />
                <DetailRow
                    label={Strings.DailyProgress.ftmManday}
                    value={progressEngineerData?.ftmManday}
                />
            </View>
        </View>
    );

    const renderScDetails = () => (
        <View style={styles.jobDetailsWrapper}>
            <View style={styles.jobDetailsContainer}>
                <DetailRow
                    label={Strings.DailyProgress.scope}
                    value={progressConsolidatedData?.scScopeE}
                />
                <DetailRow
                    label={Strings.DailyProgress.cumProgress}
                    value={progressConsolidatedData?.scProgress}
                    isPercentage
                />
                <DetailRow
                    label={Strings.DailyProgress.cumManday}
                    value={progressConsolidatedData?.scManday}
                />
            </View>
        </View>
    );

    const renderJobDetails = () => {
        const tabContent = {
            del: renderDelDetails,
            engineer: renderEngineerDetails,
            sc: renderScDetails,
        };
        return tabContent[tab]();
    };

    return (
        <View style={styles.container}>
            {/* <TouchableOpacity
                style={styles.overlay}

                onPress={() => {
                    if (onClose) onClose();
                }}
            /> */}
            <BottomPopup 
            visible={visible} 
            onCancelPress={onClose}
            minHeight={ms(420)}>
                <Text style={styles.title}>{Strings.DailyProgress.jobDetails}</Text>
                <View style={[styles.dividerView, { marginHorizontal: ms(15) }]} />
                {renderJobTypeButtons()}
                {renderJobDetails()}
            </BottomPopup>
        </View>
    );
};

export default DelEngineerScBottomPopup;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        position: 'relative',
        justifyContent: 'flex-end',
    },
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    title: {
        fontSize: ms(16),
        fontWeight: '700',
        marginBottom: ms(10),
        marginTop: ms(15),
        marginHorizontal: ms(12),
        color: Colors.textPrimary,
    },
    dividerView: {
        height: ms(1),
        backgroundColor: Colors.searchBorderGrey,
        marginVertical: ms(10),

    },
    jobDetailsWrapper: {
        height: ms(320),
        justifyContent: 'flex-start',
    },
    jobDetailsContainer: {
        backgroundColor: Colors.containerligetBlue,
        borderRadius: ms(8),
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(4),
        paddingHorizontal: ms(10),
        paddingVertical: ms(10),
        marginHorizontal: ms(15),
        marginBottom: ms(40),
        marginTop: ms(5),
    },
    detailRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: ms(5),
    },
    detailLabel: {
        fontSize: ms(14),
        color: Colors.textInputBlack,
        fontWeight: '500',
        width: (width - 60) / 2,
    },
    detailValue: {
        fontSize: ms(14),
        fontWeight: '500',
        color: Colors.textPrimary,
        alignItems: 'flex-start',
        width: (width - 60) / 2,
    },
    tabContainer: {
        marginHorizontal: ms(15),
        marginBottom: ms(10),
        marginTop: ms(5),
        backgroundColor: Colors.tabBgColoor,
        borderRadius: ms(8),
    },
});

