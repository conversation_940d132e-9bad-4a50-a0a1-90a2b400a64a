import React, { useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import Colors from '../../../utils/Colors/Colors';
import Strings from '../../../utils/Strings/Strings';
import Edit from '../../../assets/svg/edit.svg'
import BottomPopup from '../../../components/BottomPopup';
import { ms } from '../../../utils/Scale/Scaling';

const CIRCLE_SIZE = 24;
const LINE_HEIGHT = 30;

interface jobPathItem {
    id: number;
    title: string;
}

interface EditPathOptionsPopupProps {
    pathSegments?: string[];
    onEditPath?: () => void;
    visible: boolean
    onClose: () => void;
}

const EditPathOptionsPopup: React.FC<EditPathOptionsPopupProps> = ({ pathSegments, onEditPath, visible, onClose }) => {
    const jobPath: jobPathItem[] = pathSegments && pathSegments.length > 0
        ? pathSegments.map((title, idx) => ({ id: idx + 1, title })): []


    const renderItem = ({ item, index }: { item: jobPathItem; index: number }) => {
        const isLastItem = index === jobPath.length - 1;
    
        return (
          <View style={styles.itemContainer}>
            <View style={styles.leftColumn}>
              <View style={styles.circle}>
                <Text style={styles.numberText}>{item.id}</Text>
              </View>
              {!isLastItem && <View style={styles.verticalLine} />}
            </View>
            <Text style={styles.itemText}>{item.title}</Text>
          </View>
        );
      };

    return (
        <View>
             <BottomPopup visible={visible} onCancelPress={onClose}>
                <View style={styles.titleRow}>
                    <Text style={styles.title}>{Strings.DailyProgress.jobPath}</Text>
                    <TouchableOpacity style={styles.editViewStyle} onPress={onEditPath}>
                        <Edit />
                        <Text style={[styles.title, { marginLeft: 10 }]}>{Strings.DailyProgress.edit}</Text>
                    </TouchableOpacity>
                </View>
                <View style={styles.dividerView} />

                <FlatList
                    data={jobPath}
                    renderItem={renderItem}
                    keyExtractor={(item) => item.id}
                    contentContainerStyle={styles.listContent}
                />

            </BottomPopup>
        </View>
    );
};

export default EditPathOptionsPopup;

const styles = StyleSheet.create({
    titleRow: {
        flexDirection: 'row',
        marginHorizontal: ms(12),
        marginTop: ms(30),
        marginBottom: ms(5),
        justifyContent: 'space-between'
    },
    title: {
        fontSize: ms(16),
        fontWeight: '700',
        color: Colors.textPrimary,
    },
    dividerView: {
        height: ms(2),
        backgroundColor: Colors.dailyProgressItemBg,
        marginHorizontal: ms(12),
        marginVertical: ms(10)
    },
    listContent: {
        paddingBottom: ms(16),
    },
    itemContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: ms(16),
        margin: ms(12)
    },
    numberText: {
        fontSize: ms(14),
        fontWeight: '500',
        color: Colors.secondary,
    },
    itemText: {
        fontSize: ms(16),
        color: Colors.textPrimary,
        fontWeight: '500',
    },
    leftColumn: {
        width: CIRCLE_SIZE,
        alignItems: 'center',
        position: 'relative',
        marginRight: ms(12),
    },
    circle: {
        width: CIRCLE_SIZE,
        height: CIRCLE_SIZE,
        borderRadius: CIRCLE_SIZE / 2,
        backgroundColor: Colors.verticalLineColor,
        justifyContent: 'center',
        alignItems: 'center',
    },
    verticalLine: {
        width: ms(2),
        height: LINE_HEIGHT,
        backgroundColor: Colors.verticalLineColor,
        position: 'absolute',
        top: CIRCLE_SIZE,
    },
    editViewStyle: { 
        flexDirection: 'row', 
        alignItems: 'center' 
    }
});

