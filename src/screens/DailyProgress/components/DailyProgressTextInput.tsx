import React from "react";
import Colors from "../../../utils/Colors/Colors";
import { View, Text, TextInput, StyleSheet } from "react-native";
import { ms } from "../../../utils/Scale/Scaling";

interface InputFieldProps {
    customStyle?: any;
    label: string;
    value: string;
    onChangeText: (text: string) => void;
    isKeypadNumeric?: boolean;
    isMultiline?: boolean;
}

const DailyProgressTextInput: React.FC<InputFieldProps> = ({ customStyle, label, value, onChangeText, isKeypadNumeric, isMultiline }) => {
    return (
        <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{label}</Text>
            <TextInput
                style={customStyle || styles.textInput}
                value={value}
                onChangeText={onChangeText}
                placeholder="Enter"
                placeholderTextColor={Colors.primary}
                keyboardType={isKeypadNumeric ? 'numeric' : 'default'}
                multiline={isMultiline}
            />
        </View>
    );
};

export default DailyProgressTextInput;
const styles = StyleSheet.create({
    inputContainer: {
        marginVertical: ms(3),
    },
    inputLabel: {
        fontSize: ms(14),
        marginBottom: ms(3),
        fontWeight: '400',
        color: Colors.pipeIdTextBlack,
    },
    textInput: {
        backgroundColor: Colors.containerligetBlue,
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
        padding: ms(12),
        borderRadius: ms(8),
        color: Colors.primary,
        fontSize: ms(14),
    },
})