import React from "react";
import Colors from "../../../utils/Colors/Colors";
import { View, Text, TextInput, StyleSheet, Pressable } from "react-native";
import { ms } from "../../../utils/Scale/Scaling";
import { useTranslation } from "react-i18next";
import { AppFonts } from "../../../components/Fonts";

interface InputFieldProps {
    customStyle?: any;
    label: string;
    value: string | number;
    onChangeText: (text: string) => void;
    isKeypadNumeric?: boolean;
    isMultiline?: boolean;
    hasRemarkHistory?: boolean;
    editable?: boolean;
}

const DailyProgressTextInput: React.FC<InputFieldProps> = ({ customStyle, label, value, onChangeText, isKeypadNumeric, isMultiline, hasRemarkHistory, editable}) => {
    const {t} = useTranslation();
    
    return (
        <View style={styles.inputContainer}>
            <View style={styles.inputLabelContainer}>
                <Text style={styles.inputLabel}>{label}</Text>
                {hasRemarkHistory && (
                    <Pressable>
                        <Text style={styles.historyButtonText}>{t('hindranceStrings.viewRemarks')}</Text>
                    </Pressable>
                )}
            </View>
            <TextInput
                style={customStyle || styles.textInput}
                value={value}
                onChangeText={onChangeText}
                placeholder="Enter"
                placeholderTextColor={Colors.textInputBlack}
                keyboardType={isKeypadNumeric ? 'numeric' : 'default'}
                multiline={isMultiline}
                editable={editable}
            />
        </View>
    );
};

export default DailyProgressTextInput;
const styles = StyleSheet.create({
    inputContainer: {
        marginVertical: ms(4),
    },
    inputLabel: {
        fontSize: ms(14),
        marginBottom: ms(3),
        fontWeight: '400',
        color: Colors.pipeIdTextBlack,
        marginTop: ms(5),
    },
    textInput: {
        backgroundColor: Colors.containerligetBlue,
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
        padding: ms(12),
        borderRadius: ms(8),
        color: Colors.primary,
        fontSize: ms(14),
        marginTop: ms(2),
        fontFamily: 'MNMedium',
    },
    inputLabelContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    historyButtonText: {
        color: Colors.blue,
        fontSize: ms(14),
        fontFamily: AppFonts.Medium,
        textDecorationLine: 'underline'
        
    },
})