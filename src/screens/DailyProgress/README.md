# DailyProgress Module

This module provides the daily progress tracking functionality for the application. It allows users to view, update, and manage daily work progress, including handling pending approvals and bookmarks for quick access.

## Directory Structure

- **DailyProgressScreen.tsx**: Main entry point. Displays a tabbed view for recent and pending progress items, with filtering and search.
- **DailyProgressDetailsScreen.tsx**: Detailed view for entering or updating progress for a selected WBS/task, including attachments and geolocation.
- **ProgressUpdateScreen.tsx**: Handles progress update logic and UI.
- **BookmarksScreen.tsx**: Displays and manages bookmarked progress items.
- **LastUpdateScreen.tsx**: Shows the last update history for a WBS/task.
- **MapViewScreen.tsx**: Map-based view for geolocated progress items.
- **PipeIdHistoryScreen.tsx**: Displays pipe ID history (for GIS-related tasks).
- **components/**: Custom UI components (inputs, popups, etc.) used across the module.
- **Helper/**: Utility and helper functions for filtering, bookmarks, and data management.

## Features

- Tabbed navigation for recent and pending progress items.
- Filtering and searching of WBS/tasks.
- Detailed progress entry with support for:
  - Attachments (images, files)
  - Geolocation
  - Custom fields (quantity, man days, remarks, etc.)
- Bookmarking for quick access to frequently updated items.
- Offline support for progress updates (data is stored locally if offline).
- Integration with Redux for state management and WatermelonDB for persistence.

## Usage

1. **Entry Point**: Use the `DailyProgressView` component as the main screen for daily progress tracking.
2. **Navigation**: Select a WBS/task from the list to navigate to the details screen (`DailyProgressDetailsScreen`) for entering or updating progress.
3. **Tabs**: Switch between 'Recent List' and 'Pending For Approval' tabs to view and manage different sets of progress items.
4. **Bookmarks**: Use the bookmark feature to quickly access and update frequently used items.
5. **Offline Updates**: If offline, progress updates are stored locally and synced when connectivity is restored.

## Dependencies
- React Native
- Redux
- WatermelonDB
- React Navigation

## Notes
- Ensure the database is properly initialized and synced for offline support.
- Custom components and helpers are modular and reusable across the app.

---

For detailed implementation, refer to the respective files and components in this directory. 