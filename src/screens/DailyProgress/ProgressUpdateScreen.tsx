import React, { useEffect, useState, useMemo } from 'react';
import { View, StyleSheet, FlatList, Alert, Image, Touchable, TouchableOpacity, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import ProgressItem from '../../components/ProgressItem';
import Sidebar from '../../components/Sidebar';
import AppHeader from '../../components/AppHeader';
import Strings from '../../utils/Strings/Strings';
import Colors from '../../utils/Colors/Colors';
import SearchComponent from '../../components/SearchComponent';
import { database } from '../../database/index';
import { Q } from '@nozbe/watermelondb';
import { useDispatch, useSelector } from 'react-redux'; // For accessing currentJobId from Redux (or however you store it)
import { RootState } from '../../redux/Root/rootStore';
import { EntityConstants } from '../../model/DailyProgress/DailyProgressData';
import WBSJob from '../../database/model/WBSJob';
import WBSTask from '../../database/model/WBSTask';
import { ms } from '../../utils/Scale/Scaling';
import { StoreWbsTaskDataUtil } from '../../database/WBSDataInsert/StoreWbsTaskData';

interface WBSItem {
  id: string;
  entity_Code: string;
  job_Code: string;
  entity_Type: string;
  leaf_Node_Tag: string;
  entity_Description: string;
  parent_WBS_Code: string | null;
  parent_entity_code?: string;
  parent_Task?: string;
  gis_Tag?: string;
  isChild?: boolean;
  et_Code?: string | undefined;
};

interface GISNodeRecord {
  entity_Code: string;
  job_Code: string;
  description: string;
  Parent_Entity_Code: string;
  Parent_Task: string;
};

interface WBSDetailsRecord {
  WBS: string;
  JobCode: string;
  // Add other fields as needed
}

const ProgressUpdateScreen: React.FC = () => {
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const [selectedItems, setSelectedItems] = useState<WBSItem[]>([]);
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');
  const [wbsItems, setWbsItems] = useState<WBSItem[]>([]);
  const [wbsHistory, setWbsHistory] = useState<WBSItem[][]>([]);
  const [currentLeafItem, setCurrentLeafItem] = useState<WBSItem | null>(null);
  const [storedDetails, setStoredDetails] = useState<{
    inputDetails: any;
    wbsPath: string;
    selectedItem: WBSItem;
    parentItems: any[];
    fromWhere: string;
  } | null>(null);

  const { currentJobId } = useSelector((state: RootState) => state.home);

  useEffect(() => {
    if (currentJobId) {
      loadWBSItems(currentJobId);
    }
  }, [currentJobId]);

  useEffect(() => {
    console.log('🔁 [useEffect] wbsHistory updated:', wbsHistory);
    if (wbsHistory.length > 0) {
      setWbsItems(wbsHistory[wbsHistory.length - 1]);
    } else {
      setWbsItems([]); // <-- Clear the UI
    }
  }, [wbsHistory]);


  const loadWBSItems = async (jobId: string, parentCode: string = jobId) => {
    try {
      const items = await database
        .get('WBSJob')
        .query(
          Q.where('Job_Code', jobId),
          Q.where('Parent_WBS_Code', parentCode),
          Q.where('ET_Code', Q.notEq('PR')),
          Q.where('Is_Active', 'Y')
        )
        .fetch();

      const formattedItems: WBSItem[] = items.map(item => {
        const raw = item._raw as unknown as WBSJob;
        return {
          id: item.id,
          entity_Code: raw.WBS_Code,
          job_Code: raw.Job_Code,
          entity_Type: raw.entity_Type,
          leaf_Node_Tag: raw.Leaf_Node_Tag,
          entity_Description: raw.WBS_Description,
          parent_WBS_Code: raw.Parent_WBS_Code,
          et_Code: raw.ET_Code,
        };
      });
      setWbsItems([]); // clear
      setWbsItems(formattedItems);
    } catch (error) {
      console.error('ProgressUpdate -- Error loading WBS items:', error);
    }
  };

  const handleItemClick = async (item: WBSItem) => {
    const isAlreadySelected = selectedItems.find(i => i.entity_Code === item.entity_Code);

    if (!isAlreadySelected) {
      const currentWbsItems = [...wbsItems];
      setWbsHistory(prev => [...prev, currentWbsItems]);
      setSelectedItems(prev => [...prev, item]);
    }

    setSidebarOpen(true);
    console.log('handleItemClick -- item.entity_Type: ', item.entity_Type, ' -- item.leaf_Node_Tag: ', item.leaf_Node_Tag);

    try {
      if ((item.entity_Type === EntityConstants.WBSJob && item.leaf_Node_Tag === EntityConstants.Y) ||
        item.entity_Type === EntityConstants.WBSTask) {
        // setWbsItems([]);
        await GotoWBSTask(item);
      } else if (item.entity_Type === EntityConstants.WBSJob && item.leaf_Node_Tag !== EntityConstants.Y) {
        const childItems = await database
          .get('WBSJob')
          .query(
            Q.where('Job_Code', item.job_Code),
            Q.where('Parent_WBS_Code', item.entity_Code),
            Q.where('ET_Code', Q.notEq('PR')),
            Q.where('Is_Active', 'Y')
          )
          .fetch();

        console.log('childItems.length: ', childItems.length);

        if (childItems.length > 0) {
          const formattedChildItems = childItems.map(child => {
            const raw = child._raw as unknown as WBSJob;
            return {
              id: child.id,
              entity_Code: raw.WBS_Code,
              job_Code: raw.Job_Code,
              entity_Type: raw.entity_Type,
              leaf_Node_Tag: raw.Leaf_Node_Tag,
              entity_Description: raw.WBS_Description,
              parent_WBS_Code: raw.Parent_WBS_Code,
              et_Code: raw.ET_Code,
              isChild: true,
            };
          });
          setWbsItems(formattedChildItems);
        } else {
          await GotoWBSTask(item);
        }
      } else if (item.entity_Type === EntityConstants.GISNode) {
        /// This should be handled when GIS Details is selected
        console.log('handleWBSSelection -- GIS node handling not implemented yet');
      }
    } catch (error) {
      console.error('Error handling WBS selection:', error);
    }
  };


  const GotoWBSTask = async (item: WBSItem) => {
    try {
      console.log('GotoWBSTask ');

      // try {
      //   const allTasks = await database.get('WBSTask').query().fetch();
      //   console.log('All WBSTask records in DB:', allTasks.map(task => task._raw));
      // } catch (error) {
      //   console.error('Error fetching all WBSTask records:', error);
      // }

      // try {
      //   console.log('Fetching WBSTask...');
      //   const inputDetailCollection = database.get('WBSTask');
      //   const records = await inputDetailCollection.query().fetch();

      //   if (records.length === 0) {
      //     console.log('No records found in WBSTask');
      //   } else {
      //     console.log('InputDetails records:', records.length);
      //   }
      // } catch (error) {
      //   console.error('Error fetching InputDetails:', error);
      // }

      // let taskQuery = database.get('WBSTask').query(
      //   Q.where('Is_Active', 'Y'),
      //   Q.where('Job_Code', item.job_Code),
      //   Q.where('Parent_Task_Code', item.entity_Code),
      //   Q.where('Parent_WBS', item.entity_Code)
      // );

      // // if (item.parent_WBS_Code) {
      // //   taskQuery = taskQuery.extend(Q.where('parent_WBSCode', item.parent_WBS_Code));
      // // }
      // // if (item.parent_Task) {
      // //   taskQuery = taskQuery.extend(Q.where('parent_Task', item.parent_Task));
      // // }
      // const tasks = await taskQuery.fetch();

      const taskQuery = await database
        .get('WBSTask')
        .query(
          Q.where('Is_Active', 'Y'),
          Q.where('Job_Code', item.job_Code),
          Q.where('Parent_Task_Code', item.entity_Code),
          Q.where('Parent_WBS', item.entity_Code)
        ).fetch();

      console.log('GotoWBSTask -- Tasks found:', taskQuery.length, ' -- item.job_Code: ', item.job_Code, ' -- item.entity_Code for Parent_Task_Code in db : ', item.entity_Code, ' taskQuery: ', taskQuery.map(t => t._raw));
      if (taskQuery && taskQuery.length > 0) {
        // Navigate to DPR input entry

        // const formattedTasks = tasks.map(task => {
        //   const raw = task._raw as unknown as WBSTask;
        //   return {
        //     id: task.id,
        //     entity_Code: raw.taskCode,
        //     job_Code: raw.jobCode,
        //     entity_Type: EntityConstants.WBSTask,
        //     // leaf_Node_Tag: raw.leafNodeTag,
        //     entity_Description: raw.taskDescription,
        //     parent_WBS_Code: raw.Parent_WBS_Code,
        //     et_Code: raw.etCode,
        //     isChild: true
        //   };
        // });
        // setWbsItems(formattedTasks);


        const formattedChildItems: WBSItem[] = taskQuery.map(task => {
          const wbsTask = task as WBSTask;
          return {
            id: wbsTask.id,
            entity_Code: wbsTask.taskCode,
            job_Code: wbsTask.jobCode,
            entity_Type: EntityConstants.WBSTask,
            leaf_Node_Tag: 'Y',
            entity_Description: wbsTask.taskDescription,
            parent_WBS_Code: wbsTask.parentWbs,
            et_Code: wbsTask.etCode,
            isChild: true,
          };
        });
        setWbsItems(formattedChildItems);
        console.log('formattedChildItems: ', formattedChildItems);
      } else {
        if (item.gis_Tag === 'Y' && item.entity_Code.includes('~')) {
          const gisNodes = await database
            .get('GISNode')
            .query(
              Q.where('Job_Code', item.job_Code),
              Q.where('Parent_Entity_Code', item.parent_entity_code || ''),
              Q.where('Parent_Task', item.parent_Task || '')
            )
            .fetch();

          if (gisNodes.length > 0) {
            const formattedNodes = gisNodes.map(node => {
              const raw = node._raw as unknown as GISNodeRecord;
              return {
                id: node.id,
                entity_Code: raw.entity_Code,
                job_Code: raw.job_Code,
                entity_Type: EntityConstants.GISNode,
                leaf_Node_Tag: 'N',
                entity_Description: raw.description,
                parent_WBS_Code: null,
                isChild: true
              };
            });
            setWbsHistory(prev => [...prev, wbsItems]);
            setWbsItems(formattedNodes);
          } else {
            Alert.alert('No Plan Found', 'No plan has been found for this item.');
          }
        } else {
          setCurrentLeafItem(item);
          setWbsItems([]);
          await GetInputDetail([item]);
        }
      }
    } catch (error) {
      console.error('Error in GotoWBSTask:', error);
    }
  };

  const GetInputDetail = async (entityWBS: WBSItem[]) => {
    console.log('GetInputDetail -- entityWBS:', entityWBS.length);
    try {
      // Get the last node from the WBS hierarchy
      const lastNode = entityWBS[entityWBS.length - 1];

      console.log('GetInputDetail -- Starting with last node:', {
        entity_Code: lastNode.entity_Code,
        job_Code: lastNode.job_Code,
        parent_WBS_Code: lastNode.parent_WBS_Code,
        et_Code: lastNode.et_Code,
      });

      // Get all parent WBS items recursively
      const getAllParentItems = async (jobCode: string, parentCode: string | null): Promise<WBSJob[]> => {
        if (!parentCode) return [];

        const parent = await database
          .get('WBSJob')
          .query(
            Q.where('Job_Code', jobCode),
            Q.where('WBS_Code', parentCode)
          )
          .fetch();

        if (parent.length === 0) return [];

        const raw = parent[0]._raw as unknown as WBSJob;
        const grandParents = await getAllParentItems(jobCode, raw.Parent_WBS_Code);
        return [...grandParents, raw];
      };

      // Get all parent items
      const parentItems = await getAllParentItems(entityWBS[0].job_Code, lastNode.parent_WBS_Code);

      console.log('GetInputDetail -- Parent items found:', parentItems.length);
      if (parentItems.length > 0) {
        console.log('GetInputDetail -- First parent item:', parentItems[0]);
      }

      // Construct the full WBS path including all parent codes
      const wbsPath = [];

      // Add all parent codes to the path
      for (const parent of parentItems) {
        console.log('GetInputDetail -- Processing parent:', {
          WBS_Code: parent.WBS_Code,
          ET_Code: parent.ET_Code,
          Parent_WBS_Code: parent.Parent_WBS_Code,
        });
        wbsPath.push(parent.WBS_Code);
      }

      // Add the current node's code
      wbsPath.push(lastNode.entity_Code);

      // Join all codes with '~'
      const wbs = wbsPath.join('~');

      console.log('GetInputDetail -- Final WBS path:', {
        wbsPath,
        joinedWBS: wbs,
        lastNodeET_Code: lastNode.et_Code,
      });

      // First try to get an exact match
      let inputList = await database
        .get('WBSDetails')
        .query(
          Q.where('JobCode', entityWBS[0].job_Code),
          Q.where('WBS', wbs)
        )
        .fetch();

      // If no exact match, try to find a match with the last node's code
      if (inputList.length === 0) {
        inputList = await database
          .get('WBSDetails')
          .query(
            Q.where('JobCode', entityWBS[0].job_Code),
            Q.where('WBS', Q.like(`%${lastNode.entity_Code}%`))
          )
          .fetch();
      }

      console.log('GetInputDetail -- Query results:', {
        found: inputList.length,
        firstResult: inputList[0]?._raw,
        queryParams: {
          jobCode: entityWBS[0].job_Code,
          wbs,
          task: lastNode.entity_Code,
        }
      });

      if (inputList && inputList.length > 0) {
        console.log('GetInputDetail -- Found matching items -- inputList.length:', inputList.length);
        console.log('GetInputDetail -- inputList full data:', inputList.map(item => item._raw));

        // Find the most specific match (exact WBS match)
        const exactMatch = inputList.find(item => (item._raw as unknown as WBSDetailsRecord).WBS === wbs);
        const matchingItem = exactMatch || inputList[0];

        // Store the details instead of navigating
        setStoredDetails({
          inputDetails: matchingItem._raw,
          wbsPath: wbs,
          selectedItem: lastNode,
          parentItems: parentItems.map(item => ({
            WBS_Code: item.WBS_Code,
            WBS_Description: item.WBS_Description,
            ET_Code: item.ET_Code
          })),
          fromWhere: 'ProgressUpdate',
        });
      } else {
        Alert.alert('No Plan Found', 'No plan has been found for this item.');
      }
    } catch (error) {
      console.error('Error in GetInputDetail:', error);
      Alert.alert('Error', 'An error occurred while getting input details.');
    }
  };

  // Add new function to handle navigation when item is clicked again
  const handleItemClickAgain = () => {
    console.log('storedDetails:', storedDetails);
    if (storedDetails) {
      navigation.navigate('DailyProgressDetailsView', storedDetails);

    }
  };

  const handleSidebarItemClick = async (itemToRemove: WBSItem) => {
    const index = selectedItems.findIndex(i => i.entity_Code === itemToRemove.entity_Code);
    if (index === -1) return;

    // Keep items before the clicked one
    const newSelectedItems = selectedItems.slice(0, index);
    const newWbsHistory = wbsHistory.slice(0, index);

    console.log('SidebarClick -- itemToRemove:', itemToRemove);
    console.log('SidebarClick -- selectedItems:', selectedItems.map(i => i.entity_Code));
    console.log('SidebarClick -- index:', index);
    console.log('SidebarClick -- newSelectedItems length:', newSelectedItems.length);
    console.log('SidebarClick -- newWbsHistory length:', newWbsHistory.length);

    // Update states first to prevent race conditions
    setSelectedItems(newSelectedItems);
    setWbsHistory(newWbsHistory);

    try {
      if (newSelectedItems.length === 0) {
        // If removing all items, load initial items
        setSidebarOpen(false);
        setCurrentLeafItem(null); // Reset current leaf item
        setWbsItems([]); // Clear current items before loading new ones
        await loadWBSItems(itemToRemove.job_Code);
      } else {
        // Get the last selected item
        const lastSelected = newSelectedItems[newSelectedItems.length - 1];

        // If the last selected item is a leaf node or task
        if ((lastSelected.entity_Type === EntityConstants.WBSJob && lastSelected.leaf_Node_Tag === EntityConstants.Y) ||
          lastSelected.entity_Type === EntityConstants.WBSTask) {
          setWbsItems([lastSelected]);
          setCurrentLeafItem(lastSelected);
        } else {
          // Load children of the last selected item
          const childItems = await database
            .get('WBSJob')
            .query(
              Q.where('Job_Code', lastSelected.job_Code),
              Q.where('Parent_WBS_Code', lastSelected.entity_Code),
              Q.where('ET_Code', Q.notEq('PR')),
              Q.where('Is_Active', 'Y')
            )
            .fetch();

          if (childItems.length > 0) {

            const formattedChildItems = childItems.map(child => {
              const raw = child._raw as unknown as WBSJob;
              return {
                id: child.id,
                entity_Code: raw.WBS_Code,
                job_Code: raw.Job_Code,
                entity_Type: raw.entity_Type,
                leaf_Node_Tag: raw.Leaf_Node_Tag,
                entity_Description: raw.WBS_Description,
                parent_WBS_Code: raw.Parent_WBS_Code,
                et_Code: raw.ET_Code,
                isChild: true
              };
            });
            setWbsItems(formattedChildItems);
            setCurrentLeafItem(null);
          } else {
            // If no children, show the last selected item
            setWbsItems([lastSelected]);
            setCurrentLeafItem(lastSelected);
          }
        }
      }
    } catch (error) {
      console.error('Error while going back to parent level:', error);
    }
  };

  const filteredItems = useMemo(() => {
    return wbsItems.filter(item =>
      item.entity_Description.toLowerCase().includes(search.toLowerCase())
      // item.entity_Description
    );
  }, [wbsItems, search]);

  // const handleLogs = async () => {
  //   await StoreWbsTaskDataUtil.logInsertedTaskDetails();
  // };

  return (
    <View style={styles.container}>
      <AppHeader
        title={Strings.DailyProgress.newProgressUpdate}
        onBookmarkPress={() => {
        }}
      />

      <SearchComponent customStyle={styles.search} value={search} onChange={setSearch} />

      {/* <TouchableOpacity style={styles.emptyImage} onPress={() => handleLogs()}>
        <Text>Log Task Records </Text>
      </TouchableOpacity> */}

      <View style={styles.row}>
        {/* {sidebarOpen && <Sidebar selectedItems={selectedItems.map(i => i.entity_Description)} />} */}
        {sidebarOpen && <Sidebar
          selectedItems={selectedItems}
          onItemPress={handleSidebarItemClick}
        />}

        <View style={styles.listContainer}>
          {/* <FlatList
            data={filteredItems}
            keyExtractor={(item) => item.entity_Code}
            renderItem={({ item }) => (
              <ProgressItem text={item.entity_Description} onClick={() => handleItemClick(item)} />
            )}
          /> */}

          <FlatList
            data={filteredItems.length > 0 ? filteredItems : currentLeafItem ? [currentLeafItem] : []}
            keyExtractor={item => item.entity_Code}
            renderItem={({ item }) => {
              console.log('entity_Description:', item.entity_Description, typeof item.entity_Description);
              return (
                <ProgressItem
                  text={typeof item.entity_Description === 'string' ? item.entity_Description : JSON.stringify(item.entity_Description)}
                  onClick={() => {
                    if (currentLeafItem && item.entity_Code === currentLeafItem.entity_Code) {
                      handleItemClickAgain();
                    } else {
                      handleItemClick(item);
                    }
                  }}
                />
              );
            }}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Image
                  source={require('../../assets/png/NoJobs.png')}
                  style={styles.emptyImage}
                  resizeMode="contain"
                />
              </View>
            }
          />

        </View>
      </View>
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.containerligetBlue,
  },
  search: {
    marginTop: ms(16),
    backgroundColor: Colors.white,
  },
  row: {
    flex: 1,
    flexDirection: 'row',
  },
  listContainer: {
    flex: ms(3),
    padding: ms(6),
    marginLeft: ms(16),
    marginRight: ms(16),
  },
  listContainerShrink: {
    flex: 2,
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyImage: {
    width: ms(100),
    height: ms(100),
  },
});

export default ProgressUpdateScreen;
