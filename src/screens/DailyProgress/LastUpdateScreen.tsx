import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, SafeAreaView, Modal, Image } from 'react-native';
import DirectionArrow from '../../assets/svg/direction_arrow.svg';
import AppHeader from '../../components/AppHeader';
import Strings from '../../utils/Strings/Strings';
import Colors from '../../utils/Colors/Colors';
import EditPathOptionsPopup from './components/EditPathOptionsBottomPopup';
import { NavigationProp, RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { formatDateRangeWithStartEndDate } from '../../utils/Constants/Validations';
import { ms } from '../../utils/Scale/Scaling';

type LastUpdateScreenParams = {
  historyData: {
    updateDate: string;
    uom: string;
    quantity: number;
    manDays: number;
  }[];
  plannedStartDate?: string;
  plannedEndDate?: string;
  actualStartDate?: string;
  actualEndDate?: string;
  fullPath?: string;
};

interface historyItem {
    updateDate: string;
    uom: string;
    quantity: number;
    manDays: number;
}

const LastUpdateScreen = () => {
    const [pathEditOptionsModalVisible, setPathEditOptionsModalVisible] = useState(false);
    const route = useRoute<RouteProp<Record<string, LastUpdateScreenParams>, string>>();
    const navigation = useNavigation<NavigationProp<any>>();

    // Use historyData from params if available, else fallback
    const historyData: historyItem[] = (route.params && (route.params as any).historyData);
    const fullPath = route.params?.fullPath || '';
    const pathSegments = fullPath.split(' / ').filter(Boolean);

    const renderHistoryItem = ({ item }: { item: historyItem }) => (
        <View style={styles.historyItem}>
            <View style={styles.historyHeader}>
                <Text style={styles.historyHeaderText}>Update Date</Text>
                <Text style={styles.updateDate}>{item.updateDate}</Text>
            </View>

            <View style={styles.historyHeader}>
                <Text style={styles.historyHeaderText}>UOM</Text>
                <Text style={styles.updateDate}>{item.uom}</Text>
            </View>

            <View style={styles.historyHeader}>
                <Text style={styles.historyHeaderText}>Quantity</Text>
                <Text style={styles.updateDate}>{item.quantity}</Text>
            </View>

            <View style={styles.historyHeader}>
                <Text style={styles.historyHeaderText}>Man Days</Text>
                <Text style={styles.updateDate}>{item.manDays}</Text>
            </View>
        </View>
    );

    return (
        <SafeAreaView style={styles.container}>
            <AppHeader
                title={Strings.DailyProgress.lastUpdate}
                onBookmarkPress={() => {

                }} />
            <View style={styles.overallView}>
                <TouchableOpacity style={styles.pipelineInfo} onPress={() => {
                    setPathEditOptionsModalVisible(true)
                }}>
                    <Text style={styles.pipelineText}>
                        {fullPath}
                    </Text>
                    <View style={styles.arrowIconStyle}>
                        <DirectionArrow />
                    </View>
                </TouchableOpacity>

                <View style={styles.dateContainer}>
                    <View>
                        <Text style={styles.dateLabel}>Planned Start & End Date</Text>
                        <Text style={[styles.dateValue, { textAlign: 'center' }]}>
                            {formatDateRangeWithStartEndDate(route.params?.plannedStartDate, route.params?.plannedEndDate)}
                        </Text>
                    </View>
                    <View>
                        <Text style={styles.dateLabel}>Actual Start & End Date</Text>
                        <Text style={[styles.dateValue, { textAlign: 'center' }]}>
                            {formatDateRangeWithStartEndDate(route.params?.actualStartDate, route.params?.actualEndDate)}
                        </Text>
                    </View>
                </View>

                <Text style={styles.historyTitle}>History</Text>

                <FlatList
                    data={historyData}
                    renderItem={renderHistoryItem}
                    keyExtractor={(item) => item.updateDate}
                    style={styles.flatListStyle}
                    contentContainerStyle={styles.flatListContainerStyle}
                    ListEmptyComponent={
                        <View style={styles.emptyContainer}>
                            <Image
                                source={require('../../assets/png/NoJobs.png')}
                                style={styles.emptyImage}
                                resizeMode="contain"
                            />
                        </View>
                    }
                />

                <Modal
                    animationType="none"
                    transparent
                    visible={pathEditOptionsModalVisible}
                    onRequestClose={() => setPathEditOptionsModalVisible(false)}
                >
                    <View style={styles.modalOverlay}>
                        <EditPathOptionsPopup pathSegments={pathSegments}
                            onEditPath={() => {
                                setPathEditOptionsModalVisible(false);
                                navigation.reset({
                                    index: 2,
                                    routes: [
                                        { name: 'Home' },
                                        { name: 'DailyProgressView' },
                                        { name: 'ProgressUpdateView' }
                                    ],
                                });
                            }} />
                    </View>
                </Modal>
            </View>
        </SafeAreaView>
    );
};

export default LastUpdateScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.dailyProgressItemBg,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: ms(16),
        borderBottomWidth: ms(1),
        borderBottomColor: Colors.searchBorderGrey,
    },
    backButton: {
        marginRight: ms(16),
        marginLeft: ms(4)
    },
    headerTitle: {
        fontSize: ms(16),
        fontWeight: 'bold',
    },
    overallView: {
        flex: 1,
        paddingVertical: ms(16)
    },
    pipelineInfo: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        backgroundColor: Colors.containerligetBlue,
        paddingHorizontal: ms(16),
        paddingVertical: ms(12),
        marginHorizontal: ms(20),
        borderRadius: ms(8),
        marginBottom: ms(16),
        borderColor: Colors.blue,
        borderWidth: ms(1.5),
    },
    pipelineText: {
        flex: 1,
        marginRight: ms(8),
        fontSize: ms(14),
        color: Colors.textPrimary,
    },
    arrowIconStyle: {
        justifyContent: 'flex-end'
    },
    dateContainer: {
        flexDirection: 'row',
        paddingHorizontal: ms(16),
        paddingVertical: ms(12),
        borderRadius: ms(10),
        marginHorizontal: ms(20),
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
        justifyContent: 'space-between',
    },
    dateItem: {
        flex: 1,
        alignItems: 'center',
    },
    dateLabel: {
        fontSize: ms(12),
        color: Colors.textInputBlack,
        marginBottom: ms(4),
        fontWeight: '500',
        textAlign: 'center',
    },
    dateValue: {
        fontSize: ms(14),
        fontWeight: '700',
        color: Colors.textPrimary,
    },
    historyTitle: {
        fontSize: ms(16),
        fontWeight: '600',
        color: Colors.textInputBlack,
        marginHorizontal: ms(20),
        marginTop: ms(16)
    },
    historyItem: {
        flexDirection: 'row',
        paddingVertical: ms(12),
        borderBottomWidth: ms(1),
        borderBottomColor: Colors.containerligetBlue,
    },
    historyHeader: {
        paddingHorizontal: ms(20),
        paddingVertical: ms(3),
        justifyContent: 'space-between',
    },
    historyHeaderText: {
        fontSize: ms(12),
        fontWeight: '500',
        color: Colors.textInputBlack,
    },
    updateDate: {
        fontSize: ms(14),
        fontWeight: '600',
        color: Colors.textPrimary,
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: Colors.modelOverlay,
    },
    flatListStyle: {
        flex: 1
    },
    flatListContainerStyle: {
        paddingBottom: ms(24) 
    },
    emptyContainer: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyImage: {
        width: ms(100),
        height: ms(100),
    },
});


