// Data filter based on deliverable type

import { FilterSource, WBSItem } from "../../../model/DailyProgress/DailyProgressData";
import { MapData } from "../../../model/DailyProgress/MapData";
import { DeliveryType } from "../components/BottomPopupDeliverables";

export const filterDataByType = (type: DeliveryType, wbsItems: WBSItem[], pendingItems: WBSItem[] = [], bookmarkItems: WBSItem[], source: FilterSource) => {
    try {
        if (source === 'Book Mark') {
            // For bookmark screen, only filter wbsItems by et_Code
            let filterType;
            if (type === 'Billable') {
                filterType = 'IT';
            } else if (type === 'GIS Node') {
                filterType = 'GIS';
            } else {
                filterType = 'BQ';
            }
            const filteredBookmark = bookmarkItems.filter(item => item.entity_Type === filterType);
            return { filteredBookmark };
        } else {
            // For daily progress screen, filter both
            // if (type === 'GIS Node') {
            //     // Filter for GIS items: gis_Tag is 'Y'
            //     // const filteredWBS = wbsItems.filter(item => item.gis_Tag && item.gis_Tag.trim() === 'Y');
            //     // const filteredPending = pendingItems.filter(item => item.gis_Tag && item.gis_Tag.trim() === 'Y');
            //     // return { filteredWBS, filteredPending };

            //     const filteredWBS = wbsItems.filter(item => item.et_Code === 'GIS');
            //     const filteredPending = pendingItems.filter(item => item.et_Code === 'GIS');
            //     return { filteredWBS, filteredPending };

            // } else {
            // For BQ/IT: filter by et_Code directly
            let filterType;
            if (type === 'Billable') {
                filterType = 'IT';
            } else if (type === 'GIS Node') {
                filterType = 'GIS';
            } else {
                filterType = 'BQ';
            }
            const filteredWBS = wbsItems.filter(item => item.et_Code === filterType);
            const filteredPending = pendingItems.filter(item => item.et_Code === filterType);
            return { filteredWBS, filteredPending };
            // }
        }
    }
    catch (error) {
        console.log('Error in filterDataByType -- error:', error);
        return { filteredWBS: [], filteredPending: [], filteredBookmark: [] };
    }
}

export const getFullPathDescriptionForBookmarks = (item: WBSItem): string => {
    item.fullPath = [
        item.entity_Description,
        item.wpDescription,
        item.childWorkDescription,
        item.deliverableCodeDesc
    ]
        .filter(value => value !== null && value !== undefined && value !== '')
        .join(' / ');
    return item.fullPath
};

export const getFullPathDescriptionForMaps = (item: MapData): string => {
    console.log('getFullPathDescriptionForMaps -- item.ilDesc: ', item.ilDesc, ' - item.wpDesc: ', item.wpDesc, ' - item.cwpDesc: ', item.cwpDesc, ' - item.deliverableCodeDesc: ', item.deliverableCodeDesc);
    item.fullPath = [
        item.ilDesc,
        item.wpDesc,
        item.cwpDesc,
        item.deliverableCodeDesc
    ]
        .filter(value => value !== null && value !== undefined && value !== '')
        .join(' / ');
    return item.fullPath
};

export const getFormattedApproverPath = (item: WBSItem): string => {

    let wbsDescription = '';
    if (item.entity_Description) {
        const parts = item.entity_Description.split('-->');
        // Remove the first segment (e.g., "LE23M849") and join the rest
        if (parts.length > 1) {
            wbsDescription = parts.slice(1).join(' / ').replace(/\r/g, '').replace(/\n/g, '');
        } else {
            // fallback if it doesn't contain -->
            wbsDescription = item.entity_Description.replace(/\r/g, '').replace(/\n/g, '');
        }
    }

    if (item.taskDescription && item.taskDescription.trim() !== '') {
        return `${wbsDescription} / ${item.taskDescription}`;
    } else {
        return wbsDescription;
    }
};
