// Data filter based on deliverable type

import { DeliveryType } from "../../../components/BottomPopupDeliverables";
import { FilterSource, WBSItem } from "../../../model/DailyProgress/DailyProgressData";


export const filterDataByType = (type: DeliveryType, wbsItems: WBSItem[], pendingItems: WBSItem[] = [], bookmarkItems: WBSItem[], source: FilterSource) => {
    if (source === 'Book Mark') {
        // For bookmark screen, only filter wbsItems by et_Code
        const filteredBookmark = bookmarkItems.filter(item => item.et_Code === type);
        return { filteredBookmark };
    } else {
        // For daily progress screen, filter both
        const filteredWBS = wbsItems.filter(item => item.et_Code === type);
        const filteredPending = pendingItems.filter(item => item.et_Code === type);
        return { filteredWBS, filteredPending };
    }
}
