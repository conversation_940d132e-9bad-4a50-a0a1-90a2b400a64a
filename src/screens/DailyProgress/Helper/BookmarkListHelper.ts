import { database } from '../../../database';
import BookMarkList from '../../../database/model/BookmarkList';

export interface InsertBookMarkListParams {
  jobCode: string;
  wbsCode: string;
  userId: number;
  hierarchyLevel: string;
  isActive: string;
  jobDescription: string;
  ilCode: string;
  ilDescription: string;
  wpCode: string;
  wpDescription: string;
  childWorkCode: string;
  childWorkDescription: string;
  deliverableCode: number;
  deliverableCodeDesc: string;
  etCode: string;
}

export const insertBookMarkListRecord = async (params: InsertBookMarkListParams) => {
  try {
    await database.write(async () => {
      await database.get('BookMarkList').create(record => {
        const bookmarkRecord = record as BookMarkList;
        bookmarkRecord.jobCode = params.jobCode;
        bookmarkRecord.wbsCode = params.wbsCode;
        bookmarkRecord.userId = params.userId;
        bookmarkRecord.hierarchyLevel = params.hierarchyLevel;
        bookmarkRecord.isActive = params.isActive;
        bookmarkRecord.jobDescription = params.jobDescription;
        bookmarkRecord.ilCode = params.ilCode;
        bookmarkRecord.ilDescription = params.ilDescription;
        bookmarkRecord.wpCode = params.wpCode;
        bookmarkRecord.wpDescription = params.wpDescription;
        bookmarkRecord.childWorkCode = params.childWorkCode;
        bookmarkRecord.childWorkDescription = params.childWorkDescription;
        bookmarkRecord.deliverableCode = params.deliverableCode;
        bookmarkRecord.deliverableCodeDesc = params.deliverableCodeDesc;
        bookmarkRecord.etCode = params.etCode;
      });
    });
    console.log('Inserted into BookMarkList');
    return true;
  } catch (error) {
    console.error('Failed to insert into BookMarkList:', error);
    return false;
  }
};

export const removeBookmarkByParentWBSCode = async (parentWBSCode: string) => {
  try {
    await database.write(async () => {
      const collection = database.get('BookMarkList');
      const records = await collection.query().fetch();
      const toDelete = records.find((record: any) => record.wbsCode === parentWBSCode);
      if (toDelete) {
        await toDelete.destroyPermanently();
        console.log('Bookmark removed from BookMarkList for parent_WBS_Code:', parentWBSCode);
      } else {
        console.log('No matching bookmark found for parent_WBS_Code:', parentWBSCode);
      }
    });
    return true;
  } catch (error) {
    console.error('Failed to remove bookmark from BookMarkList:', error);
    return false;
  }
}; 