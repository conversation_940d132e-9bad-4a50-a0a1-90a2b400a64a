import { View, Text, StyleSheet, SafeAreaView } from 'react-native'
import React from 'react'
import { useRoute, RouteProp } from '@react-navigation/native';
import Colors from '../../utils/Colors/Colors';
import CustomMapView from '../../components/MapView/MapView';

interface MapDataItem {
    id: string;
    jobCode: string;
    wbsCode: string;
    latitude: number;
    longitude: number;
    hierarchyLevel: string;
    isActive: string;
    jobDesc: string;
    il: string;
    ilDesc: string;
    wp: string;
    wpDesc: string;
    swp: string;
    cwpDesc: string;
    deliverableCode: number;
    deliverableCodeDesc: string;
}

type MapViewScreenParams = {
    mapData: MapDataItem[];
};

const MapViewScreen = () => {
    const route = useRoute<RouteProp<{ params: MapViewScreenParams }, 'params'>>();
    const { mapData } = route.params;

    return (
        <SafeAreaView style={styles.container}>
            <CustomMapView mapData={mapData} />
        </SafeAreaView>
    )
}

export default MapViewScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.dailyProgressBg,
    },
})