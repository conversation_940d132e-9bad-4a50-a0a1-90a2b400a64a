import { View, Text, StyleSheet, SafeAreaView } from 'react-native'
import React from 'react'
import { useRoute, RouteProp } from '@react-navigation/native';
import Colors from '../../utils/Colors/Colors';
import CustomMapView from '../../components/MapView/MapView';
import { MapData } from '../../model/DailyProgress/MapData';

type MapViewScreenParams = {
    mapData: MapData[];
};

const MapViewScreen = () => {
    const route = useRoute<RouteProp<{ params: MapViewScreenParams }, 'params'>>();
    const { mapData } = route.params;
    console.log('MapViewScreen -- mapData: ', mapData);
    return (
        <SafeAreaView style={styles.container}>
            <CustomMapView mapData={mapData} />
        </SafeAreaView>
    )
}

export default MapViewScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.dailyProgressBg,
    },
})