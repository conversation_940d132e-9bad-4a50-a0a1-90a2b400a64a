import { StackActions, useNavigation } from "@react-navigation/native";
import React, { useCallback, useEffect, useState, useRef } from "react";
import { Text, TouchableOpacity, FlatList, StyleSheet, Modal, Pressable, Alert } from 'react-native';
import { SafeAreaView } from "react-native-safe-area-context";
import DirectionArrow from '../../assets/svg/direction_arrow.svg';
import ArrowDown from '../../assets/svg/arrow_down.svg';
import AddPath from '../../assets/svg/add_circle_blue.svg';
import MapDenoter from '../../assets/svg/map_denoter_blue.svg';
import PrintLog from "../../utils/Logger/PrintLog";
import AppHeader from "../../components/AppHeader";
import Strings from "../../utils/Strings/Strings";
import Colors from "../../utils/Colors/Colors";
import { ms } from "../../utils/Scale/Scaling";
import SearchComponent from "../../components/SearchComponent";
import BottomPopupDeliverables, { DeliveryType } from "./components/BottomPopupDeliverables";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/Root/RootReducer";
import { loadWBSItems } from "../../database/helper/DatabaseHelper";
import { filterDataByType } from "./Helper/DataFilter";
import { WBSItem } from "../../model/DailyProgress/DailyProgressData";
import { t } from "i18next";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../navigation/AppNavigator";

export const BooksmarksView = () => {
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const { currentJobId } = useSelector((state: RootState) => state.home);

    const [searchQuery, setSearchQuery] = useState<string>('');
    const [bookmarkItems, setBookmarkItems] = useState<WBSItem[]>([]);
    const [filteredItems, setFilteredItems] = useState<WBSItem[]>([]);
    const [selectedFilter, setSelectedFilter] = useState<DeliveryType>('SWP');
    const [modalVisible, setModalVisible] = useState(false);
    const isFilteringRef = useRef<boolean>(false);

    type tabType = 'Recent List' | 'Pending For Approval';

    type itemType = {
        id: string;
        title: string;
    };

    // Load initial bookmark items or navigate to download screen
    useEffect(() => {
        if (!currentJobId) {
            // navigation.navigate('DownloadWBS');
            navigation.dispatch(StackActions.replace('DownloadWBS'));
            return;
        }

        loadBookMarkedItems(currentJobId);
    }, [currentJobId]);

    useEffect(() => {
        if (isFilteringRef.current) {
            isFilteringRef.current = false;
            return;
        }

        if (searchQuery.trim()) {
            const filteredBookmarkList = filteredItems.filter(item =>
                item.fullPath.toLowerCase().includes(searchQuery.toLowerCase())
            );

            setFilteredItems(filteredBookmarkList);
        } else {
            setFilteredItems(bookmarkItems);
        }
    }, [searchQuery]);

    const items: itemType[] = [
        { id: '1', title: 'Pipeline / HDPE / Cluster Dist. HDPE 200 mm / ROW / m' },
        { id: '2', title: 'Pipeline / PVC / Cluster Dist. PVC 150 mm / ROW / m' },
        { id: '3', title: 'Pipeline / PE / Cluster Dist. PE 100 mm / ROW / m' },
        { id: '4', title: 'Pipeline / Steel / Cluster Dist. Steel 250 mm / ROW / m' },
        { id: '5', title: 'Pipeline / Copper / Cluster Dist. Copper 75 mm / ROW / m' },
        { id: '6', title: 'Pipeline / Aluminum / Cluster Dist. Alumi 50 mm / m' },
        { id: '7', title: 'Pipeline / Fiberglass / Cluster Dist. Fiberg 300 mm / m' },
        { id: '8', title: 'Pipeline / Concrete / Cluster Dist. Concrete / 400 mm / m' },
    ];

    const handleSearchChange = useCallback((text: string) => {
        setSearchQuery(text);
    }, []);

    const handleFilterPress = useCallback((type: DeliveryType) => {
        isFilteringRef.current = true;
        setSelectedFilter(type);
        setSearchQuery('');
        setModalVisible(false);
        const { filteredBookmark } = filterDataByType(type, [], [], bookmarkItems, 'Book Mark');
        setFilteredItems(filteredBookmark || []);
    }, [bookmarkItems]);

    const onItemClick = useCallback((item: WBSItem) => {
        navigation.navigate('DailyProgressDetailsView', {
            selectedDeliverable: selectedFilter,
            inputDetail: item
        });
    }, [selectedFilter]);

    const loadBookMarkedItems = async (jobCode: string) => {
        try {
            const finalItems = await loadWBSItems(jobCode, t('BookmarkStrings.bookmarks'));
            if (!finalItems) {
                setFilteredItems([]);
                return;
            }
            const { filteredBookmark } = filterDataByType(selectedFilter, [], [], finalItems, 'Book Mark');
            setBookmarkItems(filteredBookmark || []);
            setFilteredItems(filteredBookmark || []);
        } catch (error) {
            Alert.alert('Bookmark -- Error loading pending items');
        }
    }

    const renderItem = useCallback(({ item }: { item: WBSItem }) => (
        <TouchableOpacity style={styles.itemContainer} onPress={() => onItemClick(item)}>
            <Text style={styles.itemText}>{item.fullPath || item.entity_Description}</Text>
            <DirectionArrow />
        </TouchableOpacity>
    ), [onItemClick]);

    return (
        <SafeAreaView style={styles.container}>
            <AppHeader
                title={t('BookmarkStrings.bookmarks')}
                rightContent={
                    <Pressable style={styles.filterRow} onPress={() => setModalVisible(true)}>
                        <Text style={styles.filterText}>{selectedFilter}</Text>
                        <ArrowDown />
                    </Pressable>
                }
                onBookmarkPress={() => {

                }} />

            <SearchComponent value={searchQuery} onChange={setSearchQuery} />
            <FlatList
                data={filteredItems}
                renderItem={renderItem}
                keyExtractor={(item, index) => `${item.id}${index}`}
                contentContainerStyle={styles.listContainer}
            />
            <TouchableOpacity style={styles.mapButton}>
                <MapDenoter />
            </TouchableOpacity>

            <TouchableOpacity style={styles.fabButton}>
                <AddPath />
            </TouchableOpacity>

            <Modal
                animationType="none"
                transparent
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <Pressable style={styles.modalOverlay} onPress={() => setModalVisible(false)}>
                    <BottomPopupDeliverables
                        selectedType={selectedFilter}
                        onSelectType={(type) => {
                            handleFilterPress(type);
                        }} />

                </Pressable>
            </Modal>
        </SafeAreaView>
    );
};

export default BooksmarksView;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.containerligetBlue,
    },
    filterRow: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    filterTextColor: {
        marginRight: ms(4),
        fontSize: ms(14),
        color: Colors.textPrimary
    },
    leftContainer: {
        flexDirection: 'row', // To place Item 1 and Item 2 side by side            // Adjust gap between the two texts
    },
    filterText: {
        marginHorizontal: ms(8),
        fontSize: ms(12),
        color: Colors.textPrimary,
        fontWeight: '700',
    },
    listContainer: {
        paddingHorizontal: ms(16),
        marginHorizontal: ms(5),
    },
    itemContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.white,
        paddingHorizontal: ms(15),
        paddingVertical: ms(10),
        marginBottom: ms(12),
        borderWidth: ms(1),
        borderRadius: ms(10),
        borderColor: Colors.searchBorderGrey,
    },
    itemText: {
        flex: 1,
        marginRight: ms(8),
        fontSize: ms(13),
        color: Colors.textPrimary,
    },
    mapButton: {
        position: 'absolute',
        bottom: ms(150),
        right: ms(5),
    },
    fabButton: {
        position: 'absolute',
        bottom: ms(80),
        right: ms(-3),
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: Colors.modelOverlay,
    },
});

