import { StackActions, useNavigation } from "@react-navigation/native";
import React, { useCallback, useEffect, useState, useRef } from "react";
import { Text, TouchableOpacity, FlatList, StyleSheet, Modal, Pressable, Alert, View, Image } from 'react-native';
import { SafeAreaView } from "react-native-safe-area-context";
import DirectionArrow from '../../assets/svg/direction_arrow.svg';
import ArrowDown from '../../assets/svg/arrow_down.svg';
import AddPath from '../../assets/svg/add_circle_blue.svg';
import MapDenoter from '../../assets/svg/map_denoter_blue.svg';
import PrintLog from "../../utils/Logger/PrintLog";
import AppHeader from "../../components/AppHeader";
import Strings from "../../utils/Strings/Strings";
import Colors from "../../utils/Colors/Colors";
import { ms } from "../../utils/Scale/Scaling";
import SearchComponent from "../../components/SearchComponent";
import BottomPopupDeliverables, { DeliveryType } from "./components/BottomPopupDeliverables";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/Root/RootReducer";
import { loadLatLongHierarchyData } from "../../database/helper/DatabaseHelper";
import { filterDataByType, getFullPathDescriptionForBookmarks } from "./Helper/DataFilter";
import { WBSItem } from "../../model/DailyProgress/DailyProgressData";
import { t } from "i18next";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../navigation/AppNavigator";
import { customAlertWithOK } from "../../components/CustomAlert";
import { database } from "../../database";
import { Q } from "@nozbe/watermelondb";
import BookMarkList from "../../database/model/BookmarkList";

export const BooksmarksView = () => {
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const { currentJobId } = useSelector((state: RootState) => state.home);
    const [isLoading, setIsLoading] = useState(false);
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [bookmarkItems, setBookmarkItems] = useState<WBSItem[]>([]);
    const [filteredItems, setFilteredItems] = useState<WBSItem[]>([]);
    const [selectedFilter, setSelectedFilter] = useState<DeliveryType>('Non-Billable');
    const [modalVisible, setModalVisible] = useState(false);
    const isFilteringRef = useRef<boolean>(false);
    const [latLongData, setLatLongData] = useState<any[]>([]);


    // Load initial bookmark items or navigate to download screen
    useEffect(() => {
        if (!currentJobId) {
            // navigation.navigate('DownloadWBS');
            navigation.dispatch(StackActions.replace('DownloadWBS'));
            return;
        }

        loadBookMarkedItems(currentJobId);
    }, [currentJobId]);

    useEffect(() => {
        if (isFilteringRef.current) {
            isFilteringRef.current = false;
            return;
        }

        if (searchQuery.trim()) {
            const filteredBookmarkList = filteredItems.filter(item =>
                item?.fullPath?.toLowerCase().includes(searchQuery.toLowerCase())
            );
            setFilteredItems(filteredBookmarkList);
        } else {
            setFilteredItems(bookmarkItems);
        }
    }, [searchQuery]);

    const handleFilterPress = useCallback((type: DeliveryType) => {
        isFilteringRef.current = true;
        setSelectedFilter(type);
        setSearchQuery('');
        setModalVisible(false);
        const { filteredBookmark } = filterDataByType(type, [], [], bookmarkItems, 'Book Mark');
        setFilteredItems(filteredBookmark || []);
    }, [bookmarkItems]);

    const onItemClick = async (item: WBSItem) => {

        const getTrimmedWBS = (wbsCode: string): string => {
            console.log('getTrimmedWBS -- wbsCode: ', wbsCode?.split('~').slice(1).join('~'));
            return wbsCode?.split('~').slice(1).join('~') ?? '';
        };

        const wbsDetails = await database
            .get('WBSDetails')
            .query(
                Q.where('JobCode', item.job_Code),
                Q.where('WBS', getTrimmedWBS(item.parent_WBS_Code ?? ''))
            )
            .fetch();

        console.log('onItemClickPendingForApproval -- wbsDetails: ', wbsDetails)

        if (wbsDetails.length > 0) {
            const params = {
                inputDetails: wbsDetails[0]._raw,
                wbsPath: item.fullPath ?? '',
                selectedItem: null,
                parentItems: null,
                fromWhere: 'DailyProgress',
            };
            navigation.navigate('DailyProgressDetailsView', params);
        } else {
            customAlertWithOK('No details found', 'No input details found for the selected item.');
        }
    };

    // const BookmarkData = [
    //     {
    //         "id": "1",
    //         "PRCGB_Job_Code": "LE23M849",
    //         "PRCGB_WBS_Code": "IL7~WP100072~SWP102~1061",
    //         "PRCGB_User_ID": 100078676,
    //         "PRCGB_Hierarchy_Level": "WP ",
    //         "PRCGB_IsActive": "Y",
    //         "JOB_DESC": "Chittorgarh Package-I",
    //         "IL": "IL7",
    //         "IL_DESC": "Other Civil Works",
    //         "WP": "WP100072",
    //         "WP_DESC": "Ancilliary Buildings",
    //         "SWP": "SWP102",
    //         "CWP_DESC": "J2 Staff Quarters - Intake Bhainsrodgarh",
    //         "DELIVERABLE_CODE": 1061,
    //         "DELIVERABLE_CODE_DESC": "RCC - Upto plinth level",
    //         "taskType": "BQ",
    //     },
    //     {
    //         "id": "2",
    //         "PRCGB_Job_Code": "LE23M849",
    //         "PRCGB_WBS_Code": "IL7~WP100072~SWP102~1062",
    //         "PRCGB_User_ID": 100078676,
    //         "PRCGB_Hierarchy_Level": "WP ",
    //         "PRCGB_IsActive": "Y",
    //         "JOB_DESC": "Chittorgarh Package-I",
    //         "IL": "IL7",
    //         "IL_DESC": "Other Civil Works",
    //         "WP": "WP100072",
    //         "WP_DESC": "Ancilliary Buildings",
    //         "SWP": "SWP102",
    //         "CWP_DESC": "J2 Staff Quarters - Intake Bhainsrodgarh",
    //         "DELIVERABLE_CODE": 1062,
    //         "DELIVERABLE_CODE_DESC": "RCC - Upto half height",
    //         "taskType": "IT",
    //     },
    //     {
    //         "id": "3",
    //         "PRCGB_Job_Code": "LE23M849",
    //         "PRCGB_WBS_Code": "IL7~WP100072~SWP102~1063",
    //         "PRCGB_User_ID": 100078676,
    //         "PRCGB_Hierarchy_Level": "WP ",
    //         "PRCGB_IsActive": "Y",
    //         "JOB_DESC": "Chittorgarh Package-I",
    //         "IL": "IL7",
    //         "IL_DESC": "Other Civil Works",
    //         "WP": "WP100072",
    //         "WP_DESC": "Ancilliary Buildings",
    //         "SWP": "SWP102",
    //         "CWP_DESC": "J2 Staff Quarters - Intake Bhainsrodgarh",
    //         "DELIVERABLE_CODE": 1063,
    //         "DELIVERABLE_CODE_DESC": "RCC - Upto full height",
    //         "taskType": "BQ",
    //     },
    //     {
    //         "id": "4",
    //         "PRCGB_Job_Code": "LE23M849",
    //         "PRCGB_WBS_Code": "IL7~WP100072~SWP102~1064",
    //         "PRCGB_User_ID": 100078676,
    //         "PRCGB_Hierarchy_Level": "WP ",
    //         "PRCGB_IsActive": "Y",
    //         "JOB_DESC": "Chittorgarh Package-I",
    //         "IL": "IL7",
    //         "IL_DESC": "Other Civil Works",
    //         "WP": "WP100072",
    //         "WP_DESC": "Ancilliary Buildings",
    //         "SWP": "SWP102",
    //         "CWP_DESC": "J2 Staff Quarters - Intake Bhainsrodgarh",
    //         "DELIVERABLE_CODE": 1064,
    //         "DELIVERABLE_CODE_DESC": "Centering & Shuttering - BGL",
    //         "taskType": "IT",
    //     },
    // ];

    const loadBookMarkedItems = async (jobCode: string) => {
        setIsLoading(true);
        try {
            // const [latLongHierarchyData] = await Promise.all([
            //     loadLatLongHierarchyData(jobCode)
            // ]);

            // setLatLongData(latLongHierarchyData || []);

            const allBookmarkItems = await database
                .get('BookMarkList')
                .query(
                    Q.where('PRCGB_Job_Code', jobCode),
                    Q.where('PRCGB_IsActive', 'Y') //this filter is required but api data missing this param
                )
                .fetch();

            console.log('loadBookMarkedItems -- allBookmarkItems:', allBookmarkItems, ' -- allBookmarkItems.length: ', allBookmarkItems.length);
            const bookmarkedItems: WBSItem[] = (allBookmarkItems as BookMarkList[]).map((item) => {
                return {
                    id: item.id,
                    entity_Code: item.ilCode,
                    job_Code: item.jobCode,
                    entity_Type: item.etCode,
                    leaf_Node_Tag: '',
                    entity_Description: item.ilDescription,
                    parent_WBS_Code: item.wbsCode,
                    parent_entity_code: '',
                    parent_Task: '',
                    gis_Tag: '',
                    isChild: false,
                    et_Code: '',
                    fullPath: '',
                    fullDescriptionForTooltip: '',
                    taskDescription: '',
                    userID: item.userId,
                    hierarchyLevel: item.hierarchyLevel,
                    isActive: item.isActive,
                    Job_Description: item.jobDescription,
                    wpCode: item.wpCode,
                    wpDescription: item.wpDescription,
                    childWorkCode: item.childWorkCode,
                    childWorkDescription: item.childWorkDescription,
                    deliverableCode: item.deliverableCode,
                    deliverableCodeDesc: item.deliverableCodeDesc,
                };
            });

            // const bookmarkedItems: WBSItem[] = BookmarkData.map((item) => {
            //     return {
            //         id: item.id,
            //         entity_Code: item.IL,
            //         job_Code: item.PRCGB_Job_Code,
            //         entity_Type: item.taskType,
            //         leaf_Node_Tag: '',
            //         entity_Description: item.IL_DESC,
            //         parent_WBS_Code: item.PRCGB_WBS_Code,
            //         parent_entity_code: '',
            //         parent_Task: '',
            //         gis_Tag: '',
            //         isChild: true,
            //         et_Code: '',
            //         fullPath: '',
            //         fullDescriptionForTooltip: '',
            //         taskDescription: '',
            //         userID: item.PRCGB_User_ID,
            //         hierarchyLevel: item.PRCGB_Hierarchy_Level,
            //         isActive: item.PRCGB_IsActive,
            //         Job_Description: item.JOB_DESC,
            //         wpCode: item.WP,
            //         wpDescription: item.WP_DESC,
            //         childWorkCode: item.SWP,
            //         childWorkDescription: item.CWP_DESC,
            //         deliverableCode: item.DELIVERABLE_CODE,
            //         deliverableCodeDesc: item.DELIVERABLE_CODE_DESC,
            //     };
            // });

            const [latLongHierarchyData] = await Promise.all([
                loadLatLongHierarchyData(jobCode, 'Bookmarks', bookmarkedItems)
            ]);
            console.log('loadBookMarkedItems -- latLongHierarchyData: ', latLongHierarchyData.length);
            setLatLongData(latLongHierarchyData || []);

            setBookmarkItems(bookmarkedItems || []);
            // Apply initial filter
            const { filteredBookmark } = filterDataByType(selectedFilter, [], [], bookmarkedItems, 'Book Mark');
            setFilteredItems(filteredBookmark || []);
            console.log('useeffecct -- bookmarkedItems -- filteredBookmark: ', filteredBookmark);
        } catch (error) {
            console.log('useeffecct -- FilteredPendingItems Error fetching PendingForApprovalBQIT:', error);
            setBookmarkItems([]);
            setFilteredItems([]);
        } finally {
            setIsLoading(false);
        }
    }

    const renderItem = useCallback(({ item }: { item: WBSItem }) => (
        <TouchableOpacity style={styles.itemContainer} onPress={() => { onItemClick(item); }}>
            <Text style={styles.itemText}>{getFullPathDescriptionForBookmarks(item)}</Text>
            <DirectionArrow />
        </TouchableOpacity>
    ), [onItemClick]);

    return (
        <SafeAreaView style={styles.container}>
            <AppHeader
                title={t('BookmarkStrings.bookmarks')}
                rightContent={
                    <Pressable style={styles.filterRow} onPress={() => setModalVisible(true)}>
                        <Text style={styles.filterText}>{selectedFilter}</Text>
                        <ArrowDown />
                    </Pressable>
                }
                onBookmarkPress={() => {

                }} />

            <SearchComponent value={searchQuery} onChange={setSearchQuery} />
            <FlatList
                data={filteredItems}
                renderItem={renderItem}
                keyExtractor={(item, index) => `${item.id}${index}`}
                contentContainerStyle={styles.listContainer}
                ListEmptyComponent={
                    <View style={styles.emptyContainer}>
                        <Image
                            source={require('../../assets/images/NoJobs.png')}
                            style={styles.emptyImage}
                            resizeMode="contain"
                        />
                    </View>
                }
            />
            <Pressable style={styles.mapButton} onPress={() => navigation.navigate('DailyProgressMapView', { mapData: latLongData })}>
                <MapDenoter />
            </Pressable>

            <Pressable style={styles.fabButton} onPress={() => navigation.navigate('ProgressUpdateView')}>
                <AddPath />
            </Pressable>

            {/* <Modal
                animationType="none"
                transparent
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <Pressable style={styles.modalOverlay} onPress={() => setModalVisible(false)}>
                    <BottomPopupDeliverables
                        selectedType={selectedFilter}
                        onSelectType={(type) => {
                            handleFilterPress(type);
                        }} />

                </Pressable>
            </Modal> */}

            <BottomPopupDeliverables
                visible={modalVisible}
                onClose={() => setModalVisible(false)}
                selectedType={selectedFilter}
                onSelectType={(type) => {
                    handleFilterPress(type);
                }} />
        </SafeAreaView>
    );
};

export default BooksmarksView;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.containerligetBlue,
    },
    filterRow: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    filterTextColor: {
        marginRight: ms(4),
        fontSize: ms(14),
        color: Colors.textPrimary
    },
    leftContainer: {
        flexDirection: 'row', // To place Item 1 and Item 2 side by side            // Adjust gap between the two texts
    },
    filterText: {
        marginHorizontal: ms(8),
        fontSize: ms(12),
        color: Colors.textPrimary,
        fontWeight: '700',
    },
    listContainer: {
        paddingHorizontal: ms(16),
        marginHorizontal: ms(5),
    },
    itemContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.white,
        paddingHorizontal: ms(15),
        paddingVertical: ms(10),
        marginBottom: ms(12),
        borderWidth: ms(1),
        borderRadius: ms(10),
        borderColor: Colors.searchBorderGrey,
    },
    itemText: {
        flex: 1,
        marginRight: ms(8),
        fontSize: ms(13),
        color: Colors.textPrimary,
    },
    mapButton: {
        position: 'absolute',
        bottom: 180,
        right: 5,
    },
    fabButton: {
        position: 'absolute',
        bottom: 110,
        right: -ms(3),
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: Colors.modelOverlay,
    },
    emptyContainer: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyImage: {
        width: ms(100),
        height: ms(100),
    },
});

