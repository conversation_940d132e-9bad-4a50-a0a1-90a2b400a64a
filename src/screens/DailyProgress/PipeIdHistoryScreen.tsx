import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, SafeAreaView, StatusBar, Modal } from 'react-native';
import FilterIcon from '../../assets/svg/filter.svg';
import DirectionArrow from '../../assets/svg/direction_arrow.svg';
import Strings from '../../utils/Strings/Strings';
import Colors from '../../utils/Colors/Colors';
import AppHeader from '../../components/AppHeader';
import BottomCalendarModal from '../../components/CalendarPicker/BottomPopupCalendar';
import GisDetailsPipeHistoryBottomPopup from './components/GisDetailsPipeHistoryBottomPopup';
import { ms } from '../../utils/Scale/Scaling';

interface historyItem {
  id: string;
  details: string;
  date: string;
}

const PipeIdHistoryScreen: React.FC = () => {
  const [gisModalVisible, setGisModalVisible] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  const PipeIdHistoryItem: React.FC<{ item: historyItem }> = ({ item }) => (
    <TouchableOpacity style={styles.itemContainer} onPress={() => {
      setGisModalVisible(true)
    }}>
      <View style={styles.itemContent}>
        <Text style={styles.itemText}>{item.details}</Text>
        <Text style={styles.itemDate}>{item.date}</Text>
      </View>
      <DirectionArrow />
    </TouchableOpacity>
  );

  const historyData: historyItem[] = [
    { id: '1', details: 'Pipeline / HDPE / Cluster Dist. HDPE 200 mm / ROW / m', date: '11/05/2025' },
    { id: '2', details: 'Pipeline / PVC / Main Dist. PVC 150 mm / ROW / m', date: '12/06/2025' },
    { id: '3', details: 'Pipeline / PE / Secondary Dist. PE 100 mm / ROW / m', date: '01/07/2025' },
    { id: '4', details: 'Pipeline / PPR / Tertiary Dist. PPR 50 mm / ROW / m', date: '15/08/2025' },
    { id: '5', details: 'Pipeline / Steel / Main Supply Steel 300 mm / ROW / m', date: '20/09/2025' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      {/* <View style={styles.header}>
        <TouchableOpacity>
          <DirectionArrow />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{Strings.DailyProgress.history}</Text>
        <TouchableOpacity>
          <DirectionArrow />
        </TouchableOpacity>
        <Text style={styles.dateRange}>11/05 - 30/10</Text>
      </View> */}


      <AppHeader
        title={Strings.DailyProgress.history}
        rightContent={
          <View style={styles.filterRow}>
            <FilterIcon />
            <Text style={styles.filterTextColor}>11/05 - 30/10</Text>
          </View>
        }
        onBookmarkPress={() => {
          setModalVisible(true)
        }} />

      <FlatList
        data={historyData}
        renderItem={({ item }) => <PipeIdHistoryItem item={item} />}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
      />

      <Modal
        animationType="none"
        transparent
        visible={gisModalVisible}
        onRequestClose={() => setGisModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <GisDetailsPipeHistoryBottomPopup
          />
        </View>
      </Modal>

      <Modal
        animationType="none"
        transparent
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <BottomCalendarModal
            visible={modalVisible}
            onClose={() => setModalVisible(false)}
            onApply={() => setModalVisible(false)}
          />

        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default PipeIdHistoryScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.containerligetBlue,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: ms(16),
    backgroundColor: Colors.white,
  },
  headerTitle: {
    fontSize: ms(20),
    fontWeight: 'bold',
    marginLeft: ms(16),
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  filterTextColor: {
    marginLeft: ms(4),
    fontSize: ms(14),
    color: Colors.textPrimary
  },
  dateRange: {
    marginLeft: 'auto',
    fontSize: ms(14),
    color: Colors.textPrimary,
  },
  listContainer: {
    padding: ms(16),
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderWidth: ms(1),
    borderColor: Colors.searchBorderGrey,
    borderRadius: ms(8),
    paddingVertical: ms(16),
    paddingHorizontal: ms(12),
    marginBottom: ms(8),
  },
  itemContent: {
    flex: 1,
  },
  itemText: {
    fontSize: ms(14),
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: ms(4),
  },
  itemDate: {
    fontSize: ms(14),
    fontWeight: '500',
    color: Colors.textInputBlack,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: Colors.modelOverlay,
  },

});

