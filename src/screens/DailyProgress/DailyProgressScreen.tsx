import { StackActions, useNavigation, CommonActions } from "@react-navigation/native";
import { RootState } from "../../redux/Root/rootStore";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { Text, TouchableOpacity, View, FlatList, StyleSheet, Image, Modal, Alert, Pressable, ActivityIndicator } from 'react-native';
import { SafeAreaView } from "react-native-safe-area-context";
import DirectionArrow from '../../assets/svg/direction_arrow.svg';
import ArrowDown from '../../assets/svg/arrow_down.svg';
import AddPath from '../../assets/svg/add_circle_blue.svg';
import MapDenoter from '../../assets/svg/map_denoter_blue.svg';
import Bookmark from '../../assets/svg/Bookmark.svg';
import PrintLog from "../../utils/Logger/PrintLog";
import AppHeader from "../../components/AppHeader";
import Strings from "../../utils/Strings/Strings";
import Colors from "../../utils/Colors/Colors";
import { database } from "../../database/index";
import { Database, Q } from "@nozbe/watermelondb";
import { NavigationProp } from "@react-navigation/native";
import { EntityConstants, PendingForApprovalBQITRecord, WBSItem, WBSJobRecord } from "../../model/DailyProgress/DailyProgressData";
import { ms } from "../../utils/Scale/Scaling";
import SearchComponent from "../../components/SearchComponent";
import BottomPopupDeliverables, { DeliveryType } from "./components/BottomPopupDeliverables";
import { filterDataByType } from "./Helper/DataFilter";
import LoadingOverlay from "../../components/LoadingOverlay";
import { loadLatLongHierarchyData, getAllWBSItemsWithPathAllLevels, buildWBSCodePath } from "../../database/helper/DatabaseHelper";
import { useTranslation } from "react-i18next";
import { customAlertWithOK } from "../../components/CustomAlert";
import { getUserInfo } from "../../utils/DataStorage/Storage";
import PendingForApprovalBQIT from "../../database/model/PendingForApprovalBQIT";
import WBSDetails from "../../database/model/WBSDetails";

export const DailyProgressView = () => {
    const navigation = useNavigation<NavigationProp<any>>();
    const { t } = useTranslation();
    const [activeTab, setActiveTab] = useState<tabType>('Recent List');
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [selectedFilter, setSelectedFilter] = useState<DeliveryType>('Non-Billable');
    const [modalVisible, setModalVisible] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const selectedFilterRef = useRef<DeliveryType>('Non-Billable');
    const isFilteringRef = useRef<boolean>(false);
    type tabType = 'Recent List' | 'Pending For Approval';
    const [wbsItems, setWbsItems] = useState<WBSItem[]>([]);
    const [pendingItems, setPendingItems] = useState<WBSItem[]>([]);
    // const [pendingItems, setPendingItems] = useState<PendingForApprovalBQIT[]>([]);

    const [filteredItems, setFilteredItems] = useState<WBSItem[]>([]);
    const [filteredPendingItems, setFilteredPendingItems] = useState<WBSItem[]>([]);
    // const [filteredPendingItems, setFilteredPendingItems] = useState<PendingForApprovalBQIT[]>([]);
    const { currentJobId } = useSelector((state: RootState) => state.home);
    const [latLongData, setLatLongData] = useState<any[]>([]);
    const user = getUserInfo();
    console.log("DailyPgrs: ", user?.UID);
    // Load initial data
    useEffect(() => {
        if (!currentJobId) {
            navigation.dispatch(StackActions.replace('DownloadWBS'));
            return;
        } else {
            setIsLoading(true)
        }
    }, [currentJobId]);

    // useEffect(() => {
    //     isLoading &&
    //         setTimeout(() => {
    //             loadInitialData(currentJobId);
    //         }, 100);
    // }, [isLoading])

    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true);
            if (activeTab === 'Recent List') {
                await loadInitialData(currentJobId);
            } else if (activeTab === 'Pending For Approval') {
                loadPendingForApprovalListData(currentJobId);
            }
        };
        if (currentJobId) {
            fetchData();
        }
    }, [activeTab, currentJobId]);


    // const loadInitialData = async (jobCode: string) => {
    //     try {
    //         setIsLoading(true);

    //         // Load WBS items and LatLongHierarchy data in parallel
    //         const [wbsItems, pendingItems, latLongHierarchyData] = await Promise.all([
    //             loadWBSItems(jobCode, t('RecentStrings.recentList')),
    //             loadWBSItems(jobCode, t('PendingForApprovalStrings.pendingForApproval')),
    //             loadLatLongHierarchyData(jobCode)
    //         ]);

    //         setWbsItems(wbsItems || []);
    //         // setPendingItems(pendingItems || []);
    //         setLatLongData(latLongHierarchyData || []);

    //         // Apply initial filter
    //         const { filteredWBS } = filterDataByType(selectedFilter, wbsItems || [], pendingItems || [], [], 'Recent');
    //         setFilteredItems(filteredWBS || []);
    //     } catch (error) {
    //         console.error('Error loading initial data:', error);
    //     } finally {
    //         setIsLoading(false);
    //     }
    // };

    const loadInitialData = async (currentJobId: string) => {
        setIsLoading(true);

        try {
            const [latLongHierarchyData] = await Promise.all([
                loadLatLongHierarchyData(currentJobId)
            ]);
            // console.log('loadInitialData -- latLongHierarchyData: ', latLongHierarchyData);
            const allWBSItems = await getAllWBSItemsWithPathAllLevels(currentJobId, database, EntityConstants);
            // console.log('allWBSItems.length', allWBSItems.length, '-- allWBSItems.et_Code: ', allWBSItems[0]?.et_Code);   
            setWbsItems(allWBSItems || []);
 
            const { filteredWBS } = filterDataByType(selectedFilter, allWBSItems || [], pendingItems || [], [], 'Recent');
            // console.log('selectedFilter:', selectedFilter);
            setFilteredItems(filteredWBS || []);
            setLatLongData(latLongHierarchyData || []);

        } catch (error) {
            console.error('Error in loadInitialData:', error);
            setIsLoading(false);
        } finally {
            setIsLoading(false);
        }
    }

    const loadPendingForApprovalListData = async (currentJobId: string) => {
        try {
            if (user) {
                const allPendingItems = await database
                    .get('PendingForApprovalBQIT')
                    .query(
                        Q.where('JOB', currentJobId),
                    )
                    .fetch();

                const pendingWBSItems: WBSItem[] = (allPendingItems as PendingForApprovalBQIT[]).map((item) => {
                    return {
                        id: item.id,
                        entity_Code: '',
                        job_Code: item.job,
                        entity_Type: item.taskType,
                        leaf_Node_Tag: '',
                        entity_Description: item.wbsDescription,
                        parent_WBS_Code: item.wbs,
                        parent_entity_code: '',
                        parent_Task: '',
                        gis_Tag: '',
                        isChild: false,
                        et_Code: item.taskType,
                        fullPath: '',
                        fullDescriptionForTooltip: '',
                        taskDescription: item.taskCustomDescription,
                        ActualQuantity: item.actualQuantity,
                        Alignment: item.alignment,
                        Distance_From_Center: item.distanceFromCenter,
                        From_Length: item.fromLength,
                        IS_Completed: item.isCompleted,
                        Image_ID: item.imageId,
                        Image_URL: item.imageUrl,
                        Is_Hindrance: item.isHindrance,
                        JOB: item.job,
                        Latitude: item.latitude,
                        Longitude: item.longitude,
                        Manpower: item.manpower,
                        NodeId: item.nodeId,
                        Progress_Length: item.progressLength,
                        Quantity: item.quantity,
                        ReferanceNodeId: item.referanceNodeId,
                        Remarks: item.remarks,
                        TDate: item.tDate,
                        Task: item.task,
                        // TaskType: string;
                        // Task_Custom_Description: string;
                        // Task_Description: string;
                        TotalQuantity: item.totalQuantity,
                        Total_Length: item.totalLength,
                        UOM: item.uom,
                        UserID: item.userId,
                        UserName: item.userName,
                        // WBS: string;
                        // WBS_Custom_Description: string;
                        // WBS_Description: string;
                    };
                });
                setPendingItems(pendingWBSItems || []);
                // Apply initial filter
                const { filteredPending } = filterDataByType(selectedFilter, [], pendingWBSItems || [], [], 'Recent');
                setFilteredPendingItems(filteredPending || []);
                // console.log('useeffecct -- FilteredPendingItems: ', filteredPendingItems);
            }
        } catch (error) {
            console.log('useeffecct -- FilteredPendingItems Error fetching PendingForApprovalBQIT:', error);
            setPendingItems([]);
            setFilteredPendingItems([]);
        } finally {
            setIsLoading(false);
        }
    };

    // Handle search
    useEffect(() => {
        if (isFilteringRef.current) {
            isFilteringRef.current = false;
            return;
        }

        if (searchQuery.trim()) {
            const filteredRecentList = filteredItems.filter(item =>
                item.fullPath?.toLowerCase().includes(searchQuery.toLowerCase())
            );
            const filteredPendingList = filteredPendingItems.filter(item =>
                item.fullPath?.toLowerCase().includes(searchQuery.toLowerCase())
            );
            // console.log('filteredRecentList: ', filteredRecentList);
            setFilteredItems(filteredRecentList);
            setFilteredPendingItems(filteredPendingList);
        } else {
            // setFilteredItems(filteredItems);
            // setFilteredPendingItems(filteredPendingItems);
            loadInitialData(currentJobId);
            loadPendingForApprovalListData(currentJobId);
        }
    }, [searchQuery])

    const handleTabChange = useCallback((tab: tabType) => {
        setActiveTab(tab);
    }, []);

    const handleFilterPress = useCallback((type: DeliveryType) => {
        isFilteringRef.current = true;
        selectedFilterRef.current = type;
        setSelectedFilter(type);
        setSearchQuery('');
        setModalVisible(false);

        const { filteredWBS, filteredPending } = filterDataByType(type, wbsItems, pendingItems, [], 'Recent');
        // setWbsItems(filteredWBS);
        // setPendingItems(filteredPending);
        setFilteredItems(filteredWBS || []);
        setFilteredPendingItems(filteredPending || []);

        PrintLog.debug('DailyProgressView', { 'handleFilterPress -- selectedFilter: ': type });
    }, [wbsItems, pendingItems]);

    

    const onItemClick = async (item: WBSItem, type: string) => {
        setIsLoading(true);
        try {

            // const wbsDetails = await database.get('WBSDetails').query(
            //     Q.where('JobCode', item.job_Code),
            //     Q.or(
            //         Q.where('WBS', item.parent_WBS_Code),
            //         Q.where('Task', item.Task),
            //     )
            // ).fetch();

            const wbsCodePath = await buildWBSCodePath(item);

            // console.log('onItemClick -- item: ', item);
            // console.log('onItemClick -- wbsCodePath: ', wbsCodePath);


            let wbsCode = item.parent_WBS_Code;
            if (type === 'Recents') {
                wbsCode = item.parent_WBS_Code + '~' + item.entity_Code;
            }

            // console.log('onItemClickPendingForApproval -- item: ', item);
            const conditions: any[] = [
                Q.where('JobCode', item.job_Code)
            ];

            if (item.parent_WBS_Code && item.Task) {
                // Both exist — apply both (AND)
                conditions.push(Q.where('WBS', wbsCode));
                conditions.push(Q.where('Task', item.Task));
            } else if (item.parent_WBS_Code) {
                // Only WBS exists
                conditions.push(Q.where('WBS', wbsCode));
            } else if (item.Task) {
                // Only Task exists
                conditions.push(Q.where('Task', item.Task));
            }

            // Then use it in a query:
            // console.log('onClick -- selectedFilter: ', selectedFilter);
            let wbsDetails;
            if (selectedFilter === 'GIS Node') {
                wbsDetails = await database
                    .get('WBSGISDetails')
                    .query(...conditions)
                    .fetch();
            } else {
                wbsDetails = await database
                    .get('WBSDetails')
                    .query(...conditions)
                    .fetch();
            }


            // console.log('onItemClickPendingForApproval -- query: ', conditions, ' -- wbsDetails: ', wbsDetails,)
            if (wbsDetails.length > 0) {
                const params = {
                    inputDetails: wbsDetails[0]._raw,
                    wbsPath: item.fullPath,
                    selectedItem: null,
                    parentItems: null,
                    fromWhere: 'DailyProgress',
                    wbsCodePathWithLevels: wbsCodePath,
                    selectedTab: activeTab,
                    imageUrl: item.Image_URL,
                    imageId: item.Image_ID,
                };
                navigation.navigate('DailyProgressDetailsView', params);
            }
            else {
                customAlertWithOK('No details found', 'No input details found for the selected item.');
            }
        } catch (error) {
            PrintLog.error('Error in onItemClick:', error);
            customAlertWithOK('Error', 'An error occurred while fetching details.');
        } finally {
            setIsLoading(false);
        }
    };

    const getFormattedTaskDescription = (item: WBSItem): string => {
        if (item.entity_Type === 'IT') {
            const parts = item.taskDescription?.split('::') ?? [];
            // console.log('parts:', parts);
            let updatedDescription = '';

            if (parts.length >= 3) {
                updatedDescription = `${parts[2].trim()}::${parts[0].trim()}`;
            } else if (parts.length === 1) {
                updatedDescription = parts[0].trim();
            }

            // const cleanedWbsDescription = item.entity_Description
            //     ? item.entity_Description.replace('-->', ' / ').replace(/\r?\n|\r/g, '')
            //     : '';

            let cleanedWbsDescription = '';
            if (item.entity_Description) {
                const segments = item.entity_Description.split('-->');
                // Remove the first segment (e.g., 'LE23M849') and join the rest
                if (segments.length > 1) {
                    cleanedWbsDescription = segments.slice(1).join(' / ').replace(/\r?\n|\r/g, '');
                } else {
                    cleanedWbsDescription = item.entity_Description.replace(/\r?\n|\r/g, '');
                }
            }

            item.fullPath = `${cleanedWbsDescription} / ${updatedDescription}`;

            if (parts.length > 1) {
                item.fullDescriptionForTooltip = parts[1].trim();
            }
            return item.fullPath;
        } else {
            return item.fullPath = getFullDescriptionForNonIT(item);
        }
    };

    // const getFullDescriptionForNonIT = (item: WBSItem): string => {
    //     if (item.taskDescription && item.taskDescription.trim() !== '') {
    //         const wbsDescription = item.entity_Description
    //             ? item.entity_Description.replace(/-->/g, ' / ').replace(/\r/g, '').replace(/\n/g, '')
    //             : '';
    //         return `${wbsDescription} / ${item.taskDescription}`;
    //     } else {
    //         const wbsDescription = item.entity_Description
    //             ? item.entity_Description.replace(/-->/g, ' / ').replace(/\r/g, '').replace(/\n/g, '')
    //             : '';
    //         return `${wbsDescription}`;
    //     }
    // };

    const getFullDescriptionForNonIT = (item: WBSItem): string => {
        let wbsDescription = '';
        // console.log('getFullDescriptionForNonIT -- item: ', item.entity_Description, ' -- item.taskDescription: ', item.taskDescription);
        if (item.entity_Description) {
            const parts = item.entity_Description.split('-->');

            // Remove the first part (e.g., "LE23M849") and join the rest
            if (parts.length > 1) {
                wbsDescription = parts.slice(1).join(' / ').replace(/\r/g, '').replace(/\n/g, '');
            } else {
                // fallback if it doesn't contain -->
                wbsDescription = item.entity_Description.replace(/\r/g, '').replace(/\n/g, '');
            }
        }

        if (item.taskDescription && item.taskDescription.trim() !== '') {
            item.fullPath = `${wbsDescription} / ${item.taskDescription}`;
            // return `${wbsDescription} / ${item.taskDescription}`;
        } else {
            item.fullPath = wbsDescription;
            // return wbsDescription;
        }
        return item.fullPath;
    };

    const renderItem = ({ item }: { item: WBSItem }) => (
        <TouchableOpacity
            style={styles.itemContainer}
            onPress={() => onItemClick(item, 'Recents')}
        >
            <Text style={styles.itemText}>{item.fullPath || item.entity_Description}</Text>

            <DirectionArrow />
        </TouchableOpacity>
    );

    const renderItemPendingForApproval = ({ item }: { item: WBSItem }) => {
        // console.log('item description:', item.taskDescription);
        return (
            <TouchableOpacity
                style={styles.itemContainer}
                onPress={() => onItemClick(item, '{PendingForApproval}')}
            >
                <Text style={styles.itemText}>{getFormattedTaskDescription(item)}</Text>
                <DirectionArrow />
            </TouchableOpacity>
        );
    };

    return (
        <SafeAreaView style={styles.container}>
            <AppHeader
                title={Strings.DailyProgress.progressUpdate}
                rightContent={<Bookmark />}
                onBookmarkPress={() => {
                    navigation.navigate('BookmarksView')
                }}
                onBackPress={() => {
                    navigation.dispatch(
                        CommonActions.reset({
                            index: 0,
                            routes: [{ name: 'Home' }],
                        })
                    );
                }}
            />

            <View style={styles.tabContainer}>
                <View style={styles.leftContainer}>
                    {['Recent List', 'Pending For Approval'].map((tab) => (
                        <TouchableOpacity
                            key={tab}
                            style={[styles.tab, activeTab === tab && styles.activeTab]}
                            onPress={() => handleTabChange(tab as tabType)}
                        >
                            <Text style={[styles.tabText,
                            activeTab === tab && styles.activeTabText,
                            activeTab !== tab && tab === 'Recent List' && styles.recentLisMargin,
                            activeTab !== tab && tab === 'Pending For Approval' && styles.pendingTabMargin
                            ]}>{tab}</Text>
                        </TouchableOpacity>
                    ))}
                </View>
                <TouchableOpacity style={styles.filterButton} onPress={() => {
                    setModalVisible(true)
                }}>
                    <Text style={styles.filterText}>{selectedFilter}</Text>
                    <ArrowDown />
                </TouchableOpacity>
            </View>

            <View style={styles.mainContainer}>
                <SearchComponent value={searchQuery} onChange={setSearchQuery} customStyle = {styles.searchStyle} />
                <View style={styles.listWrapper}>
                    {activeTab === 'Recent List' ? (
                        <FlatList
                            data={filteredItems}
                            renderItem={renderItem}
                            keyExtractor={(item, index) => `${item.id}${index}`}
                            contentContainerStyle={styles.listContainer}
                            showsVerticalScrollIndicator={true}
                            bounces={true}
                            ListEmptyComponent={!isLoading ? (
                                <View style={styles.emptyContainer}>
                                    <Image
                                        source={require('../../assets/images/NoJobs.png')}
                                        style={styles.emptyImage}
                                        resizeMode="contain"
                                    />
                                </View>
                            ) : null}
                        />
                    ) : (
                        <FlatList
                            data={filteredPendingItems}
                            renderItem={renderItemPendingForApproval}
                            keyExtractor={(item, index) => `${item.id}${index}`}
                            contentContainerStyle={styles.listContainer}
                            showsVerticalScrollIndicator={true}
                            bounces={true}
                            ListEmptyComponent={!isLoading ? (
                                <View style={styles.emptyContainer}>
                                    <Image
                                        source={require('../../assets/images/NoJobs.png')}
                                        style={styles.emptyImage}
                                        resizeMode="contain"
                                    />
                                </View>
                            ) : null}
                        />
                    )}
                </View>
            </View>

            {activeTab === 'Recent List' && (
                <>
                    <Pressable style={styles.mapButton} onPress={() => navigation.navigate('DailyProgressMapView', { mapData: latLongData })}>
                        <MapDenoter />
                    </Pressable>

                    <Pressable style={styles.fabButton} onPress={() => navigation.navigate('ProgressUpdateView')}>
                        <AddPath />
                    </Pressable>
                </>
            )}

            <BottomPopupDeliverables
                visible={modalVisible}
                onClose={() => setModalVisible(false)}
                selectedType={selectedFilter}
                onSelectType={(type) => {
                    handleFilterPress(type);
                }} />

            {isLoading && <LoadingOverlay visible={isLoading} message={t('commonStrings.loadingData')} />}
        </SafeAreaView>
    );
};

export default DailyProgressView;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.containerligetBlue,
    },
    mainContainer: {
        flex: 1,
        position: 'relative',
    },
    listWrapper: {
        flex: 1,
        position: 'relative',
    },
    listContainer: {
        paddingHorizontal: ms(16),
        flexGrow: 1,
    },
    tabContainer: {
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.containerligetBlue,
        marginHorizontal: ms(5),
        marginTop: ms(5),
        paddingLeft: ms(16),
        paddingBottom: ms(8),
    },
    leftContainer: {
        flexDirection: 'row', // To place Item 1 and Item 2 side by side            // Adjust gap between the two texts
        flex: 1
    },
    tab: {
        paddingTop: ms(10),
        paddingBottom: ms(5),
        borderBottomWidth: ms(1),
        borderBottomColor: Colors.grey,
    },
    tabText: {
        fontSize: ms(12),
        color: Colors.searchTextBlack,
    },
    activeTab: {
        borderBottomWidth: ms(2),
        borderBottomColor: Colors.secondary,
    },
    activeTabText: {
        fontSize: ms(12),
        color: Colors.secondary,
        fontWeight: 'bold',
    },
    pendingTabMargin: {
        marginLeft: ms(16),
    },
    recentLisMargin: {
        marginRight: ms(25),
    },
    filterButton: {
        alignItems: 'center',
        flexDirection: 'row',
        marginHorizontal: ms(16),
        marginTop: ms(5),
    },
    filterText: {
        marginHorizontal: ms(8),
        fontSize: ms(12),
        color: Colors.textPrimary,
        fontWeight: '700',
    },
    itemContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.white,
        paddingHorizontal: ms(15),
        paddingVertical: ms(10),
        marginBottom: ms(12),
        borderWidth: ms(1),
        borderRadius: ms(10),
        borderColor: Colors.searchBorderGrey,
    },
    itemText: {
        flex: 1,
        marginRight: ms(8),
        fontSize: ms(13),
        color: Colors.textPrimary,
    },
    mapButton: {
        position: 'absolute',
        bottom: 180,
        right: 5,
    },
    fabButton: {
        position: 'absolute',
        bottom: 110,
        right: -ms(3),
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: Colors.modelOverlay,
    },
    emptyContainer: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyImage: {
        width: ms(100),
        height: ms(100),
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: ms(10),
        fontSize: ms(16),
        fontWeight: 'bold',
        color: Colors.textPrimary,
    },
    searchStyle:{
        marginHorizontal: ms(16),
    }
});



