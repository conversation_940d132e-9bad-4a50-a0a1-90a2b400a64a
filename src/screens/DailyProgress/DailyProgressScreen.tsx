import { StackActions, useNavigation, CommonActions } from "@react-navigation/native";
import { RootState } from "../../redux/Root/rootStore";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { Text, TouchableOpacity, View, FlatList, StyleSheet, Image, Modal, Alert, Pressable, ActivityIndicator } from 'react-native';
import { SafeAreaView } from "react-native-safe-area-context";
import DirectionArrow from '../../assets/svg/direction_arrow.svg';
import ArrowDown from '../../assets/svg/arrow_down.svg';
import AddPath from '../../assets/svg/add_circle_blue.svg';
import MapDenoter from '../../assets/svg/map_denoter_blue.svg';
import Bookmark from '../../assets/svg/Bookmark.svg';
import PrintLog from "../../utils/Logger/PrintLog";
import AppHeader from "../../components/AppHeader";
import Strings from "../../utils/Strings/Strings";
import Colors from "../../utils/Colors/Colors";
import { database } from "../../database/index";
import { Q } from "@nozbe/watermelondb";
import { NavigationProp } from "@react-navigation/native";
import { EntityConstants, PendingForApprovalBQITRecord, WBSItem, WBSJobRecord } from "../../model/DailyProgress/DailyProgressData";
import { ms } from "../../utils/Scale/Scaling";
import SearchComponent from "../../components/SearchComponent";
import BottomPopupDeliverables, { DeliveryType } from "./components/BottomPopupDeliverables";
import { filterDataByType } from "./Helper/DataFilter";
import LoadingOverlay from "../../components/LoadingOverlay";
import { GetInputDetail, loadWBSItems, loadLatLongHierarchyData } from "../../database/helper/DatabaseHelper";
import { useTranslation } from "react-i18next";

export const DailyProgressView = () => {
    const navigation = useNavigation<NavigationProp<any>>();
    const { t } = useTranslation();
    const [activeTab, setActiveTab] = useState<tabType>('Recent List');
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [selectedFilter, setSelectedFilter] = useState<DeliveryType>('SWP');
    const [modalVisible, setModalVisible] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const selectedFilterRef = useRef<DeliveryType>('SWP');
    const isFilteringRef = useRef<boolean>(false);
    type tabType = 'Recent List' | 'Pending For Approval';
    const [wbsItems, setWbsItems] = useState<WBSItem[]>([]);
    const [pendingItems, setPendingItems] = useState<WBSItem[]>([]);
    const [filteredItems, setFilteredItems] = useState<WBSItem[]>([]);
    const [filteredPendingItems, setFilteredPendingItems] = useState<WBSItem[]>([]);
    const { currentJobId } = useSelector((state: RootState) => state.home);
    const [latLongData, setLatLongData] = useState<any[]>([]);

    // Load initial data
    useEffect(() => {
        if (!currentJobId) {
            navigation.dispatch(StackActions.replace('DownloadWBS'));
            return;
        } else {
            setIsLoading(true)
        }
    }, [currentJobId]);

    useEffect(() => {
        isLoading &&
            setTimeout(() => {
                loadInitialData(currentJobId);
            }, 100);
    }, [isLoading])

    const loadInitialData = async (jobCode: string) => {
        try {
            setIsLoading(true);

            // Load WBS items and LatLongHierarchy data in parallel
            const [wbsItems, pendingItems, latLongHierarchyData] = await Promise.all([
                loadWBSItems(jobCode, t('RecentStrings.recentList')),
                loadWBSItems(jobCode, t('PendingForApprovalStrings.pendingForApproval')),
                loadLatLongHierarchyData(jobCode)
            ]);

            setWbsItems(wbsItems || []);
            setPendingItems(pendingItems || []);
            setLatLongData(latLongHierarchyData || []);

            // Apply initial filter
            const { filteredWBS, filteredPending } = filterDataByType(selectedFilter, wbsItems || [], pendingItems || [], [], 'Recent');
            setFilteredItems(filteredWBS || []);
            setFilteredPendingItems(filteredPending || []);

        } catch (error) {
            console.error('Error loading initial data:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const walkWBSBranch = async (
        current: WBSItem,
        allItems: any[],
        path: WBSItem[],
        finalItems: WBSItem[]
    ) => {
        const newPath = [...path, current];

        if (current.leaf_Node_Tag === EntityConstants.Y) {
            // Try WBSTask
            const tasks = await database
                .get('WBSTask')
                .query(
                    Q.where('Job_Code', current.job_Code),
                    Q.where('Task_Code', current.entity_Code),
                    Q.where('Parent_WBS', current.parent_WBS_Code || ''),
                    Q.where('Parent_Task', current.parent_Task || ''),
                    Q.where('ET_Code', 'IT'),
                    Q.where('Is_Active', 'Y')
                ).fetch();

            if (tasks.length > 0) {
                // Task found
            } else if (current.gis_Tag === 'Y' && current.entity_Code.includes('~')) {
                const gisNodes = await database
                    .get('GISNode')
                    .query(
                        Q.where('Job_Code', current.job_Code),
                        Q.where('Parent_Entity_Code', current.parent_entity_code || ''),
                        Q.where('Parent_Task', current.parent_Task || '')
                    ).fetch();

                if (gisNodes.length > 0) {
                    // GIS node found
                }
            } else {
                await GetInputDetail(newPath);
            }

            // Final path
            const fullPath = newPath.map(i => i.entity_Description).join(' / ');
            finalItems.push({
                ...current,
                fullPath
            });

            return;
        }

        // Continue traversing children
        const children = allItems.filter(item => {
            const raw = item._raw as WBSJobRecord;
            return raw.Parent_WBS_Code === current.entity_Code && raw.ET_Code !== 'PR';
        });

        if (children.length === 0) {
            // Treat as final path if no children
            const fullPath = newPath.map(i => i.entity_Description).join(' / ');
            finalItems.push({
                ...current,
                fullPath
            });
            return;
        }

        for (const child of children) {
            const raw = child._raw as WBSJobRecord;

            const childItem: WBSItem = {
                id: child.id,
                entity_Code: raw.WBS_Code,
                job_Code: raw.Job_Code,
                entity_Type: EntityConstants.WBSJob,
                leaf_Node_Tag: raw.Leaf_Node_Tag,
                entity_Description: raw.WBS_Description,
                parent_WBS_Code: raw.Parent_WBS_Code,
                et_Code: raw.ET_Code,
                fullPath: '',
                parent_Task: undefined,
                gis_Tag: raw.GIS_Tag,
                parent_entity_code: raw.Parent_Entity_Code
            };

            await walkWBSBranch(childItem, allItems, newPath, finalItems);
        }
    };

    // Handle search
    useEffect(() => {
        if (isFilteringRef.current) {
            isFilteringRef.current = false;
            return;
        }

        if (searchQuery.trim()) {
            const filteredRecentList = filteredItems.filter(item =>
                item.fullPath.toLowerCase().includes(searchQuery.toLowerCase())
            );
            const filteredPendingList = filteredPendingItems.filter(item =>
                item.fullPath.toLowerCase().includes(searchQuery.toLowerCase())
            );

            setFilteredItems(filteredRecentList);
            setFilteredPendingItems(filteredPendingList);
        } else {
            setFilteredItems(wbsItems);
            setFilteredPendingItems(pendingItems);
        }
    }, [searchQuery])


    const handleTabChange = useCallback((tab: tabType) => {
        setActiveTab(tab);
    }, []);

    const handleFilterPress = useCallback((type: DeliveryType) => {
        isFilteringRef.current = true;
        selectedFilterRef.current = type;
        setSelectedFilter(type);
        setSearchQuery('');
        setModalVisible(false);

        const { filteredWBS, filteredPending } = filterDataByType(type, wbsItems, pendingItems, [], 'Recent');
        // setWbsItems(filteredWBS);
        // setPendingItems(filteredPending);
        setFilteredItems(filteredWBS || []);
        setFilteredPendingItems(filteredPending || []);

        PrintLog.debug('DailyProgressView', { 'handleFilterPress -- selectedFilter: ': type });
    }, [wbsItems, pendingItems]);

    const onItemClick = async (entityWBS: WBSItem[]) => {
        const result = await GetInputDetail(entityWBS);
        navigation.navigate('DailyProgressDetailsView', {
            selectedDeliverable: selectedFilterRef.current,
            inputDetail: result
        });
    };

    const renderItem = useCallback(({ item }: { item: WBSItem }) => (
        <TouchableOpacity
            style={styles.itemContainer}
            onPress={() => onItemClick([item])}
        >
            <Text style={styles.itemText}>{item.fullPath || item.entity_Description}</Text>
            <DirectionArrow />
        </TouchableOpacity>
    ), []);

    return (
        <SafeAreaView style={styles.container}>
            <AppHeader
                title={Strings.DailyProgress.progressUpdate}
                rightContent={<Bookmark />}
                onBookmarkPress={() => {
                    navigation.navigate('BookmarksView')
                }}
                onBackPress={() => {
                    navigation.dispatch(
                        CommonActions.reset({
                            index: 0,
                            routes: [{ name: 'Home' }],
                        })
                    );
                }}
            />

            <View style={styles.tabContainer}>
                <View style={styles.leftContainer}>
                    {['Recent List', 'Pending For Approval'].map((tab) => (
                        <TouchableOpacity
                            key={tab}
                            style={[styles.tab, activeTab === tab && styles.activeTab]}
                            onPress={() => handleTabChange(tab as tabType)}
                        >
                            <Text style={[styles.tabText,
                            activeTab === tab && styles.activeTabText,
                            activeTab !== tab && tab === 'Recent List' && styles.recentLisMargin,
                            activeTab !== tab && tab === 'Pending For Approval' && styles.pendingTabMargin
                            ]}>{tab}</Text>
                        </TouchableOpacity>
                    ))}
                </View>
                <TouchableOpacity style={styles.filterButton} onPress={() => {
                    setModalVisible(true)
                }}>
                    <Text style={styles.filterText}>{selectedFilter}</Text>
                    <ArrowDown />
                </TouchableOpacity>
            </View>

            <View style={styles.mainContainer}>
                <SearchComponent value={searchQuery} onChange={setSearchQuery} />
                <View style={styles.listWrapper}>
                    {activeTab === 'Recent List' ? (
                        <FlatList
                            data={filteredItems}
                            renderItem={renderItem}
                            keyExtractor={(item, index) => `${item.id}${index}`}
                            contentContainerStyle={styles.listContainer}
                            showsVerticalScrollIndicator={true}
                            bounces={true}
                            ListEmptyComponent={!isLoading ? (
                                <View style={styles.emptyContainer}>
                                    <Image
                                        source={require('../../assets/images/NoJobs.png')}
                                        style={styles.emptyImage}
                                        resizeMode="contain"
                                    />
                                </View>
                            ) : null}
                        />
                    ) : (
                        <FlatList
                            data={filteredPendingItems}
                            renderItem={renderItem}
                            keyExtractor={(item, index) => `${item.id}${index}`}
                            contentContainerStyle={styles.listContainer}
                            showsVerticalScrollIndicator={true}
                            bounces={true}
                            ListEmptyComponent={!isLoading ? (
                                <View style={styles.emptyContainer}>
                                    <Image
                                        source={require('../../assets/images/NoJobs.png')}
                                        style={styles.emptyImage}
                                        resizeMode="contain"
                                    />
                                </View>
                            ) : null}
                        />
                    )}
                </View>
            </View>

            {activeTab === 'Recent List' && (
                <>
                    <Pressable style={styles.mapButton} onPress={() => navigation.navigate('DailyProgressMapView', { mapData: latLongData })}>
                        <MapDenoter />
                    </Pressable>

                    <Pressable style={styles.fabButton} onPress={() => navigation.navigate('ProgressUpdateView')}>
                        <AddPath />
                    </Pressable>
                </>
            )}

            <Modal
                animationType="none"
                transparent
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <Pressable
                    style={styles.modalOverlay}
                    onPress={() => setModalVisible(false)}
                >
                    <Pressable>
                        <BottomPopupDeliverables
                            selectedType={selectedFilter}
                            onSelectType={(type) => {
                                handleFilterPress(type);
                            }} />
                    </Pressable>
                </Pressable>
            </Modal>
            {isLoading && <LoadingOverlay visible={isLoading} message={t('commonStrings.loadingData')} />}
        </SafeAreaView>
    );
};

export default DailyProgressView;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.containerligetBlue,
    },
    mainContainer: {
        flex: 1,
        position: 'relative',
    },
    listWrapper: {
        flex: 1,
        position: 'relative',
    },
    listContainer: {
        paddingHorizontal: ms(16),
        flexGrow: 1,
    },
    tabContainer: {
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.containerligetBlue,
        marginHorizontal: ms(5),
        marginTop: ms(5),
        paddingLeft: ms(16),
        paddingBottom: ms(8),
    },
    leftContainer: {
        flexDirection: 'row', // To place Item 1 and Item 2 side by side            // Adjust gap between the two texts
        flex: 1
    },
    tab: {
        paddingTop: ms(10),
        paddingBottom: ms(5),
        borderBottomWidth: ms(1),
        borderBottomColor: Colors.grey,
    },
    tabText: {
        fontSize: ms(12),
        color: Colors.searchTextBlack,
    },
    activeTab: {
        borderBottomWidth: ms(2),
        borderBottomColor: Colors.secondary,
    },
    activeTabText: {
        fontSize: ms(12),
        color: Colors.secondary,
        fontWeight: 'bold',
    },
    pendingTabMargin: {
        marginLeft: ms(16),
    },
    recentLisMargin: {
        marginRight: ms(25),
    },
    filterButton: {
        alignItems: 'center',
        flexDirection: 'row',
        marginHorizontal: ms(16),
        marginTop: ms(5),
    },
    filterText: {
        marginHorizontal: ms(8),
        fontSize: ms(12),
        color: Colors.textPrimary,
        fontWeight: '700',
    },
    itemContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.white,
        paddingHorizontal: ms(15),
        paddingVertical: ms(10),
        marginBottom: ms(12),
        borderWidth: ms(1),
        borderRadius: ms(10),
        borderColor: Colors.searchBorderGrey,
    },
    itemText: {
        flex: 1,
        marginRight: ms(8),
        fontSize: ms(13),
        color: Colors.textPrimary,
    },
    mapButton: {
        position: 'absolute',
        bottom: 180,
        right: 5,
    },
    fabButton: {
        position: 'absolute',
        bottom: 110,
        right: -ms(3),
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: Colors.modelOverlay,
    },
    emptyContainer: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyImage: {
        width: ms(100),
        height: ms(100),
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: ms(10),
        fontSize: ms(16),
        fontWeight: 'bold',
        color: Colors.textPrimary,
    },
});

