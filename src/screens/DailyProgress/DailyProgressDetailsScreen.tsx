import React, { useState, useCallback, useEffect, useMemo } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    SafeAreaView,
    ScrollView,
    Modal,
    ActivityIndicator,
    KeyboardAvoidingView,
    Platform,
    PermissionsAndroid,
} from 'react-native';
import Strings from '../../utils/Strings/Strings';
import DirectionArrow from '../../assets/svg/direction_arrow.svg';
import CalenderIcon from '../../assets/svg/calendar.svg';
import RadioSelected from '../../assets/svg/radio_selected.svg';
import RadioUnselected from '../../assets/svg/radio_unselected.svg';
import BookmarkUpdate from '../../assets/svg/bookmark_update.svg';
import { useNavigation } from '@react-navigation/native';
import { Picker } from '@react-native-picker/picker';
import BottomPopupImageUpload from '../../components/BottomPopupImageUpload';
import PrintLog from '../../utils/Logger/PrintLog';
import BottomCalendarModal from '../../components/CalendarPicker/BottomPopupCalendar';
import AppHeader from '../../components/AppHeader';
import TopTabNavigator from '../../components/TopTabNavigator';
import Colors from '../../utils/Colors/Colors';
import ButtonComponent from '../../components/ButtonComponent';
import { database } from '../../database'; // adjust path as needed
import WBSDetails from '../../database/model/WBSDetails';
import { Q } from '@nozbe/watermelondb';
import { ms } from '../../utils/Scale/Scaling';
import DailyProgressTextInput from './components/DailyProgressTextInput';
import EditPathOptionsPopup from './components/EditPathOptionsBottomPopup';
import DelEngineerScBottomPopup from './components/DelEngineerScBottomPopup';
import ProgressDetailsConsolidated from '../../database/model/ProgressDetailsConsolidated';
import ProgressUpdateEngineer from '../../database/model/ProgressUpdateEngineer';
import ViewLastUpdateBQIT from '../../database/model/ViewLastUpdateBQIT';
import ViewLastUpdateGIS from '../../database/model/ViewLastUpdateGIS';
import { RootState } from '../../redux/Root/rootStore';
import { useSelector, useDispatch } from 'react-redux';
import { formatDateWithFullYear, formatDateWithTwoDigitYear } from '../../utils/Constants/Validations';
import AttachmentComponent from '../../components/Attachment';
import { InsertProgressUpdateDetails, logInputDetails, logParentItems } from '../../database/ProgressUpdate/ProgressUpdateDBData';
// import { ProgressUpdateRequest } from '../../model/ProgressUpdate/ProgressUpdateActions';
import { t } from 'i18next';
import { customAlertWithOK } from '../../components/CustomAlert';
import LoadingOverlay from '../../components/LoadingOverlay';
import { getUserInfo } from '../../utils/DataStorage/Storage';
import { isNetworkConnected } from '../../utils/Network/NetworkConnection';
import { API, OPERATION_TYPES } from '../../utils/Constants/ApiConstants';
import { BookmarksRequestData } from '../../model/DailyProgress/BookmarksData';
import { bookmarkRequest, clearBookmarkData } from '../../redux/DailyProgressRedux/Bookmark/BookmarkActions';
import { showInfoToast, showSuccessToast } from '../../components/CustomToast';
import { getPendingBookmarks, saveBookmarkToLocalDB, updateBookmarkStatus } from '../../database/ProgressUpdate/BookmarkUpdateDBData';
import BookMarkList from '../../database/model/BookmarkList';
import { UploadImageResponse } from '../../model/DailyProgress/DailyProgressData';
import Geolocation from '@react-native-community/geolocation';
import { ProgressUpdateRequest } from '../../redux/ProgressUpdate/ProgressUpdateActions';
import EpragatiGisPipe from '../../database/model/EpragatiGisPipe';
import WBSGISDetails from '../../database/model/WBSGISDetails';
import DropDownPicker from '../../components/DropDownPicker';
import CustomDropdownPicker from '../../components/DropDownPicker';
import { insertBookMarkListRecord, removeBookmarkByParentWBSCode } from './Helper/BookmarkListHelper';
import moment from 'moment';
// import Geolocation from 'react-native-geolocation-service';

const DailyProgressDetailsScreen = ({ route }: { route: any }) => {
    const navigation = useNavigation();
    const dispatch = useDispatch();
    const { loading, error } = useSelector((state: RootState) => state.progressUpdate);

    const [progressQty, setProgressQty] = useState<string>('');
    const [manDays, setManDays] = useState<string>('');
    const [fromlength, setFromLength] = useState<string>('');
    const [actualLength, setActualLength] = useState<string>('');
    const [distance, setDistance] = useState<string>('');
    const [date, setDate] = useState(new Date());
    const [dateNew, setDateNew] = React.useState('');
    const [remarks, setRemarks] = useState<string>('');
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [alignment, setAlignment] = useState('Right');
    const [wbsDiameter, setWbsDiameter] = useState<string>('');
    const [designDia, setDesignDia] = useState<string>('');
    const [material, setMaterial] = useState<string>('');
    const [zone, setZone] = useState<string>('');
    const [startNodeId, setStartNodeId] = useState<string>('');
    const [stopNodeId, setStopNodeId] = useState<string>('');
    const [designLength, setDesignLength] = useState<string>('');
    const [imgUploadModalVisible, setImgUploadModalVisible] = useState(false);
    const [pathEditOptionsModalVisible, setPathEditOptionsModalVisible] = useState(false);
    const [delEngineerScModalVisible, setDelEngineerScModalVisible] = useState(false);
    const [startUpdate, setStartUpdate] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [tab, setTab] = useState<'dailyProgress' | 'hindrance' | 'nodeDetails'>('dailyProgress');
    const [wbsCode, setWbsCode] = useState<string>('');
    const [deliverableTypeCode, setDeliverableTypeCode] = useState<string>('');
    const [trimmedWBSPath, setTrimmedWBSPath] = useState<string>('');
    const [progressConsolidatedData, setProgressConsolidatedData] = useState<ProgressDetailsConsolidated | null>(null);
    const [progressEngineerData, setProgressEngineerData] = useState<ProgressUpdateEngineer | null>(null);
    const [gisDetailsData, setGisDetailsData] = useState<WBSGISDetails | null>(null);
    const { inputDetails, wbsPath, selectedItem, parentItems, fromWhere, wbsCodePathWithLevels,
        selectedTab = 'Recent List', imageUrl, imageId } = route.params;
    const { currentJobId } = useSelector((state: RootState) => state.home);
    const [progressInputDetails, setProgressInputDetails] = useState();
    const [updatedSuccessfully, setUpdated] = useState(false);
    const [apiFailed, setApiFailed] = useState(false);
    const user = getUserInfo();
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const { bookmarksData, bookmarksLoading, } = useSelector((state: RootState) => state.bookmark);
    const { response } = useSelector((state: RootState) => state.progressUpdate);
    const [imageResposne, setImageResponse] = React.useState<UploadImageResponse[]>([]);
    const [currrentLatitude, setCurrentLatitude] = useState<number>();
    const [currentLongitude, setCurrentLongitude] = useState<number>();
    const [pipeRecords, setPipeRecords] = useState<EpragatiGisPipe[]>([]);
    const [pipeIdList, setPipeIdList] = useState<{ label: string; value: string }[]>([]);
    const [selectedPipeRecord, setSelectedPipeRecord] = useState<EpragatiGisPipe | null>(null);
    const [selectedPipeId, setSelectedPipeId] = useState<string>('');
    const [isCalendarPopupVisible, setIsCalendarPopupVisible] = useState(false);
    const initialDate = inputDetails?.TDate ? moment(inputDetails.TDate) : moment();
    const [selectedDate, setSelectedDate] = useState(initialDate.format('DD/MM/YYYY'));

    // Split wbsPath and wbsCodePathWithLevels into separate variables
    const [entity_Description, wpDescription, childWorkDescription, deliverableCodeDesc] = (wbsPath || '').split(' / ').map((part: string) => part.trim());
    const [jobCode, entity_Code, wpCode, childWorkCode, deliverableCode] = (wbsCodePathWithLevels || '').split('~').map((part: string) => part.trim());
    // Now you can use entity_Description, wpDescription, childWorkDescription, deliverableCodeDesc, jobCode, entity_Code, wpCode, childWorkCode, deliverableCode as needed

    // Create parent_WBS_Code by removing the first segment (jobCode) from wbsCodePathWithLevels
    const parent_WBS_Code = (wbsCodePathWithLevels || '').split('~').slice(1).join('~');

    console.log('currentJobId: ', currentJobId);
    console.log('useEffect -- progressUpdate REsponsre: ', response);
    console.log('DailyProgressDetailsScreen -- Received route params:', {
        inputDetails,
        wbsPath,
        selectedItem,
        parentItems,
        fromWhere,
        wbsCodePathWithLevels,
        imageUrl,
        imageId,
    });
    // inputDetails.TaskType = 'GIS Node';
    // useEffect(() => {
    //     const tempInputDetails = { ...route.params.inputDetails };
    //     tempInputDetails.TaskType = 'GIS Node';
    //     // setInputDetails(tempInputDetails); 
    //     console.log('DailyProgressDetailsScreen - for Updating tasktype tempo-- Received route params:', {
    //       inputDetails: tempInputDetails,
    //       wbsPath: route.params.wbsPath,
    //       selectedItem: route.params.selectedItem,
    //       parentItems: route.params.parentItems,
    //       fromWhere: route.params.fromWhere,
    //       wbsCodePathWithLevels: route.params.wbsCodePathWithLevels
    //     });

    //     console.log('After assigning all -- inputDetails.TaskType: ',inputDetails.TaskType);
    //   }, []);

    useEffect(() => {
        printAllBookmarks();
    }, []);

    const requestLocationPermission = async () => {
        if (Platform.OS === 'android') {
            const granted = await PermissionsAndroid.request(
                PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
            );
            return granted === PermissionsAndroid.RESULTS.GRANTED;
        }
        return true; // iOS will ask automatically
    };

    const getCurrentLocation = (): Promise<{ latitude: number, longitude: number }> => {
        return new Promise((resolve, reject) => {
            Geolocation.getCurrentPosition(
                position => {
                    const { latitude, longitude } = position.coords;
                    resolve({ latitude, longitude });
                },
                error => {
                    if (error.code === 3) {
                        // Timeout fallback with lower accuracy
                        Geolocation.getCurrentPosition(
                            position => {
                                const { latitude, longitude } = position.coords;
                                resolve({ latitude, longitude });
                            },
                            fallbackError => {
                                reject(fallbackError);
                            },
                            {
                                enableHighAccuracy: false,
                                timeout: 10000,
                                maximumAge: 30000,
                            }
                        );
                    } else {
                        reject(error);
                    }
                },
                {
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 10000,
                }
            );
        });
    };


    // useEffect(() => {
    //     if (wbsPath) {
    //         console.log('inputDetails.WBS: ', wbsPath);

    //         // const [wbsCode, deliverableTypeCode] = wbsPath.split('~');
    //         const parts = wbsPath.split('~');
    //         // Find the value starting with 'WP'
    //         const wpValue = parts.find((part: string) => part.startsWith('WP'));
    //         // If no 'WP' found, fallback to value starting with 'SWP'
    //         const swpValue = parts.find((part: string) => part.startsWith('SWP'));

    //         // const result = wpValue || parts.find((part: string) => part.startsWith('SWP'));
    //         const result = swpValue;
    //         const deliverableTypeCode = parts[parts.length - 1];
    //         const trimmedWbsPath = parts.slice(1).join('~');
    //         console.log(result, 'deliverableTypeCode: ', deliverableTypeCode);
    //         setWbsCode(result);
    //         setDeliverableTypeCode(deliverableTypeCode);
    //         setTrimmedWBSPath(trimmedWbsPath);
    //         console.log('WBS Code:', result);
    //         console.log('DeliverableType Code:', deliverableTypeCode);
    //     }
    // }, [wbsPath]);

    useEffect(() => {
        if (fromWhere === 'DailyProgress') {
            if (inputDetails.WBS) {
                console.log('inputDetails.WBS: ', inputDetails.WBS, ' -- wbsPath: ', wbsPath,
                    ' -- wbsCodePathWithLevels: ', wbsCodePathWithLevels
                );
                const parts = inputDetails.WBS.split('~');
                // Find the value starting with 'WP'
                const wpValue = parts.find((part: string) => part.startsWith('WP'));
                // Find the value starting with 'SWP'
                const swpValue = parts.find((part: string) => part.startsWith('SWP'));
                // const result = wpValue || parts.find((part: string) => part.startsWith('SWP'));
                const result = swpValue;
                const deliverableTypeCode = parts[parts.length - 1];
                setWbsCode(result);
                setDeliverableTypeCode(deliverableTypeCode);
                console.log('WBS Code: ', result, ' -- deliverableTypeCode: ', deliverableTypeCode);
                let fullWbsPathArr: string[] = [];
                if (wbsCodePathWithLevels) {
                    fullWbsPathArr = wbsCodePathWithLevels.split('~');
                } else {
                    fullWbsPathArr = inputDetails.WBS.split('~');
                }
                const trimmedWbsPath = fullWbsPathArr.slice(1).join('~');
                setTrimmedWBSPath(trimmedWbsPath);
                console.log('trimmedWbsPath: ', trimmedWbsPath);
            }
        }
        else {
            if (inputDetails.WBS) {
                console.log('inputDetails.WBS: ', inputDetails.WBS, ' -- wbsPath: ', wbsPath);
                const parts = inputDetails.WBS.split('~');
                // Find the value starting with 'WP'
                const wpValue = parts.find((part: string) => part.startsWith('WP'));
                // Find the value starting with 'SWP'
                const swpValue = parts.find((part: string) => part.startsWith('SWP'));
                // const result = wpValue || parts.find((part: string) => part.startsWith('SWP'));
                const result = swpValue;
                const deliverableTypeCode = parts[parts.length - 1];
                setWbsCode(result);
                setDeliverableTypeCode(deliverableTypeCode);
                console.log('WBS Code: ', result, ' -- deliverableTypeCode: ', deliverableTypeCode);

                const fullWbsPathArr = wbsPath.split('~');
                const trimmedWbsPath = fullWbsPathArr.slice(1).join('~');
                setTrimmedWBSPath(trimmedWbsPath);
                console.log('trimmedWbsPath: ', trimmedWbsPath);
            }
        }
    }, [inputDetails.WBS]);

    useEffect(() => {
        return () => {
            dispatch(clearBookmarkData());
        };
    }, []);

    useEffect(() => {
        console.log('useEffect -- bookmarksData: ', bookmarksData);
        const upDateBookmarkStatus = async () => {
            if (bookmarksData) {
                // Handle Bookmark Linked
                if (bookmarksData?.bookMarklinkedOutput?.Message?.includes('Insert Successful')) {
                    await updateWBSDetailsBookmarkStatus(true);
                    const userId = user?.UID ? parseInt(user.UID.toString()) : 0;
                    const params = {
                        jobCode: inputDetails.JobCode,
                        wbsCode: parent_WBS_Code,
                        userId: userId,
                        hierarchyLevel: '', // or derive as needed
                        isActive: 'Y',
                        jobDescription: '',
                        ilCode: entity_Code || '',
                        ilDescription: entity_Description || '',
                        wpCode: wpCode || '',
                        wpDescription: wpDescription || '',
                        childWorkCode: childWorkCode || '',
                        childWorkDescription: childWorkDescription || '',
                        deliverableCode: deliverableCode,
                        deliverableCodeDesc: deliverableCodeDesc,
                        etCode: inputDetails.TaskType,
                    };
                    console.log("params before sending to db: ", params);
                    await insertBookMarkListRecord(params);
                    showSuccessToast(bookmarksData.bookMarklinkedOutput.Message);
                }
                // Handle Bookmark Unlinked
                else if (bookmarksData?.BookMarkUnlinkedOutput?.Message?.includes('Delete Successful')) {
                    await updateWBSDetailsBookmarkStatus(false);
                    await removeBookmarkByParentWBSCode(parent_WBS_Code);
                    showSuccessToast(bookmarksData.BookMarkUnlinkedOutput.Message);
                }
                // Handle other cases
                else if (
                    bookmarksData?.bookMarklinkedOutput?.Message ||
                    bookmarksData?.BookMarkUnlinkedOutput?.Message
                ) {
                    showInfoToast(
                        bookmarksData.bookMarklinkedOutput?.Message ||
                        bookmarksData.BookMarkUnlinkedOutput?.Message
                    );
                }
                setIsLoading(false);
            }
        };
        upDateBookmarkStatus();
    }, [bookmarksData]);

    // Fetch data from ProgressUpdateConsolidated table
    useEffect(() => {
        const fetchProgressConsolidatedData = async () => {

            if (wbsCode && deliverableTypeCode) {
                try {
                    const progressConsolidatedCollection = database.collections.get<ProgressDetailsConsolidated>('ProgressUpdateConsolidated');
                    const records = await progressConsolidatedCollection
                        .query(
                            Q.where('TDD_WBS_Code', wbsCode),
                            Q.where('TDD_Deliverable_Code', parseInt(deliverableTypeCode))
                        )
                        .fetch();

                    console.log('fetchProgressConsolidatedData -- records.length: ', records.length);
                    if (records.length > 0) {
                        const record = records[0];
                        setProgressConsolidatedData(record);
                        console.log('Progress Consolidated Data:', {
                            tddJobCode: record.tddJobCode,
                            tddWbsCode: record.tddWbsCode,
                            tddDeliverableCode: record.tddDeliverableCode,
                            delivscope: record.delivscope,
                            cumProg: record.cumProg,
                            cumManday: record.cumManday,
                            ftmProgress: record.ftmProgress,
                            ftmManday: record.ftmManday,
                            scScopeE: record.scScopeE,
                            scProgress: record.scProgress,
                            scManday: record.scManday,
                            cumPlanningQuantity: record.cumPlanningQuantity,
                            ftmPlanningQuantity: record.ftmPlanningQuantity
                        });
                    } else {
                        console.log('No matching records found in ProgressUpdateConsolidated');
                    }
                } catch (error) {
                    console.error('Error fetching ProgressUpdateConsolidated data:', error);
                }
            }
        };

        fetchProgressConsolidatedData();
    }, [wbsCode, deliverableTypeCode]);

    // Fetch data from ProgressUpdateEngineer table
    useEffect(() => {
        const fetchProgressEngineerData = async () => {
            console.log('fetchProgressEngineerData -- wbsCode:', wbsCode, 'deliverableTypeCode:', deliverableTypeCode);
            if (wbsCode && deliverableTypeCode) {
                try {
                    const progressEngineerCollection = database.collections.get<ProgressUpdateEngineer>('ProgressUpdateEngineer');

                    const records = await progressEngineerCollection
                        .query(
                            Q.where('TDD_WBS_Code', wbsCode),
                            Q.where('TDD_Deliverable_Code', parseInt(deliverableTypeCode))
                        )
                        .fetch();

                    console.log('fetchProgressEngineerData -- records.length: ', records.length);
                    if (records.length > 0) {
                        const record = records[0];
                        setProgressEngineerData(record);
                        console.log('Progress Engineer Data:', {
                            tddJobCode: record.tddJobCode,
                            tddWbsCode: record.tddWbsCode,
                            tddDeliverableCode: record.tddDeliverableCode,
                            delivscope: record.delivscope,
                            cumProg: record.cumProg,
                            cumManday: record.cumManday,
                            ftmProgress: record.ftmProgress,
                            ftmManday: record.ftmManday,
                            cumTargetPlanQty: record.cumTargetPlanQty,
                            ftmCumTargetPlanQty: record.ftmCumTargetPlanQty,
                            targetScope: record.targetScope
                        });
                    } else {
                        console.log('No matching records found in ProgressUpdateEngineer');
                    }
                } catch (error) {
                    console.error('Error fetching ProgressUpdateEngineer data:', error);
                }
            }
        };

        fetchProgressEngineerData();
    }, [wbsCode, deliverableTypeCode]);

    useEffect(() => {
        const fetchGISDetails = async () => {
            const wbs = 'SWP4~3679~1.B.5';
            const task = '1~6C1LS0BAM~0'
            console.log('fetchGISDetails -- wbsCode:', wbsCode, 'deliverableTypeCode:', deliverableTypeCode);
            if (inputDetails.TaskType === 'GIS Node' && wbsCode && deliverableTypeCode) {
                try {
                    const conditions: any[] = [
                        Q.where('JobCode', currentJobId)
                    ];

                    if (wbs && task) {
                        // Both exist — apply both (AND)
                        conditions.push(Q.where('WBS', wbs));
                        conditions.push(Q.where('Task', task));
                    } else if (wbs) {
                        // Only WBS exists
                        conditions.push(Q.where('WBS', wbs));
                    } else if (task) {
                        // Only Task exists
                        conditions.push(Q.where('Task', task));
                    }
                    const records = await database
                        .get('WBSGISDetails')
                        .query(...conditions)
                        .fetch();

                    console.log('fetchGISDetails -- records.length: ', records.length);
                    if (records.length > 0) {
                        const record = records[0];
                        setGisDetailsData(record as WBSGISDetails);
                        console.log('fetchGISDetails Data record: ', record._raw);
                    } else {
                        console.log('No matching records found in fetchGISDetails');
                    }
                } catch (error) {
                    console.error('Error fetching fetchGISDetails data:', error);
                }
            }
        };

        fetchGISDetails();
    }, [wbsCode, deliverableTypeCode]);

    useEffect(() => {
        const fetchNodeProgressDetails = async () => {
            console.log('fetchNodeProgressDetails -- wbsCode:', wbsCode, 'deliverableTypeCode:', deliverableTypeCode, ' -- inputDetails.taskType:', inputDetails.taskType);
            if (inputDetails.TaskType === 'GIS Node') {
                try {
                    const deliverableCode = '3679';
                    const boqCode = '1.B.5';

                    const collection = database.get<EpragatiGisPipe>('EpragatiGisPipes');
                    const gisRecords = await collection.query(
                        Q.where('Boq_Code', boqCode),
                        Q.where('Deliverable_Code', parseInt(deliverableCode))
                    ).fetch();

                    console.log('fetchNodeProgressDetails -- gisRecords.length: ', gisRecords[0]._raw);
                    if (gisRecords.length > 0) {
                        setPipeRecords(gisRecords);
                        // Extract Pipe_IDs for dropdown
                        const pipeIds = gisRecords
                            .map(rec => rec.Pipe_ID)
                            .filter(Boolean)
                            .map(id => ({ label: id, value: id }));

                        setPipeIdList(pipeIds);

                        console.log('All Pipe_IDs:', gisRecords.map(rec => rec.Pipe_ID));
                        console.log('pipeIdList.length:', pipeIdList.length);
                    } else {
                        setPipeRecords([]);
                        setPipeIdList([]);
                        console.log('No matching records found in fetchNodeProgressDetails');
                    }

                } catch (error) {
                    setPipeRecords([]);
                    setPipeIdList([]);
                    console.error('Error fetching fetchNodeProgressDetails data:', error);
                }
            }
        };

        fetchNodeProgressDetails();
    }, [wbsCode, deliverableTypeCode, inputDetails.TaskType]);

    useEffect(() => {
        console.log('pipeIdList.length:', pipeIdList.length);
        console.log('pipeIdList:', pipeIdList);
    }, [pipeIdList]);

    // Auto-select the first Pipe_ID in the dropdown if available
    useEffect(() => {
        if (pipeIdList.length > 0) {
            setSelectedPipeId(pipeIdList[0].value);
            const record = pipeRecords.find(rec => rec.Pipe_ID === pipeIdList[0].value);
            setSelectedPipeRecord(record || null);
        }
    }, [pipeIdList]);

    // const updateWBSDetailsBookmarkStatus = async (isBookmarked: boolean) => {
    //     try {
    //         const wbsDetailsRecord = await database.get<WBSDetails>('WBSDetails').find(inputDetails.id);
    //         await database.write(async () => {
    //             await wbsDetailsRecord.update(record => {
    //                 record.isBookmarked = isBookmarked;
    //             });
    //         });
    //         console.log(`WBSDetails ${wbsDetailsRecord.id} bookmarked status set to ${isBookmarked}`);
    //     } catch (error) {
    //         console.error("Failed to update WBSDetails bookmark status", error);
    //     }
    // };

    const updateWBSDetailsBookmarkStatus = async (isBookmarkedValue: boolean) => {
        console.log('updateWBSDetailsBookmarkStatus -- inputDetails.id: ', inputDetails.id);
        try {
            // Find the specific record by ID
            const wbsDetailsRecord = await database.get<WBSDetails>('WBSDetails').find(inputDetails.id);
            await database.write(async () => {
                await wbsDetailsRecord.update(record => {
                    record.isBookmarked = isBookmarkedValue;
                    // console.error('Updated Bookmark status:', isBookmarkedValue, ' -- inputDetails.id: ', inputDetails.id);
                });
            });
        } catch (error) {
            console.error('Failed to update isBookmarked status:', error);
        }
    };

    const callBookmarApi = async () => {
        const wbsDetailsRecord = await database.get<WBSDetails>('WBSDetails').find(inputDetails.id);
        const isBookmarked = wbsDetailsRecord.isBookmarked;
        const type = isBookmarked ? 'BookMarkUnLinked' : 'BookMarkLinked';
        console.log('callBookmarkApi outer -- inputDetails.id: ', inputDetails.id, ' -- isBookmarked: ', isBookmarked, ' -- trimmedWBSPath: ', trimmedWBSPath);
        const isInternetConnected = await isNetworkConnected();
        if (isInternetConnected) {
            console.log('callBookmarkApi');
            // await syncPendingBookmarks();
            const params: BookmarksRequestData = {
                Uid: user?.UID.toString() || '',
                Type: type,
                JobWorkLists: [
                    {
                        jobcode: currentJobId,
                        wpcode: trimmedWBSPath,
                    },
                ],
            };
            console.log('callBookmarApi -- params: ', params);
            dispatch(bookmarkRequest(params));
            // await updateWBSDetailsBookmarkStatus(true);
        } else {
            setIsLoading(false);
            console.log('callBookmarkApi -- No Internet, Save to Db');

            // No internet - save to local database
            const offlineBookmarkData = {
                jobCode: inputDetails.JobCode,
                wbsCode: parent_WBS_Code,
                userId: user?.UID || 0,
                hierarchyLevel: '', // or derive as needed
                isActive: 'Y',
                jobDescription: '',
                ilCode: entity_Code || '',
                ilDescription: entity_Description || '',
                wpCode: wpCode || '',
                wpDescription: wpDescription || '',
                childWorkCode: childWorkCode || '',
                childWorkDescription: childWorkDescription || '',
                deliverableCode: parseInt(deliverableCode),
                deliverableCodeDesc: deliverableCodeDesc,
                etCode: inputDetails.TaskType,
                type: type,
                wpCodeForSync: trimmedWBSPath,
            };

            const saved = await saveBookmarkToLocalDB(offlineBookmarkData);

            if (saved) {
                // await updateWBSDetailsBookmarkStatus(true);
                // await updateBookmarkStatus(bookmark.id, false); 
                customAlertWithOK(
                    'Bookmark/Unbookmark Saved',
                    'Bookmark/Unbookmark will be synced when internet connection is restored.'
                );

            } else {
                customAlertWithOK('Error', 'Failed to save bookmark offline');
            }
        }
    };

    const printAllBookmarks = async () => {
        try {
            const bookmarks = await database.get('BookMarkList').query().fetch();
            // Print each bookmark as a JSON string for clarity
            bookmarks.forEach(bm => {
                console.log('printAllBookmarks:', JSON.stringify(bm._raw, null, 2));
            });
        } catch (error) {
            console.error('printAllBookmarks Error fetching bookmarks:', error);
        }
    };

    const syncPendingBookmarks = async () => {
        try {
            const pendingBookmarks = await getPendingBookmarks();
            for (const item of pendingBookmarks) {
                const bookmark = item as BookMarkList;
                try {
                    const params: BookmarksRequestData = {
                        Uid: bookmark.userId.toString(),
                        Type: 'BookMarkLinked',
                        JobWorkLists: [
                            {
                                jobcode: bookmark.jobCode,
                                wpcode: bookmark.wbsCode,
                            },
                        ],
                    };

                    // Make API call
                    await dispatch(bookmarkRequest(params));

                    // Mark as synced
                    await updateBookmarkStatus(bookmark.id, false);
                    console.log('Synced pending bookmark:', bookmark.id);
                } catch (error) {
                    console.error('Error syncing bookmark:', bookmark.id, error);
                }
            }
        } catch (error) {
            console.error('Error in syncPendingBookmarks:', error);
        }
    };

    let fullPath = '';
    if (fromWhere === 'DailyProgress') {
        fullPath = wbsPath.split(' / ').join(' / ');
        console.log('Inside IF -- fullPath: ', fullPath);
    } else {
        fullPath = useMemo(() => {
            const pathParts = [
                ...parentItems.map((item: any) => item.WBS_Description),
                selectedItem?.entity_Description
            ].filter(Boolean);
            return pathParts.slice(1).join(' / ');
        }, [parentItems, selectedItem]);
    }
    const pathSegments = fullPath ? fullPath.split(' / ').filter(Boolean) : [];

    useEffect(() => {
        if (selectedTab === 'Pending For Approval' && inputDetails) {
            setManDays(inputDetails.PlanedLabour?.toString() ?? '');
            setRemarks(inputDetails.Remarks ?? '');
            setProgressQty(inputDetails.PlanedQty?.toString() ?? '');
            // ...other fields as needed
        } else {
            setManDays('');
            setRemarks('');
            setProgressQty('');
            // ...other fields as needed
        }
    }, [inputDetails, selectedTab]);

    useEffect(() => {
        setProgressInputDetails(inputDetails);
    }, []);

    const handleApply = (from: string, to: string) => {
        // Use the selected dates
        console.log('From:', from, 'To:', to);
    };

    const handlePipeIdChange = (item: { label: string; value: string }) => {
        setSelectedPipeId(item.value);
        const record = pipeRecords.find(rec => rec.Pipe_ID === item.value);
        setSelectedPipeRecord(record || null);
    };

    // const handleDateChange = useCallback((event: any, selectedDate?: Date) => {
    //     setShowDatePicker(false);
    //     if (selectedDate) {
    //         setDate(selectedDate);
    //     }
    // }, []);

    const handleAlignmentChange = useCallback((value: string) => {
        setAlignment(value);
    }, []);

    const handleGoBack = useCallback(() => {
        // Implement navigation logic
    }, []);

    const handleViewLastUpdate = useCallback(async () => {
        try {
            let records: (ViewLastUpdateBQIT | ViewLastUpdateGIS)[] = [];
            console.log('handleViewLastUpdate -- inputDetails.TaskType: ', inputDetails.TaskType, 'wbsCode:', wbsCode, 'deliverableTypeCode:', deliverableTypeCode);
            if (inputDetails.TaskType === 'BQ' || inputDetails.TaskType === 'IT') {
                const bqitCollection = database.collections.get<ViewLastUpdateBQIT>('ViewLastUpdateBQIT');

                records = await bqitCollection
                    .query(
                        Q.where('TDP_WBS_CODE', wbsCode),
                        Q.where('TDP_DELIVERABLE_CODE', parseInt(deliverableTypeCode))
                    )
                    .fetch();

                console.log('handleViewLastUpdate -- records.length: ', records.length);
            }
            // else if (inputDetails.TaskType === 'Gis') {
            //     const gisCollection = database.collections.get<ViewLastUpdateGIS>('ViewLastUpdateGIS');
            //     records = await gisCollection.query().fetch();
            // }
            else {
                const gisCollection = database.collections.get<ViewLastUpdateGIS>('ViewLastUpdateGIS');
                records = await gisCollection.query(
                    Q.where('TDP_WBS_CODE', wbsCode),
                    Q.where('TDP_DELIVERABLE_CODE', parseInt(deliverableTypeCode))
                ).fetch();
            }

            // Map to historyItem[] format expected by LastUpdateScreen
            const historyData = records.map(record => ({
                updateDate: record.insertedOn ? new Date(record.insertedOn).toLocaleDateString('en-GB') : '-',
                uom: record.muomShortDescription ?? '-',
                quantity: record.qty ?? 0,
                manDays: record.man ?? 0,
            }));
            console.log('handleViewLastUpdate -- historyData: ', historyData);

            navigation.navigate('LastUpdateView', {
                historyData,
                plannedStartDate: formatDateWithTwoDigitYear(inputDetails?.PlanedSDate),
                plannedEndDate: formatDateWithTwoDigitYear(inputDetails?.PlanedEDate),
                actualStartDate: formatDateWithTwoDigitYear(inputDetails?.ActualSDate),
                actualEndDate: formatDateWithTwoDigitYear(inputDetails?.ActualEDate),
                fullPath: fullPath
            });
        } catch (error) {
            console.error('Error fetching ViewLastUpdate data:', error);
        }
    }, [navigation, wbsCode, deliverableTypeCode, fullPath]);

    const handleProgressQtySelect = useCallback(() => {
        // Implement progress qty selection logicc
    }, []);

    const handleDatePicker = useCallback(() => {
        // Implement date picker logic
        logInputDetails();
        logParentItems();
    }, []);

    const handleUploadImage = useCallback(() => {
        setImgUploadModalVisible(true);
    }, []);

    const handleProgressUpdate = () => {
        console.log('handleProgressUpdate -- inputDetails.TaskType: ', inputDetails.TaskType, 'wbsCode:', wbsCode, 'deliverableTypeCode:', deliverableTypeCode, ' -- inputDetails: ', inputDetails);
        // pasted here api body params reference
        // const requestParams = {
        //     jobCode: "LE21M114",
        //     UID: "1159553",
        //     Type: "insert",
        //     Notification_Desc: "Compound wall Construction~Compound wall Construction - 170 Nos~PCC 1:4:8 - Non Billable",
        //     Quantity: 2.0,
        //     uOM: "m³",
        //     manPower: 2.0,
        //     ActualList: [
        //         {
        //             WBS: inputDetails.WBS,
        //             TaskCode: inputDetails.Task,
        //             ADate: inputDetails.ActualSDate,
        //             Quantity: progressQty,
        //             Manpower: "2",
        //             Remarks: "Test",
        //             Tasktype: "BQ",
        //             Is_Approved: "N",
        //             Tag: "Y",
        //             Latitude: 9.555293,
        //             Longitude: 78.5882628
        //         }
        //     ],
        //     "Attachments": null
        // }

        // Dispatch the ProgressUpdateRequest action
        dispatch(ProgressUpdateRequest(
            {
                jobCode: inputDetails.JobCode,
                UID: user?.UID.toString() || '',
                Type: OPERATION_TYPES.INSERT,
                Notification_Desc: "Compound wall Construction~Compound wall Construction - 170 Nos~PCC 1:4:8 - Non Billable", //hardcoded
                Quantity: parseFloat(inputDetails.PlanedQty.toFixed(1)),
                uOM: "m³", //hardcoded
                manPower: parseFloat(inputDetails.PlanedLabour.toFixed(1)),
                ActualList: [
                    {
                        WBS: inputDetails.WBS,
                        TaskCode: inputDetails.Task,
                        // ADate: date.toISOString().split('T')[0],
                        ADate: moment(selectedDate, 'DD/MM/YYYY').format('YYYY-MM-DDT00:00:00'),
                        Quantity: progressQty,
                        Manpower: manDays,
                        Remarks: remarks,
                        Tasktype: inputDetails.TaskType,
                        Is_Approved: 'N', //hardcoded
                        // Tag: selectedItem.leaf_Node_Tag,
                        // Latitude: currrentLatitude ?? 0,
                        // Longitude: currentLongitude ?? 0,
                        // Latitude: 9.555293, //hardcoded
                        // Longitude: 78.5882628 //hardcoded
                        Latitude: 11.958401,
                        Longitude: 79.829900,
                    }
                ],
                Attachments: imageResposne.map((resp: UploadImageResponse) => ({
                    WBS: inputDetails.WBS,
                    TaskCode: inputDetails.Task,
                    ADate: date.toISOString().split('T')[0],
                    Tasktype: inputDetails.TaskType,
                    SiteUrl: resp.SiteUrl,
                    Unique: resp.UniqueID
                })),
                // Attachments: [],

                // jobCode: 'LE23M849',
                // UID: '100027888' || '',
                // Type: OPERATION_TYPES.INSERT,
                // Notification_Desc: "Compound wall Construction~Compound wall Construction - 170 Nos~PCC 1:4:8 - Non Billable", //hardcoded
                // Quantity: 17.0,
                // uOM: "m³", //hardcoded
                // manPower: 10.0,
                // ActualList:       [ { WBS: 'SWP10~1022',
                // TaskCode: '1000',
                // ADate: '2025-06-27',
                // Quantity: '1757',
                // Manpower: '20',
                // Remarks: 'Test',
                // Tasktype: 'BQ',
                // Is_Approved: 'N',
                // Tag: 'Y',
                // Latitude: 9.555293,
                // Longitude: 78.5882628 } ],
                // Attachments: [],
                cb: ({ success, data }) => {
                    console.log('Progress Update Response -- success: ', success, 'data: ', data);
                    if (success && data && !data.ErrorMessage) {
                        console.log('Progress Update Response:', data);
                        handleOfflineStorage(t('ProgressUpdate.updateSuccess'));
                        // customAlertWithOK(
                        //     t('SyncData.progressUpdate'),
                        //     t('ProgressUpdate.updateSuccess'),
                        //     [{
                        //         text: t('commonStrings.ok'),
                        //         onPress: () => {
                        //             navigation.navigate('DailyProgressView');
                        //         }
                        //     }],
                        //     false
                        // );
                    } else {
                        console.log('Progress Update Response -- ELSE API FAILED: ');
                        setApiFailed(true);
                    }
                }
            }
        ));
    }

    useEffect(() => {
        updatedSuccessfully && logInputDetails();
        updatedSuccessfully && logParentItems();
    }, []);

    const handleOfflineStorage = useCallback(async (message: string) => {
        const data = {
            ...route.params,
            // inputDetails: progressInputDetails
            inputDetails: {
                ...progressInputDetails,
                WBS_Description: fullPath,
                taskCustomDescription: '',
            }
        };
        console.log('handleOfflineStorage -- data: ', data);
        InsertProgressUpdateDetails(data);
        setUpdated(startUpdate);
        customAlertWithOK(
            t('SyncData.progressUpdate'),
            t(message),
            [{
                text: t('commonStrings.ok'),
                onPress: () => {
                    navigation.navigate('DailyProgressView');
                }
            }],
            false
        );
    }, [inputDetails, progressQty, manDays, remarks, date, parentItems, selectedItem, progressInputDetails]);

    const handleUploadComplete = (imageResponse: UploadImageResponse[]) => {
        console.log('Uploaded Image IDs:', imageResponse);
        setImageResponse(imageResponse); // store or use them as needed
    };

    useEffect(() => {
        if (startUpdate) {
            // Check internet connectivity
            isNetworkConnected(false).then((isConnected) => {
                if (isConnected) {
                    // If internet is available
                    handleProgressUpdate();
                } else {
                    handleOfflineStorage(t('ProgressUpdate.storedOffline'));
                }
            });
        }
    }, [startUpdate]);

    useEffect(() => {
        if (apiFailed) {
            handleOfflineStorage(t('ProgressUpdate.storedOffline'));
            setApiFailed(false); // Reset the flag
        }
    }, [apiFailed, handleOfflineStorage]);

    const handleUpdate = useCallback(async () => {
        try {
            console.log('handleUpdate -- remarks:', remarks, 'progressQty:', progressQty, 'manDays:', manDays, 'date:', date, 'imageResposne:', imageResposne);
            // const hasPermission = await requestLocationPermission();
            // if (!hasPermission) {
            //     customAlertWithOK('Alert', 'Location Services are not enabled on device.');
            //     return;
            // }
            // const { latitude, longitude } = await getCurrentLocation();
            // setCurrentLatitude(parseFloat(latitude.toFixed(6)));
            // setCurrentLongitude(parseFloat(longitude.toFixed(6)));
            // console.log('handleUpdate -- latitude:', latitude, 'longitude:', longitude, ' -- currentLatitude:', currrentLatitude, 'currentLongitude:', currentLongitude);
            setProgressInputDetails((prev: any) => ({
                ...prev,
                Remarks: remarks,
                PlanedQty: progressQty,
                ManDays: manDays,
                date: date,
                attachmentIds: imageResposne,
                // latitude: currrentLatitude,
                // longitude: currentLongitude
            }));

            // const getCurrentLocation = (): Promise<{ latitude: number, longitude: number }> => {
            //     return new Promise((resolve, reject) => {
            //       Geolocation.getCurrentPosition(
            //         (position) => {
            //           const { latitude, longitude } = position.coords;
            //           console.log('High accuracy location success');
            //           resolve({ latitude, longitude });
            //         },
            //         (error) => {
            //           console.warn('High accuracy failed:', error);
            //           if (error.code === 3) {
            //             // Timeout fallback with low accuracy
            //             Geolocation.getCurrentPosition(
            //               (position) => {
            //                 const { latitude, longitude } = position.coords;
            //                 console.log('Low accuracy location fallback success');
            //                 resolve({ latitude, longitude });
            //               },
            //               (fallbackError) => {
            //                 console.warn('Low accuracy failed:', fallbackError);
            //                 reject(fallbackError);
            //               },
            //               {
            //                 enableHighAccuracy: false,
            //                 timeout: 10000,
            //                 maximumAge: 30000,
            //               }
            //             );
            //           } else {
            //             reject(error);
            //           }
            //         },
            //         {
            //           enableHighAccuracy: true,
            //           timeout: 10000,
            //           maximumAge: 10000,
            //         //   forceRequestLocation: true,
            //         }
            //       );
            //     });
            //   };
            console.log('hanlde update -- startUpdate:', startUpdate);
            setStartUpdate(true);
        } catch (e) {
            console.error('Error in handleUpdate:', e);
        }

    }, [remarks, progressQty, manDays, date]);

    // Auto-fill fields and update tabs based on gisDetailsData
    useEffect(() => {
        if (gisDetailsData) {
            setFromLength(gisDetailsData.fromLength?.toString() ?? '');
            setActualLength(gisDetailsData.totalLength?.toString() ?? '');
            setManDays(gisDetailsData.manpower?.toString() ?? '');
            setDistance(gisDetailsData.distanceFCenter?.toString() ?? '');
            // Map 'R'/'L' to 'Right'/'Left'
            if (gisDetailsData.alignment === 'R') {
                setAlignment('Right');
            } else if (gisDetailsData.alignment === 'L') {
                setAlignment('Left');
            } else {
                setAlignment('Right'); // default
            }
            if (gisDetailsData.isHindrance === 'N') {
                setTabs([
                    { key: 'dailyProgress', label: Strings.DailyProgress.dailyProgress },
                    { key: 'nodeDetails', label: Strings.DailyProgress.nodeDetails }
                ]);
                if (tab === 'hindrance') setTab('dailyProgress');
            } else {
                setTabs([
                    { key: 'dailyProgress', label: Strings.DailyProgress.dailyProgress },
                    { key: 'hindrance', label: Strings.DailyProgress.hindarnce },
                    { key: 'nodeDetails', label: Strings.DailyProgress.nodeDetails }
                ]);
            }
        }
    }, [gisDetailsData]);

    useEffect(() => {
        if (selectedPipeRecord) {
            setWbsDiameter(selectedPipeRecord.PRAGATI_WBS_Dia?.toString() || '');
            setDesignDia(selectedPipeRecord.Design_Dia?.toString() || '');
            setMaterial(selectedPipeRecord.Material || '');
            setZone(selectedPipeRecord.ZONE || '');
            setStartNodeId(selectedPipeRecord.Start_Node_ID || '');
            setStopNodeId(selectedPipeRecord.Stop_Node_ID || '');
            setDesignLength(selectedPipeRecord.Design_Length?.toString() || '');
        }
    }, [selectedPipeRecord]);

    const [tabs, setTabs] = useState<{ key: 'dailyProgress' | 'hindrance' | 'nodeDetails'; label: string }[]>([
        { key: 'dailyProgress', label: Strings.DailyProgress.dailyProgress },
        { key: 'hindrance', label: Strings.DailyProgress.hindarnce },
        { key: 'nodeDetails', label: Strings.DailyProgress.nodeDetails }
    ]);

    const isEditable = selectedTab === 'Recent List';

    return (
        <SafeAreaView style={styles.container}>
            <KeyboardAvoidingView
                style={styles.container}
                behavior={Platform.OS === 'ios' ? 'padding' : undefined}
            >
                <AppHeader
                    title={Strings.DailyProgress.progressUpdate}
                    rightContent={<BookmarkUpdate />}
                    onBookmarkPress={() => {
                        console.log('callBookmarkApi Triggered');
                        setIsLoading(true);
                        callBookmarApi();
                    }}
                />
                {
                    (loading) &&
                    <LoadingOverlay
                        visible={true}
                        message={t('commonStrings.pleaseWait')}
                    />
                }
                <ScrollView>
                    <View style={styles.content}>
                        <TouchableOpacity
                            style={styles.pipelineInfo}
                            onPress={() => { setPathEditOptionsModalVisible(true) }}
                        >
                            <Text style={styles.pipelineText}>
                                {fullPath.split(/(\/)/g).map((part, idx) =>
                                    part === '/' ? (
                                        <Text key={idx} style={{ color: Colors.textInputBlack }}>/</Text>
                                    ) : (
                                        <Text key={idx} style={{ color: Colors.textPrimary }}>{part}</Text>
                                    )
                                )}
                            </Text>
                            <View style={styles.arrowIconStyle}>
                                <DirectionArrow />
                            </View>
                        </TouchableOpacity>

                        <View style={styles.progressUpdateContainer}>
                            <Text style={styles.sectionTitle}>{Strings.DailyProgress.progressUpdate}</Text>
                            <TouchableOpacity onPress={() => { handleViewLastUpdate() }}>
                                <Text style={styles.viewLastUpdate}>{Strings.DailyProgress.viewLastUpdate}</Text>
                            </TouchableOpacity>
                        </View>

                        <TouchableOpacity style={styles.progressStats} onPress={() => { setDelEngineerScModalVisible(true) }}>
                            {inputDetails.TaskType !== 'IT' ? (
                                <>
                                    <View style={styles.statItem}>
                                        <Text style={styles.statLabel}>Scope</Text>
                                        <Text style={styles.statValue}>{inputDetails?.Scope ?? '-'}</Text>
                                    </View>
                                    <View style={styles.statItem}>
                                        <Text style={styles.statLabel}>Cum.Prog</Text>
                                        <Text style={styles.statValue}>{progressConsolidatedData?.cumProg ? Number(progressConsolidatedData.cumProg).toFixed(2) : '-'}</Text>
                                    </View>
                                    <View style={styles.statItem}>
                                        <Text style={styles.statLabel}>S/C Scope</Text>
                                        <Text style={styles.statValue}>{progressConsolidatedData?.cumProg ? Number(progressConsolidatedData.scScopeE).toFixed(2) : '-'}</Text>
                                    </View>
                                    <View style={styles.statItem}>
                                        <Text style={styles.statLabel}>S/C Prog</Text>
                                        <Text style={styles.statValue}>{progressConsolidatedData?.cumProg ? Number(progressConsolidatedData.scProgress).toFixed(2) : '-'}</Text>
                                    </View>
                                </>
                            ) : (<>
                                <View style={styles.statItemforIT}>
                                    <Text style={styles.statLabel}>Scope</Text>
                                    <Text style={[styles.statValue, { marginLeft: 15 }]}>{inputDetails?.Scope ?? '-'}</Text>
                                </View>
                                <View style={styles.statItemforIT}>
                                    <Text style={styles.statLabel}>Cum.Prog</Text>
                                    <Text style={[styles.statValue, { marginLeft: 15 }]}>{progressConsolidatedData?.cumProg ? Number(progressConsolidatedData.cumProg).toFixed(2) : '-'}</Text>
                                </View>
                            </>)
                            }
                            <DirectionArrow />
                        </TouchableOpacity>

                        {inputDetails.TaskType === 'GIS Node' && (
                            <>
                                <View style={styles.selectContainer}>
                                    <Text style={styles.selectLabel}>Select Pipe ID</Text>
                                    <TouchableOpacity onPress={() => { setModalVisible(true) }}>
                                        <Text style={styles.historyButtonText}>History</Text>
                                    </TouchableOpacity>
                                </View>

                                {/* <View style={styles.pickerContainer}>
                                    <Picker
                                        selectedValue={selectedPipeId}
                                        onValueChange={handlePipeIdChange}
                                        style={styles.picker}
                                    >
                                        <Picker.Item label="Pipe 100" value="Pipe 100" />
                                    </Picker>
                                </View> */}
                                {/* <DropDownPicker
                                    data={[]}
                                    defaultValue={selectedPipeId}
                                    onSelect={handlePipeIdChange}
                                    placeholder="Select Pipe ID"
                                /> */}

                                <CustomDropdownPicker
                                    value={selectedPipeId}
                                    data={pipeIdList}
                                    onSelect={handlePipeIdChange}
                                />

                                <View style={[styles.line, { marginHorizontal: 2, marginTop: 15, marginBottom: 15 }]}></View>

                                <View style={styles.tabContainer}>
                                    <TopTabNavigator
                                        containerStyle={styles.tabContainerOuterStyle}
                                        tabs={tabs}
                                        activeTab={tab}
                                        onTabChange={(key) => setTab(key as 'dailyProgress' | 'hindrance' | 'nodeDetails')}
                                    />
                                </View>
                            </>
                        )}
                        {tab !== 'nodeDetails' && (
                            <View style={styles.inputContainer}>
                                {/* <Text style={styles.inputLabel}>Date *</Text>
                                <TouchableOpacity disabled={!isEditable} style={styles.dateInput} onPress={handleDatePicker}>
                                    <Text>{formatDateWithFullYear(date)}</Text>
                                    <CalenderIcon />
                                </TouchableOpacity> */}
                                <Text style={styles.inputLabel}>Date *</Text>
                                <TouchableOpacity
                                    style={styles.dateInput}
                                    onPress={() => setIsCalendarPopupVisible(true)}>
                                    <Text>{selectedDate}</Text>
                                    <CalenderIcon />
                                </TouchableOpacity>
                            </View>
                        )}

                        {inputDetails.TaskType !== 'GIS Node' && (
                            <DailyProgressTextInput
                                label="Progress Qty *"
                                value={progressQty}
                                onChangeText={setProgressQty}
                                isKeypadNumeric={true}
                                editable={isEditable}
                            />
                        )}
                        {/* {tab !== 'nodeDetails' && (
                            <View style={styles.inputContainer}>
                                <Text style={styles.inputLabel}>Date *</Text>
                                <TouchableOpacity style={styles.dateInput} onPress={handleDatePicker}>
                                    <Text>{formatDateWithFullYear(date)}</Text>
                                    <CalenderIcon />
                                </TouchableOpacity>
                            </View>
                        )} */}
                        {inputDetails.TaskType === 'GIS Node' && tab !== 'nodeDetails' && (
                            <>
                                <DailyProgressTextInput
                                    label="From Length"
                                    value={fromlength}
                                    onChangeText={setFromLength}
                                    isKeypadNumeric={true}
                                    editable={isEditable}
                                />

                                <DailyProgressTextInput
                                    label="Actual Length"
                                    value={actualLength}
                                    onChangeText={setActualLength}
                                    isKeypadNumeric={true}
                                    editable={isEditable}
                                />
                            </>
                        )}
                        {tab !== 'nodeDetails' && (
                            <DailyProgressTextInput
                                label="Man Days *"
                                value={manDays}
                                onChangeText={setManDays}
                                isKeypadNumeric={true}
                                editable={isEditable}
                            />
                        )}

                        {inputDetails.TaskType === 'GIS Node' && tab !== 'nodeDetails' && (
                            <>
                                <DailyProgressTextInput
                                    label="Distance from road center (meter)"
                                    value={distance}
                                    onChangeText={setDistance}
                                    isKeypadNumeric={true}
                                    editable={isEditable}
                                />

                                <Text style={styles.label}>Alignment</Text>
                                <View style={styles.radioContainer}>
                                    <TouchableOpacity style={styles.flexDirectionRowStyle}
                                        onPress={() => handleAlignmentChange('Right')}
                                        disabled={!isEditable}>
                                        {alignment === 'Right' ? (
                                            <RadioSelected width={18} height={18} />
                                        ) : (
                                            <RadioUnselected width={18} height={18} />
                                        )}

                                        <Text style={styles.radioLabel}>Right</Text>
                                    </TouchableOpacity>

                                    <TouchableOpacity style={[styles.flexDirectionRowStyle, styles.marginLeftStyle]} onPress={() => handleAlignmentChange('Left')}
                                        disabled={!isEditable}>
                                        {alignment === 'Left' ? (
                                            <RadioSelected width={18} height={18} />
                                        ) : (
                                            <RadioUnselected width={18} height={18} />
                                        )}

                                        <Text style={styles.radioLabel}>Left</Text>
                                    </TouchableOpacity>
                                </View>
                            </>
                        )}
                        {tab !== 'nodeDetails' && (

                            <>
                                <DailyProgressTextInput
                                    customStyle={[styles.textInput, styles.remarksInput]}
                                    label="Remarks *"
                                    value={remarks}
                                    onChangeText={setRemarks}
                                    isMultiline={true}
                                    editable={isEditable}
                                />

                                <AttachmentComponent
                                    onUploadComplete={handleUploadComplete}
                                    uploadedImages={imageResposne}
                                    selectedTab={selectedTab}
                                    imageUrl={imageUrl}
                                    imageId={imageId}
                                />
                            </>
                        )}
                        {inputDetails.TaskType === 'GIS Node' && tab === 'nodeDetails' && selectedPipeRecord && (
                            <>
                                <DailyProgressTextInput
                                    label="WBS Diameter"
                                    value={wbsDiameter}
                                    onChangeText={setWbsDiameter}
                                    isKeypadNumeric={true}
                                    editable={isEditable}
                                />
                                <DailyProgressTextInput
                                    label="Design Dia"
                                    value={designDia}
                                    onChangeText={setDesignDia}
                                    isKeypadNumeric={true}
                                    editable={isEditable}
                                />
                                <DailyProgressTextInput
                                    label="Material"
                                    value={material}
                                    onChangeText={setMaterial}
                                    isKeypadNumeric={true}
                                    editable={isEditable}
                                />
                                <DailyProgressTextInput
                                    label="Zone"
                                    value={zone}
                                    onChangeText={setZone}
                                    isKeypadNumeric={true}
                                    editable={isEditable}
                                />
                                <DailyProgressTextInput
                                    label="Start Node ID"
                                    value={startNodeId}
                                    onChangeText={setStartNodeId}
                                    isKeypadNumeric={true}
                                    editable={isEditable}
                                />
                                <DailyProgressTextInput
                                    label="Stop Node ID"
                                    value={stopNodeId}
                                    onChangeText={setStopNodeId}
                                    isKeypadNumeric={true}
                                    editable={isEditable}
                                />
                                <DailyProgressTextInput
                                    label="Design Length"
                                    value={designLength}
                                    onChangeText={setDesignLength}
                                    isKeypadNumeric={true}
                                    editable={isEditable}
                                />
                            </>
                        )}
                    </View>

                    <View style={styles.line}></View>
                    <Modal
                        animationType="none"
                        transparent
                        visible={modalVisible}
                        onRequestClose={() => setModalVisible(false)}
                    >
                        <View style={styles.modalOverlay}>
                            <BottomCalendarModal

                                visible={modalVisible}
                                fromDateTitle={'SyncData.fromdate'}
                                initialDate={new Date()}
                                selectedFromDate={dateNew}
                                onApply={(from: string, to: string) => {
                                    console.log('fromdate', from)
                                    setDateNew(from);
                                    // setShowCalendar(false);
                                    console.log('to------', to);
                                }}
                                onClose={() => setModalVisible(false)}
                            />
                        </View>
                    </Modal>

                    <BottomCalendarModal
                        visible={isCalendarPopupVisible}
                        initialDate={inputDetails?.TDate ? new Date(inputDetails.TDate) : new Date()}
                        onClose={() => setIsCalendarPopupVisible(false)}
                        fromDateTitle="Date"
                        selectedFromDate={moment(selectedDate, 'DD/MM/YYYY').format('D/MMM/YYYY')}
                        onApply={(date, _) => {
                            // date is in 'D/MMM/YYYY', convert to 'DD/MM/YYYY' for display and 'YYYY-MM-DD' for storage
                            const displayDate = moment(date, 'D/MMM/YYYY').format('DD/MM/YYYY');
                            setSelectedDate(displayDate);
                            inputDetails.TDate = moment(date, 'D/MMM/YYYY').format('YYYY-MM-DD');
                            setIsCalendarPopupVisible(false);
                        }}
                        allowFutureDate={false}
                        singleDateMode={true}
                        showBottomButton={true}
                    />
                    <DelEngineerScBottomPopup
                        visible={delEngineerScModalVisible}
                        isShowScTab={inputDetails.taskType !== 'IT'}
                        progressConsolidatedData={progressConsolidatedData}
                        progressEngineerData={progressEngineerData}
                        onClose={() => setDelEngineerScModalVisible(false)}
                    />

                    <EditPathOptionsPopup
                        visible={pathEditOptionsModalVisible}
                        onClose={() => setPathEditOptionsModalVisible(false)}
                        pathSegments={pathSegments}
                        onEditPath={() => {
                            setPathEditOptionsModalVisible(false);
                            navigation.reset({
                                index: 1,
                                routes: [
                                    { name: 'Home' },
                                    { name: 'DailyProgressView' },
                                    { name: 'ProgressUpdateView' }
                                ],
                            });
                        }} />

                    {(isLoading || bookmarksLoading) && (
                        <ActivityIndicator style={styles.activityIndicatorStyle} size="large" color={Colors.forgotPinBlue}
                            testID="activity-indicator" />
                    )}
                </ScrollView>
                <ButtonComponent
                    title={Strings.DailyProgress.update}
                    onPress={isEditable ? () => {
                        const missingFields = [];
                        if (!progressQty) missingFields.push('Progress Quantity');
                        if (!manDays) missingFields.push('Man Days');
                        if (!selectedDate) missingFields.push('Date');
                        if (!remarks) missingFields.push('Remarks');
                        if (missingFields.length > 0) {
                            customAlertWithOK(
                                'Alert',
                                `Please enter ${missingFields.join(', ')} data`
                            );
                            return;
                        }
                        handleUpdate();
                    } : undefined}
                    disabled={!isEditable}
                />
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
};

export default DailyProgressDetailsScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.white,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: ms(16),
        backgroundColor: Colors.white,
    },
    headerTitle: {
        fontSize: ms(18),
        fontWeight: 'bold',
    },
    content: {
        paddingVertical: ms(16),
        paddingHorizontal: ms(16),
    },
    pipelineInfo: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        backgroundColor: Colors.containerligetBlue,
        padding: ms(16),
        borderRadius: ms(8),
        marginBottom: ms(16),
        borderColor: Colors.blue,
        borderWidth: ms(1),
    },
    pipelineText: {
        flex: 1,
        marginRight: ms(8),
        fontSize: ms(14),
        color: Colors.textPrimary,
    },
    arrowIconStyle: {
        justifyContent: 'flex-end'
    },
    progressUpdateContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginVertical: ms(8),
    },
    sectionTitle: {
        fontSize: ms(16),
        fontWeight: '500',
        color: Colors.textPrimary,
    },
    viewLastUpdate: {
        color: Colors.blue,
        fontSize: ms(14),
        textDecorationLine: 'underline',
    },
    progressStats: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.white,
        padding: ms(12),
        borderRadius: ms(10),
        marginBottom: ms(16),
        marginTop: ms(5),
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
    },
    statItem: {
        alignItems: 'center',
    },
    statLabel: {
        fontSize: ms(12),
        fontWeight: '500',
        color: Colors.textInputBlack,
    },
    statValue: {
        fontSize: ms(13),
        fontWeight: '600',
        marginTop: ms(5),
        color: Colors.textPrimary,
    },
    statItemforIT: {
        alignItems: 'center',
        flexDirection: 'row',
    },
    inputContainer: {
        marginVertical: ms(3),
    },
    inputLabel: {
        fontSize: ms(14),
        marginBottom: ms(3),
        fontWeight: '400',
        color: Colors.pipeIdTextBlack,
    },
    selectInput: {
        backgroundColor: Colors.containerligetBlue,
        padding: ms(12),
        borderRadius: ms(8),
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
    },
    placeholderText: {
        color: Colors.textInputBlack,
        fontSize: ms(14),
    },
    textInput: {
        backgroundColor: Colors.containerligetBlue,
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
        padding: ms(12),
        borderRadius: ms(8),
        color: Colors.searchTextBlack,
        fontSize: ms(14),
    },
    dateInput: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.containerligetBlue,
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
        paddingVertical: ms(10),
        paddingHorizontal: ms(12),
        borderRadius: ms(8),
    },
    remarksInput: {
        height: ms(100),
        textAlignVertical: 'top',
        color: Colors.primary,
        marginTop: ms(3),
    },
    uploadButtonOuter: {
        padding: ms(8),
        borderRadius: ms(5),
        borderWidth: ms(1),
        borderColor: Colors.searchBorderGrey,
    },
    uploadButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: ms(8),
        borderRadius: ms(5),
        borderWidth: ms(1),
        borderColor: Colors.secondary,
    },
    uploadButtonText: {
        color: Colors.blue,
        marginLeft: ms(8),
    },
    line: {
        backgroundColor: Colors.searchBorderGrey,
        height: ms(2),
        marginTop: ms(5),
    },
    updateButton: {
        backgroundColor: Colors.forgotPinBlue,
        padding: ms(10),
        borderRadius: ms(10),
        alignItems: 'center',
        marginVertical: ms(16),
        marginHorizontal: ms(20),
    },
    updateButtonText: {
        fontSize: ms(16),
        fontWeight: '700',
    },
    selectContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: ms(2),
        marginBottom: ms(-15),
    },
    selectLabel: {
        fontSize: ms(14),
        fontWeight: '500',
        color: Colors.pipeIdTextBlack,
    },
    historyButton: {
        padding: ms(8),
    },
    historyButtonText: {
        color: Colors.blue,
        fontSize: ms(14),
        fontWeight: '500',
    },
    pickerContainer: {
        marginBottom: ms(10),
        backgroundColor: Colors.containerligetBlue,
        borderRadius: ms(8),
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
    },
    picker: {
        height: ms(35),
        alignItems: 'center',
        justifyContent: 'center',
        alignContent: 'center',
    },
    tabContainer: {
        marginBottom: ms(16),
    },
    tabContainerOuterStyle: {
        width: '100%',
    },
    formContainer: {
        backgroundColor: Colors.white,
        borderRadius: ms(8),
        padding: ms(16),
    },
    label: {
        fontSize: ms(14),
        marginBottom: ms(7),
        marginTop: ms(10),
        fontWeight: '400',
        color: Colors.textInputBlack,
    },
    input: {
        backgroundColor: Colors.inputBgColr,
        borderRadius: ms(8),
        padding: ms(12),
        marginBottom: ms(16),
    },
    radioContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: ms(8),
    },
    radioButton: {
        width: ms(20),
        height: ms(20),
        borderRadius: ms(10),
        borderWidth: ms(2),
        borderColor: Colors.forgotPinBlue,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: ms(8),
    },
    radioButtonSelected: {
        backgroundColor: Colors.forgotPinBlue,
    },
    radioInner: {
        width: ms(10),
        height: ms(10),
        borderRadius: ms(5),
        backgroundColor: Colors.white,
    },
    radioLabel: {
        fontSize: ms(14),
        marginHorizontal: ms(8),
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: Colors.modelOverlay,
    },
    width: {
        width: '90%',
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center',
        margin: ms(20),
    },
    flexDirectionRowStyle: {
        flexDirection: 'row'
    },
    marginLeftStyle: {
        marginLeft: ms(10),
    },
    activityIndicatorStyle: {
        ...StyleSheet.absoluteFillObject,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 999,
    },
});

