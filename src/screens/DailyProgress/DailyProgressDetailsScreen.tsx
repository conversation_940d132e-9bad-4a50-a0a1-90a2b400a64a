import React, { useState, useCallback, useEffect, useMemo } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    TextInput,
    StyleSheet,
    SafeAreaView,
    StatusBar,
    ScrollView,
    Modal,
} from 'react-native';
import Strings from '../../utils/Strings/Strings';
import DirectionArrow from '../../assets/svg/direction_arrow.svg';
import CalenderIcon from '../../assets/svg/calendar.svg';
import Upload from '../../assets/svg/upload.svg';
import RadioSelected from '../../assets/svg/radio_selected.svg';
import RadioUnselected from '../../assets/svg/radio_unselected.svg';
import BookmarkUpdate from '../../assets/svg/bookmark_update.svg';
import { useNavigation } from '@react-navigation/native';
import { Picker } from '@react-native-picker/picker';
import BottomPopupImageUpload from '../../components/BottomPopupImageUpload';
import PrintLog from '../../utils/Logger/PrintLog';
import BottomCalendarModal from '../../components/CalendarPicker/BottomPopupCalendar';
import AppHeader from '../../components/AppHeader';
import TopTabNavigator from '../../components/TopTabNavigator';
import Colors from '../../utils/Colors/Colors';
import ButtonComponent from '../../components/ButtonComponent';
import { database } from '../../database'; // adjust path as needed
import WBSDetails from '../../database/model/WBSDetails';
import { Q } from '@nozbe/watermelondb';
import { ms } from '../../utils/Scale/Scaling';
import DailyProgressTextInput from './components/DailyProgressTextInput';
import EditPathOptionsPopup from './components/EditPathOptionsBottomPopup';
import DelEngineerScBottomPopup from './components/DelEngineerScBottomPopup';
import ProgressDetailsConsolidated from '../../database/model/ProgressDetailsConsolidated';
import ProgressUpdateEngineer from '../../database/model/ProgressUpdateEngineer';
import ViewLastUpdateBQIT from '../../database/model/ViewLastUpdateBQIT';
import ViewLastUpdateGIS from '../../database/model/ViewLastUpdateGIS';
import { RootState } from '../../redux/Root/rootStore';
import { useSelector, useDispatch } from 'react-redux';
import { formatDateWithFullYear } from '../../utils/Constants/Validations';
import AttachmentComponent from '../../components/Attachment';
import { InsertProgressUpdateDetails, logInputDetails, logParentItems } from '../../database/ProgressUpdate/ProgressUpdateDBData';
import { ProgressUpdateRequest } from '../../model/ProgressUpdate/ProgressUpdateActions';
import { t } from 'i18next';
import { customAlertWithOK } from '../../components/CustomAlert';
import LoadingOverlay from '../../components/LoadingOverlay';
import { getUserInfo } from '../../utils/DataStorage/Storage';
import { isNetworkConnected } from '../../utils/Network/NetworkConnection';
import { API, OPERATION_TYPES } from '../../utils/Constants/ApiConstants';


const DailyProgressDetailsScreen = ({ route }: { route: any }) => {
    const navigation = useNavigation();
    const dispatch = useDispatch();
    const { loading, error } = useSelector((state: RootState) => state.progressUpdate);

    const [progressQty, setProgressQty] = useState<string>('');
    const [manDays, setManDays] = useState<string>('5');
    const [fromlength, setFromLength] = useState<string>('5');
    const [actualLength, setActualLength] = useState<string>('5');
    const [distance, setDistance] = useState<string>('5');
    const [date, setDate] = useState(new Date());
    const [remarks, setRemarks] = useState<string>('');
    const [selectedPipeId, setSelectedPipeId] = useState('Pipe 100');
    const [selectedTab, setSelectedTab] = useState('Daily Progress');
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [alignment, setAlignment] = useState('Right');
    const [wbsDiameter, setWbsDiameter] = useState<string>('');
    const [designDia, setDesignDia] = useState<string>('');
    const [material, setMaterial] = useState<string>('');
    const [zone, setZone] = useState<string>('');
    const [startNodeId, setStartNodeId] = useState<string>('');
    const [stopNodeId, setStopNodeId] = useState<string>('');
    const [designLength, setDesignLength] = useState<string>('');
    const [imgUploadModalVisible, setImgUploadModalVisible] = useState(false);
    const [pathEditOptionsModalVisible, setPathEditOptionsModalVisible] = useState(false);
    const [delEngineerScModalVisible, setDelEngineerScModalVisible] = useState(false);
    const [startUpdate, setStartUpdate] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [tab, setTab] = useState<'dailyProgress' | 'hindrance' | 'nodeDetails'>('dailyProgress');
    const [wbsCode, setWbsCode] = useState<string>('');
    const [deliverableTypeCode, setDeliverableTypeCode] = useState<string>('');
    const [progressConsolidatedData, setProgressConsolidatedData] = useState<ProgressDetailsConsolidated | null>(null);
    const [progressEngineerData, setProgressEngineerData] = useState<ProgressUpdateEngineer | null>(null);
    const { inputDetails, wbsPath, selectedItem, parentItems } = route.params;
    const { currentJobId } = useSelector((state: RootState) => state.home);
    const [progressInputDetails, setProgressInputDetails] = useState();
    const [updatedSuccessfully, setUpdated] = useState(false);
    const [apiFailed, setApiFailed] = useState(false);

    const user = getUserInfo();

    console.log('currentJobId: ', currentJobId);
    console.log('DailyProgressDetailsScreen -- Received route params:', {
        inputDetails,
        wbsPath,
        selectedItem,
        parentItems
    });

    useEffect(() => {
        if (inputDetails?.WBS) {
            const [wbsCode, deliverableTypeCode] = inputDetails.WBS.split('~');
            setWbsCode(wbsCode);
            setDeliverableTypeCode(deliverableTypeCode);
            console.log('WBS Code:', wbsCode);
            console.log('DeliverableType Code:', deliverableTypeCode);
        }
    }, [inputDetails?.WBS]);

    // Fetch data from ProgressUpdateConsolidated table
    useEffect(() => {
        const fetchProgressConsolidatedData = async () => {
            if (wbsCode && deliverableTypeCode) {
                try {
                    const progressConsolidatedCollection = database.collections.get<ProgressDetailsConsolidated>('ProgressUpdateConsolidated');
                    const records = await progressConsolidatedCollection
                        .query(
                            Q.where('TDD_WBS_Code', wbsCode),
                            Q.where('TDD_Deliverable_Code', parseInt(deliverableTypeCode))
                        )
                        .fetch();

                    if (records.length > 0) {
                        const record = records[0];
                        setProgressConsolidatedData(record);
                        console.log('Progress Consolidated Data:', {
                            tddJobCode: record.tddJobCode,
                            tddWbsCode: record.tddWbsCode,
                            tddDeliverableCode: record.tddDeliverableCode,
                            delivscope: record.delivscope,
                            cumProg: record.cumProg,
                            cumManday: record.cumManday,
                            ftmProgress: record.ftmProgress,
                            ftmManday: record.ftmManday,
                            scScopeE: record.scScopeE,
                            scProgress: record.scProgress,
                            scManday: record.scManday,
                            cumPlanningQuantity: record.cumPlanningQuantity,
                            ftmPlanningQuantity: record.ftmPlanningQuantity
                        });
                    } else {
                        console.log('No matching records found in ProgressUpdateConsolidated');
                    }
                } catch (error) {
                    console.error('Error fetching ProgressUpdateConsolidated data:', error);
                }
            }
        };

        fetchProgressConsolidatedData();
    }, [wbsCode, deliverableTypeCode]);

    // Fetch data from ProgressUpdateEngineer table
    useEffect(() => {
        const fetchProgressEngineerData = async () => {
            if (wbsCode && deliverableTypeCode) {
                try {
                    const progressEngineerCollection = database.collections.get<ProgressUpdateEngineer>('ProgressUpdateEngineer');
                    const records = await progressEngineerCollection
                        .query(
                            Q.where('TDD_WBS_Code', wbsCode),
                            Q.where('TDD_Deliverable_Code', parseInt(deliverableTypeCode))
                        )
                        .fetch();

                    if (records.length > 0) {
                        const record = records[0];
                        setProgressEngineerData(record);
                        console.log('Progress Engineer Data:', {
                            tddJobCode: record.tddJobCode,
                            tddWbsCode: record.tddWbsCode,
                            tddDeliverableCode: record.tddDeliverableCode,
                            delivscope: record.delivscope,
                            cumProg: record.cumProg,
                            cumManday: record.cumManday,
                            ftmProgress: record.ftmProgress,
                            ftmManday: record.ftmManday,
                            cumTargetPlanQty: record.cumTargetPlanQty,
                            ftmCumTargetPlanQty: record.ftmCumTargetPlanQty,
                            targetScope: record.targetScope
                        });
                    } else {
                        console.log('No matching records found in ProgressUpdateEngineer');
                    }
                } catch (error) {
                    console.error('Error fetching ProgressUpdateEngineer data:', error);
                }
            }
        };

        fetchProgressEngineerData();
    }, [wbsCode, deliverableTypeCode]);

    // Create the full path string
    const fullPath = useMemo(() => {
        const pathParts = [
            ...parentItems.map((item: any) => item.WBS_Description),
            selectedItem?.entity_Description
        ].filter(Boolean);
        return pathParts.join(' / ');
    }, [parentItems, selectedItem]);
    const pathSegments = fullPath.split(' / ').filter(Boolean);

    useEffect(() => {
        if (inputDetails) {
            // Set values from inputDetails
            setManDays(inputDetails.PlanedLabour?.toString() ?? '');
            setRemarks(inputDetails.Remarks ?? '');
            setProgressQty(inputDetails.PlanedQty?.toString() ?? '');
            setDesignLength(inputDetails.PlanedQty?.toString() ?? '');
            // Add more field mappings as needed
        }
    }, [inputDetails]);

    useEffect(() => {
        setProgressInputDetails(inputDetails);
    }, []);

    const handleApply = (from: string, to: string) => {
        // Use the selected dates
        console.log('From:', from, 'To:', to);
    };

    const handlePipeIdChange = useCallback((itemValue: string) => {
        setSelectedPipeId(itemValue);
    }, []);

    const handleDateChange = useCallback((event: any, selectedDate?: Date) => {
        setShowDatePicker(false);
        if (selectedDate) {
            setDate(selectedDate);
        }
    }, []);

    const handleAlignmentChange = useCallback((value: string) => {
        setAlignment(value);
    }, []);

    const handleGoBack = useCallback(() => {
        // Implement navigation logic
    }, []);

    const handleBookmark = useCallback(() => {
        // Implement bookmark logic
    }, []);

    const handleViewLastUpdate = useCallback(async () => {
        try {
            let records: (ViewLastUpdateBQIT | ViewLastUpdateGIS)[] = [];
            if (inputDetails.taskType === 'BQ' || inputDetails.taskType === 'IT') {
                const bqitCollection = database.collections.get<ViewLastUpdateBQIT>('ViewLastUpdateBQIT');
                records = await bqitCollection.query().fetch();
            }
            // else if (inputDetails.taskType === 'Gis') {
            //     const gisCollection = database.collections.get<ViewLastUpdateGIS>('ViewLastUpdateGIS');
            //     records = await gisCollection.query().fetch();
            // }
            else {
                const gisCollection = database.collections.get<ViewLastUpdateGIS>('ViewLastUpdateGIS');
                records = await gisCollection.query().fetch();
            }

            // Map to historyItem[] format expected by LastUpdateScreen
            const historyData = records.map(record => ({
                updateDate: record.insertedOn ? new Date(record.insertedOn).toLocaleDateString('en-GB') : '-',
                uom: record.muomShortDescription ?? '-',
                quantity: record.qty ?? 0,
                manDays: record.man ?? 0,
            }));
            console.log('handleViewLastUpdate -- historyData: ', historyData);

            navigation.navigate('LastUpdateView', {
                historyData,
                plannedStartDate: formatDateWithTwoDigitYear(inputDetails?.PlanedSDate),
                plannedEndDate: formatDateWithTwoDigitYear(inputDetails?.PlanedEDate),
                actualStartDate: formatDateWithTwoDigitYear(inputDetails?.ActualSDate),
                actualEndDate: formatDateWithTwoDigitYear(inputDetails?.ActualEDate),
                fullPath: fullPath
            });
        } catch (error) {
            console.error('Error fetching ViewLastUpdate data:', error);
        }
    }, [navigation, inputDetails, fullPath]);

    const handleProgressQtySelect = useCallback(() => {
        // Implement progress qty selection logicc
    }, []);

    const handleDatePicker = useCallback(() => {
        // Implement date picker logic
        logInputDetails();
        logParentItems();
    }, []);

    const handleUploadImage = useCallback(() => {
        setImgUploadModalVisible(true);
    }, []);

    const handleProgressUpdate = () => {
        // pasted here api body params reference
        // const requestParams = {
        //     jobCode: "LE21M114",
        //     UID: "1159553",
        //     Type: "insert",
        //     Notification_Desc: "Compound wall Construction~Compound wall Construction - 170 Nos~PCC 1:4:8 - Non Billable",
        //     Quantity: 2.0,
        //     uOM: "m³",
        //     manPower: 2.0,
        //     ActualList: [
        //         {
        //             WBS: inputDetails.WBS,
        //             TaskCode: inputDetails.Task,
        //             ADate: inputDetails.ActualSDate,
        //             Quantity: progressQty,
        //             Manpower: "2",
        //             Remarks: "Test",
        //             Tasktype: "BQ",
        //             Is_Approved: "N",
        //             Tag: "Y",
        //             Latitude: 9.555293,
        //             Longitude: 78.5882628
        //         }
        //     ],
        //     "Attachments": null
        // }

        // Dispatch the ProgressUpdateRequest action
        dispatch(ProgressUpdateRequest(
            {
                jobCode: inputDetails.JobCode,
                UID: user?.UID.toString() || '',
                Type: OPERATION_TYPES.INSERT,
                Notification_Desc: "Compound wall Construction~Compound wall Construction - 170 Nos~PCC 1:4:8 - Non Billable", //hardcoded
                Quantity: inputDetails.PlanedQty,
                uOM: "m³", //hardcoded
                manPower: inputDetails.PlanedLabour,
                ActualList: [
                    {
                        WBS: inputDetails.WBS,
                        TaskCode: inputDetails.Task,
                        ADate: date.toISOString().split('T')[0],
                        Quantity: progressQty,
                        Manpower: manDays,
                        Remarks: remarks,
                        Tasktype: inputDetails.TaskType,
                        Is_Approved: "N", //hardcoded
                        Tag: selectedItem.leaf_Node_Tag,
                        Latitude: 9.555293, //hardcoded
                        Longitude: 78.5882628 //hardcoded
                    }
                ],
                Attachments: null, //hardcoded - attachment saved in state unique id in tilted fomat
                cb: ({ success, data }) => {
                    if (success && data && !data.ErrorMessage) {
                        customAlertWithOK(
                            t('SyncData.progressUpdate'),
                            t('ProgressUpdate.updateSuccess'),
                            [{
                                text: t('commonStrings.ok'),
                                onPress: () => {
                                    navigation.navigate('DailyProgressView');
                                }
                            }],
                            false
                        );
                    } else {
                        setApiFailed(true);
                    }
                }
            }
        ));
    }

    useEffect(() => {
        updatedSuccessfully && logInputDetails();
        updatedSuccessfully && logParentItems();
    }, []);

    const handleOfflineStorage = useCallback(async () => {
        // Create data with inputDetails as-is, just adding current form values
        const currentInputDetails = {
            ...inputDetails, // Use inputDetails as-is
            // Only override with current form values
            PlanedQty: progressQty || inputDetails?.PlanedQty || '',
            PlanedLabour: manDays || inputDetails?.PlanedLabour || '',
            ActualQty: progressQty || inputDetails?.ActualQty || '',
            ActualLabour: manDays || inputDetails?.ActualLabour || '',
            Remarks: remarks || inputDetails?.Remarks || '',
            ManDays: manDays || inputDetails?.ManDays || '',
            date: date.toISOString().split('T')[0] || inputDetails?.date || '',
        };

        const data = {
            inputDetails: currentInputDetails,
            parentItems: parentItems || [],
            selectedItem: selectedItem
        };

        // Store data locally and wait for completion
        await InsertProgressUpdateDetails(data);
        setUpdated(true);
        customAlertWithOK(
            t('SyncData.progressUpdate'),
            t('ProgressUpdate.storedOffline'),
            [{
                text: t('commonStrings.ok'),
                onPress: () => {
                    navigation.navigate('SyncData');
                }
            }],
            false
        );
    }, [inputDetails, progressQty, manDays, remarks, date, parentItems, selectedItem]);

    useEffect(() => {
        if (startUpdate) {
            // Check internet connectivity
            isNetworkConnected(false).then((isConnected) => {
                if (isConnected) {
                    // If internet is available
                    handleProgressUpdate();
                } else {
                    handleOfflineStorage();
                }
            });
        }
    }, [startUpdate]);

    useEffect(() => {
        if (apiFailed) {
            handleOfflineStorage();
            setApiFailed(false); // Reset the flag
        }
    }, [apiFailed, handleOfflineStorage]);

    const handleUpdate = useCallback(() => {
        setProgressInputDetails((prev: any) => ({
            ...prev,
            Remarks: remarks,
            PlanedQty: progressQty,
            ManDays: manDays,
            date: date,
        }));
        setStartUpdate(true);
    }, []);

    return (
        <SafeAreaView style={styles.container}>
            <AppHeader
                title={Strings.DailyProgress.progressUpdate}
                rightContent={<BookmarkUpdate />}
                onBookmarkPress={() => {
                    // navigation.navigate('BookmarksView')
                }}
            />
            {
                (loading) &&
                <LoadingOverlay
                    visible={true}
                    message={t('commonStrings.pleaseWait')}
                />
            }
            <ScrollView>
                <View style={styles.content}>
                    <TouchableOpacity
                        style={styles.pipelineInfo}
                        onPress={() => { setPathEditOptionsModalVisible(true) }}
                    >
                        <Text style={styles.pipelineText}>
                            {fullPath}
                        </Text>
                        <View style={styles.arrowIconStyle}>
                            <DirectionArrow />
                        </View>
                    </TouchableOpacity>

                    <View style={styles.progressUpdateContainer}>
                        <Text style={styles.sectionTitle}>{Strings.DailyProgress.progressUpdate}</Text>
                        <TouchableOpacity onPress={handleViewLastUpdate}>
                            <Text style={styles.viewLastUpdate}>{Strings.DailyProgress.viewLastUpdate}</Text>
                        </TouchableOpacity>
                    </View>

                    <TouchableOpacity style={styles.progressStats} onPress={() => { setDelEngineerScModalVisible(true) }}>
                        {inputDetails.taskType !== 'IT' ? (
                            <>
                                <View style={styles.statItem}>
                                    <Text style={styles.statLabel}>Scope</Text>
                                    <Text style={styles.statValue}>{inputDetails?.Scope ?? '-'}</Text>
                                </View>
                                <View style={styles.statItem}>
                                    <Text style={styles.statLabel}>Cum.Prog</Text>
                                    <Text style={styles.statValue}>{progressConsolidatedData?.cumProg ? Number(progressConsolidatedData.cumProg).toFixed(2) : '-'}</Text>
                                </View>
                                <View style={styles.statItem}>
                                    <Text style={styles.statLabel}>S/C Scope</Text>
                                    <Text style={styles.statValue}>{progressConsolidatedData?.cumProg ? Number(progressConsolidatedData.scScopeE).toFixed(2) : '-'}</Text>
                                </View>
                                <View style={styles.statItem}>
                                    <Text style={styles.statLabel}>S/C Prog</Text>
                                    <Text style={styles.statValue}>{progressConsolidatedData?.cumProg ? Number(progressConsolidatedData.scProgress).toFixed(2) : '-'}</Text>
                                </View>
                            </>
                        ) : (<>
                            <View style={styles.statItemforIT}>
                                <Text style={styles.statLabel}>Scope</Text>
                                <Text style={[styles.statValue, { marginLeft: 15 }]}>{inputDetails?.Scope ?? '-'}</Text>
                            </View>
                            <View style={styles.statItemforIT}>
                                <Text style={styles.statLabel}>Cum.Prog</Text>
                                <Text style={[styles.statValue, { marginLeft: 15 }]}>{progressConsolidatedData?.cumProg ? Number(progressConsolidatedData.cumProg).toFixed(2) : '-'}</Text>
                            </View>
                        </>)
                        }
                        <DirectionArrow />
                    </TouchableOpacity>

                    {selectedItem === 'GIS Node' && (
                        <>
                            <View style={styles.selectContainer}>
                                <Text style={styles.selectLabel}>Select Pipe ID</Text>
                                <TouchableOpacity onPress={() => { setModalVisible(true) }}>
                                    <Text style={styles.historyButtonText}>History</Text>
                                </TouchableOpacity>
                            </View>

                            <View style={styles.pickerContainer}>
                                <Picker
                                    selectedValue={selectedPipeId}
                                    onValueChange={handlePipeIdChange}
                                    style={styles.picker}
                                >
                                    <Picker.Item label="Pipe 100" value="Pipe 100" />
                                </Picker>
                            </View>

                            <View style={[styles.line, { marginHorizontal: 2, marginBottom: 15 }]}></View>

                            <View style={styles.tabContainer}>
                                <TopTabNavigator
                                    tabs={[
                                        { key: 'dailyProgress', label: Strings.DailyProgress.dailyProgress },
                                        { key: 'hindrance', label: Strings.DailyProgress.hindarnce },
                                        { key: 'nodeDetails', label: Strings.DailyProgress.nodeDetails }
                                    ]}
                                    activeTab={tab}
                                    onTabChange={(key) => setTab(key as 'dailyProgress' | 'hindrance' | 'nodeDetails')}
                                />
                            </View>
                        </>
                    )}
                    {selectedItem !== 'GIS Node' && (
                        <DailyProgressTextInput
                            label="Progress Qty"
                            value={progressQty}
                            onChangeText={setProgressQty}
                            isKeypadNumeric={true} />
                    )}
                    {tab !== 'nodeDetails' && (
                        <View style={styles.inputContainer}>
                            <Text style={styles.inputLabel}>Date *</Text>
                            <TouchableOpacity style={styles.dateInput} onPress={handleDatePicker}>
                                <Text>{formatDateWithFullYear(date)}</Text>
                                <CalenderIcon />
                            </TouchableOpacity>
                        </View>
                    )}
                    {selectedItem === 'GIS Node' && tab !== 'nodeDetails' && (
                        <>
                            <DailyProgressTextInput
                                label="From Length"
                                value={fromlength}
                                onChangeText={setFromLength}
                                isKeypadNumeric={true} />

                            <DailyProgressTextInput
                                label="Actual Length"
                                value={actualLength}
                                onChangeText={setActualLength}
                                isKeypadNumeric={true} />
                        </>
                    )}
                    {tab !== 'nodeDetails' && (
                        <DailyProgressTextInput
                            label="Man Days"
                            value={manDays}
                            onChangeText={setManDays}
                            isKeypadNumeric={true} />
                    )}

                    {selectedItem === 'GIS Node' && tab !== 'nodeDetails' && (
                        <>
                            <DailyProgressTextInput
                                label="Distance from road center (meter)"
                                value={distance}
                                onChangeText={setDistance}
                                isKeypadNumeric={true} />


                            <Text style={styles.label}>Alignment</Text>
                            <View style={styles.radioContainer}>
                                <TouchableOpacity style={styles.flexDirectionRowStyle}
                                    onPress={() => handleAlignmentChange('Right')}>
                                    {alignment === 'Right' ? (
                                        <RadioSelected width={18} height={18} />
                                    ) : (
                                        <RadioUnselected width={18} height={18} />
                                    )}

                                    <Text style={styles.radioLabel}>Right</Text>
                                </TouchableOpacity>

                                <TouchableOpacity style={[styles.flexDirectionRowStyle, styles.marginLeftStyle]} onPress={() => handleAlignmentChange('Left')}>
                                    {alignment === 'Left' ? (
                                        <RadioSelected width={18} height={18} />
                                    ) : (
                                        <RadioUnselected width={18} height={18} />
                                    )}

                                    <Text style={styles.radioLabel}>Left</Text>
                                </TouchableOpacity>
                            </View>
                        </>
                    )}
                    {/* 
                    <View style={styles.inputContainer}>
                        <Text style={styles.inputLabel}>Remarks *</Text>
                        <TextInput
                            style={[styles.textInput, styles.remarksInput]}
                            value={remarks}
                            onChangeText={setRemarks}
                            placeholder="Enter"
                            multiline
                        />
                    </View> */}

                    {tab !== 'nodeDetails' && (

                        <>
                            <DailyProgressTextInput
                                customStyle={[styles.textInput, styles.remarksInput]}
                                label="Remarks *"
                                value={remarks}
                                onChangeText={setRemarks}
                                isMultiline={true} />

                            <View style={styles.inputContainer}>
                                <Text style={styles.inputLabel}>Attachments *</Text>
                                <View style={styles.uploadButtonOuter}>
                                    <TouchableOpacity style={styles.uploadButton} onPress={handleUploadImage}>
                                        <Upload />
                                        <Text style={styles.uploadButtonText}>{Strings.DailyProgress.uploadImage}</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            {/* <AttachmentComponent /> */}
                        </>
                    )}
                    {selectedItem === 'GIS Node' && tab === 'nodeDetails' && (
                        <>
                            <DailyProgressTextInput
                                label="WBS Diameter"
                                value={wbsDiameter}
                                onChangeText={setWbsDiameter}
                                isKeypadNumeric={true} />

                            <DailyProgressTextInput
                                label="Design Dia"
                                value={designDia}
                                onChangeText={setDesignDia}
                                isKeypadNumeric={true} />

                            <DailyProgressTextInput
                                label="Material"
                                value={material}
                                onChangeText={setMaterial}
                                isKeypadNumeric={true} />

                            <DailyProgressTextInput
                                label="Zone"
                                value={zone}
                                onChangeText={setZone}
                                isKeypadNumeric={true} />

                            <DailyProgressTextInput
                                label="Start Node ID"
                                value={startNodeId}
                                onChangeText={setStartNodeId}
                                isKeypadNumeric={true} />

                            <DailyProgressTextInput
                                label="Stop Node ID"
                                value={stopNodeId}
                                onChangeText={setStopNodeId}
                                isKeypadNumeric={true} />

                            <DailyProgressTextInput
                                label="Design Length"
                                value={designLength}
                                onChangeText={setDesignLength}
                                isKeypadNumeric={true} />
                        </>
                    )}
                </View>

                <View style={styles.line}></View>
                <Modal
                    animationType="none"
                    transparent
                    visible={imgUploadModalVisible}
                    onRequestClose={() => setImgUploadModalVisible(false)}
                >
                    <View style={styles.modalOverlay}>

                        <BottomPopupImageUpload
                            onCameraPress={() => {
                                PrintLog.debug('BottomPopupImageUpload', 'Camera Pressed');
                            }
                            }
                            onGalleryPress={() => {
                                PrintLog.debug('BottomPopupImageUpload', 'Gallery Pressed');
                            }
                            } />

                    </View>
                </Modal>

                <Modal
                    animationType="none"
                    transparent
                    visible={modalVisible}
                    onRequestClose={() => setModalVisible(false)}
                >
                    <View style={styles.modalOverlay}>
                        <BottomCalendarModal
                            visible={modalVisible}
                            onClose={() => setModalVisible(false)}
                            onApply={handleApply}
                        />
                    </View>
                </Modal>

                {/* <View style={styles.modalOverlay}> */}
                <DelEngineerScBottomPopup
                    visible={delEngineerScModalVisible}
                    isShowScTab={inputDetails.taskType !== 'IT'}
                    progressConsolidatedData={progressConsolidatedData}
                    progressEngineerData={progressEngineerData}
                    onClose={() => setDelEngineerScModalVisible(false)}
                />
                {/* </View> */}

                <Modal
                    animationType="none"
                    transparent
                    visible={pathEditOptionsModalVisible}
                    onRequestClose={() => setPathEditOptionsModalVisible(false)}
                >
                    <View style={styles.modalOverlay}>
                        <EditPathOptionsPopup
                            pathSegments={pathSegments}
                            onEditPath={() => {
                                setPathEditOptionsModalVisible(false);
                                navigation.reset({
                                    index: 1,
                                    routes: [
                                        { name: 'Home' },
                                        { name: 'DailyProgressView' },
                                        { name: 'ProgressUpdateView' }
                                    ],
                                });
                            }} />
                    </View>
                </Modal>
            </ScrollView>
            <ButtonComponent
                title={Strings.DailyProgress.update}
                onPress={() => {
                    handleUpdate();
                }} />
        </SafeAreaView>
    );
};

export default DailyProgressDetailsScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.white,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: ms(16),
        backgroundColor: Colors.white,
    },
    headerTitle: {
        fontSize: ms(18),
        fontWeight: 'bold',
    },
    content: {
        paddingVertical: ms(16),
        paddingHorizontal: ms(20),
    },
    pipelineInfo: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        backgroundColor: Colors.containerligetBlue,
        padding: ms(16),
        borderRadius: ms(8),
        marginBottom: ms(16),
        borderColor: Colors.blue,
        borderWidth: ms(1),
    },
    pipelineText: {
        flex: 1,
        marginRight: ms(8),
        fontSize: ms(14),
        color: Colors.textPrimary,
    },
    arrowIconStyle: {
        justifyContent: 'flex-end'
    },
    progressUpdateContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginVertical: ms(8),
    },
    sectionTitle: {
        fontSize: ms(16),
        fontWeight: '500',
        color: Colors.textPrimary,
    },
    viewLastUpdate: {
        color: Colors.blue,
        fontSize: ms(14),
        textDecorationLine: 'underline',
    },
    progressStats: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.white,
        padding: ms(12),
        borderRadius: ms(10),
        marginBottom: ms(16),
        marginTop: ms(5),
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
    },
    statItem: {
        alignItems: 'center',
    },
    statLabel: {
        fontSize: ms(12),
        fontWeight: '500',
        color: Colors.textInputBlack,
    },
    statValue: {
        fontSize: ms(13),
        fontWeight: '600',
        color: Colors.textPrimary,
    },
    inputContainer: {
        marginVertical: ms(3),
    },
    inputLabel: {
        fontSize: ms(14),
        marginBottom: ms(3),
        fontWeight: '400',
        color: Colors.pipeIdTextBlack,
    },
    selectInput: {
        backgroundColor: Colors.containerligetBlue,
        padding: ms(12),
        borderRadius: ms(8),
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
    },
    placeholderText: {
        color: Colors.textInputBlack,
        fontSize: ms(14),
    },
    textInput: {
        backgroundColor: Colors.containerligetBlue,
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
        padding: ms(12),
        borderRadius: ms(8),
        color: Colors.searchTextBlack,
        fontSize: ms(14),
    },
    dateInput: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.containerligetBlue,
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
        paddingVertical: ms(10),
        paddingHorizontal: ms(12),
        borderRadius: ms(8),
    },
    remarksInput: {
        height: ms(100),
        textAlignVertical: 'top',
        color: Colors.primary,
    },
    uploadButtonOuter: {
        padding: ms(8),
        borderRadius: ms(5),
        borderWidth: ms(1),
        borderColor: Colors.searchBorderGrey,
    },
    uploadButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: ms(8),
        borderRadius: ms(5),
        borderWidth: ms(1),
        borderColor: Colors.secondary,
    },
    uploadButtonText: {
        color: Colors.blue,
        marginLeft: ms(8),
    },
    line: {
        backgroundColor: Colors.searchBorderGrey,
        height: ms(2),
        marginTop: ms(5),
    },
    updateButton: {
        backgroundColor: Colors.forgotPinBlue,
        padding: ms(10),
        borderRadius: ms(10),
        alignItems: 'center',
        marginVertical: ms(16),
        marginHorizontal: ms(20),
    },
    updateButtonText: {
        fontSize: ms(16),
        fontWeight: '700',
    },
    selectContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginVertical: ms(2),
    },
    selectLabel: {
        fontSize: ms(14),
        fontWeight: '500',
        color: Colors.pipeIdTextBlack,
    },
    historyButton: {
        padding: ms(8),
    },
    historyButtonText: {
        color: Colors.blue,
        fontSize: ms(14),
        fontWeight: '500',
    },
    pickerContainer: {
        marginBottom: ms(10),
        backgroundColor: Colors.containerligetBlue,
        borderRadius: ms(8),
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
    },
    picker: {
        height: ms(60),
        alignItems: 'center',
        justifyContent: 'center',
        alignContent: 'center',
    },
    tabContainer: {
        marginBottom: ms(16),
    },
    formContainer: {
        backgroundColor: Colors.white,
        borderRadius: ms(8),
        padding: ms(16),
    },
    label: {
        fontSize: ms(14),
        marginVertical: ms(5),
        fontWeight: '400',
        color: Colors.textInputBlack,
    },
    input: {
        backgroundColor: Colors.inputBgColr,
        borderRadius: ms(8),
        padding: ms(12),
        marginBottom: ms(16),
    },
    radioContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: ms(16),
    },
    radioButton: {
        width: ms(20),
        height: ms(20),
        borderRadius: ms(10),
        borderWidth: ms(2),
        borderColor: Colors.forgotPinBlue,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: ms(8),
    },
    radioButtonSelected: {
        backgroundColor: Colors.forgotPinBlue,
    },
    radioInner: {
        width: ms(10),
        height: ms(10),
        borderRadius: ms(5),
        backgroundColor: Colors.white,
    },
    radioLabel: {
        fontSize: ms(14),
        marginHorizontal: ms(8),
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: Colors.modelOverlay,
    },
    width: {
        width: '90%',
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center',
        margin: ms(20),
    },
    flexDirectionRowStyle: {
        flexDirection: 'row'
    },
    marginLeftStyle: {
        marginLeft: ms(10),
    },
});

