import React, { useEffect } from 'react';
import { ActivityIndicator, View, StyleSheet } from 'react-native';
import { clearAllData, getUserInfo, getUserLastLoggedInDate, getUserRolesInfo } from '../../utils/DataStorage/Storage';
import Colors from '../../utils/Colors/Colors';
import { useNavigation } from '@react-navigation/native';
import dayjs from 'dayjs';
import { useDispatch } from 'react-redux';
import { logoutRequest } from '../../redux/AuthRedux/Logout/LogoutAction';
import { getSelectedJobs } from '../../utils/Storage/Storage';
import { setJobsDownloaded, setSelectedCurrentJobId } from '../../redux/HomeRedux/HomeActions';

const SplashScreen = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation<any>();
  const user = getUserInfo();
  const roles = getUserRolesInfo();
  const lastLoginDate = getUserLastLoggedInDate(); // e.g., "2025-05-30"
  const today = dayjs().format('YYYY-MM-DD');

  const isLoggedInToday = lastLoginDate === today;

  useEffect(() => {
    console.log('SplashScreen -- useEffect: ');
    const checkAuth = () => {
      if (user && roles && isLoggedInToday) {
        navigation.reset({
          index: 0,
          routes: [{ name: 'Home' }],
        });

        const selectedJobs = getSelectedJobs();
        const hasSelectedJobs = selectedJobs && selectedJobs.length > 0;
        dispatch(setJobsDownloaded(hasSelectedJobs));
        dispatch(setSelectedCurrentJobId(selectedJobs[0]?.id));
      } else {
        clearAllData();
        dispatch(logoutRequest());
        navigation.reset({
          index: 0,
          routes: [{ name: 'Login' }],
        });
      }
    };

    checkAuth();
  }, []);

  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color={Colors.primary}
        testID="activity-indicator" />
    </View>
  );
};

export default SplashScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.white,
  },
});
