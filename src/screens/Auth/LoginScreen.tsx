import React, { useCallback, useEffect, useState } from 'react';
import { Text, View, StyleSheet, KeyboardAvoidingView, Platform, SafeAreaView, Keyboard, ImageBackground } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../redux/Root/rootStore';
import Strings from '../../utils/Strings/Strings';
import Colors from '../../utils/Colors/Colors';
import AppLogo from '../../assets/svg/app_logo.svg'
import { useNavigation } from '@react-navigation/native';
import { removeEmojis } from "../../utils/Constants/Validations";
import TextInputComponent from '../../components/TextInputComponent';
import { validateTokenRequest } from '../../redux/AuthRedux/Token/TokenActions';
import BaseApiConstants from '../../utils/Constants/CommonConstant';
import DeviceInfo from 'react-native-device-info';
import { loginRequest } from '../../redux/AuthRedux/Login/LoginActions';
import { isNetworkConnected } from '../../utils/Network/NetworkConnection';
import PrintLog from '../../utils/Logger/PrintLog';
import { LoginRequestData } from '../../model/Auth/LoginData';
import { getUserInfo, getUserRolesInfo, removeUserInfo, saveUserInfo, saveUserRolesInfo, setUserLastLoggedInDate } from '../../utils/DataStorage/Storage';
import { ms } from '../../utils/Scale/Scaling';
import { encryptString } from '../../utils/Crypto/Encryption';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import ButtonComponent from '../../components/ButtonComponent';
import dayjs from 'dayjs';
import { t } from 'i18next';
import { getPendingApproverWbsRequest, wbsRequest } from '../../redux/WBSDownload/WBSActions';
import LoadingOverlay from '../../components/LoadingOverlay';
import { rolesRequest } from '../../redux/RolesRedux/RolesActions';
import { RolesRequest } from '../../model/Roles/RolesData';

const LoginView = () => {
    const dispatch = useDispatch();
    const navigation = useNavigation();
    const insets = useSafeAreaInsets();
    const { user, error } = useSelector((state: RootState) => state.auth);
    const { token, error: tokenError } = useSelector((state: RootState) => state.token);
    const { roles } = useSelector((state: RootState) => state.rolesData);
    const [rolesUpdated, setRolesUpdate] = useState(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [loginErrorMsg, setLoginErrorMsg] = useState<string>('');

    useEffect(() => {
        if(__DEV__){
            setUsername('20331354');
            setPassword('Jeethu=1996=');
        }
    }, []);

    useEffect(() => {
        (async () => {
            const isInternetConnected = await isNetworkConnected();
            if (isInternetConnected) {
                dispatch(validateTokenRequest());
            }
        })();
    }, []);

    const rolesDataSuccess = useCallback(() => {
        saveUserRolesInfo(roles);
        setUserLastLoggedInDate(dayjs().format('YYYY-MM-DD'));
        PrintLog.debug('Login screen -- getRolesUserInfo.Functional_ROLE: ', getUserRolesInfo()?.Functional_ROLE);

        // Get current date for Todate
        let today = new Date();
        let toDate = today.toISOString().split('T')[0]; // Format: YYYY-MM-DD

        // Set Fromdate to 2 years ago
        let fromDate = new Date();
        fromDate.setFullYear(fromDate.getFullYear() - 2);
        let fromDateStr = fromDate.toISOString().split('T')[0]; // Format: YYYY-MM-DD

        // If only one role, trigger WBS download for that job
        if (roles?.RolesList?.length === 1) {
            const selectedJob = roles?.RolesList[0];
            if(roles.RolesList[0]?.Functional_ROLE.toLowerCase() !== Strings.loginScreen.approver) {
                dispatch(wbsRequest({
                userId: selectedJob.User_ID.toString(),
                objJoblist: [{ jobCode: selectedJob.Jobcode }],
                Fromdate: fromDateStr,
                Todate: toDate,
                selectedJob: [{
                    id: selectedJob.Jobcode,
                    name: selectedJob.JobDesc,
                    role: selectedJob.Functional_ROLE,
                }],
                isLogin: true,
                cb: ({success}) => {
                    setIsLoading(false);
                    if(success){
                        navigation.reset({
                            index: 0,
                            routes: [{ name: 'Home' }],
                        });
                    }
                }
            }));
        } 
        if (roles?.RolesList[0]?.Functional_ROLE.toLowerCase() === Strings.loginScreen.approver) {
            const selectedJob = roles?.RolesList[0];
            dispatch(getPendingApproverWbsRequest({
                jobCode: selectedJob.Jobcode,
                type: "ForApproval",
                selectedJob: [{
                    id: selectedJob.Jobcode,
                    name: selectedJob.JobDesc,
                    role: selectedJob.Functional_ROLE
                }],
                objJoblist: [{ jobCode: selectedJob.Jobcode }],
                cb: ({success}) => {
                    setIsLoading(false);
                    if(success){
                        navigation.reset({
                            index: 0,
                            routes: [{ name: 'Home' }],
                        });
                    }  
                }
            }));
        } 

        } else {
            setIsLoading(false);
            // If multiple roles, navigate to home
            navigation.reset({
                index: 0,
                routes: [{ name: 'Home' }],
            });
        }
    }, [roles]);

    useEffect(() => {
        if(rolesUpdated) {
            rolesDataSuccess();
        }
    }, [roles?.RolesList]);

    useEffect(() => {
        if (user || error) {
            if (error) {
                setIsLoading(false);
                setLoginErrorMsg(Strings.loginScreen.credsErrorMsg);
                setUsername('');
                setPassword('');
                removeUserInfo();
            }
            if (user) {
                saveUserInfo(user);
                PrintLog.debug('Login screen -- user details from Storage -- getUserInfo()?.UID: ', getUserInfo()?.UID, ' -- Hardcoded USERID: 100078676');
                setTimeout(() => {
                    try {
                        const userData = getUserInfo();
                        if (userData && userData.UID) {
                            const params: RolesRequest = {
                                User_ID: userData?.UID?.toString(),
                                RoleType: t('commonStrings.roles'),
                                cb: ({success, data}) => {
                                    if(success){
                                            setRolesUpdate(success);
                                    } else {
                                        setIsLoading(false);
                                        PrintLog.error('Roles API returned empty or invalid RolesList:', roles);
                                    }
                                }
                            };
                            dispatch(rolesRequest(params));
                        } else {
                            setIsLoading(false);
                            PrintLog.error('Login screen -- User Details is Empty');
                        }
                    } catch (rolesDispatchError) {
                        setIsLoading(false);
                        PrintLog.error('Error dispatching findRolesRequest:', rolesDispatchError);
                    }
                }, 3000);
            }
        }
    }, [user, error]);
      

    const handleUsernameChange = (text: string) => {
        setUsername(removeEmojis(text));
    };
    const handlePasswordChange = (text: string) => {
        setPassword(removeEmojis(text));
    };

    const callLoginApi = useCallback(() => {
        Keyboard.dismiss();
        setLoginErrorMsg('');
        setIsLoading(true);
        // Validate inputs
        if (!username && !password) {
            setLoginErrorMsg(Strings.loginScreen.credtsCommonErrorMsg);
            setIsLoading(false);
            return;
        }
        if (!username) {
            setLoginErrorMsg(Strings.loginScreen.userIdErrorMsg);
            setIsLoading(false);
            return;
        }
        if (!password) {
            setLoginErrorMsg(Strings.loginScreen.passwordErrorMsg);
            setIsLoading(false);
            return;
        }
        setTimeout(() => {
            requestAnimationFrame(async () => {
                const connected = await isNetworkConnected();
                if (connected) {
                    const params: LoginRequestData = {
                        UserName: encryptString(username),
                        Password: encryptString(password),
                        CompanyCode: BaseApiConstants.companyCode,
                        strDeviceUniqueCode: await DeviceInfo.getUniqueId() || '',
                        Token: token!,
                        intAppCode: BaseApiConstants.appCode,
                        isipcheck: 'N',
                    };
                    PrintLog.debug('LoginSCreen', 'params -- ', params);
                    dispatch(loginRequest(params));
                }else{
                    setIsLoading(false);
                }
            });
        }, 500);
    }, [username, password]);

    return (
        <View style={[styles.logoContainer, { paddingBottom: insets.bottom }]}
            testID="login-screen">
            <ImageBackground source={require('../../assets/png/app_bg.png')}
                style={styles.bgImage}
                resizeMode="cover" >
                <SafeAreaView style={styles.logoContainer}>
                    <KeyboardAvoidingView
                        style={styles.logoContainer}
                        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                    >
                        <View style={styles.cardWrapper}>
                            <View style={styles.card}>
                                <AppLogo style={styles.appLogo} />
                                <Text style={styles.loginTitle}>{Strings.loginScreen.title}</Text>
                                <TextInputComponent
                                    placeholder={Strings.loginScreen.usernamePlaceholder}
                                    value={username}
                                    onChangeText={handleUsernameChange}
                                    textLabelStye={styles.inputLabel}
                                    label={Strings.loginScreen.usernamePlaceholder}
                                />
                                <TextInputComponent
                                    placeholder={Strings.loginScreen.passwordPlaceholder}
                                    value={password}
                                    onChangeText={handlePasswordChange}
                                    secureTextEntry
                                    showToggleIcon
                                    isNumeric={false}
                                    textLabelStye={styles.inputLabel}
                                    label={Strings.loginScreen.passwordPlaceholder}
                                />
                                <ButtonComponent
                                    title={Strings.loginScreen.title}
                                    onPress={() => callLoginApi()}
                                    isDisabled={false}
                                    customWidth={styles.loginButton}
                                    testID="LoginButton"
                                    mainContainerStyle={styles.buttonMainContainer}
                                />

                                {loginErrorMsg !== '' && (
                                    <Text style={styles.errorText}>{loginErrorMsg}</Text>
                                )}

                                {loginErrorMsg === '' && tokenError && (
                                    <Text style={styles.errorText}>{tokenError}</Text>
                                )}

                            </View>
                        </View>
                    </KeyboardAvoidingView>
                </SafeAreaView>
            </ImageBackground>

            <LoadingOverlay
                visible={isLoading}
                message={t('commonStrings.pleaseWait')}
            />
        </View>

    );
};

export default LoginView;

const styles = StyleSheet.create({
    logoContainer: {
        flex: 1,
    },
    bgImage: {
        width: '100%',
        height: '100%',
        flex: 1
    },
    cardWrapper: {
        position: 'absolute',
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    card: {
        width: '90%',
        backgroundColor: Colors.white,
        borderRadius: ms(20),
        paddingHorizontal: ms(15),
        paddingVertical: ms(25),
    },
    appLogo: {
        alignSelf: 'center',
        marginBottom: ms(20),
    },
    activityIndicatorStyle: {
        ...StyleSheet.absoluteFillObject,
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
    },
    loginTitle: {
        fontSize: ms(18),
        fontWeight: 'bold',
        color: Colors.black,
        alignSelf: 'center',
        marginBottom: ms(20),
        fontFamily: 'MNBold',
    },
    inputLabel: {
        color: Colors.pipeIdTextBlack,
        fontSize: ms(12),
        marginTop: ms(12),
        marginBottom: 0,
        marginLeft: ms(2),
        fontFamily: 'MNRegular',
    },
    errorText: {
        color: Colors.error,
        fontSize: ms(12),
        textAlign: 'center',
        fontFamily: 'MNRegular',
    },
    loginButton: {
        marginTop: ms(35),
        marginBottom: ms(5),
        width: '100%',
        alignItems: 'center',
        fontWeight: '700',
        fontFamily: 'MNBold',
    },
    buttonMainContainer: {
        elevation: 0,
        backgroundColor: 'transparent',
        borderTopWidth: 0
    }
});
