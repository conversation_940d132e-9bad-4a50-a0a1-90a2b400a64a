import React, { useEffect, useState } from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Image, ActivityIndicator } from 'react-native';
import Home from '../../assets/svg/Home.svg';
import Report from '../../assets/svg/Report.svg';
import GISMap from '../../assets/svg/GISMap.svg';
import Profile from '../../assets/svg/default_user.svg';
import HomeScreen from '../Home/HomeScreen';
import { ms } from '../../utils/Scale/Scaling';
import Colors from '../../utils/Colors/Colors';
import { clearAllData, getUserRolesInfo } from '../../utils/DataStorage/Storage';
import Strings from '../../utils/Strings/Strings';
import Logout from '../../components/LogOut';
import { CommonActions, NavigationProp, useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { logoutRequest } from '../../redux/AuthRedux/Logout/LogoutAction';

const TABS = [
    { label: Strings.bottomTabs.home, icon: Home },
    { label: Strings.bottomTabs.reports, icon: Report },
    { label: Strings.bottomTabs.gisMap, icon: GISMap },
    { label: Strings.bottomTabs.profile, icon: Profile },
];

const MainView = () => {
    const navigation = useNavigation<NavigationProp<any>>();
    const dispatch = useDispatch();
    const [selected, setSelected] = useState(0);
    const [isLogoutClicked, setisLogoutClicked] = useState<boolean>(false);
    const [userRole, setUserRole] = useState<string | null>(null);
    const [isShowLoader, setIsShowLoader] = useState<boolean>(false);

    useEffect(() => {
        const fetchUserRole = async () => {
            const roles = await getUserRolesInfo();
            const role = roles?.RolesList?.[0]?.Functional_ROLE ?? null;
            console.log('MAIN userRole: ', role);
            setUserRole(role);
        };

        fetchUserRole();
    }, []);

    const renderMainContent = () => {
        switch (selected) {
            case 0:
                return <HomeScreen />;

            default:
                return <HomeScreen />;
        }
    };

    const handleSelect = (position: number) => {
        if (position === 3) {
            // Don't update selected index, just show logout popup
            setisLogoutClicked(true);
        } else {
            console.log(`Position ${position} selected`);
            setSelected(position); // Only update selected when it's NOT logout
        }
    };

    const callLogoutFunction = () => {
        clearAllData();
        console.log('Main logoutUser -- useEffect: ');
        dispatch(logoutRequest());
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes: [{ name: 'Login' }],
            })
        );
        setIsShowLoader(false);
        setisLogoutClicked(false);
    }

    return (
        <View style={styles.container}>
            <View style={[styles.MainContentContainer, { height: userRole === 'Site Engineer' ? '90%' : '100%' }]}>
                {renderMainContent()}
            </View>
            {userRole === 'Site Engineer' && (
                <View style={styles.bottomTabBarContainer}>
                    <Image
                        source={require('../../assets/png/bottom_tab_bg.png')}
                        style={styles.background}
                    />
                    <View style={styles.tabsRow}>
                        {TABS.map((tab, idx) => {
                            const focused = selected === idx;
                            const Icon = tab.icon;
                            return (
                                <TouchableOpacity
                                    key={tab.label}
                                    testID={`tab-${tab.label}`}
                                    style={styles.tab}
                                    activeOpacity={0.8}
                                    onPress={() => handleSelect(idx)}
                                >
                                    <Icon width={24} height={24} style={focused ? styles.focusedIcon : styles.icon} />
                                    <Text style={[styles.label, focused ? styles.focusedIcon : styles.icon]}>{tab.label}</Text>
                                </TouchableOpacity>
                            );
                        })}
                    </View>
                </View>
            )}
            {(isShowLoader) && (
                <ActivityIndicator style={styles.activityIndicatorStyle} size="large" color={Colors.primary}
                    testID="activity-indicator" />
            )}
            {isLogoutClicked && (
                <View style={styles.logoutPopupContainer}>
                    <Logout
                        visible={isLogoutClicked}
                        onClose={() => setisLogoutClicked(false)}
                        onCancel={() => setisLogoutClicked(false)}
                        onConfirm={() => {
                            setIsShowLoader(true);
                            callLogoutFunction();
                        }}
                    />
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1
    },
    MainContentContainer: {
        height: '100%',
    },
    bottomTabBarContainer: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    background: {
        position: 'absolute',
        backgroundColor: Colors.offWhite,
        alignSelf: 'center',
        width: '100%',
        height: ms(100),
        alignContent: 'center',
        marginVertical: ms(20),
    },
    tabsRow: {
        flexDirection: 'row',
        alignSelf: 'center',
        width: "95%",
        alignItems: 'center',
        justifyContent: 'center',
    },
    tab: {
        alignItems: 'center',
        justifyContent: 'center',
        marginVertical: ms(10),
        marginHorizontal: ms(20)
    },
    label: {
        fontFamily: 'MNSemiBold',
        fontSize: ms(12),
    },
    focusedIcon: {
        opacity: 1,
    },
    icon: {
        opacity: 0.6,
    },
    logoutPopupContainer: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 20,
    },
    activityIndicatorStyle: {
        ...StyleSheet.absoluteFillObject,
        justifyContent: 'center',
        alignItems: 'center'
    },
});

export default MainView;

