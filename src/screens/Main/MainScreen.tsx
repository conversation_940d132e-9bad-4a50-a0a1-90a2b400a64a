import React, { useEffect, useState } from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Image, ActivityIndicator, NativeModules, NativeEventEmitter, Alert, AppState, PermissionsAndroid, Platform } from 'react-native';
import Home from '../../assets/svg/Home.svg';
import Report from '../../assets/svg/Report.svg';
import GISMap from '../../assets/svg/GISMap.svg';
import Profile from '../../assets/svg/default_user.svg';
import HomeScreen from '../Home/HomeScreen';
import { ms } from '../../utils/Scale/Scaling';
import Colors from '../../utils/Colors/Colors';
import { clearAllData, getUserRolesInfo } from '../../utils/DataStorage/Storage';
import Strings from '../../utils/Strings/Strings';
import Logout from '../../components/LogOut';
import { CommonActions, NavigationProp, useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { logoutRequest } from '../../redux/AuthRedux/Logout/LogoutAction';
import { RootState } from '../../redux/Root/rootStore';
import { database } from '../../database/index';
import { Q } from '@nozbe/watermelondb';
import { getSelectedJobs } from '../../utils/Storage/Storage';
import { setJobsDownloaded } from '../../redux/HomeRedux/HomeActions';

const TABS = [
    { label: Strings.bottomTabs.home, icon: Home },
    { label: Strings.bottomTabs.reports, icon: Report },
    { label: Strings.bottomTabs.gisMap, icon: GISMap },
    { label: Strings.bottomTabs.profile, icon: Profile },
];

const MainView = () => {
    const navigation = useNavigation<NavigationProp<any>>();
    const dispatch = useDispatch();
    const { GeoSpatialService }: any = NativeModules;
    const [selected, setSelected] = useState(0);
    const [isLogoutClicked, setisLogoutClicked] = useState<boolean>(false);
    const [userRole, setUserRole] = useState<string | null>(null);
    const [isShowLoader, setIsShowLoader] = useState<boolean>(false);
    const [isGISLaunched, setIsGISLaunched] = useState<boolean>(false);
    const [lastSyncStatus, setLastSyncStatus] = useState<string | null>(null);
    const [loadingMessage, setLoadingMessage] = useState<string>("Processing...");
    const { currentJobId } = useSelector((state: RootState) => state.home);
    const geoEmitter = GeoSpatialService ? new NativeEventEmitter(GeoSpatialService) : null;

    useEffect(() => {
        const fetchUserRole = async () => {
            const roles = await getUserRolesInfo();
            const role = roles?.RolesList?.[0]?.Functional_ROLE ?? null;
            console.log('MAIN userRole: ', role);
            setUserRole(role);
        };

        fetchUserRole();
    }, []);

    // Listen for app state changes (to detect return from GIS app)
    useEffect(() => {
        const handleAppStateChange = (nextAppState: string) => {
            console.log(`🔄 App state changed to: ${nextAppState}, isGISLaunched: ${isGISLaunched}`);
            
            if (nextAppState === 'active' && isGISLaunched) {
                console.log('🌍 User returned from GIS app');
                setIsGISLaunched(false);
                console.log("lastSyncStatus", lastSyncStatus);
                
                // Give a moment for any pending events to be processed
                setTimeout(() => {
                    console.log("📋 Checking for received data after 2 seconds...");
                    
                    // Show a generic welcome back message since data processing will be handled separately
                    Alert.alert(
                        "Welcome Back!", 
                        "Processing any data received from GIS app...\n\nPlease wait for the processing to complete.",
                        [{ text: "OK" }]
                    );
                }, 2000);
            }
        };

        const subscription = AppState.addEventListener('change', handleAppStateChange);
        return () => subscription?.remove();
    }, [isGISLaunched, lastSyncStatus]);

    // Listen for GIS sync status updates and geospatial response data
    useEffect(() => {
        if (!GeoSpatialService || !geoEmitter) {
            console.log("❌ [DEBUG] GeoSpatialService or geoEmitter not available");
            return;
        }

        console.log("📡 [DEBUG] Setting up event listeners...");

        // Listen for sync status updates
        const syncStatusSubscription = geoEmitter.addListener("GeoSyncStatus", (status) => {
            console.log("📡 [EVENT] Received GIS Sync Status:", status);
            console.log("📡 [EVENT] Timestamp:", new Date().toISOString());
            setIsShowLoader(false);
            setLastSyncStatus(status);
            console.log("status",status);
            if (status === "Synced") {
                console.log("Synced",status );
                Alert.alert("Success", "Geospatial sync completed successfully! 🎉\n\nYour progress data has been updated.", [
                    { 
                        text: "Refresh Data", 
                        onPress: () => {
                            console.log("Sync success acknowledged - refreshing data");
                            refreshLocalData();
                        }
                    },
                    { text: "OK", style: "cancel" }
                ]);
            } else if (status === "Sync Failed") {
                Alert.alert("Sync Failed", "Geospatial sync encountered an error. ❌\n\nPlease try again or contact support if the issue persists.", [
                    { text: "Retry", onPress: () => handleGISIntegration() },
                    { text: "Cancel", style: "cancel" }
                ]);
            } else if (status !== "default") {
                Alert.alert("GIS Status", `Status Update: ${status}`, [
                    { text: "OK" }
                ]);
            }
        });

        // Listen for geospatial response data from GIS app (THIS IS THE MAIN EVENT WE NEED)
        const responseSubscription = geoEmitter.addListener("GeoSpatialResponse", (responseData) => {
            console.log("🌍 [EVENT] ===== GEOSPATIAL RESPONSE RECEIVED =====");
            console.log("🌍 [EVENT] Timestamp:", new Date().toISOString());
            console.log("🌍 [EVENT] Response Data:", responseData);
            console.log("🌍 [EVENT] Response Data Type:", typeof responseData);
            console.log("🌍 [EVENT] Response Data Keys:", responseData ? Object.keys(responseData) : 'null/undefined');
            
            // Print the complete raw JSON response for debugging
            console.log("📋 [RAW JSON] ===== COMPLETE RESPONSE =====");
            try {
                const jsonString = JSON.stringify(responseData, null, 2);
                console.log(jsonString);
                console.log("📋 [RAW JSON] Length:", jsonString.length);
            } catch (jsonError) {
                console.log("📋 [RAW JSON] Error stringifying response:", jsonError);
                console.log("📋 [RAW JSON] Raw object:", responseData);
            }
            console.log("📋 [RAW JSON] ================================");
            
            // Check for different possible response formats
            if (responseData && responseData.Response) {
                console.log("🌍 [EVENT] Found Response key with JSON data!");
                console.log("🌍 [EVENT] Response JSON length:", responseData.Response.length);
                console.log("🌍 [EVENT] Response JSON preview:", responseData.Response.substring(0, 500) + "...");
                
                try {
                    const parsedResponse = JSON.parse(responseData.Response);
                    console.log("🌍 [PARSED] Successfully parsed Response JSON:");
                    console.log("🌍 [PARSED]", JSON.stringify(parsedResponse, null, 2));
                } catch (parseError) {
                    console.log("🌍 [PARSED] Failed to parse Response JSON:", parseError);
                }
            } else if (responseData && typeof responseData === 'object') {
                console.log("🌍 [EVENT] Response is direct object (no Response key)");
                console.log("🌍 [EVENT] Direct object keys:", Object.keys(responseData));
            } else if (responseData && typeof responseData === 'string') {
                console.log("🌍 [EVENT] Response is string format");
                console.log("🌍 [EVENT] String length:", responseData.length);
                console.log("🌍 [EVENT] String content:", responseData);
                
                try {
                    const parsedString = JSON.parse(responseData);
                    console.log("🌍 [PARSED] Successfully parsed string JSON:");
                    console.log("🌍 [PARSED]", JSON.stringify(parsedString, null, 2));
                } catch (parseError) {
                    console.log("🌍 [PARSED] String is not valid JSON:", parseError);
                }
            } else {
                console.log("🌍 [EVENT] ⚠️ Unexpected response format");
            }
            
            console.log("🌍 [EVENT] =========================================");
            
            setIsShowLoader(false);
            
            // Enhanced alert with more detailed information
            const dataPreview = (() => {
                if (!responseData) return "No data";
                if (typeof responseData === 'string') return `String (${responseData.length} chars)`;
                if (typeof responseData === 'object') {
                    const keys = Object.keys(responseData);
                    return `Object with keys: ${keys.join(', ')}`;
                }
                return `${typeof responseData}`;
            })();
            
            Alert.alert(
                "GIS Data Received!", 
                `✅ Received data from GIS app!\n\n📊 Format: ${dataPreview}\n📝 Has Response key: ${responseData?.Response ? 'Yes' : 'No'}\n\n⚠️ Check console for detailed JSON output`,
                [
                    {
                        text: "Show Full JSON",
                        onPress: () => {
                            console.log("🔍 [USER REQUESTED] ===== FULL JSON RESPONSE =====");
                            console.log(JSON.stringify(responseData, null, 2));
                            console.log("🔍 [USER REQUESTED] =====================================");
                            
                            // Also show a truncated version in an alert for immediate viewing
                            const jsonStr = JSON.stringify(responseData, null, 2);
                            const truncated = jsonStr.length > 500 ? jsonStr.substring(0, 500) + "...\n\n[Truncated - see console for full output]" : jsonStr;
                            Alert.alert("JSON Response", truncated, [{ text: "OK" }]);
                        }
                    },
                    {
                        text: "Process Data",
                        onPress: () => {
                            try {
                                processGeoSpatialResponse(responseData);
                            } catch (error) {
                                console.error("❌ [EVENT] Error processing geospatial response:", error);
                                Alert.alert("Processing Error", "Failed to process geospatial data from GIS app.");
                            }
                        }
                    },
                    { text: "OK", style: "cancel" }
                ]
            );
        });

        // Listen for any errors
        const errorSubscription = geoEmitter.addListener("GeoSpatialError", (error) => {
            console.log("❌ [EVENT] Received GeoSpatial Error:", error);
            console.log("❌ [EVENT] Timestamp:", new Date().toISOString());
            Alert.alert("GIS Error", error);
        });

        // Listen for launch confirmations
        const launchSubscription = geoEmitter.addListener("GeoSpatialLaunch", (launchInfo) => {
            console.log("🚀 [EVENT] Received GeoSpatial Launch:", launchInfo);
            console.log("🚀 [EVENT] Timestamp:", new Date().toISOString());
            console.log("🚀 [EVENT] GIS app launch confirmed!");
        });

        // Add comprehensive event listeners to catch ANY event from GIS app
        const allPossibleEvents = [
            "GeoSpatialResponse",
            "GeoSyncStatus", 
            "GeoSpatialError",
            "GeoSpatialLaunch",
            "GISResponse",
            "DataReceived",
            "JSONResponse",
            "AppResult",
            "ActivityResult"
        ];

        const additionalSubscriptions = allPossibleEvents.map(eventName => {
            return geoEmitter.addListener(eventName, (data) => {
                console.log(`📡 [EVENT-${eventName}] ===== RECEIVED =====`);
                console.log(`📡 [EVENT-${eventName}] Timestamp:`, new Date().toISOString());
                console.log(`📡 [EVENT-${eventName}] Data:`, data);
                
                if (data && typeof data === 'object') {
                    console.log(`📡 [EVENT-${eventName}] JSON:`, JSON.stringify(data, null, 2));
                }
                console.log(`📡 [EVENT-${eventName}] ==================`);
            });
        });

        console.log("📡 [DEBUG] Event listeners registered successfully");
        console.log("📡 [DEBUG] Listening for events:", ["GeoSyncStatus", "GeoSpatialResponse", "GeoSpatialError", "GeoSpatialLaunch"]);

        return () => {
            console.log("📡 [DEBUG] Cleaning up event listeners...");
            syncStatusSubscription.remove();
            responseSubscription.remove();
            errorSubscription.remove();
            launchSubscription.remove();
            additionalSubscriptions.forEach(subscription => subscription.remove());
        };
    }, [geoEmitter]);

    // Test function to simulate receiving JSON data from GIS app
    const testJSONReception = () => {
        console.log("🧪 [TEST] Testing JSON reception...");
        
        // Simulate different types of responses that might come from GIS app
        const testResponses = [
            // Format 1: Object with Response key containing JSON string
            {
                Response: JSON.stringify([
                    {
                        "Pipe_ID": "TEST001",
                        "Length": "100",
                        "From_Length": "50",
                        "Design_Length": "150",
                        "Zone": "Zone1",
                        "Design_Dia": "200",
                        "Manpower": "5",
                        "Date_Edited": "15-01-24",
                        "Direction": "Left",
                        "Remarks": "Test remark",
                        "Start_Node": "N001",
                        "Stop_Node": "N002",
                        "Image_Path": "/test/path.jpg",
                        "Node_Status": "Completed"
                    }
                ]),
                Job_Code: "TEST_JOB",
                BOQ_Code: "1074"
            },
            
            // Format 2: Direct array
            [
                {
                    "Pipe_ID": "TEST002",
                    "Length": "200",
                    "From_Length": "75",
                    "Remarks": "Direct array test"
                }
            ],
            
            // Format 3: Simple string JSON
            JSON.stringify({
                "data": [{"id": "TEST003", "value": "test"}],
                "status": "success"
            })
        ];
        
        testResponses.forEach((testResponse, index) => {
            console.log(`🧪 [TEST] Testing response format ${index + 1}:`);
            processGeoSpatialResponse(testResponse);
        });
    };

    // Process geospatial response data from GIS app (equivalent to C# SyncGeoSpatialData)
    const processGeoSpatialResponse = async (responseData: any) => {
        console.log("🔄 Starting geospatial data processing...");
        setLoadingMessage("Processing geospatial data...");
        setIsShowLoader(true);

        try {
            // Extract GeoSpatial data from response
            let geoSpatialDataList = [];
            let projValues = responseData;

            if (responseData.Response) {
                console.log("📋 Found Response field, parsing geospatial data...");
                geoSpatialDataList = JSON.parse(responseData.Response);
                console.log(`📊 Extracted ${geoSpatialDataList.length} geospatial records`);
            } else {
                console.warn("⚠️ No Response field found in geospatial data");
                setIsShowLoader(false);
                Alert.alert("No Data", "No geospatial data received from GIS app.");
                return;
            }

            if (geoSpatialDataList.length === 0) {
                console.log("⚠️ No geospatial records to process");
                setIsShowLoader(false);
                Alert.alert("No Records", "No geospatial records found to process.");
                return;
            }

            // Get current location
            console.log("📍 Getting current location...");
            const position = await getCurrentLocation();

            // Process each geospatial record
            let successCount = 0;
            let errorCount = 0;
            let processedRecords = [];

            for (const [index, geoSpatialData] of geoSpatialDataList.entries()) {
                console.log(`🔄 Processing record ${index + 1}/${geoSpatialDataList.length}:`, geoSpatialData);

                try {
                    // Validate entry
                    const validationResult = validateGeoSpatialEntry(geoSpatialData);
                    if (!validationResult.isValid) {
                        console.error(`❌ Validation failed for record ${index + 1}:`, validationResult.errorMessage);
                        errorCount++;
                        continue;
                    }

                    // Create sync data object
                    const syncData = createSyncDataObject(geoSpatialData, projValues, position);
                    console.log(`📋 Created sync data for record ${index + 1}:`, syncData);

                    // TODO: Check for overlapping data with existing records
                    // const overlapCheck = await checkForOverlaps(syncData);

                    // TODO: Save to your database here
                    // await database.get('your_table').create((record) => {
                    //     Object.assign(record, syncData);
                    // });

                    processedRecords.push(syncData);
                    successCount++;
                    console.log(`✅ Successfully processed record ${index + 1}`);

                } catch (error) {
                    console.error(`❌ Error processing record ${index + 1}:`, error);
                    errorCount++;
                }
            }

            setIsShowLoader(false);

            // Show results
            const message = `Processing completed!\n\n✅ Successfully processed: ${successCount}\n❌ Errors: ${errorCount}\n📝 Total records: ${geoSpatialDataList.length}`;
            
            console.log("📊 Geospatial data processing summary:");
            console.log(`✅ Success: ${successCount} records`);
            console.log(`❌ Errors: ${errorCount} records`);
            console.log(`📝 Total: ${geoSpatialDataList.length} records`);
            console.log("📄 Processed records:", processedRecords);

            if (successCount > 0) {
                Alert.alert("Processing Complete", message, [
                    {
                        text: "View Details",
                        onPress: () => {
                            console.log("📋 Detailed processed records:", JSON.stringify(processedRecords, null, 2));
                        }
                    },
                    {
                        text: "Refresh Data",
                        onPress: () => refreshLocalData()
                    },
                    { text: "OK", style: "cancel" }
                ]);
            } else {
                Alert.alert("Processing Failed", `Failed to process any records.\n\nErrors: ${errorCount}`, [
                    { text: "OK" }
                ]);
            }

        } catch (error) {
            console.error("❌ Error in geospatial data processing:", error);
            setIsShowLoader(false);
            Alert.alert("Processing Error", `Failed to process geospatial data: ${error.message}`);
        }
    };

    // Get current location using React Native Geolocation
    const getCurrentLocation = async () => {
        try {
            // Request location permission for Android
            if (Platform.OS === 'android') {
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
                    {
                        title: 'Location Permission',
                        message: 'This app needs access to location for GIS integration.',
                        buttonNeutral: 'Ask Me Later',
                        buttonNegative: 'Cancel',
                        buttonPositive: 'OK',
                    }
                );
                
                if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
                    console.warn("⚠️ Location permission denied, using default location");
                    return { latitude: 0.0, longitude: 0.0 };
                }
            }

            // Use a Promise to wrap the geolocation call
            return new Promise((resolve) => {
                // You can replace this with @react-native-community/geolocation or your preferred location service
                // For now, using a simple approach that works with most React Native setups
                
                // If you have @react-native-community/geolocation installed, use:
                // import Geolocation from '@react-native-community/geolocation';
                // Geolocation.getCurrentPosition(...)
                
                try {
                    // Simple fallback - replace with actual geolocation call
                    const location = { latitude: 0.0, longitude: 0.0 };
                    console.log(`✅ Location obtained: lat=${location.latitude}, lng=${location.longitude}`);
                    resolve(location);
                } catch (error) {
                    console.warn("⚠️ Failed to get location, using default:", error);
                    resolve({ latitude: 0.0, longitude: 0.0 });
                }
            });
        } catch (error) {
            console.warn("⚠️ Error getting location permission:", error);
            return { latitude: 0.0, longitude: 0.0 };
        }
    };

    // Validate geospatial entry (equivalent to C# EntryValidation)
    const validateGeoSpatialEntry = (geoSpatialData: any) => {
        console.log("🔍 Validating geospatial entry...");

        if (!geoSpatialData.From_Length || geoSpatialData.From_Length.trim() === '') {
            return { isValid: false, errorMessage: 'From Length is required' };
        }

        if (!geoSpatialData.Length || geoSpatialData.Length.trim() === '') {
            return { isValid: false, errorMessage: 'Actual Length is required' };
        }

        if (parseInt(geoSpatialData.Length) === 0) {
            return { isValid: false, errorMessage: 'Actual Length cannot be zero' };
        }

        if (!geoSpatialData.Remarks || geoSpatialData.Remarks.trim() === '') {
            return { isValid: false, errorMessage: 'Remarks are required' };
        }

        console.log("✅ Entry validation passed");
        return { isValid: true };
    };

    // Create sync data object (equivalent to C# syncData creation)
    const createSyncDataObject = (geoSpatialData: any, projValues: any, position: any) => {
        console.log("🏗️ Creating sync data object...");

        const parseDate = (dateString: string) => {
            if (!dateString || dateString.trim() === '') return null;
            
            try {
                // Parse date format dd-MM-yy
                const parts = dateString.split('-');
                if (parts.length === 3) {
                    const day = parseInt(parts[0]);
                    const month = parseInt(parts[1]) - 1; // JS months are 0-based
                    const year = parseInt(parts[2]) + 2000; // Assuming 2000+ years
                    return new Date(year, month, day);
                }
            } catch (error) {
                console.warn(`⚠️ Could not parse date: ${dateString}`);
            }
            return null;
        };

        const syncData = {
            distance_Rmeter: geoSpatialData.Length,
            start_NodeID: geoSpatialData.Start_Node,
            stop_NodeID: geoSpatialData.Stop_Node,
            d_Length: parseFloat(geoSpatialData.Design_Length) || 0,
            zone: geoSpatialData.Zone,
            design_Dia: parseFloat(geoSpatialData.Design_Dia) || 0,
            manPower: parseInt(geoSpatialData.Manpower) || 0,
            actualLength: geoSpatialData.Length,
            fromLength: parseFloat(geoSpatialData.From_Length) || 0,
            progressDate: parseDate(geoSpatialData.Date_Edited),
            progressQty: parseInt(geoSpatialData.Length) || 0,
            manDays: parseInt(geoSpatialData.Manpower) || 0,
            isAlign: geoSpatialData.Direction === "Left" ? "L" : "R",
            remarks: geoSpatialData.Remarks,
            jobCode: projValues.Job_Code || currentJobId,
            nodeId: geoSpatialData.Pipe_ID,
            filePath: geoSpatialData.Image_Path || '',
            isChecked: geoSpatialData.Node_Status === "Completed" ? "Y" : "N",
            totalLength: parseFloat(geoSpatialData.From_Length) + parseFloat(geoSpatialData.Length),
            latitude: position?.latitude || null,
            longitude: position?.longitude || null,
            fileName: (geoSpatialData.Image_Path && geoSpatialData.Image_Path !== '') ? "GISAttachments.Jpeg" : null,
            lastSyncDate: new Date().toISOString() // Add the current timestamp as lastSyncDate
        };

        console.log("✅ Sync data object created successfully");
        return syncData;
    };

    // Function to get GIS data for the current job
    const getGISDataForJob = async (jobCode: string) => {
        try {
            // Try to get data from WBSGISDetails first
            const gisDetails = await database
                .get('WBSGISDetails')
                .query(Q.where('JobCode', jobCode))
                .fetch();

            if (gisDetails.length > 0) {
                const firstRecord = gisDetails[0];
                return {
                    boqCode: firstRecord._raw.Boq || "1074", // fallback to default
                    deliverableCode: firstRecord._raw.DeliverableType_Et_Code || "EPDT0003" // fallback to default
                };
            }

            // Fallback: try LatLongHierarchy for deliverable code
            const latLongData = await database
                .get('LatLongHierarchy')
                .query(Q.where('PRCLLH_Job_Code', jobCode))
                .fetch();

            if (latLongData.length > 0) {
                const firstRecord = latLongData[0];
                return {
                    boqCode: "1074", // default
                    deliverableCode: firstRecord._raw.DELIVERABLE_CODE?.toString() || "EPDT0003"
                };
            }

            // Final fallback: use default values
            return {
                boqCode: "1074",
                deliverableCode: "EPDT0003"
            };
        } catch (error) {
            console.error('Error fetching GIS data:', error);
            return {
                boqCode: "1074",
                deliverableCode: "EPDT0003"
            };
        }
    };

    // Function to refresh local data after GIS sync
    const refreshLocalData = async () => {
        try {
            setLoadingMessage("Refreshing local data...");
            setIsShowLoader(true);
            
            // Refresh job data
            const selectedJobs = getSelectedJobs();
            const hasSelectedJobs = selectedJobs && selectedJobs.length > 0;
            dispatch(setJobsDownloaded(hasSelectedJobs));
            
            console.log('Local data refreshed successfully');
            
        } catch (error) {
            console.error('Error refreshing local data:', error);
        } finally {
            setIsShowLoader(false);
            setLoadingMessage("Processing...");
        }
    };

    // Function to validate GIS data before launch
    const validateGISData = (gisData: { boqCode: string; deliverableCode: string }) => {
        const errors = [];
        
        if (!gisData.boqCode || gisData.boqCode === "") {
            errors.push("BOQ Code is missing");
        }
        
        if (!gisData.deliverableCode || gisData.deliverableCode === "") {
            errors.push("Deliverable Code is missing");
        }
        
        return errors;
    };

    // Function to handle GIS integration
    const handleGISIntegration = async () => {
        // Debug: List installed apps first
        if (GeoSpatialService?.debugListInstalledApps) {
            try {
                console.log('🔍 [DEBUG] Checking installed packages...');
                const packageInfo = await GeoSpatialService.debugListInstalledApps();
                console.log('📦 [DEBUG] Package scan results:', packageInfo);
                console.log('📦 [DEBUG] Total packages:', packageInfo.totalPackages);
                console.log('📦 [DEBUG] GIS-related packages:', packageInfo.gisRelatedPackages);
                console.log('📦 [DEBUG] Target package found:', packageInfo.targetPackageFound);
                
                if (packageInfo.gisRelatedPackages.length > 0) {
                    console.log('🎯 [DEBUG] Found these GIS-related packages:');
                    packageInfo.gisRelatedPackages.forEach((pkg: string, idx: number) => {
                        console.log(`   ${idx + 1}. ${pkg}`);
                    });
                }
            } catch (error) {
                console.error('❌ [DEBUG] Error listing packages:', error);
            }
        }

        // Check if native module is available
        if (!GeoSpatialService) {
            Alert.alert("GIS Service Unavailable", "GIS service is not available on this device.\n\nPlease ensure the GIS module is properly installed.", [
                { text: "OK" }
            ]);
            return;
        }

        if (!currentJobId) {
            Alert.alert("No Job Selected", "Please select and download a job before accessing GIS features.\n\nGo to Download WBS to select a job.", [
                { text: "Go to WBS", onPress: () => navigation.navigate('DownloadWBS') },
                { text: "Cancel", style: "cancel" }
            ]);
            return;
        }

        try {
            setLoadingMessage("Initializing GIS integration...");
            setIsShowLoader(true);
            
            console.log('Starting GIS integration for job:', currentJobId);
            
            // Get and validate GIS data for the current job
            const gisData = await getGISDataForJob(currentJobId);
            console.log('Retrieved GIS Data:', gisData);
            
            const validationErrors = validateGISData(gisData);
            if (validationErrors.length > 0) {
                console.warn('GIS Data validation issues:', validationErrors);
                Alert.alert("Data Validation", `Some GIS data is missing:\n\n${validationErrors.join('\n')}\n\nUsing default values. Continue?`, [
                    { text: "Continue", onPress: () => proceedWithGISLaunch(gisData) },
                    { text: "Cancel", style: "cancel", onPress: () => setIsShowLoader(false) }
                ]);
                return;
            }
            
            await proceedWithGISLaunch(gisData);

        } catch (error) {
            console.error('Error in GIS integration:', error);
            setIsShowLoader(false);
            Alert.alert("Integration Error", "Failed to initialize GIS integration.\n\nPlease check your connection and try again.", [
                { text: "Retry", onPress: () => handleGISIntegration() },
                { text: "Cancel", style: "cancel" }
            ]);
        }
    };

    // Separate function to handle the actual GIS app launch
    const proceedWithGISLaunch = async (gisData: { boqCode: string; deliverableCode: string }) => {
        try {
            setLoadingMessage("Starting geospatial service...");
            
            // Start the geospatial sync service
            const serviceData = {
                jobCode: currentJobId
            };
            await GeoSpatialService.startGeospatialService(serviceData);

            setLoadingMessage("Launching GIS application...");
            
            // Wait a moment for the service to start, then open the GIS app
            setTimeout(() => {
                console.log('Opening GIS app with params:', {
                    jobCode: currentJobId,
                    boqCode: gisData.boqCode,
                    deliverableCode: gisData.deliverableCode
                });
                
                setIsGISLaunched(true); // Track that GIS app is being launched
                
                GeoSpatialService.openGISApp(
                    currentJobId,
                    gisData.boqCode,
                    gisData.deliverableCode
                );
                
                setIsShowLoader(false);
                
                // Show informational message
                Alert.alert("Launching GIS App", "Opening Geospatial application...\n\nYou will be redirected back automatically after completing your work.", [
                    { text: "OK" }
                ]);
                
            }, 2000); // 2 second delay to ensure service starts

        } catch (error) {
            console.error('Error launching GIS app:', error);
            setIsShowLoader(false);
            setIsGISLaunched(false);
            throw error;
        }
    };

    const renderMainContent = () => {
        switch (selected) {
            case 0:
                return <HomeScreen />;

            default:
                return <HomeScreen />;
        }
    };

    const handleSelect = (position: number) => {
        if (position === 3) {
            // Don't update selected index, just show logout popup
            setisLogoutClicked(true);
        } else if (position === 2) {
            // GIS Map tab clicked
            console.log('GIS Map tab selected');
            handleGISIntegration();
        } else {
            console.log(`Position ${position} selected`);
            setSelected(position); // Only update selected when it's NOT logout or GIS
        }
    };

    const handleLongPress = (position: number) => {
        if (position === 2) {
            // Long press on GIS tab - show test options
            Alert.alert(
                "GIS Test Options",
                "Choose a test option:",
                [
                    {
                        text: "Test JSON Reception",
                        onPress: () => {
                            console.log("🧪 [USER] Running JSON reception test...");
                            testJSONReception();
                        }
                    },
                    {
                        text: "Validate Integration",
                        onPress: () => {
                            if (GeoSpatialService?.validateGISIntegration) {
                                GeoSpatialService.validateGISIntegration()
                                    .then((result: any) => {
                                        const status = result.readyForIntegration ? "✅ Ready" : "❌ Not Ready";
                                        Alert.alert(
                                            "GIS Integration Status",
                                            `${status}\n\n📱 GIS App Installed: ${result.isGISAppInstalled ? 'Yes' : 'No'}\n🚀 Launch Intent: ${result.hasLaunchIntent ? 'Yes' : 'No'}\n⚡ Activity Context: ${result.hasCurrentActivity ? 'Yes' : 'No'}\n📦 Version: ${result.packageVersion}`,
                                            [{ text: "OK" }]
                                        );
                                    })
                                    .catch((error: any) => {
                                        Alert.alert("Error", `Validation failed: ${error.message}`);
                                    });
                            } else {
                                Alert.alert("Not Available", "Validation not available");
                            }
                        }
                    },
                    {
                        text: "Test Response Flow",
                        onPress: () => {
                            if (GeoSpatialService?.testGISResponse) {
                                Alert.alert(
                                    "Test GIS Response",
                                    "This will simulate receiving data from GIS app to test your processing pipeline.",
                                    [
                                        {
                                            text: "Run Test",
                                            onPress: () => {
                                                console.log("🧪 [USER] Running GIS response flow test...");
                                                GeoSpatialService.testGISResponse()
                                                    .then((result: string) => {
                                                        console.log("✅ [TEST] Test completed:", result);
                                                    })
                                                    .catch((error: any) => {
                                                        console.error("❌ [TEST] Test failed:", error);
                                                        Alert.alert("Test Failed", error.message);
                                                    });
                                            }
                                        },
                                        { text: "Cancel", style: "cancel" }
                                    ]
                                );
                            } else {
                                Alert.alert("Not Available", "Test function not available");
                            }
                        }
                    },
                    {
                        text: "Normal GIS Launch",
                        onPress: () => handleGISIntegration()
                    },
                    { text: "Cancel", style: "cancel" }
                ]
            );
        }
    };

    const callLogoutFunction = () => {
        clearAllData();
        console.log('Main logoutUser -- useEffect: ');
        dispatch(logoutRequest());
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes: [{ name: 'Login' }],
            })
        );
        setIsShowLoader(false);
        setisLogoutClicked(false);
    }

    return (
        <View style={styles.container}>
            <View style={[styles.MainContentContainer, { height: userRole === 'Site Engineer' ? '90%' : '100%' }]}>
                {renderMainContent()}
            </View>
            {userRole === 'Site Engineer' && (
                <View style={styles.bottomTabBarContainer}>
                    <Image
                        source={require('../../assets/png/bottom_tab_bg.png')}
                        style={styles.background}
                    />
                    <View style={styles.tabsRow}>
                        {TABS.map((tab, idx) => {
                            const focused = selected === idx;
                            const Icon = tab.icon;
                            return (
                                <TouchableOpacity
                                    key={tab.label}
                                    testID={`tab-${tab.label}`}
                                    style={styles.tab}
                                    activeOpacity={0.8}
                                    onPress={() => handleSelect(idx)}
                                    onLongPress={() => handleLongPress(idx)}
                                >
                                    <Icon width={24} height={24} style={focused ? styles.focusedIcon : styles.icon} />
                                    <Text style={[styles.label, focused ? styles.focusedIcon : styles.icon]}>{tab.label}</Text>
                                </TouchableOpacity>
                            );
                        })}
                    </View>
                </View>
            )}
             {(isShowLoader) && (
                <View style={styles.activityIndicatorStyle}>
                    <ActivityIndicator size="large" color={Colors.primary} testID="activity-indicator" />
                    <Text style={styles.loadingText}>{loadingMessage}</Text>
                </View>
              )}
            {isLogoutClicked && (
                <View style={styles.logoutPopupContainer}>
                    <Logout
                        onCancel={() => setisLogoutClicked(false)}
                        onConfirm={() => {
                            setIsShowLoader(true);
                            callLogoutFunction();
                        }}
                    />
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1
    },
    MainContentContainer: {
        height: '100%',
    },
    bottomTabBarContainer: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    background: {
        position: 'absolute',
        backgroundColor: Colors.offWhite,
        alignSelf: 'center',
        width: '100%',
        height: ms(100),
        alignContent: 'center',
        marginVertical: ms(20),
    },
    tabsRow: {
        flexDirection: 'row',
        alignSelf: 'center',
        width: "95%",
        alignItems: 'center',
        justifyContent: 'center',
    },
    tab: {
        alignItems: 'center',
        justifyContent: 'center',
        marginVertical: ms(10),
        marginHorizontal: ms(20)
    },
    label: {
        fontFamily: 'MNSemiBold',
        fontSize: ms(12),
    },
    focusedIcon: {
        opacity: 1,
    },
    icon: {
        opacity: 0.6,
    },
    logoutPopupContainer: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 20,
    },
    activityIndicatorStyle: {
        ...StyleSheet.absoluteFillObject,
        justifyContent: 'center',
        alignItems: 'center'
      },
    loadingText: {
        marginTop: ms(10),
        fontSize: ms(14),
        fontFamily: 'MNRegular',
        color: Colors.gray,
    },
});

export default MainView;

