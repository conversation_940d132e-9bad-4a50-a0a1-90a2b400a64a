import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Text,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Image,
} from 'react-native';
import { ms } from '../../utils/Scale/Scaling';
import ProgressCard from '../../components/ProgressCard';
import { StackActions, useNavigation } from '@react-navigation/native';
import { useFocusEffect } from '@react-navigation/native';
import moment from 'moment';
import AppHeader from '../../components/AppHeader';
import FilterIcon from '../../assets/svg/filter.svg';
import Strings from '../../utils/Strings/Strings';
import BottomCalendarModal from '../../components/CalendarPicker/BottomPopupCalendar';
import MapDenoter from '../../assets/svg/map_denoter_blue.svg';
import Colors from '../../utils/Colors/Colors';
import BottomPopup from '../../components/BottomPopup';
import SearchComponent from '../../components/SearchComponent';
import { WBSItem } from '../../model/DailyProgress/DailyProgressData';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../redux/Root/rootStore';
import LoadingOverlay from '../../components/LoadingOverlay';
import { getFormattedApproverPath } from '../DailyProgress/Helper/DataFilter';
import { approveRequest } from '../../redux/DailyProgressApprover/ApproveActions';
import { handleApprovalAction } from './Helper/ApprovalHelper';
import { isNetworkConnected } from '../../utils/Network/NetworkConnection';
import { customAlertWithOK } from '../../components/CustomAlert';
import { t } from 'i18next';
import { APPROVE_FAILURE, APPROVE_SUCCESS } from '../../redux/DailyProgressApprover/ApproveActionTypes';
import { getPendingApproverWbsRequest } from '../../redux/WBSDownload/WBSActions';

const DailyProgressApproverMapView: React.FC = () => {
  const [searchText, setSearchText] = useState('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [activeModal, setActiveModal] = useState<null | 'calendar' | 'delete'>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [selectedItem, setSelectedItem] = useState<WBSItem | null>(null);
  const [pendingItems, setPendingItems] = useState<WBSItem[]>([]);
  const [filteredPendingData, setFilteredPendingData] = useState<WBSItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { response, loading, error } = useSelector((state: RootState) => state.approveReducer); // or state.approver if that's your key
  const [lastApprovalAction, setLastApprovalAction] = useState<'approve' | 'reject' | null>(null);
  const [rejectionModalVisible, setRejectionModalVisible] = useState(false);
  const pendingApprovalResponse = useSelector((state: RootState) => state.wbs.pendingApprovalResponse);
  const dispatch = useDispatch();

  const navigation = useNavigation();
  const { currentJobId } = useSelector((state: RootState) => state.home);

  // Add fetchPendingApprovals function, memoized
  const fetchPendingApprovals = React.useCallback(() => {
    if (currentJobId) {
      setIsLoading(true);
      dispatch(getPendingApproverWbsRequest({
        jobCode: currentJobId,
        type: 'ForApproval',
        selectedJob: [{ id: currentJobId }],
        objJoblist: [{ jobCode: currentJobId }],
        UID: 0,
        cb: ({ success }) => {
          setIsLoading(false);
          if (!success) {
            // handle error if needed
          }
        }
      }));
    }
  }, [currentJobId, dispatch]);

  // Refetch approvals when screen comes into focus (e.g., after returning from map view)
  useFocusEffect(
    React.useCallback(() => {
      fetchPendingApprovals();
    }, [fetchPendingApprovals])
  );

  // Replace useEffect for API fetch to use the function
  useEffect(() => {
    fetchPendingApprovals();
  }, [fetchPendingApprovals]);

  // Map API response to WBSItem and setPendingItems
  useEffect(() => {
    console.log('pendingApprovalResponse:', pendingApprovalResponse);
    if (pendingApprovalResponse && Array.isArray(pendingApprovalResponse)) {
      // Sort by TDate descending (most recent first)
      const sorted = [...pendingApprovalResponse].sort((a, b) => {
        const dateA = moment(a.TDate, ['YYYY-MM-DD', 'YYYY-MM-DDTHH:mm:ss']);
        const dateB = moment(b.TDate, ['YYYY-MM-DD', 'YYYY-MM-DDTHH:mm:ss']);
        return dateB.valueOf() - dateA.valueOf();
      });
      const pendingWBSItems = sorted.map((item) => ({
        id: item.id || item.WBS + '_' + item.Task + '_' + item.TDate,
        entity_Code: '',
        job_Code: item.JOB,
        entity_Type: item.TaskType,
        leaf_Node_Tag: '',
        entity_Description: item.WBS_Description,
        parent_WBS_Code: item.WBS,
        parent_entity_code: '',
        parent_Task: '',
        gis_Tag: '',
        isChild: false,
        et_Code: item.TaskType,
        fullPath: '',
        fullDescriptionForTooltip: '',
        taskDescription: item.Task_Custom_Description,
        ActualQuantity: item.ActualQuantity,
        Alignment: item.Alignment,
        Distance_From_Center: item.Distance_From_Center,
        From_Length: item.From_Length,
        IS_Completed: item.IS_Completed,
        Image_ID: item.Image_ID,
        Image_URL: item.Image_URL,
        Is_Hindrance: item.Is_Hindrance,
        JOB: item.JOB,
        Latitude: item.Latitude,
        Longitude: item.Longitude,
        Manpower: item.Manpower,
        NodeId: item.NodeId,
        Progress_Length: item.Progress_Length,
        Quantity: item.Quantity,
        ReferanceNodeId: item.ReferanceNodeId,
        Remarks: item.Remarks,
        TDate: item.TDate,
        Task: item.Task,
        TotalQuantity: item.TotalQuantity,
        Total_Length: item.Total_Length,
        UOM: item.UOM,
        UserID: item.UserID,
        UserName: item.UserName,
      }));
      console.log('pendingWBSItems after mapping:', pendingWBSItems);
      // Deduplicate by id
      const uniquePendingWBSItems = [];
      const seen = new Set();
      for (const item of pendingWBSItems) {
        if (!seen.has(item.id)) {
          seen.add(item.id);
          uniquePendingWBSItems.push(item);
        }
      }
      setPendingItems(uniquePendingWBSItems);
    } else {
      setPendingItems([]);
    }
  }, [pendingApprovalResponse]);

  useEffect(() => {
    if (!lastApprovalAction) return;
    console.log('useeffect -- response: ', response);

    if (
      response &&
      Array.isArray((response as any).Table) &&
      (response as any).Table[0]?.Msg
    ) {
      // Show API message, fallback to your string if not present
      let message = (response as any).Table[0]?.Msg;
      // if (!message) {
      message = lastApprovalAction === 'approve'
        ? 'Progress Approved Succesfully'
        : 'Progress Rejected Succesfully';
      // }

      customAlertWithOK(
        t('SyncData.progressUpdate'),
        message,
        [{
          text: t('commonStrings.ok'),
          onPress: () => {
            fetchPendingApprovals(); // <-- reload API on OK
            if (lastApprovalAction === 'reject') {
              setRejectionReason('');
            }
          }
        }],
        false
      );
      console.log('useefect - rem -- selectedItem: ', selectedItem);
      setLastApprovalAction(null);
      dispatch({ type: APPROVE_SUCCESS, payload: null });
    }
    else {
      customAlertWithOK(
        t('SyncData.progressUpdate'),
        error ? error : response?.data?.ErrorMessage,
        [{
          text: t('commonStrings.ok'),
          onPress: () => {
            // navigation.navigate('DailyProgressView');
          }
        }],
        false
      );
      setLastApprovalAction(null); // Reset after handling
      dispatch({ type: APPROVE_FAILURE, payload: null });
    }
  }, [response, error]);

  useEffect(() => {
    if (!currentJobId) {
      navigation.dispatch(StackActions.replace('DownloadWBS'));
      return;
    } else {
      setIsLoading(true);
    }
  }, [currentJobId, navigation]);

  // Filter data based on search and date (no BQ/IT type filtering - shows all pending items)
  useEffect(() => {
    const filtered = pendingItems.filter(item => {
      // Parse item.TDate in the format it is stored (likely 'YYYY-MM-DD' or 'YYYY-MM-DDTHH:mm:ss')
      const itemDate = moment(item.TDate, ['YYYY-MM-DD', 'YYYY-MM-DDTHH:mm:ss']).toDate();
      const isDateMatch =
        (!startDate || !endDate) || (itemDate >= startDate && itemDate <= endDate);

      // Enhanced search across multiple fields
      const searchLower = searchText.toLowerCase();
      const isSearchMatch = searchText === '' ||
        // User information
        item.UserName?.toLowerCase().includes(searchLower) ||
        // item.UserID?.toString().includes(searchText) ||

        // Task and description fields
        item.taskDescription?.toLowerCase().includes(searchLower) ||
        item.entity_Description?.toLowerCase().includes(searchLower) ||
        item.Task?.toLowerCase().includes(searchLower) ||
        item.entity_Type?.toLowerCase().includes(searchLower) ||

        // WBS and job information
        // item.parent_WBS_Code?.toLowerCase().includes(searchLower) ||
        // item.JOB?.toLowerCase().includes(searchLower) ||

        // Quantity and manpower (numbers)
        item.Quantity?.toString().includes(searchText) ||
        item.Manpower?.toString().includes(searchText) ||
        // item.ActualQuantity?.toString().includes(searchText) ||
        // item.TotalQuantity?.toString().includes(searchText) ||

        // Length and measurements
        // item.Progress_Length?.toString().includes(searchText) ||
        // item.Total_Length?.toString().includes(searchText) ||
        // item.From_Length?.toString().includes(searchText) ||

        // UOM and alignment
        // item.UOM?.toLowerCase().includes(searchLower) ||
        // item.Alignment?.toLowerCase().includes(searchLower) ||

        // Date (search in formatted date)
        moment(item.TDate).format('DD/MM/YYYY').includes(searchText) ||
        moment(item.TDate).format('DD-MM-YYYY').includes(searchText) ||
        moment(item.TDate).format('YYYY-MM-DD').includes(searchText);

      // Remarks
      // item.Remarks?.toLowerCase().includes(searchLower) ||

      // Node and reference information
      // item.NodeId?.toLowerCase().includes(searchLower) ||
      // item.ReferanceNodeId?.toLowerCase().includes(searchLower) ||

      // Coordinates (as strings)
      // item.Latitude?.toString().includes(searchText) ||
      // item.Longitude?.toString().includes(searchText);

      return isDateMatch && isSearchMatch;
    });
    console.log('filteredPendingData before set:', filtered);
    setFilteredPendingData(filtered);
  }, [pendingItems, searchText, startDate, endDate]);

  // Remove the old loadPendingForApprovalListData function and its usage

  const handleDateApply = (from: string, to: string) => {
    // from and to are in D/MMM/YYYY format
    const start = moment(from, 'D/MMM/YYYY').startOf('day').toDate();
    const end = moment(to, 'D/MMM/YYYY').endOf('day').toDate();
    setStartDate(start);
    setEndDate(end);
    setTimeout(() => setActiveModal(null), 150);
  };

  const handleReject = (item: any) => {
    // setTimeout(() => setRejectionModalVisible(true), 150);
    setRejectionModalVisible(true);
    setSelectedItem(item);
    // setActiveModal('delete');
    console.log('handleReject -- item:', item);
  };

  const handleApprove = (item: WBSItem) => {
    setSelectedItem(item); // Ensure selectedItem is set for approve as well
    (async () => {
      const isInternetConnected = await isNetworkConnected();
      console.log('isInternetConnected: ', isInternetConnected);
      if (isInternetConnected) {
        setLastApprovalAction('approve');
        const payload = handleApprovalAction(item, 'approve', getFormattedApproverPath, '', []);
        dispatch(approveRequest(payload));
      }
    })();
  };

  const handleRejectSubmit = async () => {
    console.log('handleRejectSubmit -- selectedItem: ', selectedItem);
    if (!selectedItem) return;
    const isInternetConnected = await isNetworkConnected();
    console.log('isInternetConnected: ', isInternetConnected);
    if (isInternetConnected) {
      setLastApprovalAction('reject');
      const payload = handleApprovalAction(selectedItem, 'reject', getFormattedApproverPath, rejectionReason, []);
      dispatch(approveRequest(payload));
      // setTimeout(() => setActiveModal(null), 150);
      // setRejectionReason('');

      setTimeout(() => setRejectionModalVisible(false), 150);
      // setRejectionReason('');
      // setSelectedMarker(null);
    }
  };

  const handleCloseModal = () => {
    setActiveModal(null);
    setRejectionReason('');
    setSelectedItem(null);
  };

  const handleRejectCancel = () => {
    setTimeout(() => setRejectionModalVisible(false), 150);
    setRejectionReason('');
    setSelectedItem(null);
  };

  // Add this useEffect for debugging selectedItem changes
  useEffect(() => {
    console.log('selectedItem changed:', selectedItem);
  }, [selectedItem]);

  // Add this before your component or inside the component before rendering
  const today = moment();
  const thirtyDaysAgo = moment().subtract(30, 'days');

  // Inside your component:
  // Find the existing useState for startDate and endDate and do not redeclare them.
  // Only add the initialization useEffect if they are already declared.

  useEffect(() => {
    if (!startDate && !endDate) {
      setStartDate(thirtyDaysAgo.toDate());
      setEndDate(today.toDate());
    }
  }, [startDate, endDate, thirtyDaysAgo, today]);

  // Log filteredPendingData before rendering
  console.log('filteredPendingData before render:', filteredPendingData);

  return (
    <>
      <View style={styles.root}>
        <AppHeader
          title={Strings.DailyProgress.progressUpdate}
          rightContent={
            <TouchableOpacity
              onPress={() => setTimeout(() => setActiveModal('calendar'), 150)}
              style={styles.filterRow}>
              <FilterIcon />
              <Text style={styles.filterTextColor}>
                {startDate && endDate
                  ? `${moment(startDate).format('DD/MM')} - ${moment(endDate).format('DD/MM')}`
                  : ''}
              </Text>
            </TouchableOpacity>
          }
          onBookmarkPress={() => {
            setTimeout(() => setActiveModal('calendar'), 150);
          }}
        />
        <View style={styles.searchContainer}>
          <SearchComponent
            value={searchText}
            onChange={setSearchText}
            customStyle={styles.SearchComponentstyle}
          />
        </View>

        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}>
          {/* Show No Data message if no filtered results */}
          {filteredPendingData.length === 0 ? (
            // <View style={{ alignItems: 'center', marginTop: 40 }}>
            //   <Text style={{ color: Colors.textInputBlack, fontSize: 14, fontWeight: '500' }}>No pending approvals for this date range</Text>
            // </View>
            <View style={styles.emptyContainer}>
              <Image
                source={require('../../assets/images/NoJobs.png')}
                style={styles.emptyImage}
                resizeMode="contain"
              />
            </View>
          ) : (
            filteredPendingData.map((item: WBSItem, idx: number) => (
              <View style={styles.cardWrapper} key={`${idx}`}>
                <ProgressCard
                  name={item.UserName || 'Unknown User'}
                  details={getFormattedApproverPath(item)}
                  progressQty={item.Quantity?.toString() || '0'}
                  manDays={item.Manpower?.toString() || '0'}
                  date={item.TDate ? moment(item.TDate).format('DD/MM/YYYY') : ''}
                  onApprove={() => handleApprove(item)}
                  onReject={() => handleReject(item)}
                  onArrow={() => {
                    console.log('DailyProgressApprove - navigating with item:', item);
                    console.log('DailyProgressApprove - item.Manpower:', item.Manpower);
                    console.log('DailyProgressApprove - item.Quantity:', item.Quantity);
                    navigation.navigate(
                      'DailyProgressApproverUpdate' as any,
                      item, // Pass the complete item data
                    );
                  }}
                />
              </View>
            ))
          )}
        </ScrollView>

        <TouchableOpacity
          style={styles.bottomRightButton}
          onPress={() => {
            console.log('Navigating to map with filtered data:', filteredPendingData.length, 'items');
            navigation.navigate('DailyProgressApproverMapView' as any, {
              pendingItems: filteredPendingData
            });
          }}>
          <MapDenoter />
        </TouchableOpacity>

        {/* Calendar Modal */}
        {/* <Modal
          animationType="none"
          transparent
          visible={activeModal === 'calendar'}
          onRequestClose={handleCloseModal}>
          <View style={styles.modalOverlay}> */}
        <BottomCalendarModal
          visible={activeModal === 'calendar'}
          initialDate={new Date()}
          onClose={handleCloseModal}
          fromDateTitle="From Date"
          toDateTitle="End Date"
          showFromDate={true}
          showToDate={true}
          showBottomButton={true}
          selectedFromDate={startDate ? moment(startDate).format('D/MMM/YYYY') : ''}
          seelctedToDate={endDate ? moment(endDate).format('D/MMM/YYYY') : ''}
          onApply={handleDateApply}
          allowFutureDate={false}
        />
        {/* </View>
        </Modal> */}

        {/* Rejection Modal */}
        {/* <Modal
          key={modalKey}
          animationType="slide"
          transparent={true}
          visible={rejectionModalVisible}
          onRequestClose={handleRejectCancel}
          onDismiss={() => {
            setSelectedItem(null);
            setRejectionReason('');
          }}>
          <View style={styles.modalOverlay}> */}
        <BottomPopup visible={rejectionModalVisible} onCancelPress={handleRejectCancel}>
          <Text style={styles.modalTitle}>
            {Strings.DailyProgressApprover.reasonForReject}
          </Text>
          <View style={styles.titleUnderline} />
          <View style={styles.inputContainerStyle}>
            <Text style={styles.remarksLabel}>Remarks *</Text>
            <TextInput
              style={styles.remarksInput}
              multiline
              placeholder="Enter"
              value={rejectionReason}
              onChangeText={setRejectionReason}
              placeholderTextColor={Colors.textInputBlack}

            />
          </View>
          <View style={styles.shadowLine} />
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleRejectCancel}>
              <Text style={styles.cancelButtonText}>
                {Strings.DailyProgressApprover.cancel}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={
                styles.modelRejectButton
              }
              onPress={handleRejectSubmit}
              disabled={rejectionReason.trim() === ''}>
              <Text style={styles.modelRejectButtonText}>
                {Strings.DailyProgressApprover.reject}
              </Text>
            </TouchableOpacity>
          </View>
        </BottomPopup>
        {/* </View> */}
        {/* </Modal> */}
      </View>
      {loading && <ActivityIndicator style={styles.activityIndicatorStyle} size="large" color={Colors.primary}
        testID="activity-indicator" />}

      {isLoading && <LoadingOverlay visible={isLoading} message="Loading pending approvals..." />}
    </>
  );
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: Colors.containerligetBlue,
  },
  scrollContent: {
    paddingBottom: ms(12),
  },
  searchContainer: {
    marginHorizontal: ms(16),
    marginTop: ms(8),
  },
  cardWrapper: {
    marginBottom: ms(16),
    marginHorizontal: ms(16),
  },
  bottomRightButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',


    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,

    position: 'absolute',
    bottom: 20,
    right: 30,
  },

  bottomRightButtonText: {
    color: Colors.white,
    fontSize: ms(16),
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterTextColor: {
    marginLeft: ms(4),
    fontSize: ms(14),
    color: Colors.black
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: '#000000aa',
    justifyContent: 'flex-end',
  },
  SearchComponentstyle: {
    backgroundColor: Colors.white,
    marginHorizontal: 0,
  },
  // Add these new styles for the rejection modal
  modalTitle: {
    fontSize: 17,
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: 20,
    marginTop: 40,
    marginLeft: 20,
    fontFamily: 'MNBold',
  },
  inputContainerStyle: {
    width: '90%',
    marginBottom: 10,
    marginLeft: 20
  },
  remarksLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    color: Colors.pipeIdTextBlack,
    fontFamily: 'MNMedium',
  },
  remarksInput: {
    borderWidth: 1,
    borderColor: Colors.grey,
    backgroundColor: Colors.containerligetBlue,
    borderRadius: 5,
    padding: 10,
    minHeight: 80,
    textAlignVertical: 'top',
    fontSize: 14,
    color: Colors.primary,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '94%',
    marginBottom: 20,
    marginHorizontal: 15,
  },
  cancelButton: {
    backgroundColor: Colors.white,
    borderColor: Colors.blue,
    borderWidth: 1.5,
    paddingVertical: ms(10),
    paddingHorizontal: ms(20),
    alignItems: 'center',
    borderRadius: ms(5),
    width: '45%',
  },
  cancelButtonText: {
    color: Colors.blue,
    fontSize: 16,
    fontWeight: 700,
    fontFamily: 'MNBold',
  },
  modelRejectButton: {
    backgroundColor: Colors.red,
    borderColor: Colors.red,
    borderWidth: 1,
    paddingVertical: ms(10),
    paddingHorizontal: ms(20),
    alignItems: 'center',
    borderRadius: ms(5),
    width: '45%',
  },
  modelRejectButtonText: {
    color: Colors.updateTextLightBlue,
    fontSize: 16,
    fontWeight: 700,
    fontFamily: 'MNBold',
  },
  disabledButton: {
    opacity: 0.5,
  },
  titleUnderline: {
    height: 1,
    backgroundColor: Colors.grey,
    width: '90%',
    marginLeft: 20,
    marginBottom: 20,
  },
  shadowLine: {
    height: 1,
    backgroundColor: '#ddd',
    width: '100%',
    marginBottom: 25,
    marginTop: 15,
    shadowColor: Colors.dailyProgressBg,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 5,
  },
  activityIndicatorStyle: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyImage: {
    width: ms(100),
    height: ms(100),
  },
});

export default DailyProgressApproverMapView; 