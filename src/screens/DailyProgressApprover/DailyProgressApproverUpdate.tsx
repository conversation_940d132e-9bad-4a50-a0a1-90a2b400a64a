import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Modal,
  ActivityIndicator,
  Platform,
  Alert,
  Linking,
  Image,
} from 'react-native';
import {
  launchCamera,
  launchImageLibrary,
  MediaType,
  PhotoQuality,
  Asset as ImagePickerAsset,
} from 'react-native-image-picker';
import Strings from '../../utils/Strings/Strings';
import DirectionArrow from '../../assets/svg/direction_arrow.svg';
import CalenderIcon from '../../assets/svg/calendar.svg';
import Upload from '../../assets/svg/upload.svg';
import Colors from '../../utils/Colors/Colors';
import { useNavigation, useRoute } from '@react-navigation/native';
import DailyProgressTextInput from '../DailyProgress/components/DailyProgressTextInput';
import BottomPopupImageUpload from '../../components/BottomPopupImageUpload';
import DelEngineerScBottomPopup from '../DailyProgress/components/DelEngineerScBottomPopup';
import EditPathOptionsPopup from '../DailyProgress/components/EditPathOptionsBottomPopup';
import AppHeader from '../../components/AppHeader';
import { ms, width } from '../../utils/Scale/Scaling';
import BottomPopup from '../../components/BottomPopup';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../redux/Root/rootStore';
import moment from 'moment';
import { getFormattedApproverPath } from '../DailyProgress/Helper/DataFilter';
import { UploadImageResponse, WBSItem } from '../../model/DailyProgress/DailyProgressData';
import AttachmentComponent from '../../components/Attachment';
import { isNetworkConnected } from '../../utils/Network/NetworkConnection';
import { handleApprovalAction } from './Helper/ApprovalHelper';
import { approveRequest } from '../../redux/DailyProgressApprover/ApproveActions';
import { getUserInfo } from '../../utils/DataStorage/Storage';
import { APPROVE_FAILURE, APPROVE_SUCCESS } from '../../redux/DailyProgressApprover/ApproveActionTypes';
import { customAlertWithOK } from '../../components/CustomAlert';
import { t } from 'i18next';
import { database } from '../../database';
import BottomCalendarModal from '../../components/CalendarPicker/BottomPopupCalendar';


const DailyProgressApproverUpdate: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch();

  // Get the passed data from navigation
  const passedData = route.params as any;

  // Debug logging to verify data
  console.log('DailyProgressApproverUpdate - passedData:', passedData);
  console.log('DailyProgressApproverUpdate - Manpower:', passedData?.Manpower);
  console.log('DailyProgressApproverUpdate - Quantity:', passedData?.Quantity);

  // const { loading, response } = useSelector(
  //   (state: RootState) => state.approveReducer || { loading: false, response: null },
  // );

  const [progressQuantity, setProgressQuantity] = useState<string>(passedData?.Quantity?.toString() || '');
  const [manDays, setManDays] = useState<string>(passedData?.Manpower?.toString() || '');
  const [remarks, setRemarks] = useState<string>(passedData?.Remarks?.toString() || '');
  const [imgUploadModalVisible, setImgUploadModalVisible] = useState(false);
  const [pathEditOptionsModalVisible, setPathEditOptionsModalVisible] = useState(false);
  const [delEngineerScModalVisible, setDelEngineerScModalVisible] = useState(false);
  const [rejectionModalVisible, setRejectionModalVisible] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [selectedImages, setSelectedImages] = useState<ImagePickerAsset[]>([]);
  const [uploadingImages, setUploadingImages] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadedUniqueIds, setUploadedUniqueIds] = useState<string>('');
  const [imageResposne, setImageResponse] = React.useState<UploadImageResponse[]>([]);
  const [lastApprovalAction, setLastApprovalAction] = useState<'approve' | 'reject' | null>(null);
  const { response, loading, error } = useSelector((state: RootState) => state.approveReducer); // or state.approver if that's your key
  const [isCalendarPopupVisible, setIsCalendarPopupVisible] = useState(false);
  // Update selectedDate initialization to always have a value in 'DD/MM/YYYY' format
  const initialDate = passedData?.TDate ? moment(passedData.TDate) : moment();
  const [selectedDate, setSelectedDate] = useState(initialDate.format('DD/MM/YYYY'));

  const user = getUserInfo();

  const handleProgressQtySelect = useCallback(() => {
    // Implement progress qty selection logic
  }, []);

  const handleDatePicker = useCallback(() => {
    // Implement date picker logic
  }, []);

  // useEffect(() => {
  //   console.log('response: ', response);
  //   if (response && response.data && !response.data.ErrorMessage) {
  //     let message = '';
  //     if (lastApprovalAction === 'approve') {
  //       message = t('ProgressUpdate.progressApproved')
  //     } else if (lastApprovalAction === 'reject') {
  //       message = t('ProgressUpdate.progressRejected')
  //     }

  //     customAlertWithOK(
  //       t('SyncData.progressUpdate'),
  //       message,
  //       [{
  //         text: t('commonStrings.ok'),
  //         onPress: () => {
  //           navigation.navigate('DailyProgressApprove');
  //         }
  //       }],
  //       false
  //     );

  //     setLastApprovalAction(null); 
  //     dispatch({ type: APPROVE_SUCCESS, payload: null });
  //   }
  //   else {
  //     customAlertWithOK(
  //       t('SyncData.progressUpdate'),
  //       error ? error : response?.data?.ErrorMessage,
  //       [{
  //         text: t('commonStrings.ok'),
  //         onPress: () => {
  //           // navigation.navigate('DailyProgressView');
  //         }
  //       }],
  //       false
  //     );
  //     setLastApprovalAction(null); // Reset after handling
  //   dispatch({ type: APPROVE_FAILURE, payload: null });
  //   }
  // }, [response, error, dispatch]);

  useEffect(() => {
    if (!lastApprovalAction) return;
    console.log('useeffect -- response: ', response);
    // if (
    //   response &&
    //   Array.isArray((response as any).Table) &&
    //   (response as any).Table[0]?.Msg
    // ) {
    //   let message = '';
    //   if (lastApprovalAction === 'approve') {
    //     message = Strings.DailyProgressApprover.progressApproved;
    //   } else if (lastApprovalAction === 'reject') {
    //     message = Strings.DailyProgressApprover.progressRejected;
    //   }

    //   customAlertWithOK(
    //     t('SyncData.progressUpdate'),
    //     message,
    //     [{
    //       text: t('commonStrings.ok'),
    //       onPress: () => {
    //         navigation.navigate('DailyProgressApprove');
    //       }
    //     }],
    //     false
    //   );

      if (
        response &&
        Array.isArray((response as any).Table) &&
        (response as any).Table[0]?.Msg
      ) {
        // Show API message, fallback to your string if not present
        let message = (response as any).Table[0]?.Msg;
        // if (!message) {
        message = lastApprovalAction === 'approve'
        ? 'Progress Approved Succesfully'
        : 'Progress Rejected Succesfully';
        // }
  
        customAlertWithOK(
          t('SyncData.progressUpdate'),
          message,
          [{
            text: t('commonStrings.ok'),
            onPress: () => {
              if (lastApprovalAction === 'reject') {
                setRejectionReason('');
              }
              navigation.navigate('DailyProgressApprove');
              
            }
          }],
          false
        );

      setLastApprovalAction(null);
      dispatch({ type: APPROVE_SUCCESS, payload: null });
    }
    else {
      customAlertWithOK(
        t('SyncData.progressUpdate'),
        error ? error : response?.data?.ErrorMessage,
        [{
          text: t('commonStrings.ok'),
          onPress: () => {
            // navigation.navigate('DailyProgressView');
          }
        }],
        false
      );
      setLastApprovalAction(null); // Reset after handling
      dispatch({ type: APPROVE_FAILURE, payload: null });
    }

    // if (passedData) {
    //   // removeItemFromList(passedData.id);
    //   database.write(async () => {
    //     try {
    //       const record = await database.get('PendingForApprovalBQIT').find(passedData.id);
    //       if (record) {
    //         await record.destroyPermanently();
    //       }
    //     } catch (e) {
    //       // Record may not exist, ignore
    //       console.log('exce in rej: ', e);
    //     }
    //   });
    // }
  }, [response, error]);



  // const checkAndRequestCameraPermission = useCallback(async () => {
  //   try {
  //     const permission = Platform.select({
  //       ios: PERMISSIONS.IOS.CAMERA,
  //       android: PERMISSIONS.ANDROID.CAMERA,
  //     });

  //     if (!permission) {
  //       Alert.alert('Error', 'Camera permission not available');
  //       return false;
  //     }

  //     const result = await check(permission);

  //     if (result === RESULTS.GRANTED) {
  //       return true;
  //     }

  //     if (result === RESULTS.DENIED) {
  //       const permissionResult = await request(permission);
  //       return permissionResult === RESULTS.GRANTED;
  //     }

  //     if (result === RESULTS.BLOCKED) {
  //       Alert.alert(
  //         'Permission Required',
  //         'Please enable camera access in your device settings to use this feature.',
  //         [
  //           { text: 'Cancel', style: 'cancel' },
  //           { text: 'Open Settings', onPress: () => Linking.openSettings() },
  //         ],
  //       );
  //       return false;
  //     }

  //     return false;
  //   } catch (error) {
  //     console.error('Error checking camera permission:', error);
  //     return false;
  //   }
  // }, []);

  // const checkAndRequestGalleryPermission = useCallback(async () => {
  //   try {
  //     let permission;
  //     if (Platform.OS === 'android') {
  //       if (Platform.Version >= 33) {
  //         permission = PERMISSIONS.ANDROID.READ_MEDIA_IMAGES;
  //       } else {
  //         permission = PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE;
  //       }
  //     } else {
  //       permission = PERMISSIONS.IOS.PHOTO_LIBRARY;
  //     }

  //     if (!permission) {
  //       Alert.alert('Error', 'Gallery permission not available');
  //       return false;
  //     }

  //     const result = await check(permission);

  //     if (result === RESULTS.GRANTED) {
  //       return true;
  //     }

  //     if (result === RESULTS.DENIED) {
  //       const permissionResult = await request(permission);
  //       return permissionResult === RESULTS.GRANTED;
  //     }

  //     if (result === RESULTS.BLOCKED) {
  //       Alert.alert(
  //         'Permission Required',
  //         'Please enable gallery access in your device settings to use this feature.',
  //         [
  //           { text: 'Cancel', style: 'cancel' },
  //           { text: 'Open Settings', onPress: () => Linking.openSettings() },
  //         ],
  //       );
  //       return false;
  //     }

  //     return false;
  //   } catch (error) {
  //     console.error('Error checking gallery permission:', error);
  //     return false;
  //   }
  // }, []);

  // // Helper to upload a single image and return its UniqueID
  // const uploadSingleImage = async (img: ImagePickerAsset, index: number): Promise<string | null> => {
  //   const url = '*********************************:443/Pragatiext/UploadImaage';
  //   const headers = {
  //     'Ocp-Apim-Subscription-Key': 'ececb381826c4379843eef6128c6360e',
  //     'Content-Type': 'application/json',
  //     Accept: 'application/json',
  //   };
  //   const payload = {
  //     FileName: `Attachment ${index + 1}.jpg `,
  //     File: img.base64 || '',
  //   };

  //   try {
  //     const response = await axios.post(url, payload, { headers });
  //     const data = response.data;
  //     if (Array.isArray(data) && data[0]?.UniqueID) {
  //       const uniqueId = data[0].UniqueID;
  //       console.log(`Successfully uploaded image ${payload.FileName} with UniqueID: ${uniqueId}`);
  //       return uniqueId;
  //     } else {
  //       console.error('Invalid response format:', data);
  //       setUploadError(`Invalid response format for ${payload.FileName}`);
  //       return null;
  //     }
  //   } catch (err: any) {
  //     console.error('Upload error:', err?.response?.data || err.message || err);
  //     setUploadError(`Failed to upload ${payload.FileName}: ${err?.response?.data || err.message || err}`);
  //     return null;
  //   }
  // };

  // // Modified image selection handlers
  // const handleCameraPress = useCallback(async () => {
  //   const hasPermission = await checkAndRequestCameraPermission();
  //   if (hasPermission) {
  //     const options = {
  //       mediaType: 'photo' as MediaType,
  //       includeBase64: true,
  //       maxHeight: 2000,
  //       maxWidth: 2000,
  //       saveToPhotos: true,
  //       quality: 0.8 as PhotoQuality,
  //     };

  //     launchCamera(options, async (response) => {
  //       if (response.didCancel) {
  //         console.log('User cancelled camera');
  //       } else if (response.errorCode) {
  //         console.log('Camera Error: ', response.errorMessage);
  //         Alert.alert('Error', 'Failed to take photo');
  //       } else if (response.assets && Array.isArray(response.assets) && response.assets.length > 0) {
  //         setSelectedImages(prev => [...prev, ...response.assets!]);
  //         setTimeout(() => setImgUploadModalVisible(false), 150);

  //         // Upload the new image immediately
  //         setUploadingImages(true);
  //         setUploadError(null);
  //         const uniqueId = await uploadSingleImage(response.assets[0], selectedImages.length);
  //         if (uniqueId) {
  //           setUploadedUniqueIds(prev => {
  //             const newIds = prev ? `${prev}~${uniqueId}` : uniqueId;
  //             console.log('Updated UniqueIDs:', newIds);
  //             return newIds;
  //           });
  //         }
  //         setUploadingImages(false);
  //       }
  //     });
  //   }
  // }, [checkAndRequestCameraPermission, selectedImages.length]);

  // const handleGalleryPress = useCallback(async () => {
  //   const hasPermission = await checkAndRequestGalleryPermission();
  //   if (hasPermission) {
  //     const options = {
  //       mediaType: 'photo' as MediaType,
  //       includeBase64: true,
  //       maxHeight: 2000,
  //       maxWidth: 2000,
  //       selectionLimit: 0,
  //       quality: 0.8 as PhotoQuality,
  //     };

  //     launchImageLibrary(options, async (response) => {
  //       if (response.didCancel) {
  //         console.log('User cancelled gallery picker');
  //       } else if (response.errorCode) {
  //         console.log('Gallery Error: ', response.errorMessage);
  //         Alert.alert('Error', 'Failed to pick image from gallery');
  //       } else if (response.assets && Array.isArray(response.assets) && response.assets.length > 0) {
  //         setSelectedImages(prev => [...prev, ...response.assets!]);
  //         setTimeout(() => setImgUploadModalVisible(false), 150);

  //         // Upload the new images immediately
  //         setUploadingImages(true);
  //         setUploadError(null);
  //         for (let i = 0; i < response.assets.length; i++) {
  //           const uniqueId = await uploadSingleImage(response.assets[i], selectedImages.length + i);
  //           if (uniqueId) {
  //             setUploadedUniqueIds(prev => {
  //               const newIds = prev ? `${prev}~${uniqueId}` : uniqueId;
  //               console.log('Updated UniqueIDs:', newIds);
  //               return newIds;
  //             });
  //           }
  //         }
  //         setUploadingImages(false);
  //       }
  //     });
  //   }
  // }, [checkAndRequestGalleryPermission, selectedImages.length]);

  const handleUploadComplete = (imageResponse: UploadImageResponse[]) => {
    console.log('aPprover details -- Uploaded Image IDs:', imageResponse);
    setImageResponse(imageResponse); // store or use them as needed
  };

  // Modified Approve/Reject handlers to use dynamic data
  const handleApprove = useCallback(async () => {
    if (loading || uploadingImages) return;

    // const payload = {
    //   jobCode: passedData?.JOB || 'LE21M114',
    //   UID: passedData?.UserID?.toString() || '1159553',
    //   Type: 'approve' as const,
    //   Notification_Desc: getFormattedApproverPath(passedData as WBSItem) || 'Progress Update',
    //   Quantity: passedData?.Quantity || 0,
    //   uOM: passedData?.UOM || 'm³',
    //   manPower: passedData?.Manpower || 0,
    //   ActualList: [
    //     {
    //       WBS: passedData?.parent_WBS_Code || 'WP100761~22597',
    //       TaskCode: passedData?.Task || '1000',
    //       ADate: passedData?.TDate || moment().format('YYYY-MM-DD'),
    //       Quantity: passedData?.Quantity?.toString() || '0',
    //       Manpower: passedData?.Manpower?.toString() || '0',
    //       Remarks: remarks || 'Approved',
    //       Tasktype: passedData?.entity_Type || 'BQ',
    //       Is_Approved: 'N',
    //       Tag: 'Y',
    //       Latitude: passedData?.Latitude || 9.555293,
    //       Longitude: passedData?.Longitude || 78.5882628,
    //     },
    //   ],
    //   Attachments: uploadedUniqueIds || null,
    // };
    // dispatch(approveRequest(payload));

    (async () => {
      const isInternetConnected = await isNetworkConnected();
      passedData.Quantity = parseFloat(progressQuantity.slice(1));
      passedData.ManPower = parseFloat(manDays.slice(1));
      passedData.Manpower = parseFloat(manDays.slice(1));
      passedData.Remarks = remarks;
      passedData.TDate = moment(selectedDate, 'DD/MM/YYYY').format('YYYY-MM-DDT00:00:00');
      console.log('isInternetConnected: ', isInternetConnected, ' -- progressQua: ', progressQuantity,
        ' -- maddays: ', manDays, ' -- passedData.quantity: ', passedData.Quantity,
        ' -- passedData.Manpower:  ', passedData.ManPower, ' -- passedData.Remarks: ', passedData.Remarks,
        ' -- passedData.TDate: ', passedData.TDate
      );
      if (isInternetConnected) {
        setLastApprovalAction('approve');
        const payload = handleApprovalAction(passedData, 'approve', getFormattedApproverPath, undefined, imageResposne);
        dispatch(approveRequest(payload));
      }
    })();
  }, [dispatch, loading, imageResposne, uploadedUniqueIds, passedData, remarks, selectedDate, manDays, progressQuantity, uploadingImages]);

  // const handleReject = (item: any) => {
  //   if (loading || uploadingImages) return;

  //   setSelectedItem(item);
  //   setModalKey(Date.now()); // update key to force modal remount
  //   setActiveModal('delete');
  //   console.log('handleReject -- item:', item);
  // };


  const handleReject = useCallback(async () => {
    if (loading || uploadingImages) return;

    // const payload = {
    //   jobCode: passedData?.JOB || 'LE21M114',
    //   UID: passedData?.UserID?.toString() || '1159553',
    //   Type: 'ADeletion' as const,
    //   Notification_Desc: getFormattedApproverPath(passedData as WBSItem) || 'Progress Update',
    //   Quantity: passedData?.Quantity || 0,
    //   uOM: passedData?.UOM || 'm³',
    //   manPower: passedData?.Manpower || 0,
    //   ActualList: [
    //     {
    //       WBS: passedData?.parent_WBS_Code || 'WP100761~22597',
    //       TaskCode: passedData?.Task || '1000',
    //       ADate: passedData?.TDate || moment().format('YYYY-MM-DD'),
    //       Quantity: passedData?.Quantity?.toString() || '0',
    //       Manpower: passedData?.Manpower?.toString() || '0',
    //       Remarks: rejectionReason || 'Rejected',
    //       Tasktype: passedData?.entity_Type || 'BQ',
    //       Is_Approved: 'N',
    //       Tag: 'Y',
    //       Latitude: passedData?.Latitude || 9.555293,
    //       Longitude: passedData?.Longitude || 78.5882628,
    //     },
    //   ],
    //   Attachments: uploadedUniqueIds || null,
    // };
    // dispatch(approveRequest(payload));
    // setTimeout(() => setRejectionModalVisible(false), 150);
    // setRejectionReason('');

    // console.log('handleRejectSubmit -- selectedItem: ', selectedItem);
    if (!passedData) return;
    (async () => {
      const isInternetConnected = await isNetworkConnected();
      passedData.Quantity = parseFloat(progressQuantity.slice(1));
      passedData.ManPower = parseFloat(manDays.slice(1));
      passedData.Manpower = parseFloat(manDays.slice(1));
      passedData.Remarks = rejectionReason;
      passedData.TDate = moment(selectedDate, 'DD/MM/YYYY').format('YYYY-MM-DDT00:00:00');
      console.log('isInternetConnected: ', isInternetConnected, ' -- progressQua: ', progressQuantity,
        ' -- maddays: ', manDays, ' -- passedData.quantity: ', passedData.Quantity,
        ' -- passedData.Manpower:  ', passedData.ManPower, ' -- passedData.Remarks: ', passedData.Remarks,
        ' -- passedData.TDate: ', passedData.TDate
      );
      if (isInternetConnected) {
        setLastApprovalAction('reject');
        const payload = handleApprovalAction(passedData, 'reject', getFormattedApproverPath, rejectionReason, imageResposne);
        dispatch(approveRequest(payload));
        // setTimeout(() => setActiveModal(null), 150);
        // setRejectionReason('');

        setTimeout(() => setRejectionModalVisible(false), 150);
        // setRejectionReason('');
        // setSelectedMarker(null);
      }
    })();
  }, [dispatch, loading, uploadingImages, uploadedUniqueIds, passedData, rejectionReason]);

  // Add effect to handle navigation after successful API call
  useEffect(() => {
    if (response && response.StatusCode === 200) {
      navigation.goBack();
    }
  }, [response, navigation]);

  const removeImage = (index: number) => {
    setSelectedImages(prev => {
      const newImages = prev.filter((_, i) => i !== index);
      // Remove the corresponding UniqueID
      setUploadedUniqueIds(prevIds => {
        if (!prevIds) return '';
        const idsArr = prevIds.split('~');
        idsArr.splice(index, 1);
        return idsArr.join('~');
      });
      return newImages;
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title={getFormattedApproverPath(passedData as WBSItem) || 'Progress Update Details'}
      />
      <ScrollView >
        <View style={styles.content}>
          <Text style={styles.heading}>
            {Strings.DailyProgressApprover.progressUpdate}
          </Text>

          <TouchableOpacity
            style={styles.progressStats}
            onPress={() => {
              // setTimeout(() => setDelEngineerScModalVisible(true), 150);
              setDelEngineerScModalVisible(true)
            }}>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Scope</Text>
              <Text style={styles.statValue}>{passedData?.TotalQuantity?.toString() || '0'}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Cum.Prog</Text>
              <Text style={styles.statValue}>{passedData?.ActualQuantity?.toString() || '0'}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>S/C Scope</Text>
              <Text style={styles.statValue}>{passedData?.Total_Length?.toString() || '0'}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>S/C Prog</Text>
              <Text style={styles.statValue}>{passedData?.Progress_Length?.toString() || '0'}</Text>
            </View>
            <DirectionArrow />
          </TouchableOpacity>
          {/* 
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Progress Qty</Text>
            <TouchableOpacity
              style={styles.selectInput}
              onPress={handleProgressQtySelect}>
              <Text style={styles.placeholderText}>{passedData?.Quantity?.toString() || '0'}</Text>
            </TouchableOpacity>
          </View> */}
          <View style={[styles.inputContainer, { marginTop: 3 }]}>
            <Text style={styles.inputLabel}>Date *</Text>
            <TouchableOpacity
              style={styles.dateInput}
              onPress={() => setIsCalendarPopupVisible(true)}>
              <Text>{selectedDate}</Text>
              <CalenderIcon />
            </TouchableOpacity>
          </View>

          <DailyProgressTextInput
            label="Progress Qty *"
            value={progressQuantity}
            onChangeText={setProgressQuantity}
            isKeypadNumeric={true}
          />

          <DailyProgressTextInput
            label="Man Days *"
            value={manDays}
            onChangeText={setManDays}
            isKeypadNumeric={true}
          />



          <DailyProgressTextInput
            customStyle={[styles.textInput, styles.remarksInput]}
            label="Remarks *"
            value={remarks}
            onChangeText={setRemarks}
            isMultiline={true}
          />

          {/* <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Attachments *</Text>
            <View style={styles.uploadButtonOuter}>
              <TouchableOpacity
                style={styles.uploadButton}
                onPress={() => setTimeout(() => setImgUploadModalVisible(true), 150)}>
                <Upload />
                <Text style={styles.uploadButtonText}>
                  {Strings.DailyProgress.uploadImage}
                </Text>
              </TouchableOpacity>
              <ScrollView horizontal style={{ marginTop: 10 }}>
                {selectedImages.map((img, idx) => (
                  <View key={idx} style={{ marginRight: 10, position: 'relative' }}>
                    <Image
                      source={{ uri: img.uri }}
                      style={{ width: 80, height: 80, margin: 10 }}
                    />
                    <TouchableOpacity
                      style={{
                        position: 'absolute',
                        top: 2,
                        right: -3,
                        backgroundColor: '#fff',
                        borderRadius: 12,
                        padding: 2,
                        elevation: 2,
                      }}
                      onPress={() => removeImage(idx)}
                    >
                      < Cross />
                    </TouchableOpacity>
                  </View>
                ))}
              </ScrollView>
            </View>
          </View> */}

          <AttachmentComponent
            onUploadComplete={handleUploadComplete}
            uploadedImages={imageResposne}
            imageUrl={passedData?.Image_URL}
            imageId={passedData?.Image_ID}
          />
        </View>

        {/* <View style={styles.approveRejectButtonContainer}>

          <TouchableOpacity
            onPress={() => setTimeout(() => setRejectionModalVisible(true), 150)}
            style={[styles.approveRejectButton, styles.rejectButton]}
            disabled={loading}>
            <Text style={styles.rejectButtonText}>Reject</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleApprove}
            style={[
              styles.approveRejectButton,
              styles.approveButton,
              loading && styles.disabledButton,
            ]}
            disabled={loading}>
            {loading ? (
              <ActivityIndicator color={Colors.white} size="small" />
            ) : (
              <Text style={styles.approveButtonText}>Approve</Text>
            )}
          </TouchableOpacity>
        </View> */}

        {/* <Modal
          animationType="none"
          transparent
          visible={imgUploadModalVisible}
          onRequestClose={() => setTimeout(() => setImgUploadModalVisible(false), 150)}>
          <View style={styles.modalOverlay}>
            <BottomPopupImageUpload
              onCameraPress={handleCameraPress}
              onGalleryPress={handleGalleryPress}
            />
          </View>
        </Modal> */}

        {/* <Modal
          animationType="none"
          transparent
          visible={delEngineerScModalVisible}
          onRequestClose={() => setTimeout(() => setDelEngineerScModalVisible(false), 150)}>
          <View style={styles.modalOverlay}>
            <DelEngineerScBottomPopup isShowScTab={true} />
          </View>
        </Modal> */}

        <DelEngineerScBottomPopup
          visible={delEngineerScModalVisible}
          isShowScTab={true}
          // progressConsolidatedData={progressConsolidatedData}
          // progressEngineerData={progressEngineerData}
          onClose={() => setDelEngineerScModalVisible(false)}
        />

        {/* <Modal
          animationType="none"
          transparent
          visible={pathEditOptionsModalVisible}
          onRequestClose={() => setTimeout(() => setPathEditOptionsModalVisible(false), 150)}>
          <View style={styles.modalOverlay}>
            <EditPathOptionsPopup />
          </View>
        </Modal> */}

        {/* <Modal
          animationType="slide"
          transparent={true}
          visible={rejectionModalVisible}
          onRequestClose={() => setTimeout(() => setRejectionModalVisible(false), 150)}>
          <View style={styles.modalOverlay}> */}
        <BottomPopup visible={rejectionModalVisible} onCancelPress={() => { setRejectionModalVisible(false) }}>
          <Text style={styles.modalTitle}>
            {Strings.DailyProgressApprover.reasonForReject}
          </Text>
          <View style={styles.titleUnderline} />
          <View style={styles.inputContainerStyle}>
            <Text style={[styles.remarksLabel, { marginTop: 30 }]}>Remarks *</Text>
            <TextInput
              style={styles.remarksInput}
              multiline
              placeholder="Enter"
              value={rejectionReason}
              onChangeText={setRejectionReason}
              placeholderTextColor={Colors.textInputBlack}

            />
          </View>
          <View style={styles.shadowLine} />
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={() => setTimeout(() => setRejectionModalVisible(false), 150)}>
              <Text style={styles.cancelButtonText}>
                {Strings.DailyProgressApprover.cancel}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.modelRejectButton}
              onPress={handleReject}
              disabled={loading || rejectionReason.trim() === ''}>
              <Text style={styles.modelRejectButtonText}>
                {Strings.DailyProgressApprover.reject}
              </Text>
            </TouchableOpacity>
          </View>
        </BottomPopup>
        {/* </View>
        </Modal> */}

        <BottomCalendarModal
          visible={isCalendarPopupVisible}
          initialDate={passedData?.TDate ? new Date(passedData.TDate) : new Date()}
          onClose={() => setIsCalendarPopupVisible(false)}
          fromDateTitle="Date"
          selectedFromDate={moment(selectedDate, 'DD/MM/YYYY').format('D/MMM/YYYY')}
          onApply={(date, _) => {
            // date is in 'D/MMM/YYYY', convert to 'DD/MM/YYYY' for display and 'YYYY-MM-DD' for storage
            const displayDate = moment(date, 'D/MMM/YYYY').format('DD/MM/YYYY');
            setSelectedDate(displayDate);
            passedData.TDate = moment(date, 'D/MMM/YYYY').format('YYYY-MM-DD');
            setIsCalendarPopupVisible(false);
          }}
          allowFutureDate={false}
          singleDateMode={true}
          showBottomButton={true}
        />

        {/* Add a modal for uploading images */}
        <Modal visible={uploadingImages} transparent animationType="fade">
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.4)' }}>
            <View style={{ backgroundColor: 'white', padding: 30, borderRadius: 10 }}>
              <Text style={{ fontSize: 18, fontWeight: 'bold' }}>Please wait...</Text>
              <Text style={{ marginTop: 10 }}>Uploading images to server</Text>
            </View>
          </View>
        </Modal>
        {/* Show error popup if upload fails */}
        {uploadError && (
          <Modal visible={!!uploadError} transparent animationType="fade">
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.4)' }}>
              <View style={{ backgroundColor: 'white', padding: 30, borderRadius: 10 }}>
                <Text style={{ fontSize: 18, fontWeight: 'bold', color: 'red' }}>Upload Error</Text>
                <Text style={{ marginTop: 10 }}>{uploadError}</Text>
                <TouchableOpacity onPress={() => setTimeout(() => setUploadError(null), 150)} style={{ marginTop: 20 }}>
                  <Text style={{ color: 'blue' }}>Close</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        )}
        {loading && <ActivityIndicator style={styles.activityIndicatorStyle} size="large" color={Colors.primary}
          testID="activity-indicator" />}

      </ScrollView>

      <View style={styles.approveRejectButtonContainer}>

        <TouchableOpacity
          onPress={() => setTimeout(() => setRejectionModalVisible(true), 150)}
          style={[styles.approveRejectButton, styles.rejectButton]}
          disabled={loading}>
          <Text style={styles.rejectButtonText}>Reject</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            const missingFields = [];
            if (!progressQuantity) missingFields.push('Progress Quantity');
            if (!manDays) missingFields.push('Man Days');
            if (!remarks) missingFields.push('Remarks');
            if (missingFields.length > 0) {
              customAlertWithOK(
                'Alert',
                `Please enter ${missingFields.join(', ')} data`
              );
              return;
            }
            handleApprove();
          }
          }
          style={[
            styles.approveRejectButton,
            styles.approveButton,
            loading && styles.disabledButton,
          ]}
          disabled={loading}>
          {loading ? (
            <ActivityIndicator color={Colors.white} size="small" />
          ) : (
            <Text style={styles.approveButtonText}>Approve</Text>
          )}
        </TouchableOpacity>
      </View>

    </SafeAreaView>
  );
};

export default DailyProgressApproverUpdate;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: Colors.white,
  },
  content: {
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  progressStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.white,
    padding: 12,
    borderRadius: 10,
    marginBottom: 16,
    marginTop: 5,
    borderColor: Colors.searchBorderGrey,
    borderWidth: 1,
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.textInputBlack,
    fontFamily: 'MNMedium',
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textPrimary,
    fontFamily: 'MNMedium',
    marginTop: 8,
  },
  statItemforIT: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  inputContainer: {
    marginVertical: 3,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 3,
    fontWeight: '400',
    color: Colors.pipeIdTextBlack,
  },
  selectInput: {
    backgroundColor: Colors.containerligetBlue,
    padding: 12,
    borderRadius: 8,
    borderColor: Colors.searchBorderGrey,
    borderWidth: 1,
  },
  placeholderText: {
    color: Colors.textInputBlack,
    fontSize: 14,
  },
  textInput: {
    backgroundColor: Colors.containerligetBlue,
    borderColor: Colors.searchBorderGrey,
    borderWidth: 1,
    padding: 12,
    borderRadius: 8,
    color: Colors.searchTextBlack,
    fontSize: 14,
  },
  dateInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.containerligetBlue,
    borderColor: Colors.searchBorderGrey,
    borderWidth: 1,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  uploadButtonOuter: {
    padding: 8,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: Colors.searchBorderGrey,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: Colors.secondary,
  },
  uploadButtonText: {
    color: Colors.textPrimary,
    marginLeft: 8,
  },
  line: {
    backgroundColor: Colors.searchBorderGrey,
    height: 2,
    marginTop: 5,
  },
  picker: {
    height: 60,
    alignItems: 'center',
    justifyContent: 'center',
    alignContent: 'center',
  },
  label: {
    fontSize: 14,
    marginVertical: 5,
    fontWeight: '400',
    color: Colors.textInputBlack,
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  width: {
    width: '90%',
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
    margin: 20,
  },
  cancelButton: {
    backgroundColor: Colors.white,
    borderColor: Colors.blue,
    borderWidth: 1.5,
    paddingVertical: ms(10),
    paddingHorizontal: ms(20),
    alignItems: 'center',
    borderRadius: ms(5),
    width: '45%',
  },
  modelRejectButton: {
    backgroundColor: Colors.red,
    borderColor: Colors.red,
    borderWidth: 1,
    paddingVertical: ms(10),
    paddingHorizontal: ms(20),
    alignItems: 'center',
    borderRadius: ms(5),
    width: '45%',
  },
  cancelButtonText: {
    color: Colors.blue,
    fontSize: 16,
    fontWeight: 700,
    fontFamily: 'MNBold',
  },
  modelRejectButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: 700,
    fontFamily: 'MNBold',

  },

  approveRejectButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: ms(20),
    backgroundColor: Colors.white,
    borderTopWidth: 0.25,
    borderTopColor: Colors.searchBorderGrey,
    width: '95%'
  },
  approveRejectButton: {
    width: '95%'
  },
  approveButton: {
    backgroundColor: Colors.onlineGreen,
    borderColor: Colors.onlineGreen,
    borderWidth: 1,
    paddingVertical: ms(10),
    paddingHorizontal: ms(20),
    alignItems: 'center',
    borderRadius: ms(5),
    marginLeft: ms(20),

    width: '50%',
  },
  approveButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'MNBold',
  },
  rejectButton: {
    backgroundColor: Colors.white,
    borderColor: Colors.red,
    borderWidth: 1,
    paddingVertical: ms(10),
    paddingHorizontal: ms(15),
    alignItems: 'center',
    borderRadius: ms(5),

    width: '50%',
  },
  rejectButtonText: {
    color: Colors.red,
    fontWeight: '500',
    fontSize: 16,
    fontFamily: 'MNBold',

  },
  heading: {
    fontWeight: '500',
    fontSize: 16,
    fontFamily: 'MNBold',
    marginBottom: ms(8),
    color: Colors.textPrimary,
    marginTop: ms(10),

  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Dim background
  },
  modalView: {
    margin: 20,
    backgroundColor: Colors.white,
    padding: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '100%', // Adjust width as needed
  },
  modalTitle: {
    fontSize: 17,
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: 20,
    marginTop: 40,
    marginLeft: 20,
    fontFamily: 'MNBold',
  },
  inputContainerStyle: {
    width: '90%',

    marginLeft: 20
  },
  remarksLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    color: Colors.pipeIdTextBlack,
    fontFamily: 'MNMedium',
    marginTop: ms(0),
  },
  remarksInput: {
    borderWidth: 1,
    borderColor: Colors.grey,
    backgroundColor: Colors.containerligetBlue,
    borderRadius: 5,
    padding: 10,
    minHeight: 80,
    textAlignVertical: 'top',
    fontSize: 14,
    color: Colors.primary,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 20,
  },
  titleUnderline: {
    height: 1,
    backgroundColor: Colors.grey,
    width: '90%',
    marginLeft: 20,
    marginBottom: 20,
  },

  shadowLine: {
    height: 1,
    backgroundColor: '#ddd',
    width: '100%',
    marginVertical: 20,
    shadowColor: Colors.dailyProgressBg,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 5,
  },
  handleContainer: {
    alignItems: 'center',
    width: '100%',
    marginBottom: 10,
  },
  disabledButton: {
    opacity: 0.5,
  },
  headerTitleStyle: {
    fontFamily: 'MNMedium',
    fontWeight: '600',
    fontSize: ms(16),
    color: Colors.secondary
  },

  activityIndicatorStyle: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },

}); 