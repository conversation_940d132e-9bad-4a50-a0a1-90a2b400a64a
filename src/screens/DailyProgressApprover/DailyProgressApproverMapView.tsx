import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Modal,
  TextInput,
  ActivityIndicator,
  PermissionsAndroid,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import MapView, { Marker } from 'react-native-maps';
import AppHeader from '../../components/AppHeader';
import SearchComponent from '../../components/SearchComponent';
import Colors from '../../utils/Colors/Colors';
import Strings from '../../utils/Strings/Strings';
import DirectionArrow from '../../assets/svg/direction_arrow.svg';
import Pointer from '../../assets/svg/pointer.svg';
import Circle from '../../assets/svg/circle.svg';
import Gps from '../../assets/svg/gps.svg';
import { ms } from '../../utils/Scale/Scaling';
import { useNavigation, useRoute } from '@react-navigation/native';
import { WBSItem } from '../../model/DailyProgress/DailyProgressData';
import Geolocation from '@react-native-community/geolocation';
import { useDispatch, useSelector } from 'react-redux';
import { approveRequest } from '../../redux/DailyProgressApprover/ApproveActions';
import { getFormattedApproverPath } from '../DailyProgress/Helper/DataFilter';
import { RootState } from '../../redux/Root/RootReducer';
import { customAlertWithOK } from '../../components/CustomAlert';
import { t } from 'i18next';
import { APPROVE_FAILURE, APPROVE_SUCCESS } from '../../redux/DailyProgressApprover/ApproveActionTypes';
import BottomPopup from '../../components/BottomPopup';
import { isNetworkConnected } from '../../utils/Network/NetworkConnection';
import { handleApprovalAction } from './Helper/ApprovalHelper';
// import { approveRequest } from '../../redux/DailyProgressApprover/ApproveActions';
// import { getFormattedApproverPath } from '../DailyProgress/Helper/DataFilter';

interface MarkerType {
  id: string;
  name: string;
  details: string;
  progressQty: string;
  manDays: string;
  date: string;
  latitude: number;
  longitude: number;
}

const DailyProgressApproverMapView = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedMarker, setSelectedMarker] = useState<MarkerType | null>(null);
  const [rejectionModalVisible, setRejectionModalVisible] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [currentLocation, setCurrentLocation] = useState<{ latitude: number, longitude: number } | null>(null);
  const [originalRegion, setOriginalRegion] = useState<{ latitude: number, longitude: number, latitudeDelta: number, longitudeDelta: number } | null>(null);
  const [isAtCurrentLocation, setIsAtCurrentLocation] = useState(false);
  const [locationLoading, setLocationLoading] = useState(false);
  const mapRef = useRef<MapView | null>(null);
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch();
  const { response, loading, error } = useSelector((state: RootState) => state.approveReducer); // or state.approver if that's your key
  const [lastApprovalAction, setLastApprovalAction] = useState<'approve' | 'reject' | null>(null);

  // Get the passed data from navigation
  const passedData = route.params as { pendingItems: WBSItem[] } | undefined;
  // const pendingItems = passedData?.pendingItems || [];

  // // Convert WBSItem to MarkerType for map display
  // const mapData: MarkerType[] = pendingItems.map((item) => ({
  //   id: item.id,
  //   name: item.UserName || 'Unknown User',
  //   details: getFormattedApproverPath(item),
  //   progressQty: item.Quantity?.toString() || '0',
  //   manDays: item.Manpower?.toString() || '0',
  //   date: item.TDate ? new Date(item.TDate).toLocaleDateString('en-GB') : '',
  //   latitude: item.Latitude || 28.6139,
  //   longitude: item.Longitude || 77.209,
  // }));

  const [pendingItems, setPendingItems] = useState<WBSItem[]>(passedData?.pendingItems || []);
  const [mapData, setMapData] = useState<MarkerType[]>(() =>
    (passedData?.pendingItems || []).map((item) => ({
      id: item.id,
      name: item.UserName || 'Unknown User',
      details: getFormattedApproverPath(item),
      progressQty: item.Quantity?.toString() || '0',
      manDays: item.Manpower?.toString() || '0',
      date: item.TDate ? new Date(item.TDate).toLocaleDateString('en-GB') : '',
      latitude: item.Latitude || 28.6139,
      longitude: item.Longitude || 77.209,
    }))
  );

  console.log('Pending items length:', pendingItems.length);
  console.log('Map data created:', mapData.length);
  console.log('First map data item:', mapData[0]);

  // Filtered data based on search query
  const filteredData = mapData.filter((item) => {
    const query = searchQuery.toLowerCase();
    return (
      item.name.toLowerCase().includes(query) ||
      item.details.toLowerCase().includes(query) ||
      item.date.toLowerCase().includes(query) ||
      item.progressQty.toString().includes(query) ||
      item.manDays.toString().includes(query)
    );
  });

  useEffect(() => {
    setMapData(
      pendingItems.map((item) => ({
        id: item.id,
        name: item.UserName || 'Unknown User',
        details: getFormattedApproverPath(item),
        progressQty: item.Quantity?.toString() || '0',
        manDays: item.Manpower?.toString() || '0',
        date: item.TDate ? new Date(item.TDate).toLocaleDateString('en-GB') : '',
        latitude: item.Latitude || 28.6139,
        longitude: item.Longitude || 77.209,
      }))
    );
  }, [pendingItems]);

  useEffect(() => {
    if (!lastApprovalAction) return;
    console.log('useeffect -- response: ', response);
    if (
      response &&
      Array.isArray((response as any).Table) &&
      (response as any).Table[0]?.Msg
    ) {
      let message = (response as any).Table[0]?.Msg;
      // if (!message) {
      message = lastApprovalAction === 'approve'
        ? 'Progress Approved Succesfully'
        : 'Progress Rejected Succesfully';
      // }


      customAlertWithOK(
        t('SyncData.progressUpdate'),
        message,
        [{
          text: t('commonStrings.ok'),
          onPress: () => {
            if (selectedMarker) {
              setSelectedMarker(null);
              setPendingItems(prev => {
                const updated = prev.filter(i => i.id !== selectedMarker.id);
                console.log('After removal, pendingItems:', updated.map(i => i.id));
                return updated;
              });
            }
            if (lastApprovalAction === 'reject') {
              setRejectionReason('');
            }
          }
        }],
        false
      );

      // Remove from state and DB
      // if (selectedItem) {
      //   removeItemFromList(selectedItem.id);
      //   database.write(async () => {
      //     try {
      //       const record = await database.get('PendingForApprovalBQIT').find(selectedItem.id);
      //       if (record) {
      //         await record.destroyPermanently();
      //       }
      //     } catch (e) {
      //       // Record may not exist, ignore
      //       console.log('exce in rej: ', e);
      //     }
      //   });
      // }
      setLastApprovalAction(null);
      dispatch({ type: APPROVE_SUCCESS, payload: null });
      // setSelectedItem(null);
    }
    else {
      customAlertWithOK(
        t('SyncData.progressUpdate'),
        error ? error : response?.data?.ErrorMessage,
        [{
          text: t('commonStrings.ok'),
          onPress: () => {
            // navigation.navigate('DailyProgressView');
          }
        }],
        false
      );
      setLastApprovalAction(null); // Reset after handling
      dispatch({ type: APPROVE_FAILURE, payload: null });
    }
  }, [response, error, dispatch]);

  // Initialize original region when component first loads
  useEffect(() => {
    if (filteredData.length > 0 && !originalRegion) {
      const firstPoint = filteredData[0];
      const region = {
        latitude: firstPoint.latitude,
        longitude: firstPoint.longitude,
        latitudeDelta: 0.002,
        longitudeDelta: 0.002,
      };
      setOriginalRegion(region);
    }
  }, [filteredData, originalRegion]);

  useEffect(() => {
    if (selectedMarker && !mapData.some(m => m.id === selectedMarker.id)) {
      setSelectedMarker(null);
    }
  }, [mapData, selectedMarker]);
  
  // Auto zoom to street view when component renders
  useEffect(() => {
    // Don't auto-zoom if we're currently viewing current location
    if (searchQuery.length === 0 && filteredData.length > 0 && !isAtCurrentLocation) {
      const timer = setTimeout(() => {
        if (mapRef.current) {
          const firstPoint = filteredData[0];
          console.log("Auto zooming to first point:", firstPoint.latitude, firstPoint.longitude);
          const region = {
            latitude: firstPoint.latitude,
            longitude: firstPoint.longitude,
            latitudeDelta: 0.002,
            longitudeDelta: 0.002,
          };

          mapRef.current.animateToRegion(region);
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [filteredData, searchQuery, isAtCurrentLocation]); // Added all dependencies

  // Debug: Track selectedMarker changes
  useEffect(() => {
  }, [selectedMarker]);

  useEffect(() => {
    // Only handle search when there's actually a search query
    if (searchQuery.length > 0) {
      if (filteredData.length > 0) {
        const matchedMarker = filteredData[0];
        mapRef.current?.animateToRegion({
          latitude: matchedMarker.latitude,
          longitude: matchedMarker.longitude,
          latitudeDelta: 0.002,
          longitudeDelta: 0.002,
        });
        setTimeout(() => {
          if (mapRef.current && mapRef.current.pointForCoordinate) {
            mapRef.current.pointForCoordinate({
              latitude: matchedMarker.latitude,
              longitude: matchedMarker.longitude,
            }).then(() => {
              setSelectedMarker(matchedMarker);
            });
          }
        }, 500);
      } else {
        // Only clear selectedMarker if search has no results
        setSelectedMarker(null);
      }
    }
    // Don't clear selectedMarker when search query is empty - let marker clicks work
  }, [searchQuery, filteredData]);

 

  const zoomToCurrentLocation = async () => {
    console.log('=== ZOOM TO CURRENT LOCATION ===');
    console.log('currentLocation:', currentLocation);
    console.log('mapRef.current:', !!mapRef.current);
setSelectedMarker(null);
    if (currentLocation && mapRef.current) {
      console.log('Using existing location, zooming...');
      setLocationLoading(true);
      setIsAtCurrentLocation(true); // Set the flag to prevent auto-zoom interference
      try {
        await mapRef.current.animateToRegion(
          {
            latitude: currentLocation.latitude,
            longitude: currentLocation.longitude,
            latitudeDelta: 0.005,
            longitudeDelta: 0.005,
          },
          1000
        );
        console.log('Successfully zoomed to current location');
      } catch (e) {
        console.warn('Error zooming to current location:', e);
      } finally {
        setLocationLoading(false);
      }
    } else {
      console.log('Getting new location...');
      setLocationLoading(true);
      let hasPermission = true;
      const getAndZoom = async () => {
        if (Platform.OS === 'android') {
          console.log('Requesting Android location permission...');
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
          );
          hasPermission = granted === PermissionsAndroid.RESULTS.GRANTED;
          console.log('Permission granted:', hasPermission);
        }
        if (hasPermission) {
          console.log('Getting current position...');
          Geolocation.getCurrentPosition(
            (position) => {
              const { latitude, longitude } = position.coords;
              console.log('Location received:', latitude, longitude);
              setCurrentLocation({ latitude, longitude });
              setIsAtCurrentLocation(true);
              if (mapRef.current) {
                mapRef.current.animateToRegion(
                  {
                    latitude,
                    longitude,
                    latitudeDelta: 0.005,
                    longitudeDelta: 0.005,
                  },
                  1000
                );
              }
              setLocationLoading(false);
            },
            (error) => {
              console.warn('Location error:', error.message);
              setIsAtCurrentLocation(false);
              setCurrentLocation(null);
              Alert.alert(
                'Location Error',
                'Unable to get current location. Please check your location settings and try again.'
              );
              setLocationLoading(false);
            },
            { enableHighAccuracy: true, timeout: 20000, maximumAge: 1000 }
          );
        } else {
          console.log('Permission denied');
          Alert.alert('Permission Denied', 'Location permission is required to show current location');
          setLocationLoading(false);
        }
      };
      getAndZoom();
    }
  };





  // Return to original state (first point location)
  const returnToOriginalState = () => {
    console.log("Returning to original state...");

    setIsAtCurrentLocation(false);
    setCurrentLocation(null);

    if (originalRegion && mapRef.current) {
      mapRef.current.animateToRegion(originalRegion);
    } else if (filteredData.length > 0 && mapRef.current) {
      // Fallback: use first data point if originalRegion is not set
      const firstPoint = filteredData[0];
      const fallbackRegion = {
        latitude: firstPoint.latitude,
        longitude: firstPoint.longitude,
        latitudeDelta: 0.002,
        longitudeDelta: 0.002,
      };
      mapRef.current.animateToRegion(fallbackRegion);
    }
  };

  const handleLeftNavPress = () => {
    console.log("handleLeftNavPress");
    if (isAtCurrentLocation) {
      returnToOriginalState();
    } else {
      zoomToCurrentLocation();
    }
  };

  const handleReject = async () => {
    if (!selectedMarker) return;
    // Find the original WBSItem data
    const item = pendingItems.find(i => i.id === selectedMarker.id);
    if (!item) return;
    // const payload = {
    //   jobCode: item.JOB || '',
    //   UID: item.UserID?.toString() || '',
    //   Type: 'ADeletion' as 'ADeletion',
    //   Notification_Desc: getFormattedApproverPath(item) || 'Progress Update',
    //   Quantity: item.Quantity || 0,
    //   uOM: item.UOM || '',
    //   manPower: item.Manpower || 0,
    //   ActualList: [
    //     {
    //       WBS: item.parent_WBS_Code || '',
    //       TaskCode: item.Task || '',
    //       ADate: item.TDate || '',
    //       Quantity: item.Quantity?.toString() || '0',
    //       Manpower: item.Manpower?.toString() || '0',
    //       Remarks: rejectionReason || 'Rejected',
    //       Tasktype: item.entity_Type || '',
    //       Is_Approved: 'N',
    //       Tag: 'Y',
    //       Latitude: item.Latitude || 0,
    //       Longitude: item.Longitude || 0,
    //     },
    //   ],
    //   Attachments: null,
    // };
    // dispatch(approveRequest(payload));
    // setTimeout(() => setRejectionModalVisible(false), 150);
    // setRejectionReason('');
    // setSelectedMarker(null);

    if (!item) return;
    const isInternetConnected = await isNetworkConnected();
    console.log('isInternetConnected: ', isInternetConnected);
    if (isInternetConnected) {
      setSearchQuery('');
      setLastApprovalAction('reject');
      const payload = handleApprovalAction(item, 'reject', getFormattedApproverPath, rejectionReason, []);
      dispatch(approveRequest(payload));
      // setTimeout(() => setActiveModal(null), 150);
      // setRejectionReason('');

      setTimeout(() => setRejectionModalVisible(false), 150);
      setRejectionReason('');

      setSelectedMarker(null);
    };
  };

  const handleApprove = (marker: MarkerType) => {
    // Find the original WBSItem data
    const item = pendingItems.find(i => i.id === marker.id);
    if (!item) return;
    // const payload = {
    //   jobCode: item.JOB || '',
    //   UID: item.UserID?.toString() || '',
    //   Type: 'approve' as 'approve',
    //   Notification_Desc: getFormattedApproverPath(item) || 'Progress Update',
    //   Quantity: item.Quantity || 0,
    //   uOM: item.UOM || '',
    //   manPower: item.Manpower || 0,
    //   ActualList: [
    //     {
    //       WBS: item.parent_WBS_Code || '',
    //       TaskCode: item.Task || '',
    //       ADate: item.TDate || '',
    //       Quantity: item.Quantity?.toString() || '0',
    //       Manpower: item.Manpower?.toString() || '0',
    //       Remarks: 'Approved',
    //       Tasktype: item.entity_Type || '',
    //       Is_Approved: 'N',
    //       Tag: 'Y',
    //       Latitude: item.Latitude || 0,
    //       Longitude: item.Longitude || 0,
    //     },
    //   ],
    //   Attachments: null,
    // };
    // dispatch(approveRequest(payload));
    // setSelectedMarker(null);
    (async () => {
      const isInternetConnected = await isNetworkConnected();
      console.log('isInternetConnected: ', isInternetConnected);
      if (isInternetConnected) {
        setSearchQuery('');
        setLastApprovalAction('approve');
        const payload = handleApprovalAction(item, 'approve', getFormattedApproverPath, undefined, []);
        dispatch(approveRequest(payload));
        setSelectedMarker(null);
      }
    })();
  };

  const handleDetailsPress = () => {
    if (selectedMarker) {
      // Find the original WBSItem data
      const originalItem = pendingItems.find(item => item.id === selectedMarker.id);
      if (originalItem) {
        (navigation as any).navigate('DailyProgressApproverUpdate', originalItem);
      }
    }
    setSelectedMarker(null);
  };

  const handleRightNavPress = () => {
    navigation.goBack();
  };

  const handleMapPress = () => {
    setSelectedMarker(null);
  };

  const handleRejectSubmit = () => {
    setTimeout(() => setRejectionModalVisible(false), 150);
    setRejectionReason('');
  };

  const handleRejectCancel = () => {
    setTimeout(() => setRejectionModalVisible(false), 150);
    setRejectionReason('');
  };

  const handleMarkerPress = (data: MarkerType) => {
    // Show the card for the selected marker
    setSelectedMarker(data);
    // Then animate to marker location with consistent zoom level
    if (mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: data.latitude,
        longitude: data.longitude,
        latitudeDelta: 0.002,
        longitudeDelta: 0.002,
      });
    }
  };

  // Calculate initial region based on data
  const getInitialRegion = () => {
    if (mapData.length === 0) {
      return {
        latitude: 28.6139,
        longitude: 77.209,
        latitudeDelta: 0.5,
        longitudeDelta: 0.5,
      };
    }

    const latitudes = mapData.map(item => item.latitude);
    const longitudes = mapData.map(item => item.longitude);

    const minLat = Math.min(...latitudes);
    const maxLat = Math.max(...latitudes);
    const minLng = Math.min(...longitudes);
    const maxLng = Math.max(...longitudes);

    return {
      latitude: (minLat + maxLat) / 2,
      longitude: (minLng + maxLng) / 2,
      latitudeDelta: Math.abs(maxLat - minLat) + 0.1,
      longitudeDelta: Math.abs(maxLng - minLng) + 0.1,
    };
  };

  console.log('Map data length:', mapData.length);
  console.log('Selected marker:', selectedMarker);

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title={Strings.DailyProgress.progressUpdate}
      />

      <View style={styles.mapContainer}>
        <MapView
          ref={mapRef}
          style={styles.map}
          initialRegion={getInitialRegion()}
          onPress={handleMapPress}
        >


          {mapData.map((data) => (
            <Marker
              key={data.id}
              coordinate={{
                latitude: data.latitude,
                longitude: data.longitude,
              }}
              onPress={(e) => {
                console.log('Marker onPress triggered for:', data.id);
                e?.stopPropagation?.(); // Prevent event bubbling
                handleMarkerPress(data);
              }}
              tracksViewChanges={false}
            >
              <Pointer width={40} height={40} />
            </Marker>
          ))}

          {/* Current Location Marker */}
          {currentLocation && (
            <Marker
              coordinate={{
                latitude: currentLocation.latitude,
                longitude: currentLocation.longitude,
              }}
              title="Current Location"
              description="Your current location"
              tracksViewChanges={false}
            >
              <View style={styles.currentLocationMarker}>
                <View style={styles.currentLocationDot} />
              </View>
            </Marker>
          )}
        </MapView>

        {/* Custom Tooltip/Card */}
        {selectedMarker && mapData.some(m => m.id === selectedMarker.id) && (
          <View
            style={[
              styles.customCardWrapper,
              {
                position: 'absolute',
                left: 30,
                right: 30,
                bottom: 100,
                zIndex: 100,
                alignSelf: 'center',
              },
            ]}
          >
            <View style={styles.triangle} />
            <View style={styles.customCard}>
              <Text style={styles.userName}>{selectedMarker.name}</Text>
              <Text style={styles.packageDetails}>
                {selectedMarker.details.split(/(\/)/g).map((part, idx) =>
                  part === '/' ? (
                    <Text key={idx} style={{ color: Colors.textInputBlack }}>/</Text>
                  ) : (
                    <Text key={idx} style={{ color: Colors.textPrimary }}>{part}</Text>
                  )
                )}
              </Text>
              <View style={styles.progressRow}>
                <View>
                  <Text style={styles.progressLabel}>Man Day</Text>
                  <Text style={styles.progressValue}>{selectedMarker.manDays}</Text>
                </View>
                <View>
                  <Text style={styles.progressLabel}>Progress Qty</Text>
                  <Text style={styles.progressValue}>{selectedMarker.progressQty}</Text>
                </View>
              </View>
              <View style={styles.cardButtonRow}>
                <TouchableOpacity
                  style={styles.rejectButton}
                  // onPress={handleReject}
                  onPress={() => setTimeout(() => setRejectionModalVisible(true), 150)}
                >
                  <Text style={styles.rejectButtonText}>Reject</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.approveButton}
                  onPress={() => handleApprove(selectedMarker)}
                >
                  <Text style={styles.approveButtonText}>Approve</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.detailsButton}
                  onPress={handleDetailsPress}
                >
                  <DirectionArrow width={16} height={16} />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}

        {/* Reject Modal */}
        {/* <Modal
          animationType="slide"
          transparent={true}
          visible={rejectionModalVisible}
          onRequestClose={handleRejectCancel}
        >
          <View style={styles.modalOverlay}> */}
        <BottomPopup visible={rejectionModalVisible} onCancelPress={() => { handleRejectCancel }}>

          <View style={styles.rejectModalCard}>
            <Text style={styles.modalTitle}>Reason for Rejection</Text>
            <View style={styles.titleUnderline} />
            <View style={styles.inputContainerStyle}>
              <Text style={styles.remarksLabel}>Remarks *</Text>
              <TextInput
                style={styles.remarksInput}
                multiline
                placeholder="Enter"
                placeholderTextColor={Colors.textInputBlack}
                value={rejectionReason}
                onChangeText={setRejectionReason}
              />
            </View>
            <View style={styles.shadowLine} />
            <View style={styles.buttonContainer}>
              <TouchableOpacity style={styles.cancelButton} onPress={handleRejectCancel}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                // style={[styles.modelRejectButton, rejectionReason.trim() === '' && styles.disabledButton]}
                style={styles.modelRejectButton}
                onPress={handleReject}
                disabled={rejectionReason.trim() === ''}
              >
                <Text style={styles.modelRejectButtonText}>Reject</Text>
              </TouchableOpacity>
            </View>
          </View>
        </BottomPopup>
        {/* </View>
        </Modal> */}

        <View style={styles.searchContainer}>
          <SearchComponent
            onChange={setSearchQuery}
            customStyle={styles.SearchComponentstyle}
            value={searchQuery}
            hintText='Search Location'
          />
        </View>

        <TouchableOpacity
          style={[styles.navIcon, styles.leftNav]}
          onPress={handleLeftNavPress}
        >
          <Gps width={25} height={25} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.rightNav]}
          onPress={handleRightNavPress}
        >
          <Circle width={100} height={100} />
        </TouchableOpacity>

        {(loading || locationLoading) && <ActivityIndicator style={styles.activityIndicatorStyle} size="large" color={Colors.primary}
          testID="activity-indicator" />}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dailyProgressBg,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
  },
  searchContainer: {
    position: 'absolute',
    top: 5,
    left: 20,
    right: 20,
    zIndex: 10,
  },
  triangle: {
    width: 16,
    height: 10,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderBottomWidth: 10,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: '#fff',
    marginBottom: -1,
    alignSelf: 'center',
    bottom: 50,
    right: 30,
  },
  userName: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.secondary,
    marginBottom: 4,
    textAlign: 'center',
    fontFamily: 'MNRegular',
  },
  packageDetails: {
    fontSize: ms(12),
    color: Colors.textPrimary,
    marginTop: 2,
    marginBottom: 8,
    textAlign: 'left',
    fontFamily: 'MNRegular',
    fontWeight: '500',
    lineHeight: 16,
  },
  progressRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 10,
  },
  progressLabel: {
    fontSize: ms(12),
    color: Colors.textInputBlack,
    textAlign: 'center',
    fontWeight: '500',
    fontFamily: 'MNRegular',
  },
  progressValue: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.textPrimary,
    marginTop: 4,
    textAlign: 'left',
    fontFamily: 'MNMedium',
  },
  cardButtonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rejectButton: {
    flex: 1,
    backgroundColor: Colors.white,
    borderWidth: 1,
    borderColor: Colors.offlineRed,
    marginRight: 8,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    alignItems: 'center',
  },
  rejectButtonText: {
    color: Colors.offlineRed,
    fontSize: 12,
    fontWeight: '700',
    fontFamily: 'MNMedium',
  },
  approveButton: {
    flex: 1,
    backgroundColor: Colors.onlineGreen,
    marginRight: 8,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    alignItems: 'center',
  },
  approveButtonText: {
    color: Colors.white,
    fontSize: 12,
    fontWeight: '700',
    fontFamily: 'MNMedium',
  },
  detailsButton: {
    padding: 6,
    borderWidth: 1,
    borderColor: Colors.secondary,
    borderRadius: 4,
  },
  navIcon: {
    position: 'absolute',
    top: '89%',
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 40,
    elevation: 5,
    shadowColor: '#000',
    shadowOpacity: 0.25,
    shadowRadius: 3,
    shadowOffset: { width: 0, height: 2 },
    zIndex: 15,
  },
  leftNav: {
    left: 20,
    transform: [{ translateY: -25 }],
  },
  rightNav: {
    position: 'absolute',
    top: '85%',
    right: 20,
    elevation: 5,
    shadowOffset: { width: 0, height: 2 },
    zIndex: 15,
    transform: [{ translateY: -25 }],
  },
  SearchComponentstyle: {
    backgroundColor: Colors.white,
    marginHorizontal: -5,
  },
  customCardWrapper: {
    alignItems: 'center',
    width: '100%',
  },
  customCard: {
    width: '100%',
    maxWidth: 280,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    position: 'relative',
    bottom: 50,
    right: 30,
  },
  rejectModalCard: {
    backgroundColor: '#fff',
    paddingVertical: 20,
    alignItems: 'center',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalTitle: {
    fontSize: 17,
    fontWeight: '600',
    marginTop: 25,
    marginBottom: 20,
    marginHorizontal: 20,
    alignSelf: 'flex-start',
    fontFamily: 'MNBold',
  },
  inputContainerStyle: {
    width: '100%',
    marginBottom: 20,

  },
  remarksLabel: {
    fontSize: 14,
    marginBottom: 5,
    color: '#697DA5',
    fontWeight: '500',
    fontFamily: 'MNMedium',
    marginHorizontal: 20,
  },
  remarksInput: {
    borderWidth: 1,
    marginHorizontal: 20,
    borderColor: Colors.grey,
    borderRadius: 5,
    padding: 10,
    minHeight: 80,
    textAlignVertical: 'top',
    fontSize: 14,
    backgroundColor: Colors.containerligetBlue,
    color: Colors.textPrimary,
  },

  shadowLine: {
    height: 1,
    backgroundColor: '#ddd',
    width: '100%',
    marginVertical: 20,
    shadowColor: Colors.dailyProgressBg,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 5,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '94%',

  },
  cancelButton: {
    backgroundColor: '#fff',
    borderWidth: 1.5,
    borderColor: Colors.blue,
    borderRadius: 5,
    paddingVertical: 12,
    width: '45%',
    alignItems: 'center',

  },
  cancelButtonText: {
    color: Colors.blue,
    fontSize: 16,
    fontWeight: '700',
    fontFamily: 'MNBold',
  },
  modelRejectButton: {
    backgroundColor: Colors.offlineRed,
    borderColor: Colors.offlineRed,
    borderWidth: 1.5,
    borderRadius: 5,
    paddingVertical: 12,
    width: '45%',

    alignItems: 'center',
  },
  modelRejectButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
    fontFamily: 'MNBold',
  },
  disabledButton: {
    opacity: 0.5,
  },
  activityIndicatorStyle: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  titleUnderline: {
    height: 1,
    backgroundColor: Colors.grey,
    width: '90%',
    marginStart: 20,
    marginEnd: 20,
    marginBottom: 20,
  },
  currentLocationMarker: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(0, 122, 255, 0.3)',
    borderWidth: 2,
    borderColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  currentLocationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#007AFF',
  },
});

export default DailyProgressApproverMapView; 