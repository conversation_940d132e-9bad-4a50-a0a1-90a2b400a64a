import { Attachment, WBSItem } from '../../../model/DailyProgress/DailyProgressData';

// export interface AttachmentData {
//   WBS: string;
//   TaskCode: string;
//   ADate: string;
//   Tasktype: string;
//   SiteUrl: string;
//   Unique: string;
// }

export interface ApprovalPayload {
  jobCode: string;
  UID: string;
  Type: 'approve' | 'ADeletion';
  Notification_Desc: string;
  Quantity: number;
  uOM: string;
  manPower: number;
  ActualList: Array<{
    WBS: string;
    TaskCode: string;
    ADate: string;
    Quantity: number;
    Manpower: number;
    Remarks: string;
    Tasktype: string;
    Is_Approved: string;
    Tag: string;
    Latitude: number;
    Longitude: number;
  }>;
  Attachments: Attachment[];
}

export const handleApprovalAction = (
  item: WBSItem,
  actionType: 'approve' | 'reject',
  getFormattedApproverPath: (item: WBSItem) => string,
  rejectionReason?: string,
  attachments?: Array<{ SiteUrl: string; UniqueID: string }>
): ApprovalPayload => {
  const payload: ApprovalPayload = {
    jobCode: item.JOB || '',
    UID: item.UserID?.toString() || '',
    Type: actionType === 'approve' ? 'approve' : 'ADeletion',
    Notification_Desc: getFormattedApproverPath(item) || 'Progress Update',
    Quantity: parseFloat(item.Quantity?.toFixed(1) ?? '0'),
    uOM: item.UOM || '',
    manPower: parseFloat(item.Manpower?.toFixed(1) ?? '0'),
    ActualList: [
      {
        WBS: item.parent_WBS_Code || '',
        TaskCode: item.Task || '',
        ADate: item.TDate || '',
        // Quantity: parseFloat(item.Quantity?.toFixed(1)  || 0), 
        Quantity: parseFloat(item.Quantity?.toFixed(1) ?? '0'),
        Manpower: parseFloat(item.Manpower?.toString() || '0'),
        Remarks: item.Remarks ? item.Remarks : actionType === 'approve' ? 'Approved' : (rejectionReason || 'Rejected'),
        Tasktype: item.entity_Type || '',
        Is_Approved: actionType === 'approve' ? 'Y' : 'N',
        Tag: 'Y',
        Latitude: item.Latitude || 0,
        Longitude: item.Longitude || 0,
      },
    ],
    Attachments: attachments ? attachments.map((resp) => ({
      WBS: item.parent_WBS_Code || '',
      TaskCode: item.Task || '',
      ADate: item.TDate || '',
      Tasktype: item.entity_Type || '',
      SiteUrl: resp.SiteUrl,
      Unique: resp.UniqueID
    })) : [],
  };

  return payload;
}; 

    //   jobCode: passedData?.JOB || 'LE21M114',
    //   UID: passedData?.UserID?.toString() || '1159553',
    //   Type: 'approve' as const,
    //   Notification_Desc: getFormattedApproverPath(passedData as WBSItem) || 'Progress Update',
    //   Quantity: passedData?.Quantity || 0,
    //   uOM: passedData?.UOM || 'm³',
    //   manPower: passedData?.Manpower || 0,
    //   ActualList: [
    //     {
    //       WBS: passedData?.parent_WBS_Code || 'WP100761~22597',
    //       TaskCode: passedData?.Task || '1000',
    //       ADate: passedData?.TDate || moment().format('YYYY-MM-DD'),
    //       Quantity: passedData?.Quantity?.toString() || '0',
    //       Manpower: passedData?.Manpower?.toString() || '0',
    //       Remarks: remarks || 'Approved',
    //       Tasktype: passedData?.entity_Type || 'BQ',
    //       Is_Approved: 'N',
    //       Tag: 'Y',
    //       Latitude: passedData?.Latitude || 9.555293,
    //       Longitude: passedData?.Longitude || 78.5882628,
    //     },
    //   ],
    //   Attachments: uploadedUniqueIds || null,
    // };