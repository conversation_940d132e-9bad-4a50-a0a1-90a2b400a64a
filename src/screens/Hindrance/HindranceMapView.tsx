import { StyleSheet, Dimensions, SafeAreaView, View, Text, Alert } from 'react-native'
import React, { useRef, useState, useEffect, useCallback } from 'react'
import Colors from '../../utils/Colors/Colors';
import { ms } from '../../utils/Scale/Scaling';
import AppHeader from '../../components/AppHeader';
import Strings from '../../utils/Strings/Strings';
import MapViewComponent from './HindranceComponents/MapView';
import ClipboardText from '../../assets/svg/clipboard-text.svg';
import BottomCalendarModal from '../../components/CalendarPicker/BottomPopupCalendar';
import { useNavigation } from '@react-navigation/native';
import MapView from 'react-native-maps';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { getHindranceDetails } from '../../redux/HindranceRedux/HindranceMapActions';
import { RootState } from '../../redux/Root/RootReducer';
import LoadingOverlay from '../../components/LoadingOverlay';
import PrintLog from '../../utils/Logger/PrintLog';
import { hindranceData } from '../../utils/helpers/temp';
import { getSelectedJobs } from '../../utils/Storage/Storage';

const HindranceMapView = () => {
    const navigation = useNavigation();
    const dispatch = useDispatch();
    const { t } = useTranslation();
    
    // Local state
    const [isCalendarVisible, setIsCalendarVisible] = useState(false);
    const [selectedFromDate, setSelectedFromDate] = useState('');
    const [selectedToDate, setSelectedToDate] = useState('');
    const [isRefreshing, setIsRefreshing] = useState(false);
    
    const mapRef = useRef<MapView | null>(null);
    const isInitialized = useRef(false);

    // Redux state with separate selectors to prevent unnecessary re-renders
    const hindranceMapItems = useSelector((state: RootState) => 
        state?.hindranceMap?.hindranceDetails || []
    );
    const isLoading = useSelector((state: RootState) => 
        state?.hindranceMap?.isLoading || false
    );
    const error = useSelector((state: RootState) => 
        state?.hindranceMap?.error || null
    );

    // Debug logging - only in development and with proper dependencies
    useEffect(() => {
        if (__DEV__) {
            PrintLog.debug('HindranceMapView', 'Redux state:', {
                itemsCount: hindranceMapItems?.length || 0,
                isLoading,
                error
            });
        }
    }, [hindranceMapItems?.length, isLoading, error]);

    /**
     * Handles calendar press to show date picker
     */
    const handleCalendarPress = () => {
        setIsCalendarVisible(true);
    };

    /**
     * Handles calendar close
     */
    const handleCalendarClose = () => {
        setIsCalendarVisible(false);
    };

    /**
     * Handles date selection and applies filter
     */
    const handleDateApply = (fromDate: string, toDate: string) => {
        setSelectedFromDate(fromDate);
        setSelectedToDate(toDate);
        setIsCalendarVisible(false);
        
        // Navigate to gap report screen
        (navigation as any).navigate('GapReportScreen', { fromDate, toDate });
    };

    /**
     * Handles pull-to-refresh functionality
     */
    const handleRefresh = useCallback(() => {
        // Refresh functionality can be implemented here if needed
        setIsRefreshing(false);
    }, []);

    /**
     * Initial data fetch on component mount
     */
    useEffect(() => {
        if (isInitialized.current) return;
        
        isInitialized.current = true;
        
        // Initialize with default parameters
        const initialParams = {
            type: 'GISVIEW',
            jobCode: getSelectedJobs()[0]?.id,
        };
        
        dispatch(getHindranceDetails(initialParams));
    }, []); // Empty dependency array to run only once

    /**
     * Handle error state
     */
    useEffect(() => {
        if (error) {
            Alert.alert(
                'Error',
                error,
                [{ text: 'OK' }]
            );
        }
    }, [error]);

    /**
     * Handle successful data fetch
     */
    useEffect(() => {
        if (hindranceMapItems.length > 0) {
            PrintLog.debug('HindranceMapView', 'Hindrance data loaded successfully', {
                count: hindranceMapItems.length
            });
        }
    }, [hindranceMapItems.length]);

    return (
        <SafeAreaView style={styles.container}>
            <AppHeader
                title={t('hindranceStrings.hindrance')}
                rightContent={
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <ClipboardText width={ms(22)} height={ms(22)} />
                    <Text style={{ color: Colors.textPrimary, fontWeight: '600', fontSize: ms(14), marginLeft: ms(6)}}>{Strings.Hindrance.gapReport}</Text>
                  </View>
                }
                onBookmarkPress={handleCalendarPress}
            />
            
            {/* Show loading overlay during initial load */}
            {isLoading && !isRefreshing && (
                <LoadingOverlay visible={true} />
            )}
            
            {/* Map component with dynamic data */}
            <MapViewComponent 
                // hindranceData={!hindranceMapItems? hindranceData : hindranceMapItems}
                hindranceData={hindranceData}
                onRefresh={handleRefresh}
                isRefreshing={isRefreshing}
            />
            
            <BottomCalendarModal
                visible={isCalendarVisible}
                onClose={handleCalendarClose}
                selectedFromDate={selectedFromDate}
                seelctedToDate={selectedToDate}
                fromDateTitle="From Date"
                toDateTitle="To Date"
                showFromDate={true}
                showToDate={true}
                showBottomButton={true}
                onApply={handleDateApply}
            />
        </SafeAreaView>
    )
}

export default HindranceMapView;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.dailyProgressBg,
    },
    emptyStateContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: ms(20),
    },
    emptyStateText: {
        fontSize: ms(18),
        fontWeight: '600',
        color: Colors.textPrimary,
        textAlign: 'center',
        marginBottom: ms(8),
    },
    emptyStateSubtext: {
        fontSize: ms(14),
        color: Colors.textSecondary,
        textAlign: 'center',
    },
    mapContainer: {
        flex: 1,
        position: 'relative',
    },
    map: {
        width: Dimensions.get('window').width,
        height: Dimensions.get('window').height,
    },
    searchContainer: {
        position: 'absolute',
        top: 10,
        left: 20,
        right: 20,
        zIndex: 10,
    },
    triangle: {
        width: 24,
        height: 12,
        backgroundColor: 'transparent',
        borderStyle: 'solid',
        borderLeftWidth: 12,
        borderRightWidth: 12,
        borderBottomWidth: 12,
        borderLeftColor: 'transparent',
        borderRightColor: 'transparent',
        borderBottomColor: '#fff',
        marginBottom: -1,
    },
    userName: {
        fontSize: 16,
        fontWeight: '700',
        color: Colors.textPrimary,
        marginBottom: 4,
    },
    userDetails: {
        fontSize: 14,
        color: Colors.textSecondary,
        marginBottom: 8,
    },
    progressContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    progressItem: {
        alignItems: 'center',
    },
    progressLabel: {
        fontSize: 12,
        color: Colors.textSecondary,
        marginBottom: 2,
    },
    progressValue: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.textPrimary,
    },
    dateContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginTop: 8,
    },
    dateText: {
        fontSize: 12,
        color: Colors.textSecondary,
    },
    statusContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    statusDot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        marginRight: 4,
    },
    statusText: {
        fontSize: 12,
        color: Colors.textSecondary,
    },
    activeStatus: {
        backgroundColor: Colors.onlineGreen,
    },
    inactiveStatus: {
        backgroundColor: Colors.offlineRed,
    },
    newGapStatus: {
        backgroundColor: Colors.blue,
    },
    progressUpdateStatus: {
        backgroundColor: Colors.forgotPinBlue,
    },
    cardButtonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    rejectButton: {
        flex: 1,
        backgroundColor: Colors.white,
        borderWidth: 1,
        borderColor: Colors.bgLightRed,
        marginRight: 10,
        paddingVertical: 8,
        paddingHorizontal: 15,
        borderRadius: 5,
        alignItems: 'center',
    },
    rejectButtonText: {
        color: Colors.bgLightRed,
        fontSize: 14,
        fontWeight: '700',
        fontFamily: 'MNMedium',
    },
    approveButton: {
        flex: 1,
        backgroundColor: Colors.onlineGreen,
        marginRight: 10,
        paddingVertical: 8,
        paddingHorizontal: 15,
        borderRadius: 5,
        alignItems: 'center',
    },
    approveButtonText: {
        color: Colors.updateTextLightBlue,
        fontSize: 14,
        fontWeight: '700',
        fontFamily: 'MNMedium',
    },
    detailsButton: {
        padding: 8,
        borderWidth: 1,
        borderColor: '#007BFF',
        borderRadius: 5,
    },
    navIcon: {
        position: 'absolute',
        top: '90%',
        backgroundColor: '#fff',
        padding: 8,
        borderRadius: 25,
        elevation: 5,
        shadowColor: '#000',
        shadowOpacity: 0.25,
        shadowRadius: 3,
        shadowOffset: { width: 0, height: 2 },
        zIndex: 15,
    },
    leftNav: {
        left: 20,
        transform: [{ translateY: -25 }],
    },
    rightNav: {
        right: 20,
        transform: [{ translateY: -25 }],
    },
    SearchComponentstyle: {
        backgroundColor: Colors.white,
        marginHorizontal: 0,
    },
    customCardWrapper: {
        alignItems: 'center',
        width: 300,
    },
    customCard: {
        width: 300,
        backgroundColor: '#fff',
        borderRadius: 10,
        padding: 15,
        elevation: 5,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    rejectModalCard: {
        backgroundColor: '#fff',
        borderRadius: 10,
        padding: 20,
        margin: 20,
        alignItems: 'center',
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 20,
        alignSelf: 'flex-start',
    },
    inputContainerStyle: {
        width: '100%',
        marginBottom: 20,
    },
    remarksLabel: {
        fontSize: 14,
        marginBottom: 5,
        color: '#333',
    },
    remarksInput: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        padding: 10,
        minHeight: 80,
        textAlignVertical: 'top',
        fontSize: 14,
        backgroundColor: '#f9f9f9',
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        width: '100%',
    },
    cancelButton: {
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#007BFF',
        borderRadius: 5,
        paddingVertical: 12,
        minWidth: 100,
        alignItems: 'center',
        marginRight: 10,
    },
    cancelButtonText: {
        color: '#007BFF',
        fontSize: 16,
    },
    modelRejectButton: {
        backgroundColor: '#E74C3C',
        borderRadius: 5,
        paddingVertical: 12,
        minWidth: 100,
        alignItems: 'center',
    },
    modelRejectButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: 'bold',
    },
    disabledButton: {
        opacity: 0.5,
    },
    debugContainer: {
        position: 'absolute',
        top: 10,
        left: 10,
        backgroundColor: 'rgba(255,255,255,0.8)',
        padding: 10,
        borderRadius: 5,
        zIndex: 100,
    },
    debugText: {
        fontSize: 12,
        color: '#333',
    },
});