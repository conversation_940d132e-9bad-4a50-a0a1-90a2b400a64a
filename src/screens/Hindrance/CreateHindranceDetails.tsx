import { View, StyleSheet, SafeAreaView, Text, Pressable, Modal, ScrollView } from 'react-native'
import React, { useCallback, useState, useEffect } from 'react'
import AppHeader from '../../components/AppHeader'
import { ms } from '../../utils/Scale/Scaling'
import Colors from '../../utils/Colors/Colors'
import Strings from '../../utils/Strings/Strings'
import Upload from '../../assets/svg/upload.svg';
import BottomPopupImageUpload from '../../components/BottomPopupImageUpload'
import PrintLog from '../../utils/Logger/PrintLog'
import ButtonComponent from '../../components/ButtonComponent'
import CalenderIcon from '../../assets/svg/calendar.svg';
import Icon from 'react-native-vector-icons/Ionicons';
import { useRoute, useNavigation, NavigationProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import HindranceBottomPopup from './HindranceComponents/HindranceBottomPopup';
import DailyProgressTextInput from '../../screens/DailyProgress/components/DailyProgressTextInput';
import { t } from "i18next";
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '../../redux/Root/rootStore'
import { getSelectedJobs } from '../../utils/Storage/Storage'
import AttachmentComponent from '../../components/Attachment'
import { setHindranceMapData } from '../../redux/HindranceRedux/HindranceMapActions'
// import DailyProgressTextInput from '../DailyProgress/components/DailyProgressTextInput'

type RootStackParamList = {
    HindranceMapView: undefined;
};

interface RouteParams {
    isSinglePointer: boolean;
    selectedLocation: {
        latitude: number;
        longitude: number;
        address: string;
        endLatitude?: number;
        endLongitude?: number;
        endAddress?: string;
    } | null;
}

const materialDiaDropDownData = [
    {
        "Categogy": "Specification",
        "CategogyId": 1003,
        "Classification_Type_Detail_Description": "DI 100",
        "Classification_Type_Detail_Code": "SPCN1043"
    },
    {
        "Categogy": "Specification",
        "CategogyId": 1003,
        "Classification_Type_Detail_Description": "DI 150",
        "Classification_Type_Detail_Code": "SPCN1044"
    },
    {
        "Categogy": "Specification",
        "CategogyId": 1003,
        "Classification_Type_Detail_Description": "DI 200",
        "Classification_Type_Detail_Code": "SPCN1045"
    },
    {
        "Categogy": "Specification",
        "CategogyId": 1003,
        "Classification_Type_Detail_Description": "DI 250",
        "Classification_Type_Detail_Code": "SPCN1046"
    },
    {
        "Categogy": "Specification",
        "CategogyId": 1003,
        "Classification_Type_Detail_Description": "HDPE 110",
        "Classification_Type_Detail_Code": "SPCN1062"
    },
    {
        "Categogy": "Specification",
        "CategogyId": 1003,
        "Classification_Type_Detail_Description": "HDPE 140",
        "Classification_Type_Detail_Code": "SPCN1064"
    },
    {
        "Categogy": "Specification",
        "CategogyId": 1003,
        "Classification_Type_Detail_Description": "HDPE 160",
        "Classification_Type_Detail_Code": "SPCN1065"
    },
    {
        "Categogy": "Specification",
        "CategogyId": 1003,
        "Classification_Type_Detail_Description": "HDPE 200",
        "Classification_Type_Detail_Code": "SPCN1067"
    },
    {
        "Categogy": "Specification",
        "CategogyId": 1003,
        "Classification_Type_Detail_Description": "HDPE 63",
        "Classification_Type_Detail_Code": "SPCN1059"
    },
    {
        "Categogy": "Specification",
        "CategogyId": 1003,
        "Classification_Type_Detail_Description": "HDPE 75",
        "Classification_Type_Detail_Code": "SPCN1060"
    },
    {
        "Categogy": "Specification",
        "CategogyId": 1003,
        "Classification_Type_Detail_Description": "HDPE 90",
        "Classification_Type_Detail_Code": "SPCN1061"
    }
];

const gapReasonData = [
    {
        "GapReason_ETD_Code": "GMGR0001",
        "GapReason_Description": "High Water accumulation /Low level / Seepage area (Water Logging)"
    },
    {
        "GapReason_ETD_Code": "GMGR0002",
        "GapReason_Description": "Non-availability of Pipe Specials/ Fittings/ Pipe"
    },
    {
        "GapReason_ETD_Code": "GMGR0003",
        "GapReason_Description": "Others (with an option to specify if \"Others\" is selected)"
    },
    {
        "GapReason_ETD_Code": "GMGR0004",
        "GapReason_Description": "Pipeline Network Damage"
    },
    {
        "GapReason_ETD_Code": "GMGR0005",
        "GapReason_Description": "Road/ River/Railway Canal/ Culvert Crossing"
    },
    {
        "GapReason_ETD_Code": "GMGR0006",
        "GapReason_Description": "ROW/ ROU issue"
    }
];

const CreateHindranceDetails = () => {
    const route = useRoute();
    const dispatch = useDispatch();
    const { isSinglePointer, selectedLocation } = route.params as RouteParams;
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const { user } = useSelector((state: RootState) => state.auth);
    const [gapLength, setGapLength] = useState<number>(0);
    const [date, setDate] = useState<string>(new Date().toISOString().split('T')[0]);
    const [remarks, setRemarks] = useState<string>('');
    const [node, setNode] = useState<string>('');

    useEffect(() => {
        navigation.addListener('beforeRemove', (e) => {
            if (e.data.action.type === 'GO_BACK') {
                e.preventDefault();
                navigation.replace('HindranceMapView');
            }
        });
    }, [navigation]);

    const [imgUploadModalVisible, setImgUploadModalVisible] = useState(false);
    const [hindranceModalVisible, setHindranceModalVisible] = useState(false);
    const [fromDate, setFromDate] = useState<string | null>(null);
    const [toDate, setToDate] = useState<string | null>(null);
    const [imageIds, setImageIds] = useState<string[]>([]);
    const [dropdownType, setDropdownType] = useState<string>('');
    const [materialDia, setMaterialDia] = useState<string>('');
    const [gapReason, setGapReason] = useState<string>('');
    const [latitude, setLatitude] = useState<number>(selectedLocation?.latitude ?? 0);
    const [longitude, setLongitude] = useState<number>(selectedLocation?.longitude ?? 0);
    const [endLatitude, setEndLatitude] = useState<number>(selectedLocation?.endLatitude ?? 0);
    const [endLongitude, setEndLongitude] = useState<number>(selectedLocation?.endLongitude ?? 0);

    const handleDatePicker = useCallback(() => {
        // Implement date picker logic
    }, []);

    const handleUploadImage = useCallback(() => {
        setImgUploadModalVisible(true)
    }, []);

    const handleMaterialDiaPress = () => {
        setDropdownType('materialDia');
        setHindranceModalVisible(true);
    };

    const handleGapReasonPress = () => {
        setDropdownType('gapReason');
        setHindranceModalVisible(true);
    };

    return (
        <SafeAreaView style={styles.container}>
            <AppHeader
                title={t('hindranceStrings.hindrance')}
                onBackPress={() => navigation.goBack()}
            />
            <ScrollView
                style={styles.scrollView}
                contentContainerStyle={styles.scrollContent}
                showsVerticalScrollIndicator={false}
            >
                <View style={styles.content}>
                    <View style={styles.inputContainer}>
                        <Text style={styles.inputLabel}>Date *</Text>
                        <Pressable style={styles.dateInput} onPress={handleDatePicker}>
                            <Text style={styles.dateText}>{new Date().toISOString().split('T')[0]}</Text>
                            <CalenderIcon />
                        </Pressable>
                    </View>
                    <View style={styles.inputContainer}>
                        <Text style={styles.inputLabel}>Pipe Material & Dia *</Text>
                        <Pressable style={styles.dateInput} onPress={handleMaterialDiaPress}>
                            <Text style={[styles.dateText, !materialDia && styles.placeholderText]}>
                                {materialDia || t('hindranceStrings.select')}
                            </Text>
                            <Icon name="chevron-down-outline" size={ms(20)} color={Colors.black} />
                        </Pressable>
                    </View>
                    <View style={styles.inputContainer}>
                        <Text style={styles.inputLabel}>Reason for Gap *</Text>
                        <Pressable style={styles.dateInput} onPress={handleGapReasonPress}>
                            <Text style={[styles.dateText, !gapReason && styles.placeholderText]}>
                                {gapReason || t('hindranceStrings.select')}
                            </Text>
                            <Icon name="chevron-down-outline" size={ms(20)} color={Colors.black} />
                        </Pressable>
                    </View>
                    <DailyProgressTextInput
                        label="Gap Length *"
                        value={gapLength.toString()}
                        onChangeText={text => setGapLength(Number(text))}
                        isKeypadNumeric={true} />

                    {
                        isSinglePointer ? (
                            <>
                                <DailyProgressTextInput
                                    label="Latitude *"
                                    value={latitude.toString()}
                                    onChangeText={text => setLatitude(Number(text))}
                                    isKeypadNumeric={true}
                                />
                                <DailyProgressTextInput
                                    label="Longitude *"
                                    value={longitude.toString()}
                                    onChangeText={text => setLongitude(Number(text))}
                                    isKeypadNumeric={true}
                                />
                            </>
                        ) : (
                            <>
                                <DailyProgressTextInput
                                    label="Start Latitude *"
                                    value={latitude.toString()}
                                    onChangeText={text => setLatitude(Number(text))}
                                    isKeypadNumeric={true}
                                />
                                <DailyProgressTextInput
                                    label="Start Longitude *"
                                    value={longitude.toString()}
                                    onChangeText={text => setLongitude(Number(text))}
                                    isKeypadNumeric={true}
                                />
                                <DailyProgressTextInput
                                    label="End Latitude *"
                                    value={endLatitude.toString()}
                                    onChangeText={text => setEndLatitude(Number(text))}
                                    isKeypadNumeric={true}
                                />
                                <DailyProgressTextInput
                                    label="End Longitude *"
                                    value={endLongitude.toString()}
                                    onChangeText={text => setEndLongitude(Number(text))}
                                    isKeypadNumeric={true}
                                />
                            </>
                        )
                    }
                    <View style={styles.inputContainer}>
                        <Text style={styles.inputLabel}>Map View *</Text>
                        {/* Map preview UI */}
                    </View>
                    <DailyProgressTextInput
                        label="Start & End Node"
                        value={node}
                        onChangeText={setNode}
                        isKeypadNumeric={true} />
                    <DailyProgressTextInput
                        customStyle={[styles.textInput, styles.remarksInput]}
                        label="Remarks *"
                        value={remarks}
                        onChangeText={setRemarks}
                        isMultiline={true} />
                        <AttachmentComponent onUploadComplete={(response) => {
                            setImageIds(prev => [...response.map(item => item.UniqueID)]);
                            // setImageIds(prev => {
                            //     const prevIds = typeof prev === 'string' ? prev.split(' ~ ').filter(Boolean) : [];
                            //     const newIds = response.map(item => item.UniqueID).filter(Boolean);
                            //     return [...prevIds, ...newIds].join(' ~ ');
                            // });
                        }} />
                </View>
            </ScrollView>

                <ButtonComponent
                    title={t('hindranceStrings.create')}
                    onPress={() => { 
                        const jobs = getSelectedJobs();
                        if(jobs.length < 0) return;
                        const updateParams = {
                            jobCode: jobs[0]?.id,
                            UID: user?.UID,
                            GapLength: gapLength,
                            Latitude_Map: selectedLocation?.latitude,
                            Longitude_Map: selectedLocation?.longitude,
                            GisPhoto: imageIds,
                            date: date,
                            Open_Active: 'N',
                            Remarks: remarks,
                            type: 'GISLENGTHINSERT',
                            Classification_Type_Detail_Code: '',
                            Latitude_End: selectedLocation?.endLatitude,
                            Longitude_End: selectedLocation?.endLongitude,
                            Start_End_Node: node,
                            Gap_Reason: gapReason,
                        };
                        dispatch(setHindranceMapData(updateParams));
                    }}
                />

            <Modal
                animationType="none"
                transparent
                visible={imgUploadModalVisible}
                onRequestClose={() => setImgUploadModalVisible(false)}
            >
                <Pressable
                    style={styles.modalOverlay}
                    onPress={() => setImgUploadModalVisible(false)}
                >
                    <BottomPopupImageUpload
                        onCameraPress={() => {
                            PrintLog.debug('BottomPopupImageUpload', 'Camera Pressed');
                        }}
                        onGalleryPress={() => {
                            PrintLog.debug('BottomPopupImageUpload', 'Gallery Pressed');
                        }}
                    />
                </Pressable>
            </Modal>
            {hindranceModalVisible &&
                <HindranceBottomPopup
                    visible={hindranceModalVisible}
                    title={dropdownType === 'materialDia' ? t('hindranceStrings.pipeMaterialDia') : t('hindranceStrings.reasonForGap')}
                    type={dropdownType}
                    data={dropdownType === 'materialDia' ? materialDiaDropDownData : gapReasonData}
                    onClose={() => setHindranceModalVisible(false)}
                    onSelect={(selected) => {
                        dropdownType === 'materialDia' ? setMaterialDia(selected) : setGapReason(selected);;
                        setHindranceModalVisible(false);
                    }}
                    selectedItem={dropdownType === 'materialDia' ? materialDia : gapReason}
                />
            }

        </SafeAreaView>
    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.white,
    },
    scrollView: {
        flex: 1,
    },
    scrollContent: {
        flexGrow: 1,
    },
    content: {
        padding: ms(16),
        paddingBottom: ms(100),
    },
    textInput: {
        backgroundColor: Colors.containerligetBlue,
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
        padding: ms(12),
        borderRadius: ms(8),
        color: Colors.primary,
        fontSize: ms(14),
    },
    dateInput: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.containerligetBlue,
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
        paddingVertical: ms(8),
        paddingHorizontal: ms(12),
        borderRadius: ms(8),
    },
    remarksInput: {
        height: ms(100),
        textAlignVertical: 'top',
    },
    inputContainer: {
        marginVertical: ms(3),
    },
    inputLabel: {
        fontSize: ms(14),
        marginBottom: ms(3),
        fontWeight: '400',
        color: Colors.pipeIdTextBlack,
    },
    uploadButtonOuter: {
        padding: ms(8),
        borderRadius: ms(5),
        borderWidth: ms(1),
        borderColor: Colors.searchBorderGrey,
    },
    uploadButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: ms(8),
        borderRadius: ms(5),
        borderWidth: ms(1),
        borderColor: Colors.secondary,
    },
    uploadButtonText: {
        color: Colors.secondary,
        marginLeft: ms(8),
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: 'rgba(0,0,0,0.3)',
    },
    width: {
        width: '90%',
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center',
        margin: ms(20),
    },
    downloadButtonContainer: {
        backgroundColor: Colors.white,
        alignItems: 'center',
        paddingVertical: ms(20),
        elevation: 10,
        borderTopWidth: 1,
        borderTopColor: Colors.grey,
    },
    dateText: {
        fontSize: ms(14),
        color: Colors.primary,
        width: '90%'
    },
    placeholderText: {
        color: Colors.primary,
    },
})

export default CreateHindranceDetails