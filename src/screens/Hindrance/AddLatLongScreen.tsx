// import AddLatLongScreen from '../Hindrance/AddLatLongScreen';

import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, SafeAreaView, ScrollView } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useDispatch } from 'react-redux';
import AppHeader from '../../components/AppHeader';
import { ms } from '../../utils/Scale/Scaling';
import Colors from '../../utils/Colors/Colors';
import ButtonComponent from '../../components/ButtonComponent';
import MapView, { Marker, Polyline } from 'react-native-maps';
import { setHindranceMapData } from '../../redux/HindranceRedux/HindranceMapActions';
import Mapicon1 from '../../assets/svg/Mapicon1.svg';
import Mapicon2 from '../../assets/svg/Mapicon2.svg';
import OpenGap from '../../assets/svg/OpenGap.svg';
import ClosedGap from '../../assets/svg/ClosedGap.svg';
import NewGapSinglePointer from '../../assets/svg/NewGapSinglePointer.svg';
import NewGapMultiPointer from '../../assets/svg/NewGapMultiPointer.svg';
import HandPointer from '../../assets/svg/handicon.svg';

type RootStackParamList = {
    HindranceUpdate: undefined;
    AddLatLong: {
        selectedItem: any;
        startLat: number;
        startLng: number;
        endLat: number;
        endLng: number;
        gapType: string;
        progressLat?: number;
        progressLng?: number;
    };
};

function getPointOnLine(
  start: { latitude: number; longitude: number },
  end: { latitude: number; longitude: number },
  t: number
) {
  return {
    latitude: start.latitude + (end.latitude - start.latitude) * t,
    longitude: start.longitude + (end.longitude - start.longitude) * t,
  };
}

function calculateTFromProgress(
  start: { latitude: number; longitude: number },
  end: { latitude: number; longitude: number },
  progressLat: number,
  progressLng: number
) {
  const dx = end.latitude - start.latitude;
  const dy = end.longitude - start.longitude;
  const lengthSquared = dx * dx + dy * dy;
  
  if (lengthSquared === 0) return 0;
  
  let t = ((progressLat - start.latitude) * dx + (progressLng - start.longitude) * dy) / lengthSquared;
  return Math.max(0, Math.min(1, t));
}

const AddLatLongScreen = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const route = useRoute();
  const dispatch = useDispatch();
  
  // Fallback/default values if params are missing
  const {
    selectedItem,
    startLat = 13.056823,
    startLng = 80.256823,
    endLat = 13.058000,
    endLng = 80.258000,
    gapType = "Open Gap",
    progressLat,
    progressLng,
  } = (route.params || {}) as {
    selectedItem?: any;
    startLat?: number;
    startLng?: number;
    endLat?: number;
    endLng?: number;
    gapType?: string;
    progressLat?: number;
    progressLng?: number;
  };

  const start = { latitude: startLat, longitude: startLng };
  const end = { latitude: endLat, longitude: endLng };

  // Calculate initial t value based on existing progress or default to 0
  const initialT = progressLat && progressLng 
    ? calculateTFromProgress(start, end, progressLat, progressLng)
    : 0;

  const [t, setT] = useState(initialT);
  const [isDragging, setIsDragging] = useState(false);
  const progress = getPointOnLine(start, end, t);

  // Helper function to get the appropriate gap icon
  const getGapIcon = (gapType: string, isMultiPoint: boolean = false) => {
    const iconSize = 40;
    
    switch (gapType) {
      case 'New Gap':
        return isMultiPoint ? 
          <NewGapMultiPointer width={iconSize} height={iconSize} /> : 
          <NewGapSinglePointer width={iconSize} height={iconSize} />;
      case 'Closed Gap':
        return <ClosedGap width={iconSize} height={iconSize} />;
      case 'Open Gap':
      default:
        return <OpenGap width={iconSize} height={iconSize} />;
    }
  };

  // Check if this is a multi-point gap (has end coordinates different from start)
  const isMultiPoint = startLat !== endLat || startLng !== endLng;

  const onMarkerDrag = (e: any) => {
    const { latitude, longitude } = e.nativeEvent.coordinate;
    const dx = end.latitude - start.latitude;
    const dy = end.longitude - start.longitude;
    const lengthSquared = dx * dx + dy * dy;
    let tNew = ((latitude - start.latitude) * dx + (longitude - start.longitude) * dy) / lengthSquared;
    tNew = Math.max(0, Math.min(1, tNew));
    setT(tNew);
  };

  const onMarkerDragStart = () => {
    setIsDragging(true);
  };

  const onMarkerDragEnd = (e: any) => {
    setIsDragging(false);
    onMarkerDrag(e);
  };

  const handleAddPress = () => {
    // Dispatch the map data to Redux
    dispatch(setHindranceMapData({
      selectedItem,
      startLat,
      startLng,
      endLat,
      endLng,
      progressLat: progress.latitude,
      progressLng: progress.longitude,
      hasMapData: true,
      gapType: gapType,
    }));
    
    // Navigate back to the previous screen
    navigation.goBack();
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: Colors.white }}>
      <AppHeader
        title="Add Latlong"
        onBackPress={() => navigation.goBack()}
      />
      <View style={{ flex: 1 }}>
        <View style={{ flex: 1 }}>
          <MapView
            style={{ flex: 1, width: '100%', alignSelf: 'stretch', borderRadius: 0 }}
            initialRegion={{
              latitude: (startLat + endLat) / 2,
              longitude: (startLng + endLng) / 2,
              latitudeDelta: Math.abs(startLat - endLat) * 2 || 0.01,
              longitudeDelta: Math.abs(startLng - endLng) * 2 || 0.01,
            }}
          >
            <Marker coordinate={start} title="Start">
              <Mapicon1 width={40} height={40} />
            </Marker>
            <Marker coordinate={end} title="End">
              <Mapicon2 width={40} height={40} />
            </Marker>
            <Polyline coordinates={[start, end]} strokeColor="#00B200" strokeWidth={3} />
            <Marker
              coordinate={progress}
              draggable
              onDrag={onMarkerDrag}
              onDragStart={onMarkerDragStart}
              onDragEnd={onMarkerDragEnd}
              title="Progress"
              centerOffset={{ x: 0, y: -32 }}
              anchor={{ x: 0.5, y: 0.5 }}
            >
              <View style={[styles.handIconContainer, isDragging && styles.handIconContainerDragging]}>
                <HandPointer width={48} height={48} />
              </View>
            </Marker>
          </MapView>
        </View>
        <View style={styles.formContainer}>
          <Text style={styles.instruction}>Drag a line update your Process</Text>
          <View style={styles.divider} />
          <Text style={styles.label}>Latitude *</Text>
          <TextInput
            style={styles.input}
            value={progress.latitude.toString()}
            editable={false}
          />
          <Text style={styles.label}>Longitude *</Text>
          <TextInput
            style={styles.input}
            value={progress.longitude.toString()}
            editable={false}
          />
          <ButtonComponent title="Add" onPress={handleAddPress} />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  scrollViewContent: {
    flexGrow: 1,
    padding: ms(16),
    paddingBottom: ms(32),
  },
  mapContainer: {
    height: '50%',
    marginBottom: ms(16),
  },
  map: {
    flex: 1,
    borderRadius: ms(12),
  },
  instruction: {
    textAlign: 'center',
    fontSize: ms(16),
    color: Colors.textPrimary,
    marginBottom: ms(18),
    fontFamily: 'MNMedium',
  },
  divider: {
    height: 1,
    backgroundColor: Colors.searchBorderGrey,
    marginBottom: ms(18),
  },
  label: {
    color: Colors.textSecondary,
    fontSize: ms(14),
    fontFamily: 'MNMedium',
    marginBottom: ms(4),
    marginLeft: ms(2),
  },
  input: {
    backgroundColor: Colors.white,
    borderColor: Colors.searchBorderGrey,
    borderWidth: ms(1),
    borderRadius: ms(8),
    padding: ms(12),
    color: Colors.textPrimary,
    fontSize: ms(16),
    marginBottom: ms(16),
    fontFamily: 'MNMedium',
  },
  handIconContainer: {
    width: 80,
    height: 80,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    borderRadius: 40,
  },
  handIconContainerDragging: {
    opacity: 0.8,
    transform: [{ scale: 1.1 }],
  },
  formContainer: {
    width: '100%',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 32,
    backgroundColor: Colors.white,
  },
});

export default AddLatLongScreen;
