import { View, StyleSheet, SafeAreaView, Text, TextInput, Pressable, Modal, ScrollView } from 'react-native'
import React, { useCallback, useState, useEffect } from 'react'
import AppHeader from '../../components/AppHeader'
import { ms } from '../../utils/Scale/Scaling'
import Colors from '../../utils/Colors/Colors'
import Strings from '../../utils/Strings/Strings'
import { useNavigation, useRoute } from '@react-navigation/native';
import ArrowUp from '../../assets/svg/arrowUp.svg';
import Upload from '../../assets/svg/upload.svg';
import BottomPopupImageUpload from '../../components/BottomPopupImageUpload'
import PrintLog from '../../utils/Logger/PrintLog'
import ButtonComponent from '../../components/ButtonComponent'
import { NativeStackNavigationProp } from '@react-navigation/native-stack'
import DailyProgressTextInput from '../../screens/DailyProgress/components/DailyProgressTextInput'
import { t } from "i18next";
import { AppFonts } from '../../components/Fonts'
import { SVG } from '../../utils/ImagePath'
import { getSelectedJobs } from '../../utils/Storage/Storage'
// import DailyProgressTextInput from '../DailyProgress/components/DailyProgressTextInput'

type RootStackParamList = {
    HindranceMapView: undefined;
};

const HindranceUpdate = () => {
    const route = useRoute();
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const { selectedItem } = route.params as { selectedItem: any };

    const [imgUploadModalVisible, setImgUploadModalVisible] = useState<boolean>(false);
    const [progressLength, setProgressLength] = useState<string>(selectedItem?.GapLength_Updated?.toString() || '');
    const [expand, setExpand] = useState<boolean>(true);
    const [remarks, setRemarks] = useState<string>(selectedItem?.Remarks || '');

    useEffect(() => {
        navigation.addListener('beforeRemove', (e) => {
            if (e.data.action.type === 'GO_BACK') {
                e.preventDefault();
                navigation.replace('HindranceMapView');
            }
        });
        setRemarks(selectedItem?.Remarks || '');
    }, [navigation]);

    const handleUploadImage = useCallback(() => {
        setImgUploadModalVisible(true)
    }, []);

    const handleProgressLengthChange = useCallback((text: string) => {
        // Only allow numeric input with decimal point
        if (/^\d*\.?\d*$/.test(text)) {
            setProgressLength(text);
        }
    }, []);

    return (
        <SafeAreaView style={styles.container}>
            <AppHeader
                title={t('hindranceStrings.updateHindrance')}
                onBackPress={() => navigation.replace('HindranceMapView')}
            />
            <ScrollView
                style={styles.scrollView}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.scrollViewContent}
            >
                <View style={styles.content}>
                    <Pressable style={styles.card(expand)}
                    onPress={() => setExpand(!expand)}>
                        {
                            !expand ? <ArrowUp width={ms(20)} height={ms(20)} style={styles.arrowUpIcon} /> : <SVG.ChevronDown width={ms(20)} height={ms(20)} style={styles.arrowUpIcon} />
                        }
                        <View style={styles.row(false)}>
                            <Text style={styles.label}>Date</Text>
                            <Text style={styles.value}>{selectedItem?.date || '-'}</Text>
                        </View>
                        <View style={styles.row(expand)}>
                            <Text style={styles.label}>Pipe Material and Dia</Text>
                            <Text style={styles.value}>{selectedItem?.Classification_Type_Detail_Description || '-'}</Text>
                        </View>
                        <View style={styles.row(expand)}>
                            <Text style={styles.label}>Reason for Gap</Text>
                            <Text style={styles.value} numberOfLines={1} ellipsizeMode="tail">{selectedItem?.LGD_Gap_Reason || '-'}</Text>
                        </View>
                        <View style={styles.row(expand)}>
                            <Text style={styles.label}>Gap Length</Text>
                            <Text style={styles.value}>{selectedItem?.GAP_Length || '-'}</Text>
                        </View>
                        {
                            selectedItem?.LGD_Latitude_End && selectedItem?.LGD_Longitude_End ? (
                                <>
                                    <View style={styles.row(expand)}>
                                        <Text style={styles.label}>Start Latitude</Text>
                                        <Text style={styles.value}>{selectedItem?.latitude || '-'}</Text>
                                    </View>
                                    <View style={styles.row(expand)}>
                                        <Text style={styles.label}>Start Longitude</Text>
                                        <Text style={styles.value}>{selectedItem?.longitude || '-'}</Text>
                                    </View>
                                    <View style={styles.row(expand)}>
                                        <Text style={styles.label}>End Latitude</Text>
                                        <Text style={styles.value}>{selectedItem?.LGD_Latitude_End || '-'}</Text>
                                    </View>
                                    <View style={styles.row(expand)}>
                                        <Text style={styles.label}>End Longitude</Text>
                                        <Text style={styles.value}>{selectedItem?.LGD_Longitude_End || '-'}</Text>
                                    </View>
                                </>
                            ) : (
                                <>
                                    <View style={styles.row(expand)}>
                                        <Text style={styles.label}>Latitude</Text>
                                        <Text style={styles.value}>{selectedItem?.latitude || '-'}</Text>
                                    </View>
                                    <View style={styles.row(expand)}>
                                        <Text style={styles.label}>Longitude</Text>
                                        <Text style={styles.value}>{selectedItem?.longitude || '-'}</Text>
                                    </View>
                                </>
                            )
                        }
                        <View style={styles.row(expand)}>
                            <Text style={styles.label}>Start & End Node</Text>
                            <Text style={styles.value}>{selectedItem?.LGD_Start_End_Node || '-'}</Text>
                        </View>
                    </Pressable>

                    <View style={styles.progressLengthContainer}>
                        <Text style={styles.progressLengthLabel}>Progress Length * </Text>
                        <View style={styles.progressLengthInputContainer}>
                            <TextInput
                                style={styles.input}
                                value={progressLength}
                                onChangeText={handleProgressLengthChange}
                                keyboardType="numeric"
                                placeholder="Enter length"
                                placeholderTextColor={Colors.textSecondary}
                            />
                            <View style={styles.balanceContainer}>
                                <Text style={styles.balanceLabel}>{t('hindranceStrings.balance')}: </Text>
                                <Text style={styles.balanceValue}>{selectedItem?.GAP_Length - Number(progressLength)}</Text>
                            </View>
                        </View>
                    </View>

                    <DailyProgressTextInput
                        customStyle={[styles.textInput, styles.remarksInput]}
                        label="Remarks *"
                        value={''}
                        onChangeText={() => { }}
                        isMultiline={true}
                        hasRemarkHistory={selectedItem.Remarks && true} />

                    <View style={styles.inputContainer}>
                        <Text style={styles.inputLabel}>Attachments *</Text>
                        <View style={styles.uploadButtonOuter}>
                            <Pressable style={styles.uploadButton} onPress={handleUploadImage}>
                                <Upload />
                                <Text style={styles.uploadButtonText}>{Strings.DailyProgress.uploadImage}</Text>
                            </Pressable>
                        </View>
                    </View>
                </View>
            </ScrollView>


                <ButtonComponent
                    title={t('hindranceStrings.update')}
                    mainContainerStyle={{
                        paddingTop: ms(15),
                        paddingBottom: ms(15),
                    }}
                    onPress={() => { 
                        const jobs = getSelectedJobs();
                        if(jobs.length < 0) return;
                        const updateParams = {
                            jobCode: jobs[0]?.id,
                            UID: selectedItem.id,
                            GapLength: selectedItem.GAP_Length,
                            Latitude_Map: selectedItem.latitude,
                            Longitude_Map: selectedItem.longitude,
                            GisPhoto: [],
                            date: selectedItem.date,
                            Open_Active: 'N',
                            Remarks: remarks,
                            type: 'GISLENGTHINSERT',
                            Classification_Type_Detail_Code: selectedItem.Classification_Type_Detail_Code,
                            Latitude_End: selectedItem.LGD_Latitude_End,
                            Longitude_End: selectedItem.LGD_Longitude_End,
                            Start_End_Node: selectedItem.LGD_Start_End_Node,
                            Gap_Reason: '',
                        };
                        console.log('updateParams-----', updateParams);
                    }}
                />

            <Modal
                animationType="none"
                transparent
                visible={imgUploadModalVisible}
                onRequestClose={() => setImgUploadModalVisible(false)}
            >
                <Pressable
                    style={styles.modalOverlay}
                    onPress={() => setImgUploadModalVisible(false)}
                >
                    <BottomPopupImageUpload
                        onCameraPress={() => {
                            PrintLog.debug('BottomPopupImageUpload', 'Camera Pressed');
                        }}
                        onGalleryPress={() => {
                            PrintLog.debug('BottomPopupImageUpload', 'Gallery Pressed');
                        }}
                    />
                </Pressable>
            </Modal>
        </SafeAreaView>
    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.white,
    },
    scrollView: {
        flex: 1,
    },
    scrollViewContent: {
        flexGrow: 1,
    },
    content: {
        flex: 1,
        padding: ms(18),
        paddingBottom: ms(100),
    },
    card: (expand: boolean) => ({
        backgroundColor: Colors.containerligetBlue,
        borderRadius: ms(10),
        paddingHorizontal: ms(12),
        paddingVertical: ms(10),
        width: '100%',
        position: 'relative',
        marginBottom: ms(20),
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
        height: expand ? ms(50) : 'auto',
    }),
    arrowUpIcon: {
        position: 'absolute',
        top: ms(15),
        right: ms(10),
        zIndex: 10,
    },
    row: (expand: boolean) => ({
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: ms(5),
        display: expand ? 'none' : 'flex',
    }),
    label: {
        color: Colors.textSecondary,
        fontSize: ms(14),
        fontFamily: AppFonts.Medium,
        flex: 1,
    },
    value: {
        color: Colors.textPrimary,
        fontSize: ms(14),
        fontFamily: AppFonts.Medium,
        flex: 1,
        textAlign: 'left',
    },
    textInput: {
        backgroundColor: Colors.containerligetBlue,
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
        padding: ms(12),
        borderRadius: ms(8),
        color: Colors.searchTextBlack,
        fontSize: ms(14),
    },
    remarksInput: {
        height: ms(100),
        textAlignVertical: 'top',
    },
    historyButtonText: {
        color: Colors.brandBlue,
        fontSize: ms(14),
        fontWeight: '500',
    },
    progressLengthContainer: {
        marginBottom: ms(15),
    },
    progressLengthLabel: {
        fontSize: ms(13.5),
        marginBottom: ms(4),
        color: Colors.textSecondary,
        fontFamily: AppFonts.Medium,
    },
    progressLengthInputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: Colors.containerligetBlue,
        borderColor: Colors.searchBorderGrey,
        borderWidth: ms(1),
        borderRadius: ms(8),
        padding: ms(12),
    },
    input: {
        flex: 1,
        height: ms(20),
        fontSize: ms(16),
        color: Colors.textPrimary,
        fontFamily: AppFonts.Medium,
        padding: 0,
    },
    balanceContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: ms(12),
        paddingLeft: ms(12),
    },
    balanceLabel: {
        color: Colors.textPrimary,
        fontSize: ms(14),
        fontFamily: AppFonts.Medium,
    },
    balanceValue: {
        color: Colors.textPrimary,
        fontSize: ms(14),
        fontFamily: AppFonts.Medium,
    },
    inputContainer: {
        marginVertical: ms(20),
    },
    inputLabel: {
        fontSize: ms(14),
        marginBottom: ms(3),
        fontFamily: AppFonts.Medium,
        color: Colors.textSecondary,
    },
    uploadButtonOuter: {
        padding: ms(8),
        borderRadius: ms(5),
        borderWidth: ms(1),
        borderColor: Colors.searchBorderGrey,
    },
    uploadButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: ms(8),
        borderRadius: ms(5),
        borderWidth: ms(1),
        borderColor: Colors.secondary,
    },
    uploadButtonText: {
        color: Colors.secondary,
        marginLeft: ms(8),
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: 'rgba(0,0,0,0.3)',
    },
    downloadButtonContainer: {
        backgroundColor: Colors.white,
        alignItems: 'center',
        paddingVertical: ms(20),
        elevation: 10,
        borderTopWidth: 1,
        borderTopColor: Colors.grey,
    },
})

export default HindranceUpdate