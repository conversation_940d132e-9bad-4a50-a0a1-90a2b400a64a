import React, { useState } from 'react';
import { View, Text, StyleSheet, Pressable, Modal, ScrollView, Image } from 'react-native';
import BottomPopup from '../../../components/BottomPopup';
import Colors from '../../../utils/Colors/Colors';
import { ms } from '../../../utils/Scale/Scaling';
import Strings from '../../../utils/Strings/Strings';
import DropDownSelection from './DropdownSelection';
import SearchBar from '../../../screens/Hindrance/HindranceComponents/SearchLocationBar';
import { AppFonts } from '../../../components/Fonts';

interface MaterialDiaItem {
    Categogy: string;
    CategogyId: number;
    Classification_Type_Detail_Description: string;
    Classification_Type_Detail_Code: string;
}

interface GapReasonItem {
    GapReason_ETD_Code: string;
    GapReason_Description: string;
}

interface HindranceBottomPopupProps {
    title: string;
    visible: boolean;
    type: string;
    data: MaterialDiaItem[] | GapReasonItem[];
    onClose: () => void;
    onSelect: (selected: string) => void;
    selectedItem: string;
}

const HindranceBottomPopup: React.FC<HindranceBottomPopupProps> = ({ title, visible, type, data, onSelect, onClose, selectedItem }) => {
    const [searchQuery, setSearchQuery] = useState<string>('');

    const filteredData = data.filter(item => {
        if (type === 'materialDia') {
            const materialItem = item as MaterialDiaItem;
            return materialItem.Classification_Type_Detail_Description.toLowerCase().includes(searchQuery.toLowerCase());
        } else {
            const gapItem = item as GapReasonItem;
            return gapItem.GapReason_Description.toLowerCase().includes(searchQuery.toLowerCase());
        }
    });

    return (
        <BottomPopup visible={visible} onCancelPress={onClose}>
            <View style={styles.messageContainer}>
                <Text style={styles.message}>{title}</Text>
            </View>
            <View style={styles.divider} />
            <View style={styles.searchContainer}>
                <SearchBar
                    mainContainerStyle={{ width: '100%' }}
                    onChange={setSearchQuery}
                    value={searchQuery}
                    placeholder={'Search'}
                />
            </View>
            <ScrollView style={styles.menuContainer} showsVerticalScrollIndicator={false}>
                {filteredData.length === 0 ? (
                    <View style={styles.noDataContainer}>
                        <Image
                            source={require('../../../assets/images/NoJobs.png')}
                            style={styles.noDataImage}
                        />
                    </View>
                ) : (
                    <>
                        {
                            type === 'materialDia' ? (
                                <>
                                    {
                                        (filteredData as MaterialDiaItem[]).map((item, index) => (
                                            <DropDownSelection
                                                key={index}
                                                option={item.Classification_Type_Detail_Description}
                                                isSelected={selectedItem === item.Classification_Type_Detail_Description}
                                                onSelect={onSelect}
                                            />
                                        ))
                                    }
                                </>
                            ) : (
                                <>
                                    {
                                        (filteredData as GapReasonItem[]).map((item, index) => (
                                            <DropDownSelection
                                                key={index}
                                                option={item.GapReason_Description}
                                                isSelected={selectedItem === item.GapReason_Description}
                                                onSelect={onSelect}
                                            />
                                        ))
                                    }
                                </>
                            )
                        }
                    </>
                )}
            </ScrollView>
        </BottomPopup>
    );
};

const styles = StyleSheet.create({
    modalContainer: {
        flex: 1,
        maxHeight: ms(400)
    },
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: Colors.overlay,
    },
    popupContainer: {
        flex: 1,
        justifyContent: 'flex-end',
    },
    messageContainer: {
        paddingVertical: ms(15),
        paddingHorizontal: ms(10),
    },
    message: {
        fontSize: ms(18),
        fontFamily: AppFonts.SemiBold,
        color: Colors.black,
    },
    divider: {
        height: 1,
        backgroundColor: Colors.searchBorderGrey,
        marginBottom: ms(8),
        marginHorizontal: ms(10)
    },
    menuContainer: {
       paddingHorizontal: ms(18)
    },
    searchContainer: {
        marginHorizontal: ms(10),
    },
    noDataContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: ms(20),
    },
    noDataImage: {
        width: ms(120),
        height: ms(100),
        marginBottom: ms(10),
    },
});

export default HindranceBottomPopup;