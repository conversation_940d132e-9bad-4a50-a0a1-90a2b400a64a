import React from 'react';
import { View, TextInput, StyleSheet, ActivityIndicator, StyleProp, ViewStyle } from 'react-native';
import SearchIcon from '../../../assets/svg/Search.svg';
import Colors from '../../../utils/Colors/Colors';
import { ms } from '../../../utils/Scale/Scaling';

interface SearchLocationBarProps {
    icon?: React.ReactNode;
    value: string;
    onChange: (text: string) => void;
    placeholder?: string;
    isLoading?: boolean;
    mainContainerStyle?: StyleProp<ViewStyle>;
}

const SearchLocationBar: React.FC<SearchLocationBarProps> = ({ icon, value, onChange, placeholder, isLoading = false, mainContainerStyle }) => {
    return (
        <View style={[styles.container, mainContainerStyle]}>
            <View style={styles.leftIcon}>
                {icon}
            </View>
            <TextInput
                style={styles.input}
                value={value}
                onChangeText={onChange}
                placeholder={placeholder}
                placeholderTextColor={Colors.textSecondary}
            />
            <View style={styles.rightIcon}>
                {isLoading ? (
                    <ActivityIndicator size="small" color={Colors.primary} />
                ) : (
                    <SearchIcon width={ms(20)} height={ms(20)} />
                )}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.white,
        borderRadius: ms(4),
        borderWidth: 1,
        borderColor: Colors.searchBorderGrey,
        paddingHorizontal: ms(12),
        height: ms(44),
        width: '90%',
        marginVertical: ms(8),
        shadowColor: Colors.black,
        shadowOpacity: 0.04,
        shadowRadius: 2,
        shadowOffset: { width: 0, height: 1 },
    },
    leftIcon: {
        marginRight: ms(12),
    },
    input: {
        flex: 1,
        fontSize: ms(14),
        color: Colors.textPrimary,
        fontFamily: 'MNMedium'
    },
    rightIcon: {
        marginLeft: ms(12),
    },
});

export default SearchLocationBar; 