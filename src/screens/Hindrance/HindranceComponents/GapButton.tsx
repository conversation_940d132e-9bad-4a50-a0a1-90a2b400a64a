import { View, Text, StyleSheet, Pressable } from 'react-native'
import React from 'react'
import Colors from '../../../utils/Colors/Colors';
import { ms } from '../../../utils/Scale/Scaling';
import { AppFonts } from '../../../components/Fonts';

interface GapButtonProps {
    label: string;
    icon: React.ReactNode;
    onPress?: () => void;
    isSelected?: boolean;
}

const GapButton: React.FC<GapButtonProps> = ({ label, icon, onPress, isSelected }) => {
    return (
        <Pressable
            style={[styles.btnContainer, isSelected && styles.selectedButton]}
            onPress={onPress}
        >
            {icon}
            <Text style={[styles.txt, isSelected && styles.selectedText]}>{label}</Text>
        </Pressable>
    )
}

export default GapButton

const styles = StyleSheet.create({
    btnContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: Colors.white,
        padding: ms(8),
        paddingHorizontal: ms(10),
        borderRadius: ms(20),
        marginRight: ms(5),
        marginTop: ms(3)
    },
    selectedButton: {
        backgroundColor: Colors.secondary,
    },
    txt: {
        fontFamily: AppFonts.SemiBold,
        fontSize: ms(12),
        color: Colors.textPrimary,
        marginLeft: ms(5)
    },
    selectedText: {
        color: Colors.white
    }
})