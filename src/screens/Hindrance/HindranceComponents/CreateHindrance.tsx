import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import BorderedButton from '../../../components/BorderedButton';
import ButtonComponent from '../../../components/ButtonComponent';
import Colors from '../../../utils/Colors/Colors';
import { ms } from '../../../utils/Scale/Scaling';
import BottomPopup from '../../../components/BottomPopup';
import { t } from "i18next";
import SearchLocationBar from '../../../components/SearchComponent';
import PointOne from '../../../assets/svg/PointOne.svg';
import PointTwo from '../../../assets/svg/PointTwo.svg';
import NewGapPointer from '../../../assets/svg/NewGapPointer.svg';
import { AppFonts } from '../../../components/Fonts';
import DeleteButton from './DeleteButton';
import MapBottomPopup from '../../../components/MapView/MapBottomView';
import { Button } from '@react-navigation/elements';

interface HindrancePopupProps {
    isVisible: boolean;
    isSinglePoint: boolean;
    confirmLabel: string;
    cancelLabel: string;
    onCancel: () => void;
    onConfirm: (location: {
        latitude: number;
        longitude: number;
        address: string;
        endLatitude: number | null;
        endLongitude: number | null;
        endAddress: string | null;
    }) => void;
    onClose?: () => void;
    selectedLocation?: {
        latitude: number;
        longitude: number;
        address: string;
        endLatitude: number | null;
        endLongitude: number | null;
        endAddress: string | null;
    } | null;
    lastSinglePlace?: { name: string, lat: number, lng: number } | null;
    lastDoublePlaces?: {
        start: { name: string, lat: number, lng: number } | null,
        end: { name: string, lat: number, lng: number } | null
    };
    onPlaceChange?: (data: any) => void;
}

const CreateHindrance: React.FC<HindrancePopupProps> = ({
    isVisible,
    isSinglePoint,
    confirmLabel,
    cancelLabel,
    onCancel,
    onConfirm,
    onClose,
    selectedLocation,
    lastSinglePlace,
    lastDoublePlaces,
    onPlaceChange
}) => {
    const [searchValue, setSearchValue] = useState(lastSinglePlace?.name || '');
    const [endSearchValue, setEndSearchValue] = useState(lastDoublePlaces?.end?.name || '');
    const [singleCoords, setSingleCoords] = useState<{ lat: number, lng: number, address: string } | null>(lastSinglePlace ? { lat: lastSinglePlace.lat, lng: lastSinglePlace.lng, address: lastSinglePlace.name } : null);
    const [doubleCoords, setDoubleCoords] = useState<{ start: { lat: number, lng: number, address: string }, end: { lat: number, lng: number, address: string } } | null>(lastDoublePlaces ? {
        start: lastDoublePlaces.start ? { lat: lastDoublePlaces.start.lat, lng: lastDoublePlaces.start.lng, address: lastDoublePlaces.start.name } : { lat: 0, lng: 0, address: '' },
        end: lastDoublePlaces.end ? { lat: lastDoublePlaces.end.lat, lng: lastDoublePlaces.end.lng, address: lastDoublePlaces.end.name } : { lat: 0, lng: 0, address: '' }
    } : null);
    const [popupVisible] = useState(true);
    const [isSingleLoading, setIsSingleLoading] = useState(false);
    const [isStartLoading, setIsStartLoading] = useState(false);
    const [isEndLoading, setIsEndLoading] = useState(false);

    const geocodePlace = async (place: string) => {
        try {
            console.log('Geocoding place:', place);
            const apiKey = 'AIzaSyDPjsj1s3B-e1W1sa6GXoGpJ4Yai4tc7Mc';
            const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(place)}&key=${apiKey}`;
            console.log('Geocoding URL:', url);
            const res = await fetch(url);
            const data = await res.json();
            console.log('Geocoding response:', data);
            if (data.results && data.results.length > 0) {
                const loc = data.results[0].geometry.location;
                const result = { lat: loc.lat, lng: loc.lng, address: data.results[0].formatted_address };
                console.log('Geocoding result:', result);
                return result;
            }
            console.log('No geocoding results found');
            return null;
        } catch (error) {
            console.error('Geocoding error:', error);
            return null;
        }
    };

    const handleSingleInput = async (text: string) => {
        console.log('handleSingleInput called with:', text);
        setSearchValue(text);
        if (text.length > 2) {
            setIsSingleLoading(true);
            const coords = await geocodePlace(text);
            setIsSingleLoading(false);
            console.log('Geocoding result for single input:', coords);
            if (coords && coords.lat !== 0 && coords.lng !== 0) {
                setSingleCoords(coords);
                console.log('Calling onPlaceChange with single data:', { single: { name: text, lat: coords.lat, lng: coords.lng } });
                if (onPlaceChange) onPlaceChange({ single: { name: text, lat: coords.lat, lng: coords.lng } });
            } else {
                setSingleCoords(null);
                console.log('Calling onPlaceChange with null single data');
                if (onPlaceChange) onPlaceChange({ single: null });
            }
        } else {
            setIsSingleLoading(false);
            setSingleCoords(null);
            if (onPlaceChange) onPlaceChange({ single: null });
        }
    };

    const handleDoubleStartInput = async (text: string) => {
        setSearchValue(text);
        if (text.length > 2) {
            setIsStartLoading(true);
            const coords = await geocodePlace(text);
            console.log('Geocoding result for double start input:', coords);
            setIsStartLoading(false);
            if (coords && coords.lat !== 0 && coords.lng !== 0) {
                setDoubleCoords(prev => ({
                    start: coords,
                    end: prev?.end || { lat: 0, lng: 0, address: '' }
                }));
                if (onPlaceChange) onPlaceChange({ double: { start: { name: text, lat: coords.lat, lng: coords.lng }, end: lastDoublePlaces?.end } });
            } else {
                setDoubleCoords(prev => ({
                    start: { lat: 0, lng: 0, address: text },
                    end: prev?.end || { lat: 0, lng: 0, address: '' }
                }));
                if (onPlaceChange) onPlaceChange({ double: { start: null, end: lastDoublePlaces?.end } });
            }
        } else {
            setIsStartLoading(false);
            setDoubleCoords(prev => ({
                start: { lat: 0, lng: 0, address: text },
                end: prev?.end || { lat: 0, lng: 0, address: '' }
            }));
            if (onPlaceChange) onPlaceChange({ double: { start: null, end: lastDoublePlaces?.end } });
        }
    };

    const handleDoubleEndInput = async (text: string) => {
        setEndSearchValue(text);
        if (text.length > 2) {
            setIsEndLoading(true);
            const coords = await geocodePlace(text);
            setIsEndLoading(false);
            if (coords && coords.lat !== 0 && coords.lng !== 0) {
                setDoubleCoords(prev => ({
                    start: prev?.start || { lat: 0, lng: 0, address: '' },
                    end: coords
                }));
                if (onPlaceChange) onPlaceChange({ double: { start: lastDoublePlaces?.start, end: { name: text, lat: coords.lat, lng: coords.lng } } });
            } else {
                setDoubleCoords(prev => ({
                    start: prev?.start || { lat: 0, lng: 0, address: '' },
                    end: { lat: 0, lng: 0, address: text }
                }));
                if (onPlaceChange) onPlaceChange({ double: { start: lastDoublePlaces?.start, end: null } });
            }
        } else {
            setIsEndLoading(false);
            setDoubleCoords(prev => ({
                start: prev?.start || { lat: 0, lng: 0, address: '' },
                end: { lat: 0, lng: 0, address: text }
            }));
            if (onPlaceChange) onPlaceChange({ double: { start: lastDoublePlaces?.start, end: null } });
        }
    };

    const handleCreate = () => {
        // For UI testing - call onConfirm immediately
        // You can uncomment the location logic later when implementing the full functionality

        if (isSinglePoint) {
            onConfirm({
                latitude: 13.0827, // Default coordinates for testing
                longitude: 80.2707,
                address: 'Test Single Point Location',
                endLatitude: null,
                endLongitude: null,
                endAddress: null
            });
        } else {
            onConfirm({
                latitude: 13.0827, // Start coordinates for testing
                longitude: 80.2707,
                address: 'Test Start Location',
                endLatitude: 13.0927, // End coordinates for testing
                endLongitude: 80.2807,
                endAddress: 'Test End Location'
            });
        }

        // Uncomment this section when you want to use actual location data
        /*
        if (isSinglePoint && singleCoords) {
            onConfirm({
                latitude: singleCoords.lat,
                longitude: singleCoords.lng,
                address: singleCoords.address,
                endLatitude: null,
                endLongitude: null,
                endAddress: null
            });
        } else if (!isSinglePoint && doubleCoords) {
            onConfirm({
                latitude: doubleCoords.start.lat,
                longitude: doubleCoords.start.lng,
                address: doubleCoords.start.address,
                endLatitude: doubleCoords.end.lat,
                endLongitude: doubleCoords.end.lng,
                endAddress: doubleCoords.end.address
            });
        }
        */
    };

    // useEffect(() => {
    //     if (lastSinglePlace || lastDoublePlaces) {
    //         setSearchValue(lastSinglePlace?.name || '');
    //         setEndSearchValue(lastDoublePlaces?.end?.name || '');
    //     }
    // }, [lastSinglePlace, lastDoublePlaces]);

    useEffect(() => {
        if (selectedLocation) {
            setSearchValue(selectedLocation.address);
        }
    }, [selectedLocation]);

    return (
        <View style={styles.container}>
            {/* <DeleteButton onPress={onCancel} /> */}
            <MapBottomPopup>
                <View style={styles.messageContainer}>
                    {isSinglePoint ? (
                        <Text style={styles.message}>{t('hindranceStrings.singlePointMsg')}</Text>
                    ) : (
                        <Text style={styles.message}>{t('hindranceStrings.multiplePointMsg')}</Text>
                    )}
                </View>
                <View style={styles.divider} />
                {
                    isSinglePoint ? (
                        <SearchLocationBar
                            icon={<NewGapPointer width={ms(20)} height={ms(20)} />}
                            value={selectedLocation?.address || searchValue}
                            onChange={handleSingleInput}
                            placeholder='Search'
                            isLoading={isSingleLoading}
                        />
                    ) : (
                        <>
                            <SearchLocationBar
                                icon={<PointOne width={ms(20)} height={ms(20)} />}
                                value={selectedLocation?.address || searchValue}
                                onChange={handleDoubleStartInput}
                                placeholder='Search'
                                isLoading={isStartLoading}
                            />
                            <SearchLocationBar
                                icon={<PointTwo width={ms(20)} height={ms(20)} />}
                                value={selectedLocation?.endAddress || endSearchValue}
                                onChange={handleDoubleEndInput}
                                placeholder='Search'
                                isLoading={isEndLoading}
                            />
                        </>
                    )
                }


                <ButtonComponent
                    disabled={isSinglePoint ? !selectedLocation : !selectedLocation?.endLatitude && !selectedLocation?.endLongitude}
                    showSecondaryButton
                    secondaryButtonTitle={'Cancel'}
                    onSecondaryPress={onCancel}
                    mainContainerStyle={{
                        paddingBottom: ms(5),
                        paddingTop: ms(15),
                    }}
                    customWidth={styles.disabledButton(isSinglePoint ? selectedLocation : selectedLocation?.endLatitude && selectedLocation?.endLongitude)}
                    title={'Create Hindrance'}
                    onPress={onConfirm} />
            </MapBottomPopup>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        // position: 'absolute',
        // bottom: Platform.OS === 'ios' ? ms(130) : ms(0),
        // left: 0,
        // right: 0,
        backgroundColor: Colors.white,
        borderTopLeftRadius: ms(20),
        borderTopRightRadius: ms(20),
        zIndex: 9999,
    },
    messageContainer: {
        alignItems: 'center',
        marginVertical: ms(10),
        marginTop: ms(10),
    },
    message: {
        fontSize: ms(16),
        fontFamily: AppFonts.SemiBold,
        color: Colors.textPrimary,
        marginHorizontal: ms(10),
        textAlign: 'center',
        fontWeight: '600',
    },
    buttonContainer: {
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-around',
        marginTop: ms(20),
        paddingHorizontal: ms(10),
        paddingTop: ms(20),
        paddingBottom: ms(10),
        borderTopWidth: 0.2,
        borderTopColor: Colors.searchBorderGrey,
    },
    width: {
        width: '46%',
    },
    divider: {
        height: 1,
        backgroundColor: Colors.searchBorderGrey,
        width: '90%',
        marginVertical: ms(8),
    },
    disabledButton: (disabled: boolean) => {
        return {
            opacity: !disabled ? 0.5 : 1,
        }
    },
});
export default CreateHindrance;
