import { View, Text, StyleSheet, Pressable, ScrollView, Platform, PermissionsAndroid } from 'react-native'
import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react'
import MapView, { <PERSON><PERSON>, <PERSON>yline } from 'react-native-maps';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { height, ms, width } from '../../../utils/Scale/Scaling';
import SearchBar from '../../../components/SearchComponent';
import Gps from '../../../assets/svg/gps.svg';
import Ellipse from '../../../assets/svg/Ellipse.svg';
import Colors from '../../../utils/Colors/Colors';
import SinglePoint from '../../../assets/svg/singlePoint.svg';
import MultiPoint from '../../../assets/svg/multiPoint.svg';
import CreateHindrance from './CreateHindrance';
import Strings from '../../../utils/Strings/Strings';
import SinglePointer from '../../../assets/svg/SinglePointer.svg'
import GapButton from './GapButton';
import OpenGap from '../../../assets/svg/OpenGap.svg';
import ClosedGap from '../../../assets/svg/ClosedGap.svg';
import NewGapSinglePointer from '../../../assets/svg/NewGapSinglePointer.svg';
import NewGapMultiPointer from '../../../assets/svg/NewGapMultiPointer.svg';
import NewGap from '../../../assets/svg/NewGapPointer.svg';
import ProgressUpdate from '../../../assets/svg/ProgressUpdate.svg';
import Info from '../../../assets/svg/info.svg';
import { getUserRolesInfo } from '../../../utils/DataStorage/Storage';
import DeleteButton from './DeleteButton';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useWindowDimensions } from 'react-native';
import { t } from "i18next";
import Geolocation from '@react-native-community/geolocation';
import LoadingOverlay from '../../../components/LoadingOverlay';
import { AppFonts } from '../../../components/Fonts';

interface MarkerType {
    id: string;
    name: string;
    details: string;
    progressQty: string;
    manDays: string;
    date: string;
    latitude: number;
    longitude: number;
    LGD_Latitude_End?: number | null;
    LGD_Longitude_End?: number | null;
    Open_Active?: string;
    New_Gap?: string;
    Progress_Update?: string;
    GAP_Length?: number;
    GapLength_Updated?: number;
    Remarks?: string;
    LGD_Start_End_Node?: string | null;
    LGD_Gap_Reason?: string | null;
    Classification_Type_Detail_Code?: string | null;
    Classification_Type_Detail_Description?: string | null;
    // Additional fields from API response
    Jobcode?: string;
    Serialno?: number | null;
    Photo?: any[];
    MinDate?: string | null;
    markerDetails?: {
        'Date': string;
        'Gap Length': string;
    } & (
        | {
            'Latitude': number;
            'Longitude': number;
        }
        | {
            'Start Latitude': number;
            'Start Longitude': number;
            'End Latitude': number;
            'End Longitude': number;
        }
    );
}

interface AddPointSelectorProps {
    onSinglePoint: () => void;
    onMultiPoint: () => void;
}

const detailsData = ['Date', 'Gap Length', 'Latitue', 'Longitude'];
const MultipointDetailsData = ['Date', 'Gap Length', 'Start Latitude', 'Start Longitude', 'End Latitude', 'End Longitude'];

interface ProgressPolylineProps {
    start: {
        latitude: number;
        longitude: number;
    };
    end: {
        latitude: number;
        longitude: number;
    };
    totalLength: number;
    completedLength: number;
}

const ProgressPolyline: React.FC<ProgressPolylineProps> = React.memo(({
    start,
    end,
    totalLength,
    completedLength
}) => {
    const { intermediatePoint, progressRatio } = useMemo(() => {
        const ratio = totalLength > 0 ? completedLength / totalLength : 0;
        return {
            progressRatio: ratio,
            intermediatePoint: {
                latitude: start.latitude + (end.latitude - start.latitude) * ratio,
                longitude: start.longitude + (end.longitude - start.longitude) * ratio
            }
        };
    }, [start, end, totalLength, completedLength]);

    return (
        <>
            <Polyline
                coordinates={[start, intermediatePoint]}
                strokeColor={Colors.offlineRed}
                strokeWidth={3}
            />
            <Polyline
                coordinates={[intermediatePoint, end]}
                strokeColor={Colors.mapStrokeOpenLine}
                strokeWidth={3}
            />
        </>
    );
});

const AddPointSelector = ({ onSinglePoint, onMultiPoint }: AddPointSelectorProps) => (
    <View style={styles.container}>
        <Text style={styles.label}>{t('hindranceStrings.add')}:</Text>
        <Pressable style={styles.iconButton} onPress={onSinglePoint}>
            <SinglePoint width={80} height={80} />
        </Pressable>
        <Pressable style={styles.iconButton} onPress={onMultiPoint}>
            <MultiPoint width={80} height={80} />
        </Pressable>
    </View>
);

interface DataTextProps {
    label: string;
    value: string | number;
}

const DataText = ({ label, value }: DataTextProps) => (
    <View style={styles.detailsContainer}>
        <Text style={styles.labelTxt}>{label}</Text>
        <Text style={styles.valueTxt}>{value}</Text>
    </View>
)

interface MapViewComponentProps {
    hindranceData: MarkerType[];
    onRefresh?: () => void;
    isRefreshing?: boolean;
}

type RootStackParamList = {
    HindranceUpdate: { selectedItem: MarkerType };
    HindranceCreation: { isSinglePointer: boolean; selectedLocation: any };
    CreateHindranceUpdate: { isSinglePointer: boolean; selectedLocation: any };
};

const MapViewComponent = ({ hindranceData, onRefresh, isRefreshing }: MapViewComponentProps) => {
    const mapRef = useRef<MapView | null>(null);
    const { width: windowWidth, height: windowHeight } = useWindowDimensions();
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();

    const roles = getUserRolesInfo();
    const userRole: string = roles?.RolesList?.[0]?.Functional_ROLE || '';
    console.log('userRole-----', userRole.toLowerCase().trim());

    const [cardPosition, setCardPosition] = useState({ x: 0, y: 0 });
    // const [currentLocation, setCurrentLocation] = useState<{ latitude: number; longitude: number } | null>(null);
    // const [loading, setLoading] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedMarker, setSelectedMarker] = useState<MarkerType | null>(null);
    const [showCreatePopup, setShowCreatePopup] = useState<boolean>(false);
    const [isSinglePoint, setIsSinglePoint] = useState<boolean>(false);
    const [selectedFilter, setSelectedFilter] = useState<string>('');
    const [showAdminApproval, setShowAdminApproval] = useState<boolean>(false);
    const [selectedLocation, setSelectedLocation] = useState<{
        latitude: number;
        longitude: number;
        address: string;
        endLatitude: number | null;
        endLongitude: number | null;
        endAddress: string | null;
    } | null>(null);
    const [infoIconPositions, setInfoIconPositions] = useState<{ [id: string]: { x: number, y: number } }>({});
    const [lastSinglePlace, setLastSinglePlace] = useState<{ name: string, lat: number, lng: number } | null>(null);
    const [lastDoublePlaces, setLastDoublePlaces] = useState<{
        start: { name: string, lat: number, lng: number } | null,
        end: { name: string, lat: number, lng: number } | null
    }>({ start: null, end: null });
    const [currentLocation, setCurrentLocation] = useState<{ latitude: number; longitude: number } | null>(null);
    const [loading, setLoading] = useState<boolean>(false);

    // Filtered data based on search query and selected filter - memoized to prevent re-renders
    const filteredData = useMemo(() => {
        return hindranceData.filter((item: MarkerType) => {
            const query = searchQuery?.toLowerCase();
            const matchesSearch = (
                item.name?.toLowerCase().includes(query) ||
                item.details?.toLowerCase().includes(query) ||
                item.date?.toLowerCase().includes(query) ||
                item.progressQty?.toString().includes(query) ||
                item.manDays?.toString().includes(query)
            );

            if (!selectedFilter) return matchesSearch;

            switch (selectedFilter) {
                case 'Progress Update':
                    return matchesSearch && item.Progress_Update === "N";
                case 'New Gap':
                    return matchesSearch && item.New_Gap === "N";
                case 'Open Gap':
                    return matchesSearch && item.Open_Active !== "Y";
                case 'Closed Gap':
                    return matchesSearch && item.Open_Active === "Y";
                default:
                    return matchesSearch;
            }
        });
    }, [hindranceData, searchQuery, selectedFilter]);

    // Memoize engineer gap button to prevent re-creation
    const engineerGapButton = useMemo(() => {
        return (userRole.toLowerCase() === 'site engineer' || userRole.toLowerCase() === 'approver') ? ['Open Gap', 'Closed Gap'] : ['Progress Update', 'New Gap', 'Open Gap', 'Closed Gap'];
    }, [userRole]);

    // Handle search query changes - optimized to prevent infinite re-renders
    useEffect(() => {
        if (searchQuery.length === 0) {
            setSelectedMarker(null);
            setCardPosition({ x: 0, y: 0 });
            return;
        }
        
        if (filteredData.length > 0) {
            const matchedMarker = {
                ...filteredData[0],
                markerDetails: {
                    'Date': filteredData[0].date,
                    'Gap Length': filteredData[0].progressQty,
                    'Job Code': filteredData[0].Jobcode || 'N/A',
                    'Serial No': filteredData[0].Serialno?.toString() || 'N/A',
                    'Remarks': filteredData[0].Remarks || 'No remarks',
                    'Latitude': filteredData[0].latitude,
                    'Longitude': filteredData[0].longitude
                }
            };
            
            mapRef.current?.animateToRegion({
                latitude: matchedMarker.latitude,
                longitude: matchedMarker.longitude,
                latitudeDelta: 0.05,
                longitudeDelta: 0.05,
            });
            
            // Add debounce to prevent excessive updates
            const timeoutId = setTimeout(() => {
                if (mapRef.current && mapRef.current.pointForCoordinate) {
                    mapRef.current.pointForCoordinate({
                        latitude: matchedMarker.latitude,
                        longitude: matchedMarker.longitude,
                    }).then(point => {
                        setCardPosition({
                            x: point.x,
                            y: point.y + 40,
                        });
                        setSelectedMarker(matchedMarker);
                    }).catch(error => {
                        console.warn('Error getting coordinate for search result:', error);
                    });
                }
            }, 300); // Increased debounce time
            
            return () => clearTimeout(timeoutId);
        } else {
            setSelectedMarker(null);
            setCardPosition({ x: 0, y: 0 });
        }
    }, [searchQuery, filteredData.length]); // Optimized dependencies

    // Update info icon positions when filteredData or map region changes - optimized to prevent infinite re-renders
    useEffect(() => {
        const updatePositions = async () => {
            if (!mapRef.current) return;
            
            const newPositions: { [id: string]: { x: number, y: number } } = {};
            
            // Only process if we have data and user is Site Engineer
            if (filteredData.length === 0 || userRole !== 'Site Engineer') {
                setInfoIconPositions(newPositions);
                return;
            }

            try {
                for (const data of filteredData) {
                    if (data.Progress_Update === 'N') {
                        try {
                            const point = await mapRef.current.pointForCoordinate({
                                latitude: data.latitude,
                                longitude: data.longitude,
                            });
                            // Only add if within screen bounds
                            if (
                                point.x >= 0 && point.x <= windowWidth &&
                                point.y >= 0 && point.y <= windowHeight
                            ) {
                                newPositions[data.id] = point;
                            }
                        } catch (e) {
                            // ignore individual coordinate errors
                            console.warn('Error getting coordinate for marker:', data.id, e);
                        }
                    }
                }
                setInfoIconPositions(newPositions);
            } catch (error) {
                console.warn('Error updating info icon positions:', error);
                setInfoIconPositions({});
            }
        };
        
        // Add a small delay to prevent excessive updates
        const timeoutId = setTimeout(updatePositions, 100);
        
        return () => clearTimeout(timeoutId);
    }, [filteredData.length, userRole, windowWidth, windowHeight]); // Optimized dependencies

    // Get current location on mount
    useEffect(() => {
        const requestPermissionAndGetLocation = async () => {
            let hasPermission = true;
            if (Platform.OS === 'android') {
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
                );
                hasPermission = granted === PermissionsAndroid.RESULTS.GRANTED;
            }
            if (hasPermission) {
                Geolocation.getCurrentPosition(
                    (position) => {
                        const { latitude, longitude } = position.coords;
                        setCurrentLocation({ latitude, longitude });
                    },
                    (error) => {
                        console.warn(error.message);
                    },
                    { enableHighAccuracy: true, timeout: 20000, maximumAge: 1000 }
                );
            }
        };
        requestPermissionAndGetLocation();
    }, []);

    // const zoomToCurrentLocation = async () => {
    //     if (currentLocation && mapRef.current) {
    //         setLoading(true);
    //         try {
    //             await mapRef.current.animateToRegion(
    //                 {
    //                     latitude: currentLocation.latitude,
    //                     longitude: currentLocation.longitude,
    //                     latitudeDelta: 0.005,
    //                     longitudeDelta: 0.005,
    //                 },
    //                 1000
    //             );
    //         } catch (e) {
    //             console.warn('Error zooming to current location:', e);
    //         } finally {
    //             setLoading(false);
    //         }
    //     } else {
    //         // Try to get location again if not available
    //         setLoading(true);
    //         let hasPermission = true;
    //         const getAndZoom = async () => {
    //             if (Platform.OS === 'android') {
    //                 const granted = await PermissionsAndroid.request(
    //                     PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
    //                 );
    //                 hasPermission = granted === PermissionsAndroid.RESULTS.GRANTED;
    //             }
    //             if (hasPermission) {
    //                 Geolocation.getCurrentPosition(
    //                     (position) => {
    //                         const { latitude, longitude } = position.coords;
    //                         setCurrentLocation({ latitude, longitude });
    //                         if (mapRef.current) {
    //                             mapRef.current.animateToRegion(
    //                                 {
    //                                     latitude,
    //                                     longitude,
    //                                     latitudeDelta: 0.005,
    //                                     longitudeDelta: 0.005,
    //                                 },
    //                                 1000
    //                             );
    //                         }
    //                         setLoading(false);
    //                     },
    //                     (error) => {
    //                         console.warn(error.message);
    //                         setLoading(false);
    //                     },
    //                     { enableHighAccuracy: true, timeout: 20000, maximumAge: 1000 }
    //                 );
    //             } else {
    //                 setLoading(false);
    //             }
    //         };
    //         getAndZoom();
    //     }
    // };

    const handleMapPress = (event: { nativeEvent: { coordinate: { latitude: number; longitude: number } } }) => {
        setSelectedMarker(null);
        setShowAdminApproval(false);
        if (showCreatePopup) {
            const { latitude, longitude } = event.nativeEvent.coordinate;

            if (isSinglePoint) {
                // Single point selection
                setSelectedLocation({
                    latitude,
                    longitude,
                    address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
                    endLatitude: null,
                    endLongitude: null,
                    endAddress: null
                });
            } else {
                // Multi-point selection
                if (!selectedLocation) {
                    // First point (start)
                    setSelectedLocation({
                        latitude,
                        longitude,
                        address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
                        endLatitude: null,
                        endLongitude: null,
                        endAddress: null
                    });
                } else {
                    // Update second point (end)
                    setSelectedLocation({
                        ...selectedLocation,
                        endLatitude: latitude,
                        endLongitude: longitude,
                        endAddress: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`
                    });
                }
            }
        }
    };

    const handleMarkerPress = async (data: MarkerType) => {
        mapRef.current?.animateToRegion({
            latitude: data.latitude,
            longitude: data.longitude,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
        });
        setTimeout(async () => {
            if (mapRef.current && mapRef.current.pointForCoordinate) {
                const point = await mapRef.current.pointForCoordinate({
                    latitude: data.latitude,
                    longitude: data.longitude,
                });
                setCardPosition({
                    x: point.x,
                    y: point.y + 40,
                });
                const markerDetails = {
                    'Date': data.date,
                    'Gap Length': data.progressQty,
                    // 'Job Code': data.Jobcode || 'N/A',
                    // 'Serial No': data.Serialno?.toString() || 'N/A',
                    // 'Remarks': data.Remarks || 'No remarks',
                    ...(data.LGD_Latitude_End && data.LGD_Longitude_End ? {
                        'Start Latitude': data.latitude,
                        'Start Longitude': data.longitude,
                        'End Latitude': data.LGD_Latitude_End,
                        'End Longitude': data.LGD_Longitude_End
                    } : {
                        'Latitude': data.latitude,
                        'Longitude': data.longitude
                    })
                };
                setSelectedMarker({
                    ...data,
                    markerDetails
                });
            }
        }, 1000);
    };
// navigation.navigate('HindranceMapView')
    const handleInfoPress = async (data: MarkerType) => {
        mapRef.current?.animateToRegion({
            latitude: data.latitude,
            longitude: data.longitude,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
        });
        setTimeout(async () => {
            if (mapRef.current && mapRef.current.pointForCoordinate) {
                const point = await mapRef.current.pointForCoordinate({
                    latitude: data.latitude,
                    longitude: data.longitude,
                });
                setCardPosition({
                    x: point.x,
                    y: point.y + 40,
                });
                setShowAdminApproval(true);
            }
        }, 500);
    };

    const handleSinglePoint = useCallback(() => {
        setShowCreatePopup(true);
        setIsSinglePoint(true);
    }, []);

    const handleMultiPoint = useCallback(() => {
        setShowCreatePopup(true);
        setIsSinglePoint(false);
    }, []);

    const handleCreateHindrance = useCallback((_location: any) => {
        setShowCreatePopup(false);
        setIsSinglePoint(false);
        setSelectedLocation(null);

        // For UI testing - navigate immediately based on pointer type
        // You can uncomment the location logic later when implementing the full functionality
        // let selectedLocation = {
        //     latitude: 13.0827, // Default coordinates for testing
        //     longitude: 80.2707,
        //     address: 'Test Location',
        //     endLatitude: null,
        //     endLongitude: null,
        //     endAddress: null
        // };

        // Uncomment this section when you want to use actual location data
        /*
        if (isSinglePoint && location) {
            selectedLocation = {
                latitude: location.latitude,
                longitude: location.longitude,
                address: location.address || '',
                endLatitude: null,
                endLongitude: null,
                endAddress: null
            };
        } else if (!isSinglePoint && location) {
            selectedLocation = {
                latitude: location.latitude,
                longitude: location.longitude,
                address: location.address || '',
                endLatitude: location.endLatitude,
                endLongitude: location.endLongitude,
                endAddress: location.endAddress || '',
            };
        }
        */

        console.log('Navigating to CreateHindranceUpdate with:', {
            isSinglePointer: isSinglePoint,
            selectedLocation
        });

        // navigation.navigate('CreateHindranceUpdate', {
        //     isSinglePointer: isSinglePoint,
        //     selectedLocation,
        // });
            navigation.replace('HindranceCreation', { isSinglePointer: isSinglePoint, selectedLocation: selectedLocation })
    }, [isSinglePoint, selectedLocation, navigation]);

    const handleCancelCreateHindrance = useCallback(() => {
        setShowCreatePopup(false);
        setIsSinglePoint(false);
        setSelectedLocation(null);
    }, []);

    // Add handler for place changes from CreateHindrance component
    const handlePlaceChange = (data: any) => {
        console.log('handlePlaceChange called with data:', data);
        
        if (data.single) {
            // Single point search
            console.log('Single point data:', data.single);
            if (data.single.lat !== 0 && data.single.lng !== 0) {
                console.log('Setting single location:', data.single);
                setSelectedLocation({
                    latitude: data.single.lat,
                    longitude: data.single.lng,
                    address: data.single.name,
                    endLatitude: null,
                    endLongitude: null,
                    endAddress: null
                });
                // Animate map to the searched location
                mapRef.current?.animateToRegion({
                    latitude: data.single.lat,
                    longitude: data.single.lng,
                    latitudeDelta: 0.01,
                    longitudeDelta: 0.01,
                });
            } else {
                // Clear location if coordinates are invalid
                console.log('Invalid single coordinates, clearing location');
                setSelectedLocation(null);
            }
        } else if (data.double) {
            // Double point search
            console.log('Double point data:', data.double);
            if (data.double.start && data.double.end && 
                data.double.start.lat !== 0 && data.double.start.lng !== 0 &&
                data.double.end.lat !== 0 && data.double.end.lng !== 0) {
                // Both start and end points are available and valid
                console.log('Setting double location with both points');
                setSelectedLocation({
                    latitude: data.double.start.lat,
                    longitude: data.double.start.lng,
                    address: data.double.start.name,
                    endLatitude: data.double.end.lat,
                    endLongitude: data.double.end.lng,
                    endAddress: data.double.end.name
                });
                // Animate map to show both points
                const midLat = (data.double.start.lat + data.double.end.lat) / 2;
                const midLng = (data.double.start.lng + data.double.end.lng) / 2;
                const latDelta = Math.abs(data.double.start.lat - data.double.end.lat) * 1.5;
                const lngDelta = Math.abs(data.double.start.lng - data.double.end.lng) * 1.5;
                mapRef.current?.animateToRegion({
                    latitude: midLat,
                    longitude: midLng,
                    latitudeDelta: Math.max(latDelta, 0.01),
                    longitudeDelta: Math.max(lngDelta, 0.01),
                });
            } else if (data.double.start && data.double.start.lat !== 0 && data.double.start.lng !== 0) {
                // Only start point is available and valid
                console.log('Setting double location with start point only');
                setSelectedLocation({
                    latitude: data.double.start.lat,
                    longitude: data.double.start.lng,
                    address: data.double.start.name,
                    endLatitude: null,
                    endLongitude: null,
                    endAddress: null
                });
                mapRef.current?.animateToRegion({
                    latitude: data.double.start.lat,
                    longitude: data.double.start.lng,
                    latitudeDelta: 0.01,
                    longitudeDelta: 0.01,
                });
            } else if (data.double.end && data.double.end.lat !== 0 && data.double.end.lng !== 0) {
                // Only end point is available and valid, update existing start point if available
                console.log('Setting double location with end point only');
                setSelectedLocation(prev => prev ? {
                    ...prev,
                    endLatitude: data.double.end.lat,
                    endLongitude: data.double.end.lng,
                    endAddress: data.double.end.name
                } : {
                    latitude: data.double.end.lat,
                    longitude: data.double.end.lng,
                    address: data.double.end.name,
                    endLatitude: null,
                    endLongitude: null,
                    endAddress: null
                });
                mapRef.current?.animateToRegion({
                    latitude: data.double.end.lat,
                    longitude: data.double.end.lng,
                    latitudeDelta: 0.01,
                    longitudeDelta: 0.01,
                });
            } else {
                // Clear location if no valid coordinates
                console.log('No valid double coordinates, clearing location');
                setSelectedLocation(null);
            }
        }
    };

    // Add this function to handle navigation to the update screen with selectedItem
    const handleNavigation = useCallback((selectedItem: MarkerType) => {
        navigation.replace('HindranceUpdate', { selectedItem });
    }, [navigation]);

    // Helper function to determine marker icon
    const getMarkerIcon = (data: MarkerType, isMultiPoint: boolean, isFirstPoint: boolean = true) => {
        const iconSize = 40;
        const dotSize = 10;
        const infoSize = 40;

        const renderMainIcon = () => {
            if (data.New_Gap === "N") {
                return isMultiPoint ?
                    <NewGapMultiPointer style={{top: Platform.OS === 'ios' ? -ms(15) : 0, left: Platform.OS === 'ios' ? -ms(17) : 0  }} width={iconSize} height={iconSize} /> :
                    <NewGapSinglePointer style={{top: Platform.OS === 'ios' ? -ms(15) : 0, left: Platform.OS === 'ios' ? -ms(17) : 0  }} width={iconSize} height={iconSize} />;
            }

            if (isMultiPoint) {
                return data.Open_Active === "Y" ?
                    <ClosedGap style={{top: Platform.OS === 'ios' ? -ms(16) : 0, left: Platform.OS === 'ios' ? -ms(16) : 0  }} width={iconSize} height={iconSize} /> :
                    <OpenGap style={{top: Platform.OS === 'ios' ? -ms(16) : 0, left: Platform.OS === 'ios' ? -ms(16) : 0  }} width={iconSize} height={iconSize} />;
            }

            return <SinglePointer width={iconSize} height={iconSize} />;
        };

        const renderDotIcon = () => {
            if (data.Progress_Update === "N") {
                // Only show Info icon on first point for multipoint markers
                if (isMultiPoint && !isFirstPoint && userRole == "Site Engineer") {
                    return null;
                }
                return userRole !== "Site Engineer" && < ProgressUpdate width={dotSize} height={dotSize} />
            }
            return null;
        };

        return (
            <View style={[styles.markerContainer, {
                width: userRole !== "Site Engineer" ? ms(40) : ms(70)
            }]}>
                {renderMainIcon()}
                {renderDotIcon() && (
                    <View style={[
                        styles.dotContainer,
                        userRole !== "Site Engineer" && styles.infoContainer
                    ]}>
                        {renderDotIcon()}
                    </View>
                )}
            </View>
        );
    };

    // Helper function to determine filter icon
    const getFilterIcon = (filterType: string) => {
        const iconSize = 18;
        const progressUpdateIconSize = 10;

        switch (filterType) {
            case 'Progress Update':
                return <ProgressUpdate width={progressUpdateIconSize} height={progressUpdateIconSize} />;
            case 'New Gap':
                return <NewGapMultiPointer width={iconSize} height={iconSize} />;
            case 'Open Gap':
                return <OpenGap width={iconSize} height={iconSize} />;
            case 'Closed Gap':
                return <ClosedGap width={iconSize} height={iconSize} />;
            default:
                return null;
        }
    };

    const zoomToCurrentLocation = async () => {
        if (currentLocation && mapRef.current) {
            setLoading(true);
            try {
                await mapRef.current.animateToRegion(
                    {
                        latitude: currentLocation.latitude,
                        longitude: currentLocation.longitude,
                        latitudeDelta: 0.005,
                        longitudeDelta: 0.005,
                    },
                    1000
                );
            } catch (e) {
                console.warn('Error zooming to current location:', e);
            } finally {
                setLoading(false);
            }
        } else {
            // Try to get location again if not available
            setLoading(true);
            let hasPermission = true;
            const getAndZoom = async () => {
                if (Platform.OS === 'android') {
                    const granted = await PermissionsAndroid.request(
                        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
                    );
                    hasPermission = granted === PermissionsAndroid.RESULTS.GRANTED;
                }
                if (hasPermission) {
                    Geolocation.getCurrentPosition(
                        (position) => {
                            const { latitude, longitude } = position.coords;
                            setCurrentLocation({ latitude, longitude });
                            if (mapRef.current) {
                                mapRef.current.animateToRegion(
                                    {
                                        latitude,
                                        longitude,
                                        latitudeDelta: 0.005,
                                        longitudeDelta: 0.005,
                                    },
                                    1000
                                );
                            }
                            setLoading(false);
                        },
                        (error) => {
                            console.warn(error.message);
                            setLoading(false);
                        },
                        { enableHighAccuracy: true, timeout: 20000, maximumAge: 1000 }
                    );
                } else {
                    setLoading(false);
                }
            };
            getAndZoom();
        }
    };


    return (
        <View style={styles.mapContainer}>
            <MapView
                ref={mapRef}
                style={styles.map}
                initialRegion={{
                    latitude: filteredData.length > 0 ? filteredData[0].latitude : 28.6139,
                    longitude: filteredData.length > 0 ? filteredData[0].longitude : 77.209,
                    latitudeDelta: 0.5,
                    longitudeDelta: 0.5,
                }}
                onPress={handleMapPress}
                onRegionChangeComplete={() => {
                    if (selectedMarker) {
                        setSelectedMarker(null);
                    }
                    setShowAdminApproval(false);
                }}
            >
                {/* Current location marker */}
                {currentLocation && (
                    <Marker coordinate={currentLocation}>
                        {loading ? (
                            <View style={{
                                width: ms(16),
                                height: ms(16),
                                borderRadius: ms(8),
                                backgroundColor: Colors.textLightGray,
                                borderWidth: 2,
                                borderColor: Colors.white,
                                justifyContent: 'center',
                                alignItems: 'center',
                            }}>
                                <View style={{
                                    width: ms(8),
                                    height: ms(8),
                                    borderRadius: ms(4),
                                    backgroundColor: Colors.white,
                                }} />
                            </View>
                        ) : (
                            <Ellipse width={ms(20)} height={ms(20)} />
                        )}
                    </Marker>
                )}
                {filteredData.map((data) => (
                    data.LGD_Latitude_End && data.LGD_Longitude_End ? (
                        <React.Fragment key={data.id}>
                            <Marker
                                coordinate={{
                                    latitude: data.latitude,
                                    longitude: data.longitude,
                                }}
                                onPress={() => handleMarkerPress(data)}
                            >
                                {getMarkerIcon(data, true, true)}
                            </Marker>
                            <Marker
                                coordinate={{
                                    latitude: data.LGD_Latitude_End,
                                    longitude: data.LGD_Longitude_End,
                                }}
                                onPress={() => handleMarkerPress(data)}
                            >
                                {getMarkerIcon(data, true, false)}
                            </Marker>

                            <ProgressPolyline
                                start={{
                                    latitude: data.latitude,
                                    longitude: data.longitude
                                }}
                                end={{
                                    latitude: data.LGD_Latitude_End,
                                    longitude: data.LGD_Longitude_End
                                }}
                                totalLength={data.GAP_Length || 0}
                                completedLength={data.GapLength_Updated || 0}
                            />
                        </React.Fragment>
                    ) : (
                        <>
                            <Marker
                                key={data.id}
                                coordinate={{
                                    latitude: data.latitude,
                                    longitude: data.longitude,
                                }}
                                onPress={() => handleMarkerPress(data)}
                            >
                                {getMarkerIcon(data, false, true)}
                            </Marker>
                        </>
                    )
                ))}

                {selectedLocation && (
                    <>
                        <Marker
                            coordinate={{
                                latitude: selectedLocation.latitude,
                                longitude: selectedLocation.longitude
                            }}
                            title="Start Point"
                        >
                            {isSinglePoint ? <NewGapSinglePointer width={40} height={40} /> : <NewGap style={{top: Platform.OS === 'ios' ? -ms(17) : 0, left: Platform.OS === 'ios' ? -ms(1) : 0  }}  width={40} height={40} />}
                        </Marker>
                        {!isSinglePoint && selectedLocation.endLatitude && selectedLocation.endLongitude && (
                            <>
                                <Marker
                                    coordinate={{
                                        latitude: selectedLocation.endLatitude,
                                        longitude: selectedLocation.endLongitude
                                    }}
                                    title="End Point"
                                >
                                    <NewGap style={{top: Platform.OS === 'ios' ? -ms(15) : 0, left: Platform.OS === 'ios' ? -ms(1) : 0  }} width={40} height={40} />
                                </Marker>
                                <Polyline
                                    coordinates={[
                                        {
                                            latitude: selectedLocation.latitude,
                                            longitude: selectedLocation.longitude
                                        },
                                        {
                                            latitude: selectedLocation.endLatitude,
                                            longitude: selectedLocation.endLongitude
                                        }
                                    ]}
                                    strokeColor={Colors.mapStrokeLine}
                                    strokeWidth={3}
                                />
                            </>
                        )}
                    </>
                )}
            </MapView>

            {/* Overlay Info icons it's need to be enable in future as of now this progress not yet done from the client side */}
            {/* {Object.entries(infoIconPositions).map(([id, point]) => (
                <Pressable
                    key={id}
                    style={{
                        position: 'absolute',
                        left: point.x + 5,
                        top: point.y - 70,
                        zIndex: 999,
                    }}
                    onPress={() => {
                        const marker = filteredData.find(d => d.id === id);
                        if (marker) handleInfoPress(marker);
                    }}
                >
                    <Info width={ms(40)} height={ms(50)} />
                </Pressable>
            ))} */}

            <View style={styles.searchContainer}>
                <SearchBar
                    onChange={setSearchQuery}
                    placeholder="Search location"
                    customStyle={{marginHorizontal: 0}}
                    value={searchQuery}
                />
            </View>

            <View style={styles.filterContainer}>
                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.gapContainer}
                >
                    {engineerGapButton.map(item => (
                        <GapButton
                            key={item}
                            label={item}
                            icon={getFilterIcon(item)}
                            onPress={() => setSelectedFilter(selectedFilter === item ? '' : item)}
                            isSelected={selectedFilter === item}
                        />
                    ))}
                </ScrollView>
            </View>

            <Pressable
                style={[styles.navIcon, styles.leftNav]}
                onPress={zoomToCurrentLocation}
            >
                <Gps width={ms(30)} height={ms(30)} />
            </Pressable>

            <AddPointSelector
                onSinglePoint={handleSinglePoint}
                onMultiPoint={handleMultiPoint}
            />


            {selectedMarker && cardPosition.x !== 0 && cardPosition.y !== 0 && (
                <View
                    style={[
                        styles.customCardWrapper,
                        {
                            position: 'absolute',
                            left: cardPosition.x - ms(150),
                            top: cardPosition.y - ms(35),
                            zIndex: 100,
                        },
                    ]}
                >
                    <View style={styles.triangle} />
                    <View style={styles.customCard}>
                        {
                            selectedMarker?.markerDetails && Object.entries(selectedMarker.markerDetails).map(([label, value], index) => (
                                <DataText key={index} label={label} value={value} />
                            ))
                        }
                        <Pressable style={styles.updateBtn} onPress={() => handleNavigation(selectedMarker)}>
                            <Text style={styles.updateTxt}>{t('hindranceStrings.update')}</Text>
                        </Pressable>
                    </View>
                </View>
            )}

            {showAdminApproval && cardPosition.x !== 0 && cardPosition.y !== 0 && (
                <View
                    style={[
                        styles.customCardWrapper,
                        {
                            position: 'absolute',
                            left: cardPosition.x - ms(150),
                            top: cardPosition.y - ms(30),
                            zIndex: 100,
                        },
                    ]}
                >
                    <View style={styles.triangle} />
                    <View style={styles.infoCard}>
                        <Text style={styles.adminApprovalText}>{t('hindranceStrings.adminApproval')}</Text>
                    </View>
                </View>
            )}

            {showCreatePopup && (
                <View>
                    <View style={{ position: 'absolute', top: ms(-60), right: ms(5), zIndex: 1000 }}>
                        <DeleteButton onPress={handleCancelCreateHindrance} />
                    </View>
                    {/* <CreateHindrance
                        isVisible={showCreatePopup}
                        isSinglePoint={isSinglePoint}
                        confirmLabel={t('hindranceStrings.createHindrance')}
                        cancelLabel={t('commonStrings.cancel')}
                        onConfirm={handleCreateHindrance}
                        onCancel={handleCancelCreateHindrance}
                        onClose={() => {
                            setShowCreatePopup(false);
                            setSelectedLocation(null);
                        }}
                        selectedLocation={selectedLocation}
                        lastSinglePlace={lastSinglePlace}
                        lastDoublePlaces={lastDoublePlaces}
                        onPlaceChange={handlePlaceChange}
                    /> */}
                    <CreateHindrance
                        isVisible={showCreatePopup}
                        isSinglePoint={isSinglePoint}
                        confirmLabel={t('hindranceStrings.createHindrance')}
                        cancelLabel={t('commonStrings.cancel')}
                        onConfirm={handleCreateHindrance}
                        onCancel={handleCancelCreateHindrance}
                        onClose={() => {
                            setShowCreatePopup(false);
                            setSelectedLocation(null);
                        }}
                        selectedLocation={selectedLocation}
                    />
                </View>
            )
            }

            {loading && (
                <LoadingOverlay visible={loading} />
            )}
        </View >
    )
}

export default MapViewComponent;

const styles = StyleSheet.create({
    mapContainer: {
        flex: 1,
        position: 'relative',
    },
    map: {
        flex: 1,
        width: width,
        height: height,
    },
    searchContainer: {
        position: 'absolute',
        left: ms(10),
        right: ms(10),
        top: ms(10),
        zIndex: 10,
    },
    filterContainer: {
        position: 'absolute',
        top: ms(60),
        zIndex: 10,
        borderRadius: ms(8),
    },
    navIcon: {
        position: 'absolute',
        top: '90%',
        backgroundColor: Colors.white,
        padding: 10,
        borderRadius: 25,
        zIndex: 15,
    },
    leftNav: {
        left: 20,
        transform: [{ translateY: -25 }],
    },
    customCardWrapper: {
        alignItems: 'center',
        width: ms(300),
    },
    triangle: {
        width: 24,
        height: 12,
        backgroundColor: 'transparent',
        borderStyle: 'solid',
        borderLeftWidth: 12,
        borderRightWidth: 12,
        borderBottomWidth: 12,
        borderLeftColor: 'transparent',
        borderRightColor: 'transparent',
        borderBottomColor: '#fff',
        marginBottom: -1,
    },
    customCard: {
        width: ms(300),
        backgroundColor: Colors.white,
        borderRadius: ms(6),
        paddingHorizontal: ms(15),
        paddingVertical: ms(10),
        elevation: 8,
        shadowColor: Colors.white,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    infoCard: {
        width: ms(200),
        backgroundColor: Colors.white,
        borderRadius: ms(6),
        paddingTop: ms(10),
        elevation: 5,
        shadowColor: Colors.white,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    container: {
        position: 'absolute',
        bottom: ms(40),
        right: ms(20),
        backgroundColor: Colors.white,
        borderRadius: ms(40),
        alignItems: 'center',
        paddingVertical: ms(5),
        paddingHorizontal: ms(5),
        elevation: 10,
        shadowColor: Colors.black,
        shadowOpacity: 0.2,
        shadowRadius: 10,
        shadowOffset: { width: 0, height: 4 },
        zIndex: 20,
    },
    label: {
        fontFamily: AppFonts.Medium,
        fontSize: ms(12),
        color: Colors.primary,
    },
    iconButton: {
        width: ms(40),
        height: ms(40),
        borderRadius: ms(32),
        alignItems: 'center',
        justifyContent: 'center',
    },
    messageContainer: {
        alignItems: 'center',
        marginVertical: ms(10),
    },
    gapContainer: {
        flexDirection: 'row',
        paddingLeft: ms(15),
        paddingVertical: ms(5),
    },
    detailsContainer: {
        flexDirection: 'row',
        paddingVertical: ms(5),
    },
    labelTxt: {
        fontFamily: AppFonts.SemiBold,
        fontSize: ms(14),
        color: Colors.textSecondary,
        width: '50%',
    },
    valueTxt: {
        fontFamily: AppFonts.SemiBold,
        fontSize: ms(14),
        color: Colors.textPrimary,
        width: '50%',
    },
    updateBtn: {
        borderWidth: 1,
        marginTop: ms(10),
        paddingVertical: ms(5),
        borderRadius: ms(4),
        borderColor: Colors.secondary,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: ms(5)
    },
    updateTxt: {
        fontFamily: AppFonts.Medium,
        fontSize: ms(14),
        color: Colors.secondary,
        textAlign: 'center'
    },
    markerContainer: {
        position: 'relative',
        // height: ms(40),
        alignItems: 'center',
        justifyContent: 'center',
        // borderWidth: 1,
    },
    dotContainer: {
        position: 'absolute',
        top: 0,
        right: -8,
        zIndex: 1,
        width: ms(26),
        height: ms(10),
        alignItems: 'center',
        justifyContent: 'center',
    },
    infoContainer: {
        width: ms(40),
        height: ms(40),
        top: -5,
        right: 15,
        zIndex: 999,
    },
    adminApprovalText: {
        fontFamily: 'MNMedium',
        fontSize: ms(16),
        color: Colors.textPrimary,
        textAlign: 'center',
        marginBottom: ms(10),
    },
    closeButton: {
        borderWidth: 1,
        paddingVertical: ms(5),
        borderRadius: ms(4),
        borderColor: Colors.secondary,
        alignItems: 'center',
        justifyContent: 'center'
    },
    closeButtonText: {
        fontFamily: 'MNMedium',
        fontSize: ms(14),
        color: Colors.secondary,
        textAlign: 'center'
    },
    deleteButtonContainer: {
        position: 'absolute',
        top: -ms(20),
        right: ms(20),
        zIndex: 10000,
    },
    infoButtonContainer: {
        position: 'absolute',
        right: ms(20),
        top: ms(20),
        zIndex: 1000,
    },
})