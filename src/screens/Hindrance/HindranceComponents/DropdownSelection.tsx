import React from 'react';
import CHECKED from '../../../assets/svg/checked_grey.svg';
import UNCHECKED from '../../../assets/svg/unchecked_grey.svg';
import { Pressable, StyleSheet, Text } from 'react-native';
import { ms } from '../../../utils/Scale/Scaling';
import Colors from '../../../utils/Colors/Colors';

interface DropdownOptionProps {
    option: string;
    isSelected: boolean;
    onSelect: (type: string) => void;
}

const DropDownSelection: React.FC<DropdownOptionProps> = ({ option, isSelected, onSelect }) => {

    return (
        <Pressable style={styles.optionContainer} onPress={() => onSelect(option)}>
            <Text style={styles.optionText}>{option}</Text>
            {isSelected ? (
                <CHECKED />
            ) : (
                <UNCHECKED />
            )}
        </Pressable>
    );
};

export default DropDownSelection;

const styles = StyleSheet.create({
    optionContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
        paddingVertical: ms(15),
       borderBottomColor: Colors.bgSecondaryLightBlue,
       borderBottomWidth: ms(3)
    },
    optionText: {
        fontSize: ms(16),
        color: Colors.black,
        width: '90%'
    },
})