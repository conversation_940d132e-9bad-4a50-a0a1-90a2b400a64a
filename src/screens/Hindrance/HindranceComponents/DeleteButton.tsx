import React from 'react';
import { Pressable, View, Text, StyleSheet } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Colors from '../../../utils/Colors/Colors';
import { ms } from '../../../utils/Scale/Scaling';

interface DeleteButtonProps {
    onPress: () => void;
}

const DeleteButton: React.FC<DeleteButtonProps> = ({ onPress }) => (
    <Pressable style={styles.container} onPress={onPress}>
        <View style={styles.iconWrapper}>
            <Ionicons name="trash-outline" size={ms(18)} color="#FF5A36" />
        </View>
        <Text style={styles.text}>Delete</Text>
    </Pressable>
);

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: ms(24),
        borderWidth: ms(1),
        borderColor: Colors.offlineRed,
        backgroundColor: Colors.palePink,
        paddingHorizontal: ms(8),
        paddingVertical: ms(5),
        alignSelf: 'flex-end',
        margin: ms(12),
    },
    iconWrapper: {
        marginRight: ms(6),
    },
    text: {
        color: Colors.offlineRed,
        fontSize: ms(16),
        fontFamily: 'MNSemiBold',
    },
});

export default DeleteButton; 