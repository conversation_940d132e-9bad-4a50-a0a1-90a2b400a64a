import React, { useState } from 'react';
import { View, ScrollView, Pressable, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRoute, useNavigation } from '@react-navigation/native';
import AppHeader from '../../components/AppHeader';
import DailyProgressTextInput from '../DailyProgress/components/DailyProgressTextInput';
import Colors from '../../utils/Colors/Colors';
import { ms } from '../../utils/Scale/Scaling';
import Icon from 'react-native-vector-icons/Ionicons';
import ButtonComponent from '../../components/ButtonComponent';
import CustomDropdownPicker from '../../components/DropDownPicker';
import AttachmentComponent from '../../components/Attachment';
import MapView, { Marker, Polyline } from 'react-native-maps';

interface RouteParams {
  isSinglePointer: boolean;
  selectedLocation: {
    latitude: number;
    longitude: number;
    address: string;
    endLatitude?: number;
    endLongitude?: number;
    endAddress?: string;
  } | null;
}

// Dummy data for dropdowns (replace with real data if available)
const pipeMaterialOptions = [
  { label: 'DI 100mm', value: 'DI 100mm' },
  { label: 'DI 200mm', value: 'DI 200mm' },
  { label: 'MS 300mm', value: 'MS 300mm' },
];
const gapReasonOptions = [
  { label: 'Material Shortage', value: 'Material Shortage' },
  { label: 'Site Issue', value: 'Site Issue' },
  { label: 'Other', value: 'Other' },
];

const CreateHindranceUpdate = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { isSinglePointer, selectedLocation } = route.params as RouteParams;

  // Debug logs
  console.log('CreateHindranceUpdate - isSinglePointer:', isSinglePointer);
  console.log('CreateHindranceUpdate - selectedLocation:', selectedLocation);

  // State for form fields
  const [date] = useState(new Date().toISOString().split('T')[0]);
  const [pipeMaterial, setPipeMaterial] = useState('');
  const [gapReason, setGapReason] = useState('');
  const [gapLength, setGapLength] = useState('');
  const [latitude, setLatitude] = useState(selectedLocation?.latitude?.toString() || '');
  const [longitude, setLongitude] = useState(selectedLocation?.longitude?.toString() || '');
  const [endLatitude, setEndLatitude] = useState(selectedLocation?.endLatitude?.toString() || '');
  const [endLongitude, setEndLongitude] = useState(selectedLocation?.endLongitude?.toString() || '');
  const [startEndNode, setStartEndNode] = useState('');
  const [remarks, setRemarks] = useState('');

  // Map region setup
  const region = {
    latitude: Number(latitude) || 13.0827,
    longitude: Number(longitude) || 80.2707,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: Colors.white }}>
      <AppHeader
        title="Hindrance"
        onBackPress={() => navigation.goBack()}
      />
        <View style={{
                      flex: 1,
                    
                  }}>
      
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ padding: ms(16) }}
        showsVerticalScrollIndicator={false}
      >
        {/* Date */}
        <View style={{ marginBottom: ms(12) }}>
          <Text style={{ color: Colors.textSecondary, marginBottom: ms(4) }}>Date *</Text>
          <Pressable style={{ flexDirection: 'row', alignItems: 'center', backgroundColor: Colors.containerligetBlue, borderRadius: ms(5), padding: ms(12), borderWidth: ms(0.9), borderColor: Colors.inputBorder }}>
            <Text style={{ flex: 1, color: Colors.pipeIdTextBlack, fontSize: ms(13) }}>{date}</Text>
            <Icon name="calendar-outline" size={ms(20)} color={Colors.pipeIdTextBlack} />
          </Pressable>
        </View>
        {/* Pipe Material Dropdown */}
        <CustomDropdownPicker
          title="Pipe Material and Dia *"
          data={pipeMaterialOptions}
          defaultValue={pipeMaterial}
          onSelect={item => setPipeMaterial(item.value)}
          placeHolder="Select"
          mainContainerStyle={{ marginBottom: ms(12) }}
        />
        {/* Gap Reason Dropdown */}
        <CustomDropdownPicker
          title="Reason for Gap *"
          data={gapReasonOptions}
          defaultValue={gapReason}
          onSelect={item => setGapReason(item.value)}
          placeHolder="Select"
          mainContainerStyle={{ marginBottom: ms(12) }}
        />
        {/* Gap Length */}
        <DailyProgressTextInput
          label="Gap Length *"
          value={gapLength}
          onChangeText={setGapLength}
          isKeypadNumeric={true}
          customStyle={{ 
            color: "#33485E",
            backgroundColor: Colors.containerligetBlue,
            borderColor: Colors.searchBorderGrey,
            borderWidth: ms(1),
            borderRadius: ms(8)
          }}
        />
        {/* Lat/Lng fields */}
        {isSinglePointer ? (
          <>
            <DailyProgressTextInput
              label="Latitude *"
              value={latitude}
              onChangeText={setLatitude}
              isKeypadNumeric={true}
            />
            <DailyProgressTextInput
              label="Longitude *"
              value={longitude}
              onChangeText={setLongitude}
              isKeypadNumeric={true}
            />
          </>
        ) : (
          <>
            <DailyProgressTextInput
              label="Start Latitude *"
              value={latitude}
              onChangeText={setLatitude}
              isKeypadNumeric={true}
            />
            <DailyProgressTextInput
              label="Start Longitude *"
              value={longitude}
              onChangeText={setLongitude}
              isKeypadNumeric={true}
            />
            <DailyProgressTextInput
              label="End Latitude *"
              value={endLatitude}
              onChangeText={setEndLatitude}
              isKeypadNumeric={true}
            />
            <DailyProgressTextInput
              label="End Longitude *"
              value={endLongitude}
              onChangeText={setEndLongitude}
              isKeypadNumeric={true}
            />
          </>
        )}
        {/* Map View */}
        <View style={{ marginVertical: ms(12) }}>
          <Text style={{ color: Colors.textSecondary, marginBottom: ms(4) }}>Map View *</Text>
          <View style={{ height: ms(180), borderRadius: ms(8), overflow: 'hidden' }}>
            <MapView
              style={{ flex: 1 }}
              region={region}
              scrollEnabled={false}
              zoomEnabled={false}
              pitchEnabled={false}
              rotateEnabled={false}
            >
              {/* Single or double pointer markers */}
              {isSinglePointer ? (
                <Marker
                  coordinate={{ latitude: Number(latitude) || 0, longitude: Number(longitude) || 0 }}
                  title="Location"
                />
              ) : (
                <>
                  <Marker
                    coordinate={{ latitude: Number(latitude) || 0, longitude: Number(longitude) || 0 }}
                    title="Start"
                  />
                  <Marker
                    coordinate={{ latitude: Number(endLatitude) || 0, longitude: Number(endLongitude) || 0 }}
                    title="End"
                  />
                  <Polyline
                    coordinates={[
                      { latitude: Number(latitude) || 0, longitude: Number(longitude) || 0 },
                      { latitude: Number(endLatitude) || 0, longitude: Number(endLongitude) || 0 },
                    ]}
                    strokeColor={Colors.primary}
                    strokeWidth={3}
                  />
                </>
              )}
            </MapView>
            {/* Expand to full map icon */}
            <Pressable
              style={{
                position: 'absolute',
                bottom: ms(8),
                right: ms(8),
                backgroundColor: Colors.white,
                borderRadius: ms(20),
                padding: ms(6),
                elevation: 3,
                zIndex: 10,
              }}
              onPress={() => {
                navigation.navigate('HindranceMapView', {
                  latitude: Number(latitude) || 0,
                  longitude: Number(longitude) || 0,
                  endLatitude: Number(endLatitude) || 0,
                  endLongitude: Number(endLongitude) || 0,
                  isSinglePointer,
                });
              }}
            >
              <Icon name="expand" size={ms(22)} color={Colors.textPrimary} />
            </Pressable>
          </View>
        </View>
        {/* Start & End Node */}
        <DailyProgressTextInput
          label="Start & End Node"
          value={startEndNode}
          onChangeText={setStartEndNode}
        />
        {/* Remarks */}
        <DailyProgressTextInput
          label="Remarks *"
          value={remarks}
          onChangeText={setRemarks}
          isMultiline={true}
        />
        {/* Image Upload */}
        <AttachmentComponent />
        {/* Create Button */}
      
       
      </ScrollView>
      </View>
      <ButtonComponent
          title="Create"
          onPress={() => {
          }}

        />
    </SafeAreaView>
  );
};

export default CreateHindranceUpdate; 