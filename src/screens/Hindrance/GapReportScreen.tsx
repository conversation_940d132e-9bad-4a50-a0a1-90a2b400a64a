import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet, FlatList, SafeAreaView, TouchableOpacity } from 'react-native';
import Colors from '../../utils/Colors/Colors';
import { ms } from '../../utils/Scale/Scaling';
import { useNavigation, useRoute } from '@react-navigation/native';
import AppHeader from '../../components/AppHeader';
import CardView from '../../components/CardView';
import FilterIcon from '../../assets/svg/filter.svg';
import BottomPopupCalendar from '../../components/CalendarPicker/BottomPopupCalendar';

// Gap report data now with full date (DD/MM/YYYY)
const gapReportData = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON>jeeth <PERSON>',
    code: '297853',
    openGap: 12,
    openLen: 5.5,
    closedGap: 10,
    closedLen: 4.5,
    date: '11/05/2025',
  },
  {
    id: '2',
    name: '<PERSON> <PERSON>',
    code: '298754',
    openGap: 14,
    closedGap: 9,
    date: '15/06/2025',
  },
  {
    id: '3',
    name: '<PERSON><PERSON><PERSON> K',
    code: '295672',
    openGap: 15,
    closedGap: 11,
    date: '20/07/2025',
  },
  {
    id: '4',
    name: 'Nina P',
    code: '299823',
    openGap: 10,
    closedGap: 12,
    date: '05/08/2025',
  },
  {
    id: '5',
    name: 'Elena J',
    code: '296783',
    openGap: 13,
    closedGap: 8,
    date: '12/09/2025',
  },
  {
    id: '6',
    name: 'Samir L',
    code: '293214',
    openGap: 11,
    openLen: 12,
    closedGap: 10,
    closedLen: 4.5,
    date: '18/09/2025',
  },
  {
    id: '7',
    name: 'Tanya V',
    code: '294568',
    openGap: 12,
    openLen: 5.5,
    closedGap: 10,
    closedLen: 4.5,
    date: '25/10/2025',
  },
  {
    id: '8',
    name: 'Jasper W',
    code: '292456',
    openGap: 15,
    closedGap: 7,
    date: '30/10/2025',
  },
];

function parseDate(dateStr: string) {
  // dateStr: 'DD/MM/YYYY'
  const [day, month, year] = dateStr.split('/').map(Number);
  return new Date(year, month - 1, day);
}

function formatHeaderDate(dateStr: string) {
  // Returns 'MM/DD' for header
  if (!dateStr) return '';
  const [day, month] = dateStr.split('/');
  return `${month}/${day}`;
}

const GapReportScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const params = route.params as { fromDate?: string; toDate?: string };
  const [calendarVisible, setCalendarVisible] = useState(false);
  // Use today's date for initial values, or params if provided
  const today = new Date();
  const todayStr = `${String(today.getDate()).padStart(2, '0')}/${String(today.getMonth() + 1).padStart(2, '0')}/${today.getFullYear()}`;
  const [fromDate, setFromDate] = useState(params?.fromDate || todayStr);
  const [toDate, setToDate] = useState(params?.toDate || todayStr);

  // Filter data based on selected date range
  const filteredData = useMemo(() => {
    const from = parseDate(fromDate);
    const to = parseDate(toDate);
    return gapReportData.filter(item => {
      const d = parseDate(item.date);
      return d >= from && d <= to;
    });
  }, [fromDate, toDate]);

  const totalOpen = filteredData.reduce((sum, item) => sum + (item.openGap || 0), 0);
  const totalClosed = filteredData.reduce((sum, item) => sum + (item.closedGap || 0), 0);

  const renderCardItem = ({ item }: { item: any }) => (
    <CardView containerStyle={styles.cardContainer}>
      <Text style={styles.name}>{item.name} | {item.code}</Text>
      <View style={styles.gapRow}>
        <Text style={styles.gapText}>
          Open Gap{item.openLen !== undefined ? `/Len: ${item.openGap}/${item.openLen}` : ` : ${item.openGap}`}
        </Text>
        <Text style={styles.gapText}>
          Closed Gap{item.closedLen !== undefined ? `/Len: ${item.closedGap}/${item.closedLen}` : ` : ${item.closedGap}`}
        </Text>
      </View>
    </CardView>
  );

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title="Gap Report"
        rightContent={
          <TouchableOpacity style={styles.filterRow} onPress={() => setCalendarVisible(true)}>
            <FilterIcon width={22} height={22} />
            <Text style={styles.filterText}>{formatHeaderDate(fromDate)} - {formatHeaderDate(toDate)}</Text>
          </TouchableOpacity>
        }
        onBackPress={() => navigation.goBack()}
      />
      {/* Total Box */}
      <View style={styles.totalBox}>
        <Text style={styles.totalText}>Total: <Text style={styles.openText}>Open Gaps : {totalOpen}</Text>   <Text style={styles.closedText}>Closed Gaps : {totalClosed}</Text></Text>
      </View>
      {/* List */}
      <FlatList
        data={filteredData}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        renderItem={renderCardItem}
        ListEmptyComponent={<Text style={styles.emptyText}>No data in selected range</Text>}
      />
      <BottomPopupCalendar
        visible={calendarVisible}
        selectedFromDate={fromDate}
        seelctedToDate={toDate}
        fromDateTitle="From Date"
        toDateTitle="To Date"
        showFromDate={true}
        showToDate={true}
        showBottomButton={true}
        onApply={(from, to) => {
          if (from && to) {
            setFromDate(from);
            setToDate(to);
          }
          setCalendarVisible(false);
        }}
        onClose={() => setCalendarVisible(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  totalBox: {
    backgroundColor: Colors.secondary,
    borderRadius: ms(8),
    marginVertical: ms(10),
    marginHorizontal: ms(16),
    padding: ms(12),
    alignItems: 'center',
  },
  totalText: {
    color: Colors.white,
    fontSize: ms(14),
    fontWeight: '600',
  },
  openText: {
    color: Colors.white,
    fontSize: ms(14),
    fontWeight: '600',
  },
  closedText: {
    color: Colors.white,
    fontSize: ms(14),
    fontWeight: '600',
  },
  listContainer: {
    paddingHorizontal: ms(16),
    paddingBottom: ms(16),
  },
  cardContainer: {
    marginBottom: ms(10),
  },
  name: {
    fontSize: ms(16),
    fontWeight: '700',
    color: Colors.secondary,
  },
  gapRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
  },
  gapText: {
    fontSize: ms(14),
    color: Colors.pipeIdTextBlack,
    marginRight: ms(16),
    fontWeight: '500',
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderRadius: ms(8),
    paddingHorizontal: ms(8),
    paddingVertical: ms(2),
    // borderWidth: 1,
    // borderColor: Colors.dailyProgressItemBg,
    // marginRight: ms(8),
  },
  filterText: {
    color: Colors.textPrimary,
    fontWeight: '500',
    fontSize: ms(15),
    marginLeft: ms(6),
  },
  emptyText: {
    textAlign: 'center',
    color: Colors.textSecondary,
    marginTop: ms(30),
    fontSize: ms(14),
  },
});

export default GapReportScreen; 