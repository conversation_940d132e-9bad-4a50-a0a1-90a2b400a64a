import React from 'react';
import { View, Text, StyleSheet, FlatList, Pressable, Image } from 'react-native';
import Colors from '../../../utils/Colors/Colors';
import Icon from 'react-native-vector-icons/Ionicons';
import { ms } from '../../../utils/Scale/Scaling';

interface TableListItem {
    id: string;
    name: string;
    [key: string]: any;
}

interface TableListProps {
    items: TableListItem[];
    selectedIds: string | string[];
    onSelect: (id: string) => void;
    multiSelect?: boolean;
    radio?: boolean;
    sectionTitle?: string;
    isLoading?: boolean;
    showEmptyState?: boolean;
}

const TableList: React.FC<TableListProps> = ({
    items,
    selectedIds,
    onSelect,
    multiSelect = false,
    radio = false,
    sectionTitle,
    isLoading = false,
    showEmptyState = true,
}) => (
    <View>
        <FlatList
            data={items}
            keyExtractor={item => item.id}
            initialNumToRender={20}
            maxToRenderPerBatch={50}
            windowSize={10}
            renderItem={({ item, index }) => {
                const selected = selectedIds.includes(item.id);
                return (
                    <View>
                        <Pressable
                            style={styles.row}
                            onPress={() => onSelect(item.id)}
                        >
                            {radio ? (
                                <Icon
                                    name={selected ? 'radio-button-on' : 'radio-button-off-outline'}
                                    size={ms(22)}
                                    color={selected ? Colors.primary : Colors.textPrimary}
                                />
                            ) : (
                                <Icon
                                    name={selected ? 'checkbox' : 'square-outline'}
                                    size={ms(22)}
                                    color={selected ? Colors.secondary : Colors.textSecondary}
                                />
                            )}
                            <Text style={styles.text}>{item.name}</Text>
                        </Pressable>
                        {/* Divider: only show if not the last item */}
                        {index < items.length - 1 && (
                            <View style={styles.divider} />
                        )}
                    </View>
                );
            }}
            ListHeaderComponent={() => (
                radio && (items.length > 0 || showEmptyState) && <Text style={styles.sectionTitle}>
                    {sectionTitle}
                </Text>
            )}
            ListEmptyComponent={() => !isLoading && showEmptyState ? (
                <View style={[styles.emptyContainer, !radio && styles.minHeight]}>
                    <Image
                        source={require('../../../assets/images/NoJobs.png')}
                        style={styles.emptyImage}
                        resizeMode="contain"
                    />
                </View>
            ) : null}
            contentContainerStyle={!sectionTitle && styles.contentConatier}
        />
        {sectionTitle == "Current Job" && items.length > 0 && <View style={styles.divider} />}
    </View>
);

const styles = StyleSheet.create({
    sectionTitle: {
        marginLeft: ms(16),
        marginVertical: ms(8),
        color: Colors.pipeIdTextBlack,
        fontFamily: 'MNSemiBold',
        fontSize: ms(16),
    },
    row: {
        flexDirection: 'row',
        padding: ms(12),
        marginHorizontal: ms(10),
    },
    checkbox: {
        width: ms(24),
        height: ms(24),
        borderRadius: 6,
        borderWidth: ms(2),
        borderColor: Colors.primary,
        alignItems: 'center',
        justifyContent: 'center',
    },
    checked: {
        backgroundColor: Colors.primary,
    },
    checkMark: {
        width: ms(12),
        height: ms(12),
        backgroundColor: Colors.white,
        borderRadius: 3,
    },
    radioSelected: {
        borderRadius: 12,
        borderWidth: ms(2),
        borderColor: Colors.primary,
    },
    radioDot: {
        width: ms(10),
        height: ms(10),
        backgroundColor: Colors.primary,
        borderRadius: 5,
    },
    text: {
        fontFamily: 'MNMedium',
        fontSize: ms(16),
        color: Colors.textPrimary,
        marginHorizontal: ms(10),
        width: '90%',
    },
    divider: {
        height: ms(3),
        backgroundColor: Colors.containerligetBlue,
    },
    contentConatier: {
        flexGrow: 1,
        minHeight: '100%',
        paddingBottom: ms(10),
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: ms(20),
    },
    emptyImage: {
        width: ms(100),
        height: ms(100),
    },
    minHeight: {
        minHeight: '100%',
    }
});

export default TableList;