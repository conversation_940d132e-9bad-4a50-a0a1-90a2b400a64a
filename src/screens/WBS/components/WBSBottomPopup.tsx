import React from 'react';
import { View, Text, StyleSheet, Modal, Pressable } from 'react-native';
import LocationIcon from '../../../assets/svg/Location.svg';
import DeleteIcon from '../../../assets/svg/Delete.svg';
import Colors from '../../../utils/Colors/Colors';
import { ms } from '../../../utils/Scale/Scaling';
import { t } from "i18next";
import BottomPopup from '../../../components/BottomPopup';
import BorderedButton from '../../../components/BorderedButton';
import ButtonComponent from '../../../components/ButtonComponent';

interface WBSPopupProps {
    job: string;
    confirmLabel: string;
    cancelLabel: string;
    onCancel: () => void;
    onConfirm: () => void;
    onClose: () => void;
    isDeletePopup: boolean;
}

const WBSBottomPopup: React.FC<WBSPopupProps> = ({ job, confirmLabel, cancelLabel, onCancel, onConfirm, onClose, isDeletePopup }) => {
    return (
        <Modal
            transparent
            visible={true}
            animationType="fade"
            onRequestClose={onClose}
        >
            <View style={styles.modalContainer}>
                <Pressable
                    style={styles.overlay}
                    onPress={onClose}
                />
                <View style={styles.popupContainer}>
                    <BottomPopup>
                        {isDeletePopup ?
                            <DeleteIcon width={ms(96)} height={ms(96)} style={styles.icon} />
                            :
                            <LocationIcon width={ms(96)} height={ms(96)} style={styles.icon} />
                        }
                        <View style={styles.messageContainer}>
                            {isDeletePopup ? (
                                <>
                                    <Text style={styles.message}>{t('commonStrings.WBSDeleteConfirmMsg')}</Text>
                                    <Text style={styles.message}>{`${job} ${t('commonStrings.job')}?`}</Text>
                                </>
                            ) : (
                                <>
                                    <Text style={styles.message}>{`${t('commonStrings.makeCurrentJob')} ${job}`}</Text>
                                    <Text style={styles.message}>{t('commonStrings.currentWBSJobSetMSg')}</Text>
                                </>
                            )}
                        </View>
                        <ButtonComponent
                            showSecondaryButton
                            secondaryButtonTitle={cancelLabel}
                            onSecondaryPress={onCancel}
                            mainContainerStyle={{
                                paddingBottom: ms(30)
                            }}
                            title={confirmLabel}
                            onPress={onConfirm} />
                    </BottomPopup>
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    modalContainer: {
        flex: 1,
    },
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: Colors.overlay,
    },
    popupContainer: {
        flex: 1,
        justifyContent: 'flex-end',
    },
    messageContainer: {
        alignItems: 'center',
        marginVertical: ms(10),
    },
    message: {
        fontSize: ms(18),
        fontFamily: 'MNSemiBold',
        color: Colors.black,
        marginHorizontal: ms(10),
        textAlign: 'center',
    },
    buttonContainer: {
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-around',
        marginTop: ms(20),
        paddingHorizontal: ms(10),
        paddingTop: ms(20),
        paddingBottom: ms(10),
        borderTopWidth: 0.2,
        borderTopColor: Colors.grey,
    },
    width: {
        width: '46%',
    },
    icon: {
        marginTop: ms(30),
        marginBottom: ms(10),
    }
});

export default WBSBottomPopup;