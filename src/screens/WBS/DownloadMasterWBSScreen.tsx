import React, { useState, useEffect, useCallback, } from 'react';
import { View, StyleSheet, SafeAreaView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { rolesRequest } from '../../redux/RolesRedux/RolesActions';
import { getPendingApproverWbsRequest, wbsRequest } from '../../redux/WBSDownload/WBSActions';
import { RootState } from '../../redux/Root/rootStore';
import ButtonComponent from '../../components/ButtonComponent';
import Colors from '../../utils/Colors/Colors';
import AppHeader from '../../components/AppHeader';
import TopTabNavigator from '../../components/TopTabNavigator';
import { ms } from '../../utils/Scale/Scaling';
import { customAlertWithOK } from '../../components/CustomAlert';
import LoadingOverlay from '../../components/LoadingOverlay';
import { getSelectedJobs, setJobAsCurrent, deleteJobFromStorage } from '../../utils/Storage/Storage';
import { deleteWBSDataForJob } from '../../database/WBSDataDelete/DeleteWBSData';
import { t } from 'i18next';
import { getUserInfo } from '../../utils/DataStorage/Storage';
import { StackActions, useFocusEffect, useNavigation } from '@react-navigation/native';
import { setJobsDownloaded, setSelectedCurrentJobId } from '../../redux/HomeRedux/HomeActions';
import Strings from '../../utils/Strings/Strings';
import SearchComponent from '../../components/SearchComponent';
import TableList from './components/TableList';
import WBSBottomPopup from './components/WBSBottomPopup';
import { useTranslation } from 'react-i18next';

interface Job {
    id: string;
    name: string;
    role: string;
    isCurrentJob?: boolean;
}

const DownloadMasterWBSScreen = () => {
    const dispatch = useDispatch();
    const navigation = useNavigation();
    const { roles, loading, rolesError } = useSelector((state: RootState) => state.rolesData);
    const { isLoading, response, error, isPendingLoading, pendingApprovalResponse } = useSelector((state: RootState) => state.wbs);
    const { t } = useTranslation();
    const [tab, setTab] = useState<string>(t('commonStrings.allKey'));
    const [search, setSearch] = useState('');
    const [selectedJobIds, setSelectedJobIds] = useState<string[]>([]);
    const [currentJobId, setCurrentJobId] = useState<string>('');
    const [showActionPopup, setShowActionPopup] = useState(false);
    const [showDeletePopup, setShowDeletePopup] = useState(false);
    const [selectedJob, setSelectedJob] = useState<Job>({ id: '', name: '', role: '' });
    const [storedJobs, setStoredJobs] = useState<Job[]>([]);
    const [isDeleting, setIsDeleting] = useState<boolean>(false);
    const [jobList, setJobList] = useState<Job[]>([]);
    const [filteredJob, setFilteredJob] = useState<Job[]>([]);
    const [filteredOtherJob, setFilteredOtherJob] = useState<Job[]>([]);
    const [currentJob, setCurrentJob] = useState<Job>();
    const [isDownloading, setIsDownloading] = useState<boolean>(false);
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [dateType, setDateType] = useState<'from' | 'to'>('from');

    const user = getUserInfo();

    const downloadedKey = t('commonStrings.downloadedKey');
    const allKey = t('commonStrings.allKey');
    const downloaded = t('commonStrings.downloaded');
    const all = t('commonStrings.all');

    useFocusEffect(
        useCallback(() => {
            // Transform roles data to match TableList format
            const jobsList = roles?.RolesList?.map(role => ({
                id: role.Jobcode,
                name: `${role.Jobcode} - ${role.JobDesc}`,
                role: role.Functional_ROLE
            })) || [];
            // For Downloaded tab: split into current and other jobs
            const currentJob = storedJobs.find(job => job.isCurrentJob === true);
            const otherJobs = storedJobs.filter(job => job.isCurrentJob !== true);

            // Filter jobs by search based on active tab
            const filteredJobsList = jobsList.filter(job => job.name.toLowerCase().includes(search.toLowerCase()))
            const filteredOtherJobs = otherJobs.filter(job => job.name.toLowerCase().includes(search.toLowerCase()));

            setFilteredJob(filteredJobsList);
            setFilteredOtherJob(filteredOtherJobs);
            setJobList(jobsList);
            setCurrentJob(currentJob);

        }, [search, storedJobs, selectedJobIds, roles])
    );

    useEffect(() => {
        dispatch(rolesRequest({
            User_ID: user?.UID.toString() || '',
            // User_ID: '100078676',
            RoleType: t('commonStrings.roles'),
            cb: ({ success }) => {
                if (!success) {
                    customAlertWithOK(
                        t('commonStrings.wbsDownloadError'),
                        (rolesError || error || ''),
                        [{ text: t('commonStrings.ok') }],
                        false
                    );
                }
            }
        }));

        // Retrieve downloaded jobs
        const jobs = getSelectedJobs();
        if (jobs.length === 1) {
            const updatedJobs = setJobAsCurrent(jobs[0].id);
            setStoredJobs(updatedJobs);
            setCurrentJobId(jobs[0].id);

            dispatch(setSelectedCurrentJobId(jobs[0].id));
        } else {
            setStoredJobs(jobs);
            const currentJob = jobs.find(job => job.isCurrentJob === true);
            if (currentJob) {
                setCurrentJobId(currentJob.id);

                dispatch(setSelectedCurrentJobId(currentJob.id));
            }
        }
    }, []);

    const wbsUpdateDownloadStatus = useCallback(() => {
        dispatch(setJobsDownloaded(true));


        // Retrieve downloaded jobs
        const jobs = getSelectedJobs();

        if (jobs.length === 1) {
            const updatedJobs = setJobAsCurrent(jobs[0].id);
            setStoredJobs(updatedJobs);
            setCurrentJobId(jobs[0].id);
            dispatch(setSelectedCurrentJobId(jobs[0].id));
        } else {
            setStoredJobs(jobs);
            const currentJob = jobs.find(job => job.isCurrentJob === true);
            if (currentJob) {
                setCurrentJobId(currentJob.id);
                dispatch(setSelectedCurrentJobId(currentJob.id));
            }
        }

        setSelectedJobIds([]);
        setIsDownloading(false);
        // Show success message
        customAlertWithOK(
            t('commonStrings.downloadWbs'),
            t('commonStrings.wbsDownloadSuccess'),
            [{ text: t('commonStrings.ok') }],
            false
        );
        // userRole == "Approver" || 
        if (roles?.RolesList?.[0]?.Functional_ROLE  == "Site Engineer") {
            navigation.dispatch(StackActions.replace('DailyProgressView'));
        }
        else {
            navigation.dispatch(StackActions.replace('DailyProgressApprove'));
        }
    }, []);


    // Download logic: move selected jobs to downloaded list
    const handleDownload = async () => {
        const jobsToDownload = jobList.filter(job => selectedJobIds.includes(job.id));

        // Get current date for Todate
        let today = new Date();
        let toDate = today.toISOString().split('T')[0]; // Format: YYYY-MM-DD

        // Set Fromdate to 2 years ago
        let fromDate = new Date();
        fromDate.setFullYear(fromDate.getFullYear() - 2);
        // fromDate.setDate(fromDate.getDate() - 7); // 
        let fromDateStr = fromDate.toISOString().split('T')[0]; // Format: YYYY-MM-DD

        // Dispatch WBS download request for each selected job
        for (const job of jobsToDownload) {
            // For non-approver roles, call both wbsRequest and getPendingApproverWbsRequest
            if (job.role.toLowerCase() !== Strings.loginScreen.approver) {
                // First dispatch wbsRequest and wait for it to complete
                setIsDownloading(true);

                let wbsDispatchSuccess = false;

                await new Promise<void>((resolve) => {
                    dispatch(wbsRequest({
                        userId: user?.UID.toString() || '',
                        // userId: '100078676',
                        objJoblist: [
                            {
                                jobCode: job.id
                            }
                        ],
                        Fromdate: fromDateStr,
                        Todate: toDate,
                        selectedJob: jobsToDownload,
                        isLogin: false,
                        cb: ({ success }) => {
                            if (success) {
                                wbsDispatchSuccess = true; // Set local variable
                                // wbsUpdateDownloadStatus();
                            } else {
                                setSelectedJobIds([]);
                                setIsDownloading(false);
                                customAlertWithOK(
                                    t('commonStrings.wbsDownloadError'),
                                    (rolesError || error || t('commonStrings.invalidWbsResponse')),
                                    [{ text: t('commonStrings.ok') }],
                                    false
                                );
                            }
                            resolve(); // Resolve regardless of success/failure
                        }
                    }));
                });

                // Only dispatch getPendingApproverWbsRequest if first call succeeded
                if (wbsDispatchSuccess) {
                    dispatch(getPendingApproverWbsRequest({
                        jobCode: job.id,
                        type: "ForApproval",
                        selectedJob: jobsToDownload,
                        objJoblist: [{ jobCode: job.id }],
                        UID: user?.UID,
                        cb: ({ success }) => {
                            if (success) {
                                wbsUpdateDownloadStatus();
                            } else {
                                setSelectedJobIds([]);
                                setIsDownloading(false);
                                customAlertWithOK(
                                    t('commonStrings.wbsDownloadError'),
                                    (rolesError || error || ''),
                                    [{ text: t('commonStrings.ok') }],
                                    false
                                );
                            }
                        }
                    }));
                }
            }

            // For approver roles, only call getPendingApproverWbsRequest
            if (job.role.toLowerCase() === Strings.loginScreen.approver) {
                dispatch(getPendingApproverWbsRequest({
                    jobCode: job.id,
                    type: "ForApproval",
                    selectedJob: jobsToDownload,
                    objJoblist: [{ jobCode: job.id }],
                    cb: ({ success }) => {
                        if (success) {
                            wbsUpdateDownloadStatus();
                        } else {
                            setSelectedJobIds([]);
                            setIsDownloading(false);
                            customAlertWithOK(
                                t('commonStrings.wbsDownloadError'),
                                (rolesError || error || ''),
                                [{ text: t('commonStrings.ok') }],
                                false
                            );
                        }
                    }
                }));
            }
        }
    };

    // Make Current Job PopUp Trigger
    const handleOtherJobSelect = (job: Job) => {
        setSelectedJob(job);
        setShowActionPopup(true);
    };

    // Close Popups
    const handleClosePopups = () => {
        setShowActionPopup(false);
        setShowDeletePopup(false);
    }

    // Current Job Setting
    const handleMakeCurrentJob = () => {
        if (selectedJob) {
            const updatedJobs = setJobAsCurrent(selectedJob.id);
            setStoredJobs(updatedJobs);
            setCurrentJobId(selectedJob.id);

            dispatch(setSelectedCurrentJobId(selectedJob.id));

            // Use CommonActions.reset to ensure proper navigation
            // navigation.dispatch(
            //     CommonActions.reset({
            //         index: 0,
            //         routes: [
            //             { name: 'DailyProgressView' }
            //         ],
            //     })
            // );
            // navigation.navigate('DailyProgressView')
            // navigation.dispatch(StackActions.replace('DailyProgressView'));
        }
        setShowActionPopup(false);
        setSelectedJob({ id: '', name: '', role: '' });
    };

    // Deletion Popup Trigger
    const handleDeletePress = () => {
        setShowDeletePopup(true);
    };

    // Downloaded WBS Deletion
    const handleConfirmDelete = async () => {
        if (selectedJob) {
            try {
                // First delete all WBS data for this job
                setIsDeleting(true);
                setShowDeletePopup(false);
                setShowActionPopup(false);
                const deleteSuccess = await deleteWBSDataForJob(selectedJob.id);

                if (deleteSuccess) {
                    // Delete from storage
                    const updatedJobs = deleteJobFromStorage(selectedJob.id);
                    setStoredJobs(updatedJobs);

                    // Clear current job if it was the deleted one
                    if (currentJobId === selectedJob.id) {
                        setCurrentJobId('');
                    }

                    setIsDeleting(false);

                    // Show success message
                    customAlertWithOK(
                        t('commonStrings.downloadWbs'),
                        t('commonStrings.wbsDeleteSuccess'),
                        [{ text: t('commonStrings.ok') }],
                        false
                    );
                } else {
                    setIsDeleting(false);
                    customAlertWithOK(
                        t('commonStrings.alertTitle'),
                        t('commonStrings.wbsDownloadFailed'),
                        [{ text: t('commonStrings.ok') }],
                        false
                    );
                }
            } catch (error) {
                setIsDeleting(false);
                customAlertWithOK(
                    t('commonStrings.alertTitle'),
                    t('commonStrings.wbsDownloadFailed'),
                    [{ text: t('commonStrings.ok') }],
                    false
                );
            }
        }
        setShowDeletePopup(false);
        setShowActionPopup(false);
        setSelectedJob({ id: '', name: '', role: '' });
    };

    // Deletion Popup Cancellation
    const handleCancelDelete = () => {
        setShowDeletePopup(false);
        setShowActionPopup(false);
        setSelectedJob({ id: '', name: '', role: '' });
    };

    const handleSearch = (text: string) => {
        setSearch(text);
    };

    return (
        <SafeAreaView style={styles.conatiner}>
            <AppHeader title={t('commonStrings.downloadWbs')} />
            {
                (isLoading || loading || isDeleting || isPendingLoading || isDownloading) &&
                <LoadingOverlay
                    visible={true}
                    message={t('commonStrings.pleaseWait')}
                />
            }

            <View style={styles.tabContainer}>
                <TopTabNavigator
                    tabs={[
                        { key: allKey, label: all },
                        { key: downloadedKey, label: downloaded }
                    ]}
                    activeTab={tab}
                    onTabChange={(key) => setTab(key as string)}
                />
            </View>
            <SearchComponent
                customStyle={styles.searchBar}
                onChange={handleSearch}
                value={search}
            />
            {tab === t('commonStrings.allKey') ? (
                <View style={styles.mainContainer}>
                    <View style={styles.listContainer}>
                        <TableList
                            items={filteredJob}
                            selectedIds={selectedJobIds}
                            onSelect={id => {
                                setSelectedJobIds(prev =>
                                    prev.includes(id)
                                        ? prev.filter(jid => jid !== id)
                                        : [...prev, id]
                                );
                            }}
                            multiSelect
                            isLoading={loading}
                        />
                    </View>
                    <ButtonComponent
                        title={selectedJobIds.length === 1 ? t('commonStrings.downloadAsPrimary') : t('commonStrings.download')}
                        onPress={handleDownload}
                    />
                </View>
            ) : (
                <View style={styles.mainContainer}>
                    <View style={styles.listContainer}>
                        <TableList
                            items={currentJob ? [currentJob] : []}
                            selectedIds={currentJobId}
                            onSelect={() => { }}
                            radio
                            sectionTitle={t('commonStrings.currentJob')}
                            isLoading={loading}
                            showEmptyState={true}
                        />
                        <TableList
                            items={filteredOtherJob}
                            selectedIds={[]}
                            onSelect={id => {
                                const job = filteredJob.find(j => j.id === id);
                                if (job)
                                    handleOtherJobSelect(job);
                            }}
                            radio
                            sectionTitle={t('commonStrings.otherJob')}
                            isLoading={loading}
                            showEmptyState={false}
                        />
                    </View>

                    {/* BottomPopup */}
                    {showActionPopup && (
                        <View>
                            <WBSBottomPopup
                                job={selectedJob.name}
                                confirmLabel={showDeletePopup ? t('commonStrings.yes') : t('commonStrings.setCurrentJob')}
                                cancelLabel={showDeletePopup ? t('commonStrings.no') : t('commonStrings.delete')}
                                onConfirm={showDeletePopup ? handleConfirmDelete : handleMakeCurrentJob}
                                onCancel={showDeletePopup ? handleCancelDelete : handleDeletePress}
                                onClose={handleClosePopups}
                                isDeletePopup={showDeletePopup}
                            />
                        </View>
                    )}
                </View>
            )}
        </SafeAreaView >
    );
};

const styles = StyleSheet.create({
    conatiner: {
        flex: 1,
        backgroundColor: Colors.white
    },
    tabContainer: {
        backgroundColor: Colors.containerligetBlue,
        paddingVertical: ms(10)
    },
    mainContainer: {
        flex: 1,
        position: 'relative',
    },
    listContainer: {
        flex: 1,
        // marginBottom: ms(80),
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#F6F8FA',
        borderRadius: 10,
        marginHorizontal: ms(16),
        marginBottom: ms(8),
        height: ms(44),
    },
    searchIcon: {
        width: ms(22),
        height: ms(22),
        borderRadius: 11,
        backgroundColor: Colors.textSecondary,
        marginLeft: ms(10),
        opacity: 0.3,
    },
    searchBar: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.white,
        borderRadius: ms(4),
        paddingHorizontal: ms(12),
        borderWidth: 1,
        borderColor: Colors.grey,
        marginHorizontal: ms(20),
        marginVertical: ms(15),
    },
    downloadButtonContainer: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: Colors.white,
        alignItems: 'center',
        paddingVertical: ms(20),
        elevation: 10,
        borderTopWidth: 1,
        borderTopColor: Colors.grey,
    },
    divider: {
        height: ms(3),
        backgroundColor: Colors.secondary,
    },
    sectionTitle: {
        marginLeft: ms(16),
        marginTop: ms(8),
        marginBottom: ms(4),
        color: Colors.grey,
        fontFamily: 'MNSemiBold',
        fontSize: ms(16),
    },
    loadingOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
});

export default DownloadMasterWBSScreen;