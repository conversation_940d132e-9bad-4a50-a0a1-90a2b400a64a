import React from 'react';
import { View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import Colors from '../utils/Colors/Colors';
import { ms } from '../utils/Scale/Scaling';

interface LoadingOverlayProps {
    visible: boolean;
    message?: string;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ visible, message }) => {
    if (!visible) return null;

    return (
        <View style={styles.overlay}>
            <View style={styles.blurEffect} />
            <View style={styles.content}>
                <ActivityIndicator size="large" color={Colors.primary} />
                {message && <Text style={styles.message}>{message}</Text>}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    blurEffect: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: Colors.black,
        opacity: 0.5,
    },
    content: {
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: Colors.white,
        padding: ms(20),
        borderRadius: ms(10),
        shadowColor: Colors.black,
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    message: {
        marginTop: ms(10),
        fontSize: ms(16),
        color: Colors.textPrimary,
        fontFamily: 'MNRegular',
    },
});

export default LoadingOverlay; 