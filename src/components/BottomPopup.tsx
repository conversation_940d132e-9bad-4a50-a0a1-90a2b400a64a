import React, { ReactNode } from 'react';
import { View, StyleSheet, TouchableWithoutFeedback, Modal } from 'react-native';
import { ms } from '../utils/Scale/Scaling';
import Colors from '../utils/Colors/Colors';

interface BottomPopupProps {
    visible: boolean;
    minHeight?: number | string;
    onCancelPress: () => void;
    children: ReactNode;
}

const BottomPopup = ({
    children,
    minHeight,
    visible,
    onCancelPress
}: BottomPopupProps) => {
    return (
        <Modal
            animationType='slide'
            transparent
            visible={visible}
            onRequestClose={onCancelPress}>
            <TouchableWithoutFeedback onPress={onCancelPress}>
                <View
                    style={styles.modalOverlay}>
                    <View style={styles.innerContainer}>
                        <View style={styles.header}>
                            <View style={styles.handle} />
                        </View>
                        {children}
                    </View>
                </View>
            </TouchableWithoutFeedback>
        </Modal>
    );
}

export default BottomPopup;

const styles = StyleSheet.create({
    modalOverlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    header: {
        alignItems: 'center',
    },
    handle: {
        width: ms(130),
        height: ms(7),
        backgroundColor: Colors.darkBlue,
        borderBottomEndRadius: ms(8),
        borderBottomStartRadius: ms(8),
    },
    innerContainer: {
        backgroundColor: Colors.white,
        justifyContent: 'center',
        alignItems: 'center',
    }
});