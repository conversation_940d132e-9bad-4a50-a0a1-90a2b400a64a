import React, { useState } from 'react';
import { View, TextInput, StyleSheet, TouchableOpacity, Text, ViewStyle, StyleProp, TextStyle, Pressable } from 'react-native';
import PwdShow from '../assets/svg/eye.svg';
import PwdHide from '../assets/svg/eye_hide.svg';
import Colors from '../utils/Colors/Colors';
import { ms } from '../utils/Scale/Scaling';
import { AppFonts } from './Fonts';

interface UserInputProps {
    placeholder: string;
    value: string;
    onChangeText: (text: string) => void;
    secureTextEntry?: boolean;
    showToggleIcon?: boolean;
    textLabelStye?: object;
    label?: string;
    style?: object;
    isNumeric?: boolean;
    inputStyle?: StyleProp<ViewStyle | TextStyle>;
    containerStyle?:  StyleProp<ViewStyle>;
    inputContainerStyle?:  StyleProp<ViewStyle>;
    multiline?: boolean;
    showSecondaryText?: boolean;
    secondaryText?: string;
    secondaryLabelStyle?:  StyleProp<TextStyle>;
    secondaryLabel?: string;
}

const TextInputComponent: React.FC<UserInputProps> = ({
    placeholder,
    value,
    onChangeText,
    secureTextEntry = false,
    showToggleIcon = false,
    textLabelStye = {},
    label,
    style = {},
    isNumeric = false,
    inputStyle,
    containerStyle,
    inputContainerStyle,
    multiline,
    showSecondaryText,
    secondaryText,
    secondaryLabelStyle,
    secondaryLabel
}) => {
    const [isSecure, setIsSecure] = useState(secureTextEntry);

    const handleToggleSecure = () => setIsSecure(!isSecure);

    return (
        <View style={containerStyle}>
            <View style={styles.lableRowContainer}>
            <Text style={[styles.labelText, textLabelStye]}>{label}</Text>
            <Pressable>
            {secondaryLabel && <Text style={[styles.secondaryLableText, secondaryLabelStyle]}>{secondaryLabel}</Text>}
            </Pressable>
            </View>
            <View testID="input-container" style={[styles.inputContainer, inputContainerStyle]}>
                <TextInput
                    style={[styles.input, inputStyle]}
                    placeholder={placeholder}
                    placeholderTextColor={Colors.placeholder}
                    value={value}
                    onChangeText={onChangeText}
                    secureTextEntry={isSecure}
                    autoCapitalize="none"
                    keyboardType={isNumeric ? 'numeric' : 'default'}
                    multiline={multiline}
                />
                {showToggleIcon && secureTextEntry && (
                    <TouchableOpacity onPress={handleToggleSecure} style={styles.iconContainer}>
                        {isSecure ? (
                            <Text testID="pwd-hide-icon"><PwdHide /></Text>
                        ) : (
                            <Text testID="pwd-show-icon"><PwdShow /></Text>
                        )}
                    </TouchableOpacity>
                )}
                { showSecondaryText && 
                    <View>
                        <Text style={styles.secondaryText}>{secondaryText}</Text>
                    </View>
                }
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    labelText: {
        fontFamily: AppFonts.Medium,
        fontSize: ms(12),
        color: Colors.pipeIdTextBlack,
        marginBottom: ms(5)
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.containerligetBlue,
        borderColor: Colors.inputBorder,
        borderWidth: ms(1),
        borderRadius: ms(8),
        paddingHorizontal: ms(8),
        marginBottom: ms(4),
    },
    input: {
        flex: 1,
        paddingVertical: ms(12),
        color: Colors.primary,
        fontSize: ms(12),
        fontWeight: '500',
        fontFamily: 'MNSemiBold',
    },
    iconContainer: {
        marginLeft: ms(8),
    },
    secondaryText: {
        fontFamily: AppFonts.SemiBold,
        fontSize: ms(13),
        color: Colors.black
    },
    lableRowContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    secondaryLableText: {
        fontFamily: AppFonts.SemiBold,
        fontSize: ms(12),
        color: Colors.blue,
        marginBottom: ms(5)
    }
});

export default TextInputComponent; 