import React, { useState, useRef, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    Dimensions,
    Pressable,
    Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import MapView, { Marker } from 'react-native-maps';
import AppHeader from '../../components/AppHeader';
import SearchComponent from '../../components/SearchComponent';
import Colors from '../../utils/Colors/Colors';
import Strings from '../../utils/Strings/Strings';
import Pointer from '../../assets/svg/OpenGap.svg';
import { ms } from '../../utils/Scale/Scaling';
import { t } from 'i18next';

interface MarkerType {
    id: string;
    name: string;
    details: string;
    latitude: number;
    longitude: number;
}

interface MapDataItem {
    id: string;
    jobCode: string;
    wbsCode: string;
    latitude: number;
    longitude: number;
    hierarchyLevel: string;
    isActive: string;
    jobDesc: string;
    il: string;
    ilDesc: string;
    wp: string;
    wpDesc: string;
    swp: string;
    cwpDesc: string;
    deliverableCode: number;
    deliverableCodeDesc: string;
}

interface CustomMapViewProps {
    mapData?: MapDataItem[];
}

const CustomMapView: React.FC<CustomMapViewProps> = ({ mapData = [] }) => {
    const [filteredData, setFilteredData] = useState<MapDataItem[]>([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedMarker, setSelectedMarker] = useState<MapDataItem | null>(null);
    const [cardPosition, setCardPosition] = useState({ x: 0, y: 0 });
    const [isAnimating, setIsAnimating] = useState(false);
    const [pendingMarker, setPendingMarker] = useState<MapDataItem | null>(null);
    const mapRef = useRef<MapView | null>(null);
    const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
        // Clear any pending timeout
        if (searchTimeoutRef.current) {
            clearTimeout(searchTimeoutRef.current);
            searchTimeoutRef.current = null;
        }

        // Immediately clear card and marker when search is empty
        if (searchQuery.length === 0) {
            setFilteredData(mapData);
            setSelectedMarker(null);
            setCardPosition({ x: 0, y: 0 });
            setPendingMarker(null);
            return;
        }

        // Filter data based on search query
        const filteredData = mapData.filter((item) => {
            const query = searchQuery.toLowerCase();
            return (
                item.deliverableCodeDesc.toLowerCase().includes(query) ||
                item.latitude.toString().includes(query) ||
                item.longitude.toString().includes(query) ||
                item.jobDesc.toLowerCase().includes(query) ||
                item.wbsCode.toLowerCase().includes(query)
            );
        });

        setFilteredData(filteredData);

        // Clear card if no results found
        if (filteredData.length === 0) {
            setSelectedMarker(null);
            setCardPosition({ x: 0, y: 0 });
            setPendingMarker(null);
            return;
        }

        // Show first result
        const matchedMarker = filteredData[0];

        // For search results, animate to the marker and calculate position
        mapRef.current?.animateToRegion({
            latitude: matchedMarker.latitude,
            longitude: matchedMarker.longitude,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
        });

        // Calculate card position after animation
        searchTimeoutRef.current = setTimeout(async () => {
            // Check if search is still active (not cleared)
            if (searchQuery.length > 0 && mapRef.current && mapRef.current.pointForCoordinate) {
                try {
                    const point = await mapRef.current.pointForCoordinate({
                        latitude: matchedMarker.latitude,
                        longitude: matchedMarker.longitude,
                    });

                    setCardPosition({
                        x: point.x,
                        y: point.y + 40,
                    });
                    setSelectedMarker(matchedMarker);
                } catch (error) {
                    setSelectedMarker(matchedMarker);
                }
            }
        }, 500);

        // Cleanup function
        return () => {
            if (searchTimeoutRef.current) {
                clearTimeout(searchTimeoutRef.current);
                searchTimeoutRef.current = null;
            }
        };
    }, [searchQuery, mapData]);

    const handleMapPress = () => {
        setSelectedMarker(null);
        setCardPosition({ x: 0, y: 0 });
    };

    const handleRegionChange = () => {
        // Clear selected marker and card when map is moved
        if (selectedMarker && !isAnimating) {
            setSelectedMarker(null);
            setCardPosition({ x: 0, y: 0 });
        }
    };

    const handleMarkerPress = async (data: MapDataItem) => {
        setIsAnimating(true);
        setPendingMarker(data);

        mapRef.current?.animateToRegion({
            latitude: data.latitude,
            longitude: data.longitude,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
        });
    };

    const handleRegionChangeComplete = async () => {
        if (isAnimating && pendingMarker && mapRef.current) {
            setIsAnimating(false);

            // Wait a bit more to ensure the animation is fully complete
            setTimeout(async () => {
                try {
                    if (mapRef.current && mapRef.current.pointForCoordinate) {
                        const point = await mapRef.current.pointForCoordinate({
                            latitude: pendingMarker.latitude,
                            longitude: pendingMarker.longitude,
                        });

                        setCardPosition({
                            x: point.x,
                            y: point.y + 40,
                        });
                        setSelectedMarker(pendingMarker);
                        setPendingMarker(null);
                    }
                } catch (error) {
                    // Fallback: just show the marker without precise positioning
                    setSelectedMarker(pendingMarker);
                    setPendingMarker(null);
                }
            }, 100);
        }
    };


    return (
        <SafeAreaView style={styles.container}>
            <AppHeader
                title={t('ProgressUpdate.selectWbs')}
            />

            <View style={styles.mapContainer}>
                <MapView
                    ref={mapRef}
                    style={styles.map}
                    initialRegion={{
                        latitude: filteredData.length > 0 ? filteredData[0].latitude : 28.6139,
                        longitude: filteredData.length > 0 ? filteredData[0].longitude : 77.209,
                        latitudeDelta: 0.5,
                        longitudeDelta: 0.5,
                    }}
                    onPress={handleMapPress}
                    onRegionChange={handleRegionChange}
                    onRegionChangeComplete={handleRegionChangeComplete}
                >
                    {filteredData.map((data) => (
                        <Marker
                            key={data.id}
                            coordinate={{
                                latitude: data.latitude,
                                longitude: data.longitude,
                            }}
                            onPress={() => handleMarkerPress(data)}
                        >
                            <Pointer width={40} height={40} />
                        </Marker>
                    ))}
                </MapView>


                {/* Custom Tooltip/Card */}
                {selectedMarker && cardPosition.x !== 0 && cardPosition.y !== 0 && (
                    <View
                        style={[
                            styles.customCardWrapper,
                            {
                                position: 'absolute',
                                left: cardPosition.x - 150,
                                top: cardPosition.y - (Platform.OS === 'ios' ? 20 : 40),
                                zIndex: 100,
                            },
                        ]}
                    >
                        <View style={styles.triangle} />
                        <View style={styles.customCard}>
                            <Text style={styles.userName}>{selectedMarker.deliverableCodeDesc}</Text>
                            <Pressable style={styles.updateBtn} onPress={() => { }}>
                                <Text style={styles.updateTxt}>{t('PendingForApprovalStrings.selectPath')}</Text>
                            </Pressable>
                        </View>
                    </View>
                )}

                <View style={styles.searchContainer}>
                    <SearchComponent
                        onChange={setSearchQuery}
                        placeholder="Search location"
                        customStyle={styles.SearchComponentstyle}
                        value={searchQuery}
                    />
                </View>
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.dailyProgressBg,
    },
    mapContainer: {
        flex: 1,
        position: 'relative',
    },
    map: {
        width: Dimensions.get('window').width,
        height: Dimensions.get('window').height,
    },
    searchContainer: {
        position: 'absolute',
        top: 10,
        left: 20,
        right: 20,
        zIndex: 10,
    },
    triangle: {
        width: ms(24),
        height: ms(12),
        backgroundColor: 'transparent',
        borderStyle: 'solid',
        borderLeftWidth: ms(12),
        borderRightWidth: ms(12),
        borderBottomWidth: ms(12),
        borderLeftColor: 'transparent',
        borderRightColor: 'transparent',
        borderBottomColor: '#fff',
        marginBottom: -1,
    },
    userName: {
        fontSize: ms(16),
        fontFamily: 'MNMedium',
        color: Colors.secondary,
        marginBottom: 5,
        textAlign: 'center',
    },
    packageDetails: {
        fontSize: ms(14),
        color: Colors.textPrimary,
        marginBottom: 10,
        textAlign: 'center',
        fontFamily: 'MNMedium',
        fontWeight: '500',
    },
    progressRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginBottom: 15,
    },
    progressLabel: {
        fontSize: ms(14),
        color: Colors.scopeGrey,
        textAlign: 'center',
        fontWeight: '500',
        fontFamily: 'MNMedium',
    },
    progressValue: {
        fontSize: 16,
        fontWeight: '500',
        color: Colors.textPrimary,
        marginTop: 3,
        textAlign: 'center',
        fontFamily: 'MNMedium',
    },
    cardButtonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    rejectButton: {
        flex: 1,
        backgroundColor: Colors.white,
        borderWidth: 1,
        borderColor: Colors.lightRed,
        marginRight: 10,
        paddingVertical: 8,
        paddingHorizontal: 15,
        borderRadius: 5,
        alignItems: 'center',
    },
    rejectButtonText: {
        color: Colors.lightRed,
        fontSize: 14,
        fontWeight: '700',
        fontFamily: 'MNMedium',
    },
    approveButton: {
        flex: 1,
        backgroundColor: Colors.green,
        marginRight: 10,
        paddingVertical: 8,
        paddingHorizontal: 15,
        borderRadius: 5,
        alignItems: 'center',
    },
    approveButtonText: {
        color: Colors.updateTextLightBlue,
        fontSize: 14,
        fontWeight: '700',
        fontFamily: 'MNMedium',
    },
    detailsButton: {
        padding: 8,
        borderWidth: 1,
        borderColor: '#007BFF',
        borderRadius: 5,
    },
    navIcon: {
        position: 'absolute',
        top: '90%',
        backgroundColor: '#fff',
        padding: 8,
        borderRadius: 25,
        elevation: 5,
        shadowColor: '#000',
        shadowOpacity: 0.25,
        shadowRadius: 3,
        shadowOffset: { width: 0, height: 2 },
        zIndex: 15,
    },
    leftNav: {
        left: 20,
        transform: [{ translateY: -25 }],
    },
    rightNav: {
        right: 20,
        transform: [{ translateY: -25 }],
    },
    SearchComponentstyle: {
        backgroundColor: Colors.white,
        marginHorizontal: 0,
    },
    customCardWrapper: {
        alignItems: 'center',
        width: 300,
    },
    customCard: {
        width: 300,
        backgroundColor: '#fff',
        borderRadius: 10,
        padding: 15,
        elevation: 5,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    rejectModalCard: {
        backgroundColor: '#fff',
        borderRadius: 10,
        padding: 20,
        margin: 20,
        alignItems: 'center',
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 20,
        alignSelf: 'flex-start',
    },
    inputContainerStyle: {
        width: '100%',
        marginBottom: 20,
    },
    remarksLabel: {
        fontSize: 14,
        marginBottom: 5,
        color: '#333',
    },
    remarksInput: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        padding: 10,
        minHeight: 80,
        textAlignVertical: 'top',
        fontSize: 14,
        backgroundColor: '#f9f9f9',
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        width: '100%',
    },
    cancelButton: {
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#007BFF',
        borderRadius: 5,
        paddingVertical: 12,
        minWidth: 100,
        alignItems: 'center',
        marginRight: 10,
    },
    cancelButtonText: {
        color: '#007BFF',
        fontSize: 16,
    },
    modelRejectButton: {
        backgroundColor: '#E74C3C',
        borderRadius: 5,
        paddingVertical: 12,
        minWidth: 100,
        alignItems: 'center',
    },
    modelRejectButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: 'bold',
    },
    disabledButton: {
        opacity: 0.5,
    },
    tooltip: {
        position: 'absolute',
        backgroundColor: '#fff',
        borderRadius: 10,
        padding: 10,
        elevation: 5,
        shadowColor: '#000',
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        shadowOffset: { width: 0, height: 2 },
    },
    tooltipTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 5,
    },
    tooltipDetails: {
        fontSize: 14,
        color: Colors.textPrimary,
        marginBottom: 5,
    },
    tooltipButtons: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginTop: 10,
    },
    tooltipButton: {
        padding: 8,
        borderWidth: 1,
        borderColor: '#007BFF',
        borderRadius: 5,
    },
    tooltipButtonText: {
        color: '#007BFF',
        fontSize: 14,
        fontWeight: '700',
        fontFamily: 'MNMedium',
    },
    updateBtn: {
        borderWidth: 1,
        marginTop: ms(10),
        paddingVertical: ms(5),
        borderRadius: ms(4),
        borderColor: Colors.secondary,
        alignItems: 'center',
        justifyContent: 'center'
    },
    updateTxt: {
        fontFamily: 'MNMedium',
        fontSize: ms(14),
        color: Colors.secondary,
        textAlign: 'center'
    },
});

export default CustomMapView;
