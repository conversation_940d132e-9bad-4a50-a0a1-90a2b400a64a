import React, { type ReactNode } from 'react';
import { View, StyleSheet } from 'react-native';
import { ms } from '../../utils/Scale/Scaling';
import Colors from '../../utils/Colors/Colors';

interface MapBottomPopupProps {
    children: ReactNode;
}

const MapBottomPopup = ({ children }: MapBottomPopupProps) => {
    return (
        <View style={styles.container}>
            <View style={styles.header}>
                {/* <View style={styles.handle} /> */}
            </View>
            {children}
        </View>
    );
}

export default MapBottomPopup;

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.white,
        alignItems: 'center'
    },
    header: {
        alignItems: 'center',
    },
    handle: {
        width: ms(120),
        height: ms(8),
        backgroundColor: Colors.darkBlue,
        borderBottomEndRadius: 8,
        borderBottomStartRadius: 8,
    },
});
