import * as React from 'react';
import { View, Text, StyleSheet, StyleProp, ViewStyle } from 'react-native';
import {Picker} from '@react-native-picker/picker';
import { Dropdown } from 'react-native-element-dropdown';
import { Icon } from '../Icons';
import { useTranslation } from 'react-i18next';
import { ms } from '../../utils/Scale/Scaling';
import Colors from '../../utils/Colors/Colors';
import { AppFonts } from '../Fonts';
const data = [
    { label: 'Item 1', value: '1' },
    { label: 'Item 2', value: '2' },
    { label: 'Item 3', value: '3' },
    { label: 'Item 4', value: '4' },
    { label: 'Item 5', value: '5' },
    { label: 'Item 6', value: '6' },
    { label: 'Item 7', value: '7' },
    { label: 'Item 8', value: '8' },
  ];

  interface DropdownPickerProps {
    title?: string;
    data: any;
    defaultValue: string;
    search?: boolean;
    placeHolder?: string;
    onSelect: (data: any) => void;
    mainContainerStyle?: StyleProp<ViewStyle>;
  }

const CustomDropdownPicker = ({
    title,
    data, 
    defaultValue,
    placeHolder = 'commonStrings.select',
    search = false,
    onSelect,
    mainContainerStyle
}: DropdownPickerProps) => {
    const {t} = useTranslation();
    const [value, setValue] = React.useState(defaultValue);
    const [isFocus, setIsFocus] = React.useState(false);

    React.useEffect(() => {
        setValue(defaultValue);
    },[]);

    return(
        <View style={mainContainerStyle}>
            <Text style={styles.titleTextStyle}>{`${t(title ? title : '')}`}</Text>
        <Dropdown
          style={[styles.dropdown]}
          placeholderStyle={styles.placeholderStyle}
          selectedTextStyle={styles.selectedTextStyle}
          inputSearchStyle={styles.inputSearchStyle}
          itemTextStyle={styles.itemTextStyle}
          itemContainerStyle={styles.itemContainerStyle}
          showsVerticalScrollIndicator={false}
          iconStyle={styles.iconStyle}
          data={data}
          search={search}
          maxHeight={ms(150)}
          labelField="label"
          valueField="value"
          placeholder={t(placeHolder)}
          searchPlaceholder="Search..."
          value={value}
          onFocus={() => setIsFocus(true)}
          onBlur={() => setIsFocus(false)}
          onChange={item => {
            setValue(item.value);
            setIsFocus(false);
            onSelect(item);
          }}
        />
        </View>
    )
};
export default CustomDropdownPicker;

const styles = StyleSheet.create({
    container: {
      backgroundColor: 'white',
      padding: 16,
    },
    titleTextStyle: {
        fontFamily: AppFonts.Medium,
        fontSize: ms(13),
        color: Colors.pipeIdTextBlack,
        marginBottom: ms(5)
    },
    dropdown: {
      height: ms(45),
      borderColor: Colors.inputBorder,
      borderRadius: ms(5),
      borderWidth: ms(0.9),
      paddingHorizontal: ms(8),
      backgroundColor: Colors.containerligetBlue,
    },
    icon: {
      marginRight: 5,
    },
    label: {
      position: 'absolute',
      backgroundColor: 'white',
      left: 22,
      top: 8,
      zIndex: 999,
      paddingHorizontal: 8,
      fontSize: 14,
    },
    placeholderStyle: {
      fontSize: ms(13),
      color: Colors.textLightGray,
      opacity: 0.5
    },
    selectedTextStyle: {
      fontSize: ms(13),
    },
    iconStyle: {
      width: ms(25),
      height: ms(25),
      color: Colors.black
    },
    inputSearchStyle: {
      height: 40,
      fontSize: 16,
    },
    itemTextStyle: {
        fontFamily: AppFonts.Medium,
        fontSize: ms(13),
        color: Colors.black
    },
    itemContainerStyle: {
        borderBottomColor: Colors.progressAsh,
        borderBottomWidth: ms(0.5)
    }
  });