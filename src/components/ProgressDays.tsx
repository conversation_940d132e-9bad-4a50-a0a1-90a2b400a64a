import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { ms } from '../utils/Scale/Scaling';
import Colors from '../utils/Colors/Colors';
import Strings from '../utils/Strings/Strings';

interface ProgressDaysProps {
    progressDays: string;
    manDays: string;
    date: string;
    customStyle?: ViewStyle;
}


const ProgressDays: React.FC<ProgressDaysProps> = ({ progressDays, manDays, date, customStyle }) => {
    return (
        <View style={[styles.container]}>
            <View style={[styles.itemContainer, styles.leftAlign]}>
                <Text style={styles.labelLeft}>{Strings.DailyProgressApprover.progressQty}</Text>
                <Text style={styles.valueLeft}>{progressDays}</Text>
            </View>
            <View style={[styles.itemContainer, styles.centerAlign]}>
            <View style={styles.internalLeftAlign}>
                <Text style={styles.labelCenter}>{Strings.DailyProgressApprover.manDays}</Text>
                <Text style={styles.valueCenter}>{manDays}</Text>
            </View>
            </View>
            <View style={[styles.itemContainer, styles.rightAlign]}>
                <View style={styles.internalLeftAlign}>
                    <Text style={styles.labelRight}>{Strings.DailyProgressApprover.date}</Text>
                    <Text style={styles.valueRight}>{date}</Text>
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        backgroundColor: '#ffffff',
        paddingVertical: ms(8),
        paddingHorizontal: 0,
        width: '100%',
    },
    itemContainer: {
        flex: 1,
        minWidth: 0,
    },
    leftAlign: {
        alignItems: 'flex-start',
    },
    centerAlign: {
        alignItems: 'center',
    },
    rightAlign: {
        alignItems: 'flex-end',
    },
    internalLeftAlign: {
        alignItems: 'flex-start',
    },
    labelLeft: {
        fontFamily: 'MNMedium',
        fontSize: ms(13),
        color: Colors.textInputBlack,
        marginBottom: ms(4),
        textAlign: 'left',
        width: '100%',
    },
    valueLeft: {
        fontFamily: 'MNBold',
        fontSize: ms(16),
        color: Colors.textPrimary,
        textAlign: 'left',
        width: '100%',
    },
    labelCenter: {
        fontFamily: 'MNMedium',
        fontSize: ms(13),
        color: Colors.textInputBlack,
        marginBottom: ms(4),
        textAlign: 'center',
        width: '100%',
    },
    valueCenter: {
        fontFamily: 'MNBold',
        fontSize: ms(16),
        color: Colors.textPrimary,
        textAlign: 'center',
        width: '100%',
    },
    labelRight: {
        fontFamily: 'MNMedium',
        fontSize: ms(13),
        color: Colors.textInputBlack,
        marginBottom: ms(4),
        width: '100%'
    },
    valueRight: {
        fontFamily: 'MNBold',
        fontSize: ms(16),
        color: Colors.textPrimary,
        textAlign: 'right',
        width: '100%',
    },
});

export default ProgressDays; 