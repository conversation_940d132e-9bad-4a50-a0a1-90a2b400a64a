import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface ProgressItemProps {
  text: string;
  onClick: () => void;
}

const ProgressItem: React.FC<ProgressItemProps> = ({ text, onClick }) => (
  <TouchableOpacity style={styles.item} onPress={onClick}>
    <View style={styles.textContainer}>
      <Text style={styles.text} numberOfLines={2}>{text}</Text>
    </View>
    <View style={styles.plusContainer}>
      <MaterialIcons name="add" size={24} color="#1976D2" />
    </View>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // backgroundColor: '#ffffff',
    padding: 8,
    marginVertical: 4,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  textContainer: {
    flex: 1,
    marginRight: 8,
    minWidth: 0,
  },
  text: {
    fontSize: 16,
    color: '#222',
  },
  plusContainer: {
    width: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default ProgressItem; 