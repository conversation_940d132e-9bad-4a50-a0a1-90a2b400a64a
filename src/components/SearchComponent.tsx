import React from 'react';
import { View, TextInput, StyleSheet, StyleProp, ViewStyle } from 'react-native';
import SearchIcon from '../assets/svg/Search.svg'
import Colors from '../utils/Colors/Colors';
import Strings from '../utils/Strings/Strings';
import { ms } from '../utils/Scale/Scaling';

interface SearchFieldProps {
  customStyle?: StyleProp<ViewStyle>
  value: string;
  placeholder?: string;
  onChange: (text: string) => void;
  hintText?: string
}
const SearchComponent: React.FC<SearchFieldProps> = ({ customStyle, value, onChange, placeholder }) => {

  return (

    <View style={[styles.searchBar, customStyle]}>
      <SearchIcon width={ms(18)} height={ms(18)} />
      <TextInput
        style={styles.input}
        placeholder={placeholder ? placeholder : Strings.DailyProgress.search}
        value={value}
        onChangeText={onChange}
        placeholderTextColor={Colors.searchTextBlack}
      />
    </View>
  );
};

const styles = StyleSheet.create({

  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderRadius: ms(4),
    paddingHorizontal: ms(12),
    borderWidth: ms(1),
    borderColor: Colors.grey,
    marginBottom: ms(15),
    marginTop: ms(8),
    marginHorizontal: ms(20)
  },
  input: {
    flex: 1,
    fontSize: ms(14),
    marginHorizontal: ms(7),
    color: Colors.black,
    fontFamily: 'MNMedium',
    paddingVertical: ms(10),
  },
});

export default SearchComponent;