import React from 'react';
import { Pressable, Text, StyleSheet } from 'react-native';
import Colors from '../utils/Colors/Colors';
import { ms } from '../utils/Scale/Scaling';

interface BorderedButtonProps {
    testID?: string
    title: string;
    onPress: () => void;
}

const BorderedButton: React.FC<BorderedButtonProps> = ({ testID, title, onPress }) => {
    return (
        <Pressable 
        testID={testID}
        style={styles.button} onPress={onPress}>
            <Text style={styles.text}>{title}</Text>
        </Pressable>
    );
};

const styles = StyleSheet.create({
    button: {
        width: '45%',
        backgroundColor: Colors.white,
        borderColor: Colors.blue,
        borderWidth: 1,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
    },
    text: {
        color: Colors.blue,
        fontSize: ms(16),
        fontFamily: 'MNBold',
    },
});

export default BorderedButton; 