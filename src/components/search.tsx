import React from 'react';
import { View, TextInput, StyleSheet } from 'react-native';
import Search from '../assets/svg/Search.svg';
import Colors from '../utils/Colors/Colors';
import { ms } from '../utils/Scale/Scaling';
import { t } from 'i18next';

interface SearchFieldProps {
    onChange: (text: string) => void;
    value: string;
    placeholder?: string;
}

const SearchBar: React.FC<SearchFieldProps> = ({ onChange, value, placeholder }) => {
    return (
        <View style={styles.searchBar}>
            <Search />
            <TextInput
                style={styles.input}
                placeholder={placeholder || t('commonStrings.search')}
                value={value}
                onChangeText={onChange}
                placeholderTextColor={Colors.text}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    searchBar: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.white,
        borderRadius: ms(4),
        paddingHorizontal: ms(12),
        borderWidth: 1,
        borderColor: Colors.grey,
        marginHorizontal: ms(10),
        marginVertical: ms(15),
    },
    input: {
        fontFamily: 'MNMedium',
        fontSize: ms(12),
        marginHorizontal: ms(5),
        paddingVertical: ms(10),
        color: Colors.black,
        width: '90%'
    },
});

export default SearchBar;