import React, { useState } from 'react';
import { Platform, View, Text, Button } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import BottomPopup from '../BottomPopup';


const TimePickerModal = ({ visible, onClose, onTimeSelect }) => {
  const [time, setTime] = useState(new Date());
  const [showPicker, setShowPicker] = useState(Platform.OS === 'ios'); // show inline for iOS

  const handleChange = (event, selectedTime) => {
    if (Platform.OS === 'android') {
      setShowPicker(false); // hide after selection on Android
    }

    if (selectedTime) {
      setTime(selectedTime);
      onTimeSelect && onTimeSelect(selectedTime);
    }
  };

  return (
    <BottomPopup visible={visible} onCancelPress={onClose}>
      <Text style={{ fontSize: 16, margin: 12, fontWeight: '600' }}>Select Time</Text>

      <View style={{ paddingHorizontal: 20, paddingBottom: 20 }}>
        {/* For Android: Show picker only when triggered */}
        {Platform.OS === 'android' && !showPicker && (
          <Button title="Pick Time" onPress={() => setShowPicker(true)} />
        )}

        {Platform.OS === 'android' && showPicker && (
          <DateTimePicker
            value={time}
            mode="time"
            display="spinner" // spinner works best for bottom-sheet style
            onChange={handleChange}
            style={{
              backgroundColor: 'white',
              alignSelf: 'center',
            }}
          />
        )}
      </View>
    </BottomPopup>
  );
};

export default TimePickerModal;
