import { Pressable, Text, StyleSheet, StyleProp, ViewStyle, View } from 'react-native'
import React from 'react'
import { Svg, Rect, Defs, LinearGradient, Stop } from 'react-native-svg'
import Colors from '../utils/Colors/Colors';
import { ms } from '../utils/Scale/Scaling';
import { AppFonts } from './Fonts';

interface ButtonProps {
    onPress: () => void;
    onSecondaryPress?: () => void;
    title: string;
    customWidth?: StyleProp<ViewStyle>;
    testID?: string;
    mainContainerStyle?: StyleProp<ViewStyle>;
    secondaryButtonStyle?: StyleProp<ViewStyle>;
    secondaryButtonTitle?: string;
    showSecondaryButton?: boolean;
}

const ButtonComponent: React.FC<ButtonProps> = ({
    onPress,
    title,
    customWidth,
    testID = 'button',
    mainContainerStyle,
    secondaryButtonStyle,
    secondaryButtonTitle,
    showSecondaryButton,
    onSecondaryPress
}) => {
    return (
        <View style={[styles.mainContainer, mainContainerStyle]}>
            {showSecondaryButton && <Pressable 
            style={[styles.secondaryButtonStyle, secondaryButtonStyle]}
            onPress={onSecondaryPress}>
                <Text style={[styles.secondaryTextStyle]}>{secondaryButtonTitle}</Text>
            </Pressable>}

            <Pressable style={[styles.button(showSecondaryButton), customWidth]}
            onPress={onPress}>
                <Svg height={"100%"} width="100%" style={StyleSheet.absoluteFill}>
                     <Defs>
                         <LinearGradient id="grad" x1="0" y1="0" x2="0" y2="1">
                             <Stop offset="0" stopColor={Colors.darkBlue} stopOpacity="1" />
                             <Stop offset="1" stopColor={Colors.forgotPinBlue} stopOpacity="1" />
                         </LinearGradient>
                     </Defs>
                     <Rect x="0" y="0" width="100%" height={"100%"} rx={8} fill="url(#grad)" />
                 </Svg>
                <Text style={styles.text}>{title}</Text>
            </Pressable>
        </View>
    )
}

const styles = StyleSheet.create({
    mainContainer: {
            flexDirection: 'row',
            alignItems: 'center',
            elevation: 10,
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 2,
            },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            paddingHorizontal: ms(15),
            paddingTop: ms(10),
            paddingBottom: ms(10),
            backgroundColor: Colors.white,
            borderTopColor: Colors.grey,
            borderTopWidth: ms(0.5),
    },
    secondaryTextStyle: {
        fontFamily: AppFonts.Bold,
        fontSize: ms(16),
        color: Colors.blue,
    },
    secondaryButtonStyle: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        height: ms(44),
        borderWidth: ms(0.9),
        borderColor: Colors.borderColor,
        borderRadius: ms(8),
        marginRight: ms(20)
    },
    button: (showSecondaryButton: boolean) => {
        return {
        flex: showSecondaryButton ? 1 : undefined,
        width: showSecondaryButton ? undefined : '100%',
        alignItems: 'center',
        justifyContent: 'center',
        height: ms(44),
        borderWidth: ms(0.9),
        borderColor: Colors.borderColor,
        borderRadius: ms(8)
        }
    },
    text: {
        color: Colors.white,
        fontFamily: AppFonts.Bold,
        fontSize: ms(15.5),
    },
})

export default ButtonComponent;