import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import AddBlue from '../assets/svg/add_blue.svg';
import Colors from '../utils/Colors/Colors';
import { ms } from '../utils/Scale/Scaling';

interface ProgressItemProps {
  text: string;
  onClick: () => void;
}

const ProgressItem: React.FC<ProgressItemProps> = ({ text, onClick }) => (
  <TouchableOpacity style={styles.item} onPress={onClick}>
    <View style={styles.textContainer}>
      <Text style={styles.text} numberOfLines={3}>{text}</Text>
    </View>
    <AddBlue />
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: Colors.white,
    padding: ms(12),
    marginVertical: ms(4),
    borderRadius: ms(8),
    borderWidth: ms(1),
    borderColor: Colors.searchBorderGrey,
  },
  textContainer: {
    flex: 1,
    marginRight: ms(8),
    minWidth: 0,
  },
  text: {
    fontSize: ms(14),
    color: Colors.textPrimary,
    fontWeight: '600',
  },
});

export default ProgressItem; 