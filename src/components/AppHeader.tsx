import React from 'react';
import { View, Text, StyleSheet, Pressable } from 'react-native';
import Colors from '../utils/Colors/Colors';
import Icon from 'react-native-vector-icons/Ionicons';
import { ms } from '../utils/Scale/Scaling';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { AppFonts } from './Fonts';

interface AppHeaderProps {
    title: string;
    rightContent?: React.ReactNode;
    onBookmarkPress?: () => void;
    isRightIconVisible?: boolean;
    onBackPress?: () => void;
}

const AppHeader: React.FC<AppHeaderProps> = ({
    title,
    rightContent,
    onBookmarkPress,
    isRightIconVisible,
    onBackPress
}) => {
    const navigation = useNavigation<NavigationProp<any>>();

    const handleBackPress = () => {
        if (onBackPress) {
            onBackPress();
        } else {
            navigation.goBack();
        }
    };

    return (
        <View style={styles.container}>
            <View style={styles.leftContent}>
                <Pressable
                    onPress={handleBackPress}
                    testID="back-button"
                >
                    <Icon name='chevron-back-outline' size={ms(20)} color={Colors.black} />
                </Pressable>
                <Text style={styles.title}>{title}</Text>
            </View>
            {/* {
                isRightIconVisible &&
                <View style={styles.rightIcons}>
                    <Pressable
                        onPress={onBookmarkPress}
                        testID="bookmark-button"
                    >
                        <Bookmark />
                    </Pressable>
                </View>
            } */}

            {!!rightContent && (
                <View style={styles.rightIcons}>
                    <Pressable onPress={onBookmarkPress} testID="right-content-button">
                        {rightContent}
                    </Pressable>
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: ms(14),
        paddingVertical: ms(16),
        elevation: 10,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        backgroundColor: Colors.white,
    },
    title: {
        fontFamily: AppFonts.Bold,
        fontSize: ms(16),
        color: Colors.secondary,
        marginHorizontal: ms(15),
        marginBottom: ms(2),
    },
    leftContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    rightIcons: {
        flexDirection: 'row',
    },
});

export default AppHeader;