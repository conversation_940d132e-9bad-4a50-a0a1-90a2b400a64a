import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import Colors from '../utils/Colors/Colors';
import { ms } from '../utils/Scale/Scaling';

interface CancelButtonProps {
    title: string;
    onPress: () => void;
}

const CancelButton: React.FC<CancelButtonProps> = ({ title, onPress }) => {
    return (
        <TouchableOpacity style={styles.button} onPress={onPress}>
            <Text style={styles.text}>{title}</Text>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    button: {
        flex: 1,
        backgroundColor: Colors.white,
        borderColor: Colors.blue,
        borderWidth: 1,
        borderRadius: 8,
        paddingVertical: ms(12),
        marginHorizontal: ms(5),
        alignItems: 'center',
        justifyContent: 'center',
    },
    text: {
        color: Colors.blue,
        fontSize: ms(16),
        fontFamily: 'MNMedium', // Assuming a similar font style
    },
});

export default CancelButton; 