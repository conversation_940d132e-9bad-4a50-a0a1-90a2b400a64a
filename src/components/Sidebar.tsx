import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import ArrowDownBlue from '../assets/svg/arrow_down_blue.svg';
import Colors from '../utils/Colors/Colors';
import { ms } from '../utils/Scale/Scaling';
import { WBSItem } from '../model/DailyProgress/DailyProgressData';

interface SidebarProps {
  selectedItems: WBSItem[];
  onItemPress: (item: WBSItem) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ selectedItems, onItemPress }) => (
  <View style={styles.sidebar}>
    <ScrollView contentContainerStyle={styles.scrollContent}>
      {selectedItems.map((item, index) => {
        const isLast = index === selectedItems.length - 1;
        return (
          <React.Fragment key={index}>
            <View
              style={[
                styles.progressStep,
                isLast && styles.activeStep
              ]}
            >
              <TouchableOpacity key={item.entity_Code} onPress={() => onItemPress(item)}>
                <Text style={[styles.text, isLast && styles.activeText]}>
                  {item.entity_Description}
                </Text>
              </TouchableOpacity>
            </View>
            {index !== selectedItems.length - 1 && (
              <View style={styles.dividerContainer}>
                <View style={styles.verticalLine} />
                <View style={styles.arrowWrapper}>
                  <ArrowDownBlue height={15} width={15} />
                </View>
              </View>
            )}
          </React.Fragment>
        );
      })}
    </ScrollView>
  </View>
);

const styles = StyleSheet.create({
  sidebar: {
    width: ms(150),
    backgroundColor: Colors.white,
    marginTop: ms(5),
  },
  scrollContent: {
    alignItems: 'stretch',
  },
  progressStep: {
    paddingHorizontal: ms(16),
    paddingVertical: ms(10),
    alignItems: 'flex-start',
    justifyContent: 'center',
    backgroundColor: Colors.lightBlueSidebar,
  },
  activeStep: {
    backgroundColor: Colors.blue,
  },
  text: {
    fontSize: ms(13),
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  activeText: {
    color: Colors.white,
  },
  dividerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: ms(3), // space between steps
    position: 'relative',
  },
  verticalLine: {
    width: ms(2),
    backgroundColor: Colors.pipeIdTextBlack,
    height: '100%',
    position: 'absolute',
    zIndex: 0,
  },
  arrowWrapper: {
    zIndex: 1,
    padding: ms(2),
  },
});

export default Sidebar; 