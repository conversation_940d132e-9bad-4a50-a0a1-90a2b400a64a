import React from 'react';
import { View, Text, StyleSheet, Pressable } from 'react-native';
import { ms } from '../utils/Scale/Scaling';
import Colors from '../utils/Colors/Colors';
import ProgressDays from '../components/ProgressDays';
import ActionButton from '../components/ActionButton';
import Profile from '../assets/svg/approver_profile.svg';
import BoxArrow from '../assets/svg/box_arrow.svg';

interface ProgressCardProps {
  name: string;
  nameColor?: string;
  details: string;
  progressQty: string;
  manDays: string;
  date: string;
  onApprove: () => void;
  onReject: () => void;
  onArrow: () => void;
}

const ProgressCard: React.FC<ProgressCardProps> = ({
  name,
  details,
  progressQty,
  manDays,
  date,
  onApprove,
  onReject,
  onArrow,
}) => {
  return (
    <>
      <View style={styles.card}>
        <View style={styles.headerRow}>
          <View style={{ marginBottom: 5 }}>
            <Profile />
          </View>

          <Text style={styles.name}>{name}</Text>
        </View>
        <Text style={styles.details}>
          {details.split(/(\/)/g).map((part, idx) =>
            part === '/' ? (
              <Text key={idx} style={{ color: Colors.textInputBlack }}>/</Text>
            ) : (
              <Text key={idx} style={{ color: Colors.textPrimary }}>{part}</Text>
            )
          )}
        </Text>
      </View>

      <View style={styles.progressDaysWrap}>
        <ProgressDays
          progressDays={progressQty}
          manDays={manDays}
          date={date}
          customStyle={styles.progressDaysInner}
        />

        <View style={styles.buttonRow}>
          <View style={styles.actionBtnWrap}>
            <ActionButton
              type="reject"
              onPress={onReject}
              customStyle={styles.actionBtnInner}
            />
          </View>
          <View style={styles.actionBtnWrap}>
            <ActionButton
              type="approve"
              onPress={onApprove}
              customStyle={styles.actionBtnInnerGreen}
            />
          </View>
          <Pressable  onPress={onArrow}>
            <BoxArrow width={30} height={27}/>
          </Pressable>
        </View>
      </View>
    </>
  );

};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#f7f8fa',
    borderRadius: ms(10),
    marginBottom: ms(0),
    padding: ms(16),
    borderWidth: ms(1),
    borderColor: '#e6eaf0',

    // 🔽 Elevation for Android
    elevation: 5,

    // 🔽 Shadow for iOS
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cards: {
    backgroundColor: Colors.white,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: ms(4),
  },
  name: {
    fontFamily: 'MNRegular',
    fontSize: ms(16),
    color: Colors.blue,
    fontWeight: '500',
    marginLeft: 3
  },
  details: {
    fontFamily: 'MNRegular',
    fontSize: ms(14),
    color: Colors.textPrimary,
    marginBottom: ms(8),
    fontWeight: '400',
  },
  progressDaysWrap: {
    backgroundColor: Colors.white,
    borderTopWidth: ms(1),
    borderTopColor: '#e6eaf0',
    marginHorizontal: ms(1),
    marginTop: ms(-5),
    // paddingTop: ms(12),
    paddingHorizontal: ms(16),
    borderBottomLeftRadius: ms(10),
    borderBottomRightRadius: ms(10),

    elevation: 5,

    // 🔽 Shadow for iOS
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,

  },
  progressDaysInner: {
    backgroundColor: Colors.white,
    borderRadius: ms(10),
  },
  buttonRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: ms(15),
    marginTop: ms(12),
    backgroundColor: 'white',
    marginBottom: 20
  },
  actionBtnWrap: {
    backgroundColor: Colors.white,
    borderRadius: ms(10),
  },
 
  actionBtnInner: {
    backgroundColor: Colors.white,
    borderRadius: ms(4),
    width: ms(130),
    paddingHorizontal: 30
  },
  actionBtnInnerGreen: {
    backgroundColor: Colors.onlineGreen,
    borderRadius: ms(4),
  },
});

export default ProgressCard; 