import React from 'react';
import { View, Text, StyleSheet, Pressable, ViewStyle } from 'react-native';
import Colors from '../utils/Colors/Colors';
import { ms } from '../utils/Scale/Scaling';
import { AppFonts } from './Fonts';

interface Tab {
    key: string;
    label: string;
}

interface TopTabNavigatorProps {
    tabs: Tab[];
    activeTab: string;
    onTabChange: (tab: string) => void;
    containerStyle?: ViewStyle; 
}

const TopTabNavigator: React.FC<TopTabNavigatorProps> = ({ tabs, activeTab, onTabChange, containerStyle }) => {
    return (
        <View style={[styles.container, containerStyle]}>
            <View style={styles.tabContainer}>
                {tabs.map(tab => (
                    <Pressable
                        key={tab.key}
                        style={[styles.tab, activeTab === tab.key && styles.activeTab]}
                        onPress={() => onTabChange(tab.key)}
                    >
                        <Text style={[styles.tabText, activeTab === tab.key && styles.activeTabText]}>{tab.label}</Text>
                    </Pressable>
                ))}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.tabBgColoor,
        height: ms(48),
        
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: ms(6),
        width: '90%',
    },
    tabContainer: {
        flexDirection: 'row',
        width: '100%',
        paddingHorizontal: ms(5),
        paddingVertical: ms(3),
    },
    tab: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        height: ms(30),
    },
    activeTab: {
        borderBottomWidth: ms(2.5),
        borderBottomColor: Colors.secondary,
        backgroundColor: Colors.white,
        borderRadius: ms(7),
    },
    tabText: {
        fontFamily: AppFonts.SemiBold,
        fontSize: ms(13),
        color: Colors.textInputBlack,
        fontWeight: '500',
    },
    activeTabText: {
        fontFamily: AppFonts.SemiBold,
        fontSize: ms(14),
        color: Colors.secondary,
        fontWeight: '500'
    },
});

export default TopTabNavigator;