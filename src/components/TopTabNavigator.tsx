import React from 'react';
import { View, Text, StyleSheet, Pressable } from 'react-native';
import Colors from '../utils/Colors/Colors';
import { ms } from '../utils/Scale/Scaling';

interface Tab {
    key: string;
    label: string;
}

interface TopTabNavigatorProps {
    tabs: Tab[];
    activeTab: string;
    onTabChange: (tab: string) => void;
}

const TopTabNavigator: React.FC<TopTabNavigatorProps> = ({ tabs, activeTab, onTabChange }) => {
    return (
        <View style={styles.container}>
            <View style={styles.tabContainer}>
                {tabs.map(tab => (
                    <Pressable
                        key={tab.key}
                        style={[styles.tab, activeTab === tab.key && styles.activeTab]}
                        onPress={() => onTabChange(tab.key)}
                    >
                        <Text style={[styles.tabText, activeTab === tab.key && styles.activeTabText]}>{tab.label}</Text>
                    </Pressable>
                ))}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.tabBgColoor,
        height: ms(45),
        width: '90%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: ms(6),
    },
    tabContainer: {
        flexDirection: 'row',
        width: '100%',
        padding: ms(5)
    },
    tab: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        height: ms(30),
    },
    activeTab: {
        borderBottomWidth: ms(2),
        borderBottomColor: Colors.secondary,
        backgroundColor: Colors.white,
        borderRadius: ms(4),
    },
    tabText: {
        fontFamily: "MNSemiBold",
        fontSize: ms(13),
        color: Colors.textInputBlack,
        fontWeight: '700',
    },
    activeTabText: {
        fontFamily: "MNSemiBold",
        fontSize: ms(14),
        color: Colors.textPrimary,
        fontWeight: '700'
    },
});

export default TopTabNavigator;