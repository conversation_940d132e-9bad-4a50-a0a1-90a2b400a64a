import * as React from 'react';
import { StyleProp, StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native';
import Colors from '../../utils/Colors/Colors';
import { ms } from '../../utils/Scale/Scaling';

interface CardViewProps {
    containerStyle?: StyleProp<ViewStyle>;
    children: React.ReactNode;
    disabled?: boolean;
    onPress?: (data?: any) => void;
  }

const CardView: React.FC<CardViewProps> = ({
    children,
    containerStyle,
    disabled = false,
    onPress
}) => {
    return (
        <View>
        <TouchableOpacity 
        disabled={disabled} 
        style={[styles.cardContainer, containerStyle]}
        onPress={onPress && onPress}>
            {children}
        </TouchableOpacity>
        </View>
    )
};
export default CardView;

const styles = StyleSheet.create({
    cardContainer: {
        backgroundColor: Colors.white,
        borderRadius: ms(8),
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        gap: ms(12),
        paddingHorizontal: ms(10),
        paddingVertical: ms(15),
        borderWidth: ms(0.2),
        borderColor: Colors.grey
    }
});