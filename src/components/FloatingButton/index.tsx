import * as React from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import { Icon } from '../Icons';
import { ms } from '../../utils/Scale/Scaling';
import Colors from '../../utils/Colors/Colors';
import { AppFonts } from '../Fonts';
import { SVG } from '../../utils/ImagePath';

interface floatingButtonProps {
    showAddIcon?: boolean;
    showImageIcon?: boolean;
    onMapPress?: () => void;
    onAddPress: () => void;
};

const FloatingButton = ({
    showAddIcon = true, 
    showImageIcon,
    onMapPress,
    onAddPress
}: floatingButtonProps) => {
    return (
        <View style={styles.container}>
            {showImageIcon && <Pressable style={[styles.blueCircle, styles.imageCircle]} onPress={onMapPress}>
                    <SVG.MapOutline />
            </Pressable>}

            {showAddIcon && <Pressable style={styles.blueCircle} onPress={onAddPress}>
                <Icon type={'Ionicons'} name={'add-sharp'} color={Colors.white} size={ms(25)} />
            </Pressable>}
        </View>
    )
};
export default FloatingButton;

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        bottom: ms(60), 
        right: ms(30)
    },
    blueCircle: {
        width: ms(50),
        height: ms(50),
        borderRadius: ms(50) / 2,
        backgroundColor: Colors.secondary,
        alignItems: 'center',
        justifyContent: 'center'
    },
    imageCircle: {
        marginBottom: ms(15),
        backgroundColor: Colors.white,
        borderColor: Colors.circleGrey,
        borderWidth: ms(0.9)
    },
});