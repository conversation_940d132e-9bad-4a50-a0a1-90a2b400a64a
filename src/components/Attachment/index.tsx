import * as React from 'react';
import { View, Text, TouchableOpacity, ScrollView, Image, StyleSheet, Alert, Platform, Linking, Modal, TouchableWithoutFeedback } from 'react-native';
import { SVG } from '../../utils/ImagePath';
import { ms } from '../../utils/Scale/Scaling';
import { AppFonts } from '../Fonts';
import Strings from '../../utils/Strings/Strings';
import {
    launchCamera,
    launchImageLibrary,
    MediaType,
    PhotoQuality,
    Asset as ImagePickerAsset,
} from 'react-native-image-picker';
import { check, PERMISSIONS, request, RESULTS } from 'react-native-permissions';
import BottomPopupImageUpload from '../BottomPopupImageUpload';
import axios from 'axios';
import Colors from '../../utils/Colors/Colors';
import BottomPopup from '../BottomPopup';


const AttachmentComponent = () => {

    const [selectedImages, setSelectedImages] = React.useState<ImagePickerAsset[]>([]);
    const [imgUploadModalVisible, setImgUploadModalVisible] = React.useState(false);
    const [uploadingImages, setUploadingImages] = React.useState(false);
    const [uploadError, setUploadError] = React.useState<string | null>(null);
    const [uploadedUniqueIds, setUploadedUniqueIds] = React.useState<string>('');

    const checkAndRequestCameraPermission = React.useCallback(async () => {
        try {
            const permission = Platform.select({
                ios: PERMISSIONS.IOS.CAMERA,
                android: PERMISSIONS.ANDROID.CAMERA,
            });

            if (!permission) {
                Alert.alert('Error', 'Camera permission not available');
                return false;
            }

            const result = await check(permission);

            if (result === RESULTS.GRANTED) {
                return true;
            }

            if (result === RESULTS.DENIED) {
                const permissionResult = await request(permission);
                return permissionResult === RESULTS.GRANTED;
            }

            if (result === RESULTS.BLOCKED) {
                Alert.alert(
                    'Permission Required',
                    'Please enable camera access in your device settings to use this feature.',
                    [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Open Settings', onPress: () => Linking.openSettings() },
                    ],
                );
                return false;
            }

            return false;
        } catch (error) {
            console.error('Error checking camera permission:', error);
            return false;
        }
    }, []);

    const checkAndRequestGalleryPermission = React.useCallback(async () => {
        try {
            let permission;
            if (Platform.OS === 'android') {
                if (Platform.Version >= 33) {
                    permission = PERMISSIONS.ANDROID.READ_MEDIA_IMAGES;
                } else {
                    permission = PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE;
                }
            } else {
                permission = PERMISSIONS.IOS.PHOTO_LIBRARY;
            }

            if (!permission) {
                Alert.alert('Error', 'Gallery permission not available');
                return false;
            }

            const result = await check(permission);

            if (result === RESULTS.GRANTED) {
                return true;
            }

            if (result === RESULTS.DENIED) {
                const permissionResult = await request(permission);
                return permissionResult === RESULTS.GRANTED;
            }

            if (result === RESULTS.BLOCKED) {
                Alert.alert(
                    'Permission Required',
                    'Please enable gallery access in your device settings to use this feature.',
                    [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Open Settings', onPress: () => Linking.openSettings() },
                    ],
                );
                return false;
            }

            return false;
        } catch (error) {
            console.error('Error checking gallery permission:', error);
            return false;
        }
    }, []);


    const uploadSingleImage = async (img: ImagePickerAsset, index: number): Promise<string | null> => {
        const url = '*********************************:443/Pragatiext/UploadImaage';
        const headers = {
            'Ocp-Apim-Subscription-Key': 'ececb381826c4379843eef6128c6360e',
            'Content-Type': 'application/json',
            Accept: 'application/json',
        };
        const payload = {
            FileName: `Attachment ${index + 1}.jpg `,
            File: img.base64 || '',
        };

        try {
            const response = await axios.post(url, payload, { headers });
            const data = response.data;
            if (Array.isArray(data) && data[0]?.UniqueID) {
                const uniqueId = data[0].UniqueID;
                console.log(`Successfully uploaded image ${payload.FileName} with UniqueID: ${uniqueId}`);
                return uniqueId;
            } else {
                console.error('Invalid response format:', data);
                setUploadError(`Invalid response format for ${payload.FileName}`);
                return null;
            }
        } catch (err: any) {
            console.error('Upload error:', err?.response?.data || err.message || err);
            setUploadError(`Failed to upload ${payload.FileName}: ${err?.response?.data || err.message || err}`);
            return null;
        }
    };

    // Modified image selection handlers
    const handleCameraPress = React.useCallback(async () => {
        const hasPermission = await checkAndRequestCameraPermission();
        if (hasPermission) {
            const options = {
                mediaType: 'photo' as MediaType,
                includeBase64: true,
                maxHeight: 2000,
                maxWidth: 2000,
                saveToPhotos: true,
                quality: 0.8 as PhotoQuality,
            };

            launchCamera(options, async (response) => {
                if (response.didCancel) {
                    console.log('User cancelled camera');
                } else if (response.errorCode) {
                    console.log('Camera Error: ', response.errorMessage);
                    Alert.alert('Error', 'Failed to take photo');
                } else if (response.assets && Array.isArray(response.assets) && response.assets.length > 0) {
                    setSelectedImages(prev => [...prev, ...response.assets!]);
                    setImgUploadModalVisible(false);

                    // Upload the new image immediately
                    setUploadingImages(true);
                    setUploadError(null);
                    const uniqueId = await uploadSingleImage(response.assets[0], selectedImages.length);
                    if (uniqueId) {
                        setUploadedUniqueIds(prev => {
                            const newIds = prev ? `${prev}~${uniqueId}` : uniqueId;
                            console.log('Updated UniqueIDs:', newIds);
                            return newIds;
                        });
                    }
                    setUploadingImages(false);
                }
            });
        }
    }, [checkAndRequestCameraPermission, selectedImages.length]);


    const removeImage = (index: number) => {
        setSelectedImages(prev => {
            const newImages = prev.filter((_, i) => i !== index);
            // Remove the corresponding UniqueID
            setUploadedUniqueIds(prevIds => {
                if (!prevIds) return '';
                const idsArr = prevIds.split('~');
                idsArr.splice(index, 1);
                return idsArr.join('~');
            });
            return newImages;
        });
    };

    const handleGalleryPress = React.useCallback(async () => {
        const hasPermission = await checkAndRequestGalleryPermission();
        if (hasPermission) {
            const options = {
                mediaType: 'photo' as MediaType,
                includeBase64: true,
                maxHeight: 2000,
                maxWidth: 2000,
                selectionLimit: 0,
                quality: 0.8 as PhotoQuality,
            };

            launchImageLibrary(options, async (response) => {
                if (response.didCancel) {
                    console.log('User cancelled gallery picker');
                } else if (response.errorCode) {
                    console.log('Gallery Error: ', response.errorMessage);
                    Alert.alert('Error', 'Failed to pick image from gallery');
                } else if (response.assets && Array.isArray(response.assets) && response.assets.length > 0) {
                    setSelectedImages(prev => [...prev, ...response.assets!]);
                    setImgUploadModalVisible(false);

                    // Upload the new images immediately
                    setUploadingImages(true);
                    setUploadError(null);
                    for (let i = 0; i < response.assets.length; i++) {
                        const uniqueId = await uploadSingleImage(response.assets[i], selectedImages.length + i);
                        if (uniqueId) {
                            setUploadedUniqueIds(prev => {
                                const newIds = prev ? `${prev}~${uniqueId}` : uniqueId;
                                console.log('Updated UniqueIDs:', newIds);
                                return newIds;
                            });
                        }
                    }
                    setUploadingImages(false);
                }
            });
        }
    }, [checkAndRequestGalleryPermission, selectedImages.length]);

    return (
        <>
            {/* Image attachement compoentn */}
            <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Attachments *</Text>
                <View style={styles.uploadButtonOuter}>
                    <TouchableOpacity
                        style={styles.uploadButton}
                        onPress={() => setImgUploadModalVisible(true)}>
                        <SVG.Upload />
                        <Text style={styles.uploadButtonText}>
                            {Strings.DailyProgress.uploadImage}
                        </Text>
                    </TouchableOpacity>
                    <ScrollView horizontal style={{ marginTop: 10 }}>
                        {selectedImages.map((img, idx) => (
                            <View key={idx} style={{ marginRight: 10, position: 'relative' }}>
                                <Image
                                    source={{ uri: img.uri }}
                                    style={{ width: 80, height: 80, margin: 10 }}
                                />
                                <TouchableOpacity
                                    style={{
                                        position: 'absolute',
                                        top: 2,
                                        right: -3,
                                        backgroundColor: '#fff',
                                        borderRadius: 12,
                                        padding: 2,
                                        elevation: 2,
                                    }}
                                    onPress={() => removeImage(idx)}
                                >
                                    <SVG.Cross />
                                </TouchableOpacity>
                            </View>
                        ))}
                    </ScrollView>

                </View>
            </View>

 
                <BottomPopup
                visible={imgUploadModalVisible}
                onCancelPress={() => setImgUploadModalVisible(false)}>
                    <BottomPopupImageUpload
                        onCameraPress={handleCameraPress}
                        onGalleryPress={handleGalleryPress}
                    />
                </BottomPopup>

            {/* Add a modal for uploading images */}
            <Modal visible={uploadingImages} transparent animationType="fade">
                <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.4)' }}>
                    <View style={{ backgroundColor: 'white', padding: 30, borderRadius: 10 }}>
                        <Text style={{ fontSize: 18, fontWeight: 'bold' }}>Please wait...</Text>
                        <Text style={{ marginTop: 10 }}>Uploading images to server</Text>
                    </View>
                </View>
            </Modal>
            {/* Show error popup if upload fails */}
            {uploadError && (
                <Modal visible={!!uploadError} transparent animationType="fade">
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.4)' }}>
                        <View style={{ backgroundColor: 'white', padding: 30, borderRadius: 10 }}>
                            <Text style={{ fontSize: 18, fontWeight: 'bold', color: 'red' }}>Upload Error</Text>
                            <Text style={{ marginTop: 10 }}>{uploadError}</Text>
                            <TouchableOpacity onPress={() => setUploadError(null)} style={{ marginTop: 20 }}>
                                <Text style={{ color: 'blue' }}>Close</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>
            )}
        </>
    )
};
export default AttachmentComponent;

const styles = StyleSheet.create({
    inputContainer: {
        marginVertical: ms(3),
    },
    inputLabel: {
        fontSize: ms(13),
        marginBottom: 3,
        fontFamily: AppFonts.Light,
        color: Colors.pipeIdTextBlack,
    },
    uploadButtonOuter: {
        padding: ms(8),
        borderRadius: ms(5),
        borderWidth: ms(1),
        borderColor: Colors.searchBorderGrey,
    },
    uploadButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: ms(8),
        borderRadius: ms(5),
        borderWidth: ms(1),
        borderColor: Colors.secondary,
    },
    uploadButtonText: {
        color: Colors.secondary,
        marginLeft: ms(8),
    }
});