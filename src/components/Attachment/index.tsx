import * as React from 'react';
import { View, Text, TouchableOpacity, ScrollView, Image, StyleSheet, Alert, Platform, Linking, Modal } from 'react-native';
import { SVG } from '../../utils/ImagePath';
import { ms } from '../../utils/Scale/Scaling';
import { AppFonts } from '../Fonts';
import Strings from '../../utils/Strings/Strings';
import {
    launchCamera,
    launchImageLibrary,
    MediaType,
    PhotoQuality,
    Asset as ImagePickerAsset,
} from 'react-native-image-picker';
import { check, PERMISSIONS, request, RESULTS } from 'react-native-permissions';
import BottomPopupImageUpload from '../BottomPopupImageUpload';
import axios from 'axios';
import Colors from '../../utils/Colors/Colors';
import BottomPopup from '../BottomPopup';
import { UploadImageResponse, DownloadImageRequest, DownloadImageResponse } from '../../model/DailyProgress/DailyProgressData';
import { downloadImage } from '../../services/ApiRequests';
import { getUserInfo, getUserRolesInfo } from '../../utils/DataStorage/Storage';

interface AttachmentComponentProps {
    onUploadComplete?: (images: any[]) => void;
    uploadedImages?: UploadImageResponse[]; // Images to download from API when entering screen
    selectedTab?: string;
    imageUrl?: string;
    imageId?: string;
}

interface DownloadedImage {
    uniqueId: string;
    siteUrl: string;
    imageUri: string;
    loading: boolean;
    error?: string;
}

interface UploadedImage {
    uniqueId: string;
    siteUrl: string;
    imageUri: string;
    asset: ImagePickerAsset;
}

// Utility function to validate and convert base64 data
const validateAndConvertBase64 = (base64String: string): string | null => {
    try {
        // Remove any whitespace and newlines
        const cleanBase64 = base64String.trim().replace(/[\n\r\s]/g, '');

        // Basic base64 validation
        if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64)) {
            console.error('Invalid base64 format');
            return null;
        }

        // Check if it's a reasonable length for an image
        if (cleanBase64.length < 100) {
            console.error('Base64 data too short to be a valid image');
            return null;
        }

        // Try to decode a small portion to ensure it's valid
        try {
            atob(cleanBase64.substring(0, 100));
        } catch (e) {
            console.error('Base64 decode test failed');
            return null;
        }

        return cleanBase64;
    } catch (error) {
        console.error('Base64 validation error:', error);
        return null;
    }
};

const AttachmentComponent: React.FC<AttachmentComponentProps> = ({ onUploadComplete, uploadedImages = [], selectedTab: _selectedTab, imageUrl: _imageUrl, imageId: _imageId }) => {
    const selectedTab = _selectedTab;
    const imageUrl = _imageUrl;
    const imageId = _imageId;
    const [selectedImages, setSelectedImages] = React.useState<ImagePickerAsset[]>([]);
    const [imgUploadModalVisible, setImgUploadModalVisible] = React.useState(false);
    const [uploadingImages, setUploadingImages] = React.useState(false);
    const [uploadError, setUploadError] = React.useState<string | null>(null);
    const [downloadedImages, setDownloadedImages] = React.useState<DownloadedImage[]>([]);
    const [displayedUploadedImages, setDisplayedUploadedImages] = React.useState<UploadedImage[]>([]);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [uploadedImageResponses, setUploadedImageResponses] = React.useState<UploadImageResponse[]>([]);
    const [removedDownloadedIds, setRemovedDownloadedIds] = React.useState<string[]>([]);
    const userRole = getUserRolesInfo();
    const functionalRole = userRole?.RolesList?.[0]?.Functional_ROLE;
    console.log("AyyachmentCompon -- functionalRole: ", functionalRole, ' -- userRole: ', userRole);
    const updateParentWithIds = React.useCallback((imageResponse: UploadImageResponse[]) => {
        onUploadComplete?.(imageResponse);
    }, [onUploadComplete]);

    const addUploadedId = React.useCallback((uploadedImgResponse: UploadImageResponse) => {
        setUploadedImageResponses(prev => {
            const updated = [...prev, uploadedImgResponse];
            updateParentWithIds(updated);
            return updated;
        });
    }, [updateParentWithIds]);

    const removeUploadedImage = (uniqueId: string) => {
        setSelectedImages(prev => {
            const idx = prev.findIndex((_, i) => `local-${i}` === uniqueId);
            if (idx === -1) return prev;
            const newImages = prev.filter((_, i) => i !== idx);
            setUploadedImageResponses(prevIds => prevIds.filter((_, i) => i !== idx));
            setDisplayedUploadedImages(prev => prev.filter((img) => img.uniqueId !== uniqueId));
            return newImages;
        });
    };

    const removeDownloadedImage = (uniqueId: string) => {
        setDownloadedImages(prev => prev.filter(img => img.uniqueId !== uniqueId));
        setRemovedDownloadedIds(prev => [...prev, uniqueId]);
    };

    // Download image from server
    const downloadImageFromServer = async (uniqueId: string, siteUrl: string) => {
        const requestBody: DownloadImageRequest = {
            ModuleName: "PMP",
            Unique: uniqueId,
            SiteUrl: siteUrl,
        };

        return new Promise<DownloadImageResponse>((resolve, reject) => {
            downloadImage(
                requestBody,
                (response: any) => {
                    // Handle different response structures
                    let base64Data = null;
                    let success = false;
                    let message = '';

                    if (typeof response === 'string') {
                        // Direct base64 string response
                        base64Data = response;
                        success = true;
                    } else if (response && typeof response === 'object') {
                        // Object response
                        if (response.data) {
                            base64Data = response.data;
                            success = true;
                        } else if (response.base64) {
                            base64Data = response.base64;
                            success = true;
                        } else if (response.image) {
                            base64Data = response.image;
                            success = true;
                        } else if (response.success === false) {
                            message = response.message || 'Download failed';
                            success = false;
                        } else {
                            // Try to find base64 in nested properties
                            const findBase64InObject = (obj: any): string | null => {
                                for (const key in obj) {
                                    if (typeof obj[key] === 'string' && obj[key].length > 100) {
                                        const potentialBase64 = obj[key].trim();
                                        if (/^[A-Za-z0-9+/]*={0,2}$/.test(potentialBase64)) {
                                            return potentialBase64;
                                        }
                                    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                                        const found = findBase64InObject(obj[key]);
                                        if (found) return found;
                                    }
                                }
                                return null;
                            };

                            const foundBase64 = findBase64InObject(response);
                            if (foundBase64) {
                                base64Data = foundBase64;
                                success = true;
                            } else {
                                message = 'No image data found in response';
                                success = false;
                            }
                        }
                    } else {
                        message = 'Invalid response format';
                        success = false;
                    }

                    resolve({
                        success,
                        data: base64Data,
                        message
                    });
                },
                (error: any) => {
                    console.error('Download image error:', error);
                    reject(error);
                }
            );
        });
    };

    // Download and display images from API (when entering screen)
    const downloadAndDisplayImages = React.useCallback(async () => {
        // Only run for Pending For Approval with valid imageId and imageUrl
        if ((functionalRole !== 'Site Engineer' && imageId && imageUrl) || (functionalRole === 'Site Engineer' && selectedTab === 'Pending For Approval' && imageId && imageUrl)) {
            const uniqueId = imageId;
            const siteUrl = imageUrl;

            // Prevent duplicate downloaded images
            const alreadyExists = downloadedImages.some(img => img.uniqueId === uniqueId);
            if (!alreadyExists && !removedDownloadedIds.includes(uniqueId)) {
                setDownloadedImages(prev => [
                    ...prev,
                    {
                        uniqueId,
                        siteUrl,
                        imageUri: '',
                        loading: true,
                    }
                ]);
            }

            try {
                const response = await downloadImageFromServer(uniqueId, siteUrl);

                if (response.success && response.data) {
                    const validatedBase64 = validateAndConvertBase64(response.data);
                    if (!validatedBase64) throw new Error('Invalid base64 image data');

                    let imageUri = '';
                    if (validatedBase64.startsWith('iVBORw0KGgo')) {
                        imageUri = `data:image/png;base64,${validatedBase64}`;
                    } else if (validatedBase64.startsWith('/9j/')) {
                        imageUri = `data:image/jpeg;base64,${validatedBase64}`;
                    } else {
                        imageUri = `data:image/*;base64,${validatedBase64}`;
                    }

                    setDownloadedImages(prev =>
                        prev.map(img =>
                            img.uniqueId === uniqueId
                                ? { ...img, imageUri, loading: false }
                                : img
                        )
                    );
                } else {
                    setDownloadedImages(prev =>
                        prev.map(img =>
                            img.uniqueId === uniqueId
                                ? { ...img, loading: false, error: response.message || 'Download failed' }
                                : img
                        )
                    );
                }
            } catch (error: any) {
                setDownloadedImages(prev =>
                    prev.map(img =>
                        img.uniqueId === uniqueId
                            ? { ...img, loading: false, error: error.message || 'Download failed' }
                            : img
                    )
                );
            }
        }
        // Otherwise, do not download any image
    }, [selectedTab, imageId, imageUrl, downloadedImages, removedDownloadedIds]);

    // Trigger download when uploadedImages prop changes (when entering screen)
    React.useEffect(() => {
        downloadAndDisplayImages();
    }, [uploadedImages, downloadAndDisplayImages, removedDownloadedIds]);

    // Update displayed uploaded images when selectedImages change
    React.useEffect(() => {
        const newDisplayedImages = selectedImages.map((asset, index) => ({
            uniqueId: `local-${index}`,
            siteUrl: '',
            imageUri: asset.uri || '',
            asset
        }));
        setDisplayedUploadedImages(newDisplayedImages);
    }, [selectedImages]);

    const checkAndRequestCameraPermission = React.useCallback(async () => {
        try {
            const permission = Platform.select({
                ios: PERMISSIONS.IOS.CAMERA,
                android: PERMISSIONS.ANDROID.CAMERA,
            });

            if (!permission) {
                Alert.alert('Error', 'Camera permission not available');
                return false;
            }

            const result = await check(permission);
            if (result === RESULTS.GRANTED) return true;

            if (result === RESULTS.DENIED) {
                const permissionResult = await request(permission);
                return permissionResult === RESULTS.GRANTED;
            }

            if (result === RESULTS.BLOCKED) {
                Alert.alert('Permission Required',
                    'Please enable camera access in your device settings to use this feature.',
                    [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Open Settings', onPress: () => Linking.openSettings() },
                    ]
                );
            }
            return false;
        } catch (error) {
            console.error('Error checking camera permission:', error);
            return false;
        }
    }, []);

    const checkAndRequestGalleryPermission = React.useCallback(async () => {
        try {
            let permission;
            if (Platform.OS === 'android') {
                permission = Platform.Version >= 33
                    ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES
                    : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE;
            } else {
                permission = PERMISSIONS.IOS.PHOTO_LIBRARY;
            }

            if (!permission) {
                Alert.alert('Error', 'Gallery permission not available');
                return false;
            }

            const result = await check(permission);
            if (result === RESULTS.GRANTED) return true;

            if (result === RESULTS.DENIED) {
                const permissionResult = await request(permission);
                return permissionResult === RESULTS.GRANTED;
            }

            if (result === RESULTS.BLOCKED) {
                Alert.alert('Permission Required',
                    'Please enable gallery access in your device settings to use this feature.',
                    [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Open Settings', onPress: () => Linking.openSettings() },
                    ]
                );
            }

            return false;
        } catch (error) {
            console.error('Error checking gallery permission:', error);
            return false;
        }
    }, []);

    const uploadSingleImage = async (img: ImagePickerAsset, index: number): Promise<UploadImageResponse | null> => {
        const url = '*********************************:443/Pragatiext/UploadImaage';
        const headers = {
            'Ocp-Apim-Subscription-Key': 'ececb381826c4379843eef6128c6360e',
            'Content-Type': 'application/json',
            Accept: 'application/json',
        };

        const payload = {
            FileName: `Attachment ${index + 1}.jpg `,
            File: img.base64 || '',
        };

        try {
            const response = await axios.post<UploadImageResponse[]>(url, payload, { headers });
            const data = response.data;
            console.log('AttachmentPopup data: ', data);
            if (Array.isArray(data) && data[0]?.UniqueID) {
                console.log('AttachmentPopup data: ', data[0]);
                return data[0];
            } else {
                setUploadError(`Invalid response format for ${payload.FileName}`);
                return null;
            }
        } catch (err: any) {
            setUploadError(`Failed to upload ${payload.FileName}: ${err?.response?.data || err.message || err}`);
            return null;
        }
    };

    const handleCameraPress = React.useCallback(async () => {
        const hasPermission = await checkAndRequestCameraPermission();
        if (!hasPermission) return;

        const options = {
            mediaType: 'photo' as MediaType,
            includeBase64: true,
            maxHeight: 2000,
            maxWidth: 2000,
            saveToPhotos: true,
            quality: 0.8 as PhotoQuality,
        };

        launchCamera(options, async (response) => {
            if (response.didCancel || response.errorCode) {
                Alert.alert('Error', 'Failed to take photo');
                return;
            }

            if (response.assets?.length) {
                const asset = response.assets[0];
                setSelectedImages(prev => [...prev, asset]);
                setImgUploadModalVisible(false);

                setUploadingImages(true);
                setUploadError(null);
                const uploadedImgResp = await uploadSingleImage(asset, selectedImages.length);
                if (uploadedImgResp) {
                    addUploadedId(uploadedImgResp);
                }
                setUploadingImages(false);
            }
        });
    }, [checkAndRequestCameraPermission, selectedImages.length, addUploadedId]);

    const handleGalleryPress = React.useCallback(async () => {
        const hasPermission = await checkAndRequestGalleryPermission();
        if (!hasPermission) return;

        const options = {
            mediaType: 'photo' as MediaType,
            includeBase64: true,
            maxHeight: 2000,
            maxWidth: 2000,
            selectionLimit: 0,
            quality: 0.8 as PhotoQuality,
        };

        launchImageLibrary(options, async (response) => {
            if (response.didCancel || response.errorCode) {
                Alert.alert('Error', 'Failed to pick image from gallery');
                return;
            }

            if (response.assets?.length) {
                setSelectedImages(prev => [...prev, ...response.assets!]);
                setImgUploadModalVisible(false);

                setUploadingImages(true);
                setUploadError(null);
                for (let i = 0; i < response.assets.length; i++) {
                    const asset = response.assets[i];
                    const uploadedImgResp = await uploadSingleImage(asset, selectedImages.length + i);
                    if (uploadedImgResp) {
                        addUploadedId(uploadedImgResp);
                    }
                }
                setUploadingImages(false);
            }
        });
    }, [checkAndRequestGalleryPermission, selectedImages.length, addUploadedId]);

    // Unified image list for display
    const allImages = React.useMemo(() => [
        ...displayedUploadedImages,
        ...downloadedImages
    ], [displayedUploadedImages, downloadedImages]);

    // Type guard helpers
    function isDownloadedImage(img: DownloadedImage | UploadedImage): img is DownloadedImage {
        return (img as DownloadedImage).loading !== undefined;
    }
    function isUploadedImage(img: DownloadedImage | UploadedImage): img is UploadedImage {
        return (img as UploadedImage).asset !== undefined;
    }

    const lastSentImagesRef = React.useRef<string>('');
    React.useEffect(() => {
        const visibleImages = allImages.filter(
            img => !(isDownloadedImage(img) && (img.loading || img.error))
        ).map(img => {
            if (isUploadedImage(img)) {
                return {
                    uniqueId: img.uniqueId,
                    imageUri: img.imageUri,
                    source: 'uploaded',
                };
            } else if (isDownloadedImage(img)) {
                return {
                    uniqueId: img.uniqueId,
                    imageUri: img.imageUri,
                    source: 'downloaded',
                };
            }
            return undefined;
        }).filter((img): img is { uniqueId: string; imageUri: string; source: string } => !!img);

        // Serialize for comparison
        const serialized = JSON.stringify(visibleImages);
        if (serialized !== lastSentImagesRef.current) {
            lastSentImagesRef.current = serialized;
            onUploadComplete?.(visibleImages);
        }
    }, [allImages, onUploadComplete]);

    return (
        <>
            <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Attachments *</Text>
                <View style={styles.uploadButtonOuter}>
                    {selectedTab !== 'Pending For Approval' && (
                        <TouchableOpacity
                            style={styles.uploadButton}
                            onPress={() => setImgUploadModalVisible(true)}
                            disabled={selectedTab === 'Pending For Approval'}
                        >
                            <SVG.Upload />
                            <Text style={styles.uploadButtonText}>
                                {Strings.DailyProgress.uploadImage}
                            </Text>
                        </TouchableOpacity>
                    )}


                    {/* Unified image display */}
                    {allImages.length > 0 && (
                        <ScrollView horizontal style={{ marginTop: 5 }}>
                            {allImages.map((img, idx) => (
                                <View key={img.uniqueId || idx} style={styles.uploadedImageContainer}>
                                    {isDownloadedImage(img) && img.loading ? (
                                        <View style={styles.loadingContainer}>
                                            <Text style={styles.loadingText}>Loading...</Text>
                                        </View>
                                    ) : isDownloadedImage(img) && img.error ? (
                                        <View style={styles.errorContainer}>
                                            <Text style={styles.errorText}>Error</Text>
                                            <Text style={styles.errorSubText}>{img.error}</Text>
                                        </View>
                                    ) : (
                                        <Image
                                            source={{ uri: img.imageUri }}
                                            style={styles.uploadedImage}
                                            resizeMode="cover"
                                            onError={() => {
                                                if (isDownloadedImage(img)) {
                                                    setDownloadedImages(prev =>
                                                        prev.map(prevImg =>
                                                            prevImg.uniqueId === img.uniqueId
                                                                ? { ...prevImg, error: 'Image failed to load' }
                                                                : prevImg
                                                        )
                                                    );
                                                }
                                            }}
                                        />
                                    )}
                                    {/* Remove button logic */}
                                    {(isUploadedImage(img) || (functionalRole !== 'Site Engineer')) && (
                                        <TouchableOpacity
                                            style={styles.removeButton}
                                            onPress={() => {
                                                removeUploadedImage(img.uniqueId);
                                            }}>
                                            <SVG.Cross />
                                        </TouchableOpacity>
                                    )}
                                </View>
                            ))}
                        </ScrollView>
                    )}
                </View>
            </View>

            <BottomPopup visible={imgUploadModalVisible} onCancelPress={() => setImgUploadModalVisible(false)}>
                <BottomPopupImageUpload
                    onCameraPress={handleCameraPress}
                    onGalleryPress={handleGalleryPress}
                />
            </BottomPopup>

            <Modal visible={uploadingImages} transparent animationType="fade">
                <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.4)' }}>
                    <View style={{ backgroundColor: 'white', padding: 30, borderRadius: 10 }}>
                        <Text style={{ fontSize: 18, fontWeight: 'bold' }}>Please wait...</Text>
                        <Text style={{ marginTop: 10 }}>Uploading images to server</Text>
                    </View>
                </View>
            </Modal>

            {uploadError && (
                <Modal visible={!!uploadError} transparent animationType="fade">
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.4)' }}>
                        <View style={{ backgroundColor: 'white', padding: 30, borderRadius: 10 }}>
                            <Text style={{ fontSize: 18, fontWeight: 'bold', color: 'red' }}>Upload Error</Text>
                            <Text style={{ marginTop: 10 }}>{uploadError}</Text>
                            <TouchableOpacity onPress={() => setUploadError(null)} style={{ marginTop: 20 }}>
                                <Text style={{ color: 'blue' }}>Close</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>
            )}
        </>
    );
};

export default React.memo(AttachmentComponent);

const styles = StyleSheet.create({
    inputContainer: {
        marginVertical: ms(3),
    },
    inputLabel: {
        fontSize: ms(13),
        marginVertical: 3,
        fontFamily: AppFonts.Light,
        color: Colors.pipeIdTextBlack,
    },
    uploadButtonOuter: {
        padding: ms(8),
        borderRadius: ms(5),
        borderWidth: ms(1),
        borderColor: Colors.searchBorderGrey,
        marginTop: ms(3),
    },
    uploadButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: ms(8),
        borderRadius: ms(5),
        borderWidth: ms(1),
        borderColor: Colors.secondary,
    },
    uploadButtonText: {
        color: Colors.secondary,
        marginLeft: ms(8),
    },
    uploadedImageContainer: {
        marginTop: ms(15),
        marginRight: ms(20),
        borderRadius: ms(5),
        overflow: 'hidden',
        position: 'relative',
        width: 90,
        height: 100,
    },
    uploadedImage: {
        width: 80,
        height: 80,
        margin: ms(5),
    },
    loadingContainer: {
        width: 80,
        height: 80,
        margin: ms(5),
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Colors.containerligetBlue,
    },
    loadingText: {
        fontSize: ms(10),
        color: Colors.textPrimary,
    },
    errorContainer: {
        width: 80,
        height: 80,
        margin: ms(5),
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Colors.containerligetBlue,
    },
    errorText: {
        fontSize: ms(10),
        color: 'red',
        fontWeight: '500',
    },
    errorSubText: {
        fontSize: ms(8),
        color: 'red',
        textAlign: 'center',
        marginTop: ms(2),
    },
    removeButton: {
        position: 'absolute',
        bottom: ms(80),
        left: ms(70),
    },
});