import React from "react";
import { Text, View } from "react-native";

interface TabWithLabelrops {
    containerStyle?: any
    label: string;
    labelStyle?: any
    inputvalue: string;
    inputStyle?: any
}

const TabButtonComponent: React.FC<TabWithLabelrops> = ({
    containerStyle,
    label,
    labelStyle,
    inputvalue,
    inputStyle
}) => (

    <View style={containerStyle}>
        <Text style={labelStyle}>{label}</Text>
        <Text style={inputStyle}>{inputvalue}</Text>
    </View>
);

export default TabButtonComponent;
