/**
 * ePragati Design System - Typography Configuration
 * 
 * This file defines the complete typography system for the ePragati application,
 * building upon the existing Manrope font family with enhanced scaling,
 * cross-platform compatibility, and accessibility features.
 * 
 * Design Principles:
 * - Maintain existing Manrope font weights and hierarchy
 * - Provide dynamic scaling for different device types
 * - Ensure cross-platform consistency (iOS, Android, Tablet, iPad)
 * - Support accessibility requirements
 * - Integrate seamlessly with existing color system
 */

import { Dimensions, Platform, PixelRatio } from 'react-native';
import { ms as moderateScale } from '../../utils/Scale/Scaling';

// ============================================================================
// CORE FONT FAMILY CONFIGURATION - Preserving Existing System
// ============================================================================
export const AppFonts = {
  /** Extra Light (200) - Used for large decorative text */
  Extra_Light: 'Manrope-ExtraLight',
  
  /** Light (300) - Used for secondary headings and captions */
  Light: 'Manrope-Light',
  
  /** Regular (400) - Used for body text and standard content */
  Regular: 'Manrope-Regular',
  
  /** Medium (500) - Used for emphasized text and labels */
  Medium: 'Manrope-Medium',
  
  /** SemiBold (600) - Used for subheadings and important text */
  SemiBold: 'Manrope-SemiBold',
  
  /** Bold (700) - Used for headings and strong emphasis */
  Bold: 'Manrope-Bold',
  
  /** Extra Bold (800) - Used for hero text and major headings */
  Extra_Bold: 'Manrope-ExtraBold'
};

// ============================================================================
// DEVICE TYPE DETECTION
// ============================================================================
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const pixelDensity = PixelRatio.get();

export const DeviceType = {
  isTablet: screenWidth >= 768 || screenHeight >= 768,
  isSmallPhone: screenWidth < 375,
  isLargePhone: screenWidth >= 414,
  isLandscape: screenWidth > screenHeight,
  pixelDensity,
  screenWidth,
  screenHeight,
};

// ============================================================================
// DYNAMIC FONT SCALING SYSTEM
// ============================================================================

/**
 * Base font sizes following Material Design and iOS HIG principles
 * These sizes work with the moderateScale function for responsive scaling
 */
const baseFontSizes = {
  // Display Text (Hero/Large headings)
  display: {
    large: 57,    // Hero text, major headings
    medium: 45,   // Large headings
    small: 36,    // Section headings
  },
  
  // Headline Text (Primary headings)
  headline: {
    large: 32,    // Page titles
    medium: 28,   // Card titles
    small: 24,    // Section titles
  },
  
  // Title Text (Secondary headings)
  title: {
    large: 22,    // Dialog titles
    medium: 20,   // List headers
    small: 18,    // Subsection titles
  },
  
  // Label Text (Interactive elements)
  label: {
    large: 16,    // Button text, tabs
    medium: 14,   // Form labels
    small: 12,    // Chips, badges
  },
  
  // Body Text (Content)
  body: {
    large: 16,    // Primary content
    medium: 14,   // Secondary content
    small: 12,    // Captions, footnotes
  },
  
  // Caption Text (Minimal text)
  caption: {
    large: 12,    // Image captions
    medium: 11,   // Helper text
    small: 10,    // Legal text
  }
};

/**
 * Device-specific scaling factors
 */
const getScalingFactor = () => {
  if (DeviceType.isTablet) return 1.15;
  if (DeviceType.isLargePhone) return 1.05;
  if (DeviceType.isSmallPhone) return 0.95;
  return 1.0;
};

/**
 * Dynamic font size calculation with accessibility support
 */
export const getFontSize = (
  category: keyof typeof baseFontSizes,
  size: 'large' | 'medium' | 'small',
  customScale?: number
): number => {
  const baseSize = baseFontSizes[category][size];
  const deviceScale = getScalingFactor();
  const finalScale = customScale || deviceScale;
  
  return moderateScale(baseSize * finalScale);
};

// ============================================================================
// TYPOGRAPHY SCALE PRESETS
// ============================================================================

export const Typography = {
  // Display Styles (Hero/Major headings)
  display: {
    large: {
      fontSize: getFontSize('display', 'large'),
      fontFamily: AppFonts.Extra_Bold,
      lineHeight: getFontSize('display', 'large') * 1.1,
      letterSpacing: -0.5,
    },
    medium: {
      fontSize: getFontSize('display', 'medium'),
      fontFamily: AppFonts.Bold,
      lineHeight: getFontSize('display', 'medium') * 1.15,
      letterSpacing: -0.3,
    },
    small: {
      fontSize: getFontSize('display', 'small'),
      fontFamily: AppFonts.Bold,
      lineHeight: getFontSize('display', 'small') * 1.2,
      letterSpacing: -0.2,
    },
  },
  
  // Headline Styles (Primary headings)
  headline: {
    large: {
      fontSize: getFontSize('headline', 'large'),
      fontFamily: AppFonts.Bold,
      lineHeight: getFontSize('headline', 'large') * 1.25,
      letterSpacing: 0,
    },
    medium: {
      fontSize: getFontSize('headline', 'medium'),
      fontFamily: AppFonts.SemiBold,
      lineHeight: getFontSize('headline', 'medium') * 1.3,
      letterSpacing: 0,
    },
    small: {
      fontSize: getFontSize('headline', 'small'),
      fontFamily: AppFonts.SemiBold,
      lineHeight: getFontSize('headline', 'small') * 1.35,
      letterSpacing: 0,
    },
  },
  
  // Title Styles (Secondary headings)
  title: {
    large: {
      fontSize: getFontSize('title', 'large'),
      fontFamily: AppFonts.SemiBold,
      lineHeight: getFontSize('title', 'large') * 1.4,
      letterSpacing: 0.1,
    },
    medium: {
      fontSize: getFontSize('title', 'medium'),
      fontFamily: AppFonts.Medium,
      lineHeight: getFontSize('title', 'medium') * 1.4,
      letterSpacing: 0.1,
    },
    small: {
      fontSize: getFontSize('title', 'small'),
      fontFamily: AppFonts.Medium,
      lineHeight: getFontSize('title', 'small') * 1.45,
      letterSpacing: 0.1,
    },
  },
  
  // Label Styles (Interactive elements)
  label: {
    large: {
      fontSize: getFontSize('label', 'large'),
      fontFamily: AppFonts.SemiBold,
      lineHeight: getFontSize('label', 'large') * 1.5,
      letterSpacing: 0.1,
    },
    medium: {
      fontSize: getFontSize('label', 'medium'),
      fontFamily: AppFonts.Medium,
      lineHeight: getFontSize('label', 'medium') * 1.5,
      letterSpacing: 0.15,
    },
    small: {
      fontSize: getFontSize('label', 'small'),
      fontFamily: AppFonts.Medium,
      lineHeight: getFontSize('label', 'small') * 1.5,
      letterSpacing: 0.2,
    },
  },
  
  // Body Styles (Content)
  body: {
    large: {
      fontSize: getFontSize('body', 'large'),
      fontFamily: AppFonts.Regular,
      lineHeight: getFontSize('body', 'large') * 1.6,
      letterSpacing: 0.15,
    },
    medium: {
      fontSize: getFontSize('body', 'medium'),
      fontFamily: AppFonts.Regular,
      lineHeight: getFontSize('body', 'medium') * 1.6,
      letterSpacing: 0.2,
    },
    small: {
      fontSize: getFontSize('body', 'small'),
      fontFamily: AppFonts.Regular,
      lineHeight: getFontSize('body', 'small') * 1.65,
      letterSpacing: 0.25,
    },
  },
  
  // Caption Styles (Minimal text)
  caption: {
    large: {
      fontSize: getFontSize('caption', 'large'),
      fontFamily: AppFonts.Regular,
      lineHeight: getFontSize('caption', 'large') * 1.7,
      letterSpacing: 0.3,
    },
    medium: {
      fontSize: getFontSize('caption', 'medium'),
      fontFamily: AppFonts.Light,
      lineHeight: getFontSize('caption', 'medium') * 1.7,
      letterSpacing: 0.35,
    },
    small: {
      fontSize: getFontSize('caption', 'small'),
      fontFamily: AppFonts.Light,
      lineHeight: getFontSize('caption', 'small') * 1.8,
      letterSpacing: 0.4,
    },
  },
};

// ============================================================================
// COMPONENT-SPECIFIC TYPOGRAPHY PRESETS
// ============================================================================

export const ComponentTypography = {
  // Button Typography
  button: {
    primary: {
      fontSize: getFontSize('label', 'large'),
      fontFamily: AppFonts.SemiBold,
      lineHeight: getFontSize('label', 'large') * 1.2,
      letterSpacing: 0.1,
      textTransform: 'none' as const,
    },
    secondary: {
      fontSize: getFontSize('label', 'medium'),
      fontFamily: AppFonts.Medium,
      lineHeight: getFontSize('label', 'medium') * 1.2,
      letterSpacing: 0.15,
      textTransform: 'none' as const,
    },
    small: {
      fontSize: getFontSize('label', 'small'),
      fontFamily: AppFonts.Medium,
      lineHeight: getFontSize('label', 'small') * 1.2,
      letterSpacing: 0.2,
      textTransform: 'none' as const,
    },
  },
  
  // Input Field Typography
  input: {
    label: {
      fontSize: getFontSize('label', 'medium'),
      fontFamily: AppFonts.Medium,
      lineHeight: getFontSize('label', 'medium') * 1.3,
      letterSpacing: 0.1,
    },
    text: {
      fontSize: getFontSize('body', 'medium'),
      fontFamily: AppFonts.Regular,
      lineHeight: getFontSize('body', 'medium') * 1.4,
      letterSpacing: 0.15,
    },
    placeholder: {
      fontSize: getFontSize('body', 'medium'),
      fontFamily: AppFonts.Light,
      lineHeight: getFontSize('body', 'medium') * 1.4,
      letterSpacing: 0.15,
    },
    helper: {
      fontSize: getFontSize('caption', 'large'),
      fontFamily: AppFonts.Regular,
      lineHeight: getFontSize('caption', 'large') * 1.5,
      letterSpacing: 0.25,
    },
  },
  
  // Card Typography
  card: {
    title: {
      fontSize: getFontSize('title', 'medium'),
      fontFamily: AppFonts.SemiBold,
      lineHeight: getFontSize('title', 'medium') * 1.3,
      letterSpacing: 0.1,
    },
    subtitle: {
      fontSize: getFontSize('body', 'medium'),
      fontFamily: AppFonts.Medium,
      lineHeight: getFontSize('body', 'medium') * 1.4,
      letterSpacing: 0.15,
    },
    body: {
      fontSize: getFontSize('body', 'medium'),
      fontFamily: AppFonts.Regular,
      lineHeight: getFontSize('body', 'medium') * 1.5,
      letterSpacing: 0.15,
    },
    caption: {
      fontSize: getFontSize('caption', 'large'),
      fontFamily: AppFonts.Regular,
      lineHeight: getFontSize('caption', 'large') * 1.6,
      letterSpacing: 0.25,
    },
  },
  
  // Navigation Typography
  navigation: {
    title: {
      fontSize: getFontSize('title', 'medium'),
      fontFamily: AppFonts.SemiBold,
      lineHeight: getFontSize('title', 'medium') * 1.2,
      letterSpacing: 0.1,
    },
    tab: {
      fontSize: getFontSize('label', 'medium'),
      fontFamily: AppFonts.Medium,
      lineHeight: getFontSize('label', 'medium') * 1.3,
      letterSpacing: 0.15,
    },
    menu: {
      fontSize: getFontSize('body', 'medium'),
      fontFamily: AppFonts.Regular,
      lineHeight: getFontSize('body', 'medium') * 1.4,
      letterSpacing: 0.15,
    },
  },
  
  // Map Marker Typography (Specific to the handleMarkerPress card)
  mapMarker: {
    cardTitle: {
      fontSize: getFontSize('title', 'small'),
      fontFamily: AppFonts.SemiBold,
      lineHeight: getFontSize('title', 'small') * 1.3,
      letterSpacing: 0.1,
    },
    label: {
      fontSize: getFontSize('body', 'medium'),
      fontFamily: AppFonts.Medium,
      lineHeight: getFontSize('body', 'medium') * 1.4,
      letterSpacing: 0.15,
    },
    value: {
      fontSize: getFontSize('body', 'large'),
      fontFamily: AppFonts.SemiBold,
      lineHeight: getFontSize('body', 'large') * 1.4,
      letterSpacing: 0.1,
    },
    button: {
      fontSize: getFontSize('label', 'large'),
      fontFamily: AppFonts.SemiBold,
      lineHeight: getFontSize('label', 'large') * 1.2,
      letterSpacing: 0.1,
    },
  },
};

// ============================================================================
// ACCESSIBILITY UTILITIES
// ============================================================================

/**
 * Get accessible font size based on system settings
 */
export const getAccessibleFontSize = (baseSize: number): number => {
  // This would integrate with React Native's accessibility settings
  // For now, we return the base size with device scaling
  return baseSize * getScalingFactor();
};

/**
 * Check if font size meets accessibility guidelines
 */
export const isAccessibleFontSize = (fontSize: number): boolean => {
  // Minimum 14px for body text, 16px for interactive elements
  return fontSize >= 14;
};

// ============================================================================
// PLATFORM-SPECIFIC ADJUSTMENTS
// ============================================================================

interface PlatformAdjustment {
  letterSpacingAdjustment: number;
  lineHeightAdjustment: number;
}

interface TypographyStyle {
  fontSize: number;
  fontFamily: string;
  lineHeight: number;
  letterSpacing: number;
  textTransform?: 'none' | 'uppercase' | 'lowercase' | 'capitalize';
}

const platformAdjustments: Record<'ios' | 'android', PlatformAdjustment> = {
  // iOS-specific adjustments
  ios: {
    letterSpacingAdjustment: 0,
    lineHeightAdjustment: 1.0,
  },
  
  // Android-specific adjustments
  android: {
    letterSpacingAdjustment: 0.05,
    lineHeightAdjustment: 1.05,
  },
};

export const PlatformTypography = {
  ...platformAdjustments,
  
  // Get platform-adjusted typography
  getPlatformStyle: (baseStyle: TypographyStyle): TypographyStyle => {
    const platform = Platform.OS as 'ios' | 'android';
    const adjustments: PlatformAdjustment = platformAdjustments[platform] || platformAdjustments.ios;
    
    return {
      ...baseStyle,
      letterSpacing: (baseStyle.letterSpacing || 0) + adjustments.letterSpacingAdjustment,
      lineHeight: (baseStyle.lineHeight || baseStyle.fontSize) * adjustments.lineHeightAdjustment,
    };
  },
};

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type FontWeight = keyof typeof AppFonts;
export type TypographyCategory = keyof typeof Typography;
export type TypographySize = 'large' | 'medium' | 'small';
export type ComponentTypographyKey = keyof typeof ComponentTypography;

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get typography style by category and size
 */
export const getTypographyStyle = (
  category: TypographyCategory,
  size: TypographySize,
  platformAdjust: boolean = true
) => {
  const baseStyle = Typography[category][size];
  return platformAdjust ? PlatformTypography.getPlatformStyle(baseStyle) : baseStyle;
};

/**
 * Get component-specific typography
 */
export const getComponentTypography = (
  component: ComponentTypographyKey,
  variant: string,
  platformAdjust: boolean = true
) => {
  const componentStyles = ComponentTypography[component] as Record<string, TypographyStyle>;
  const baseStyle = componentStyles[variant];
  return platformAdjust ? PlatformTypography.getPlatformStyle(baseStyle) : baseStyle;
};

/**
 * Create custom typography style
 */
export const createTypographyStyle = (
  fontSize: number,
  fontWeight: FontWeight,
  customProps?: {
    lineHeight?: number;
    letterSpacing?: number;
    textTransform?: 'none' | 'uppercase' | 'lowercase' | 'capitalize';
  }
) => {
  return {
    fontSize: getAccessibleFontSize(fontSize),
    fontFamily: AppFonts[fontWeight],
    lineHeight: customProps?.lineHeight || fontSize * 1.4,
    letterSpacing: customProps?.letterSpacing || 0.1,
    textTransform: customProps?.textTransform || 'none',
  };
};