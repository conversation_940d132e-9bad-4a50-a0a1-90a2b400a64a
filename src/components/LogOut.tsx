import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import BorderedButton from './BorderedButton';
import LogoutIcon from '../assets/svg/Logout.svg'; // Import the Logout SVG
import Colors from '../utils/Colors/Colors';
import { ms } from '../utils/Scale/Scaling';
import Strings from '../utils/Strings/Strings';
import BottomPopup from './BottomPopup';
import ButtonComponent from './ButtonComponent';
import { t } from 'i18next';

interface LogoutProps {
    testIdConfirm?: string;
    testIdCancel?: string;
    onCancel: () => void;
    onConfirm: () => void;
}

const Logout: React.FC<LogoutProps> = ({ testIdConfirm, testIdCancel, onCancel, onConfirm }) => {
    return (
        <View style={styles.container}>
            <BottomPopup>
                <LogoutIcon width={ms(60)} height={ms(60)} style={styles.icon} />
                <Text style={styles.message}>{Strings.commonStrings.logoutConfirmationMessage}</Text>
                <View style={styles.buttonContainer}>
                    <BorderedButton 
                    testID={testIdCancel}
                    title={t('commonStrings.no')} onPress={onCancel} />
                    <ButtonComponent 
                    testID={testIdConfirm}
                    title={t('commonStrings.yes')} onPress={onConfirm} customWidth={styles.width} />
                </View>
            </BottomPopup>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.white,
        alignItems: 'center',
        justifyContent: 'center',
    },
    message: {
        fontSize: ms(16),
        fontFamily: 'MNBold',
        color: Colors.black,
        marginVertical: ms(10),
        textAlign: 'center',
    },
    buttonContainer: {
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-around',
        marginTop: ms(20),
        paddingHorizontal: ms(10),
        paddingTop: ms(20),
        paddingBottom: ms(10),
        borderTopWidth: 0.2,
        borderTopColor: Colors.grey,
    },
    width: {
        width: '45%',
    },
    icon: {
        marginTop: ms(30),
        marginBottom: ms(10),
    }
});

export default Logout;
