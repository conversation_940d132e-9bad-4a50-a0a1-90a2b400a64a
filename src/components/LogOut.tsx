import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import BorderedButton from './BorderedButton';
import LogoutIcon from '../assets/svg/Logout.svg'; // Import the Logout SVG
import Colors from '../utils/Colors/Colors';
import { ms } from '../utils/Scale/Scaling';
import Strings from '../utils/Strings/Strings';
import BottomPopup from './BottomPopup';
import ButtonComponent from './ButtonComponent';
import { t } from 'i18next';
 
interface LogoutProps {
    testIdConfirm?: string;
    testIdCancel?: string;
    onCancel: () => void;
    onConfirm: () => void;
    visible: boolean;
    onClose: () => void;
}
 
const Logout: React.FC<LogoutProps> = ({ testIdConfirm, testIdCancel, onCancel, onConfirm, visible,
    onClose }) => {
    return (
        <View style={styles.container}>
            <BottomPopup
            visible={visible}
            onCancelPress={onClose}
            innerContainerStyle={styles.innerContainer}>
                <LogoutIcon width={ms(60)} height={ms(60)} style={styles.icon} />
                <Text style={styles.message}>{Strings.commonStrings.logoutConfirmationMessage}</Text>
                
                    {/* <BorderedButton
                    testID={testIdCancel}
                    title={t('commonStrings.no')} onPress={onCancel} />
                    <ButtonComponent
                    testID={testIdConfirm}
                    title={t('commonStrings.yes')} onPress={onConfirm} customWidth={styles.width} /> */}
                      <ButtonComponent
                                    showSecondaryButton
                                    secondaryButtonTitle={t('commonStrings.no')}
                                    onSecondaryPress={onCancel}
                                    mainContainerStyle={{
                                        paddingBottom: ms(30)
                                    }}
                                    title={t('commonStrings.yes')}
                                    onPress={onConfirm } />
               
            </BottomPopup>
        </View>
    );
};
 
const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.white,
        alignItems: 'center',
        justifyContent: 'center',
    },
    message: {
        fontSize: ms(16),
        fontFamily: 'MNBold',
        color: Colors.black,
        marginVertical: ms(10),
        textAlign: 'center',
    },
    buttonContainer: {
        flexDirection: 'row',
        width: '100%',
       marginHorizontal: ms(10),
        justifyContent: 'space-around',
        marginTop: ms(20),
        paddingHorizontal: ms(10),
        paddingTop: ms(20),
        paddingBottom: ms(10),
        borderTopWidth: 0.2,
        borderTopColor: Colors.grey,
        alignContent: 'center',
        alignItems: 'center',
    },
    width: {
        width: '45%',
    },
    icon: {
        marginTop: ms(30),
        marginBottom: ms(10),
    },
    innerContainer: {
        alignItems: 'center',
    }
 
});
 
export default Logout;