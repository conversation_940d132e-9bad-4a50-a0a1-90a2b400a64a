import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { useGeoSpatialService } from '../hooks/useGeoSpatialService';

interface GeoSpatialIntegrationExampleProps {
  jobCode?: string;
  boqCode?: string;
  deliverableCode?: string;
}

const GeoSpatialIntegrationExample: React.FC<GeoSpatialIntegrationExampleProps> = ({
  jobCode = "DEFAULT_JOB",
  boqCode = "DEFAULT_BOQ",
  deliverableCode = "DEFAULT_DELIVERABLE"
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [syncStatus, setSyncStatus] = useState<string>('default');
  const [processingStatus, setProcessingStatus] = useState<string>('idle');
  const [processedCount, setProcessedCount] = useState(0);
  const [errorCount, setErrorCount] = useState(0);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    console.log(message); // Also log to console
    setLogs(prev => [...prev.slice(-10), `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const geoService = useGeoSpatialService({
    onSyncSuccess: (data) => {
      setIsLoading(false);
      setSyncStatus(data.status);
      addLog(`🎉 Sync completed successfully: ${data.status}`);
      Alert.alert('Success!', 'Geospatial sync completed successfully ! GeoSpatialIntegrationExample');
    },
    onSyncFailure: (data) => {
      setIsLoading(false);
      setSyncStatus(data.status);
      addLog(`❌ Sync failed: ${data.status}`);
      Alert.alert('Error', 'Geospatial sync failed. Please try again.');
    },
    onSyncStatusUpdate: (data) => {
      setSyncStatus(data.status);
      addLog(`📊 Status updated: ${data.status}`);
    },
    onError: (error) => {
      setIsLoading(false);
      setProcessingStatus('error');
      addLog(`❌ Error occurred: ${error}`);
      console.error('GeoSpatial Error:', error);
    },
    onDataProcessed: (data) => {
      if (data.success) {
        addLog('🔄 Geospatial response data received and processing started');
        setProcessingStatus('processing');
      } else {
        addLog(`❌ Data processing failed: ${data.error}`);
        setProcessingStatus('error');
        Alert.alert('Processing Error', data.error || 'Unknown error occurred');
      }
    },
    onDataProcessingComplete: (data) => {
      setProcessingStatus(data.success ? 'completed' : 'error');
      setProcessedCount(data.totalCount);
      setErrorCount(data.errorCount);
      
      if (data.success) {
        addLog(`🎉 Processing completed successfully! ${data.totalCount} records processed`);
        Alert.alert(
          'Processing Complete', 
          `Successfully processed ${data.totalCount} geospatial records`
        );
      } else {
        addLog(`⚠️ Processing completed with errors. Success: ${data.totalCount}, Errors: ${data.errorCount}`);
        Alert.alert(
          'Processing Complete with Errors', 
          `Processed: ${data.totalCount}, Errors: ${data.errorCount}`
        );
      }
      setIsLoading(false);
    }
  });

  const handleStartSync = async () => {
    try {
      setIsLoading(true);
      setProcessingStatus('syncing');
      setProcessedCount(0);
      setErrorCount(0);
      addLog(`🚀 Starting geospatial sync for job: ${jobCode}`);
      geoService.startGeospatialService(jobCode);
    } catch (error) {
      setIsLoading(false);
      setProcessingStatus('error');
      addLog(`❌ Failed to start sync: ${(error as Error).message}`);
      Alert.alert('Error', 'Failed to start geospatial service');
    }
  };

  const handleOpenGISApp = () => {
    try {
      addLog(`📱 Opening GIS app with codes: Job=${jobCode}, BOQ=${boqCode}, Deliverable=${deliverableCode}`);
      geoService.openGISApp(jobCode, boqCode, deliverableCode);
    } catch (error) {
      addLog(`❌ Failed to open GIS app: ${(error as Error).message}`);
      Alert.alert('Error', 'Failed to open GIS application');
    }
  };

  const handleGetSyncStatus = async () => {
    try {
      const status = await geoService.getSyncStatus();
      setSyncStatus(status);
      addLog(`📋 Current sync status: ${status}`);
      Alert.alert('Sync Status', `Current status: ${status}`);
    } catch (error) {
      addLog(`❌ Failed to get sync status: ${(error as Error).message}`);
      Alert.alert('Error', 'Failed to get sync status');
    }
  };

  const clearLogs = () => {
    setLogs([]);
    addLog('📝 Logs cleared');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Synced':
      case 'completed':
        return '#4CAF50'; // Green
      case 'Sync Failed':
      case 'error':
        return '#F44336'; // Red
      case 'syncing':
      case 'processing':
        return '#FF9800'; // Orange
      case 'default':
      case 'idle':
        return '#757575'; // Gray
      default:
        return '#757575'; // Gray
    }
  };

  const getProcessingStatusText = () => {
    switch (processingStatus) {
      case 'idle':
        return 'Ready';
      case 'syncing':
        return 'Syncing...';
      case 'processing':
        return 'Processing Data...';
      case 'completed':
        return 'Completed';
      case 'error':
        return 'Error';
      default:
        return 'Unknown';
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>GeoSpatial Integration Example</Text>
      
      {/* Status Section */}
      <View style={styles.statusContainer}>
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Sync Status:</Text>
          <Text style={[styles.statusText, { color: getStatusColor(syncStatus) }]}>
            {syncStatus}
          </Text>
        </View>
        
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Processing:</Text>
          <Text style={[styles.statusText, { color: getStatusColor(processingStatus) }]}>
            {getProcessingStatusText()}
          </Text>
        </View>

        {(processedCount > 0 || errorCount > 0) && (
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Records:</Text>
            <Text style={styles.statusText}>
              ✅ {processedCount} | ❌ {errorCount}
            </Text>
          </View>
        )}
      </View>

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, isLoading && styles.disabledButton]} 
          onPress={handleStartSync}
          disabled={isLoading}
        >
          {isLoading && processingStatus === 'syncing' ? (
            <ActivityIndicator color="#ffffff" />
          ) : (
            <Text style={styles.buttonText}>Start Geospatial Sync</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, styles.primaryButton]} 
          onPress={handleOpenGISApp}
          disabled={isLoading}
        >
          {isLoading && processingStatus === 'processing' ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator color="#ffffff" size="small" />
              <Text style={[styles.buttonText, { marginLeft: 10 }]}>Processing...</Text>
            </View>
          ) : (
            <Text style={styles.buttonText}>Open GIS App & Process Data</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, styles.secondaryButton]} 
          onPress={handleGetSyncStatus}
          disabled={isLoading}
        >
          <Text style={[styles.buttonText, styles.secondaryButtonText]}>
            Get Sync Status
          </Text>
        </TouchableOpacity>
      </View>

      {/* Project Info */}
      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>Project Information</Text>
        <Text style={styles.infoText}>Job Code: {jobCode}</Text>
        <Text style={styles.infoText}>BOQ Code: {boqCode}</Text>
        <Text style={styles.infoText}>Deliverable Code: {deliverableCode}</Text>
      </View>

      {/* Logs Section */}
      <View style={styles.logsContainer}>
        <View style={styles.logsHeader}>
          <Text style={styles.logsTitle}>Activity Logs</Text>
          <TouchableOpacity onPress={clearLogs} style={styles.clearButton}>
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.logsContent}>
          {logs.length === 0 ? (
            <Text style={styles.noLogsText}>No activity logs yet...</Text>
          ) : (
            logs.map((log, index) => (
              <Text key={index} style={styles.logText}>
                {log}
              </Text>
            ))
          )}
        </View>
      </View>

      {/* Instructions */}
      <View style={styles.instructionsContainer}>
        <Text style={styles.instructionsTitle}>How to use:</Text>
        <Text style={styles.instructionText}>
          1. Tap "Start Geospatial Sync" to begin the sync process
        </Text>
        <Text style={styles.instructionText}>
          2. Tap "Open GIS App & Process Data" to launch the GIS application
        </Text>
        <Text style={styles.instructionText}>
          3. Use the GIS app to collect/edit data and return to this app
        </Text>
        <Text style={styles.instructionText}>
          4. The app will automatically process the returned data
        </Text>
        <Text style={styles.instructionText}>
          5. Check the logs below for detailed processing information
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  statusContainer: {
    backgroundColor: '#ffffff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  statusText: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'right',
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#2196F3',
    padding: 15,
    borderRadius: 10,
    marginBottom: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  primaryButton: {
    backgroundColor: '#4CAF50',
  },
  disabledButton: {
    backgroundColor: '#BDBDBD',
  },
  secondaryButton: {
    backgroundColor: '#ffffff',
    borderWidth: 2,
    borderColor: '#2196F3',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#2196F3',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoContainer: {
    backgroundColor: '#ffffff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  infoText: {
    fontSize: 14,
    marginBottom: 5,
    color: '#666',
  },
  logsContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 10,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  logsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  logsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  clearButton: {
    padding: 8,
    backgroundColor: '#f44336',
    borderRadius: 5,
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  logsContent: {
    padding: 15,
    maxHeight: 200,
  },
  noLogsText: {
    textAlign: 'center',
    color: '#999',
    fontStyle: 'italic',
  },
  logText: {
    fontSize: 12,
    marginBottom: 5,
    color: '#444',
    fontFamily: 'monospace',
  },
  instructionsContainer: {
    backgroundColor: '#e3f2fd',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  instructionText: {
    fontSize: 14,
    marginBottom: 5,
    color: '#424242',
  },
});

export default GeoSpatialIntegrationExample; 