import React, { useState, useCallback } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import CAMERA from '../assets/svg/camera.svg';
import GALLERY from '../assets/svg/gallery.svg';
import Colors from '../utils/Colors/Colors';
import Strings from '../utils/Strings/Strings';
import { ms } from '../utils/Scale/Scaling';

interface imageUploadBottomPopupProps {
    onCameraPress: () => void;
    onGalleryPress: () => void;
}

const BottomPopupImageUpload: React.FC<imageUploadBottomPopupProps> = ({
    onCameraPress,
    onGalleryPress,
}) => {

    const handleCameraPress = useCallback(() => {
        try {
            onCameraPress();
        } catch (error) {
            console.error('Error handling camera press:', error);
        }
    }, [onCameraPress]);

    const handleGalleryPress = useCallback(() => {
        try {
            onGalleryPress();
        } catch (error) {
            console.error('Error handling gallery press:', error);
        }
    }, [onGalleryPress]);

    return (
        <View style={styles.container}>
            <Text style={styles.title}>{Strings.DailyProgress.uploadImageFrom}</Text>

            <View style={styles.dividerView} />

            <View style={styles.optionsContainer}>
                <TouchableOpacity style={styles.option} onPress={handleCameraPress}>
                    <View style={styles.iconContainer}>
                        <CAMERA />
                    </View>
                    <Text style={styles.optionText}>{Strings.DailyProgress.camera}</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.option} onPress={handleGalleryPress}>
                    <View style={styles.iconContainer}>
                        <GALLERY />
                    </View>
                    <Text style={styles.optionText}>{Strings.DailyProgress.gallery}</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
}

export default BottomPopupImageUpload;

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.white,
    },
    title: {
        fontSize: ms(16),
        fontWeight: 'bold',
        marginTop: ms(30),
        marginBottom: ms(5),
        marginHorizontal: ms(12),
        color: Colors.darkBlue,
    },
    dividerView: {
        height: 1,
        backgroundColor: Colors.dailyProgressItemBg,
        marginHorizontal: 12,
        marginVertical: 10
    },
    optionsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginVertical: 20
    },
    option: {
        alignItems: 'center',
    },
    iconContainer: { 
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 10,
    },
    optionText: {
        fontSize: 16,
        color: Colors.secondary,
        marginBottom: 20
    },
});

