import React, { useCallback, useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions, Modal } from 'react-native';
import { Calendar } from 'react-native-calendars';
import Colors from '../../utils/Colors/Colors';
import Strings from '../../utils/Strings/Strings';
import { useNavigation } from '@react-navigation/native';
import BottomPopup from '../BottomPopup';
import CancelButton from '../CancelButton';
import ButtonComponent from '../ButtonComponent';
import { t } from 'i18next';
import { ms } from '../../utils/Scale/Scaling';
import { AppFonts } from '../Fonts';
import { useTranslation } from 'react-i18next';
import { FormatMonthDate } from '../../utils/helpers';

const { width } = Dimensions.get('window');

type Props = {
  visible: boolean;
  fromDateTitle: string;
  toDateTitle?: string;
  onClose: () => void;
  initialDate: any;
  selectedFromDate: string;
  seelctedToDate?: string;
  showFromDate?: boolean;
  showToDate?: boolean;
  showBottomButton?: boolean;
  onApply: (from: string, to: string) => void;
};

const BottomCalendarModal: React.FC<Props> = ({ 
  visible, 
  initialDate,
  onClose, 
  selectedFromDate,
  seelctedToDate,
  fromDateTitle,
  toDateTitle = 'SyncData.todate',
  showFromDate = true,
  showToDate =  false,
  showBottomButton,
  onApply 
}) => {
  const {t} = useTranslation();
  const [fromDate, setFromDate] = useState<{
    year: number,
    month: number,
    day: number,
    timestamp: number,
    dateString: string
  }>();
  const [toDate, setToDate] = useState<{
    year: number,
    month: number,
    day: number,
    timestamp: number,
    dateString: string
  }>();
  const [selecting, setSelecting] = useState<'from' | 'to'>('from');
  const [selectedDate, setSelectedDate]= useState('');

  const handleDayPress = useCallback((day: any) => {
    if (selecting === 'from') {
      setFromDate(day);
      setSelectedDate(day.dateString);
      const days = String(day.day).padStart(2, '0');
      const month = String(day.month).padStart(2, '0');
      const year = day.year;

      !showBottomButton && onApply(`${days}/${month}/${year}`, '');
    } else {
      setToDate(day);
      setSelectedDate(day.dateString);
      const days = String(day.day).padStart(2, '0');
      const month = String(day.month).padStart(2, '0');
      const year = day.year;

      !showBottomButton && onApply('', `${days}/${month}/${year}`);
    }
  }, []);

  return (

    <View>
      <BottomPopup visible={visible} onCancelPress={onClose}>
        <Text style={styles.title}>{Strings.DailyProgress.selectDate}</Text>
        <View style={styles.dividerView} />

        <View style={styles.inputRow}>
         { showFromDate && <View style={styles.dateColomnStyle}>
            <Text style={styles.inputLabel}>{t(fromDateTitle)}</Text>
            <TouchableOpacity
              style={styles.input}
              onPress={() => {
                setSelectedDate('');
                setSelecting('from');
              }}>
              <Text style={styles.inputValue}>{FormatMonthDate(fromDate ? fromDate.dateString : new Date().toDateString()).fullDate}</Text>
            </TouchableOpacity>
          </View>}
          {showToDate && <View style={styles.dateColomnStyle}>
            <Text style={styles.inputLabel}>{t(toDateTitle)}</Text>
            <TouchableOpacity
              style={styles.input}
              onPress={() => {
                setSelectedDate('');
                setSelecting('to');
              }}
            >
              <Text style={styles.inputValue}>{FormatMonthDate(toDate ? toDate.dateString : new Date().toDateString()).fullDate}</Text>
            </TouchableOpacity>
          </View>}
        </View>
        <Calendar
          onDayPress={handleDayPress}
          initialDate={initialDate}
          date={selectedFromDate ? selectedFromDate : new Date().toDateString()}
          markedDates={
            selectedDate
              ? {
                [selectedDate]: {
                  selected: true,
                  selectedColor: Colors.forgotPinBlue,
                  selectedTextColor: Colors.white,
                },
              }
              : {}
          }
          renderHeader={(date) => {
            const month = date.toString('MMMM yyyy');
            return (
              <View style={styles.customHeader}>
                <TouchableOpacity style={styles.monthDropdown}>
                  <Text style={styles.monthText}>{month}</Text>
                </TouchableOpacity>
              </View>
            );
          }}
          theme={{
            selectedDayBackgroundColor: Colors.forgotPinBlue,
            selectedDayTextColor: Colors.white,
            todayTextColor: Colors.forgotPinBlue,
            arrowColor: Colors.forgotPinBlue,
            textMonthFontWeight: 'bold',
            textMonthFontSize: 16,
          }}
          style={{ marginBottom: 10 }}
        />

          {showBottomButton && <ButtonComponent
            title={t('commonStrings.apply')}
            showSecondaryButton
            secondaryButtonTitle={t('commonStrings.cancel')}
            onPress={() => {
              const days = String(selecting === 'from' ? fromDate?.day : toDate?.day);
              const month = String(selecting === 'from' ? fromDate?.month : toDate?.month).padStart(2, '0');
              const year = selecting === 'from' ? fromDate?.year : toDate?.year;
              onApply(`${days}/${month}/${year}`, '')
            }}
            onSecondaryPress={() => {
              // setFromDate({
              //   year: new Date().getFullYear(),
              //   month: number,
              //   day: number,
              //   timestamp: number,
              //   dateString: string
              // });
              // setToDate({});
              // setSelectedDate('');
              onClose();
            }}
            />}
      </BottomPopup>
    </View >
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: ms(16),
    fontFamily: AppFonts.SemiBold,
    marginTop: ms(30),
    marginBottom: ms(5),
    marginHorizontal: ms(12),
    color: Colors.darkBlue,
  },
  dividerView: {
    height: ms(1),
    backgroundColor: Colors.dailyProgressItemBg,
    marginHorizontal: ms(12),
    marginVertical: ms(10),
  },
  dividerViewBottom: {
    height: ms(1),
    backgroundColor: Colors.dailyProgressItemBg,
    marginVertical: ms(15),
  },
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
  },
  input: {
    borderWidth: ms(1),
    borderColor: Colors.searchBorderGrey,
    borderRadius: ms(8),
    padding: ms(10),
    backgroundColor: Colors.containerligetBlue,
  },
  dateColomnStyle: {
    flex: 1, 
    marginHorizontal: ms(12),
    marginTop: ms(5),
  },
  inputLabel: {
    color: Colors.pipeIdTextBlack,
    fontSize: ms(13),
    fontFamily: AppFonts.Medium,
    marginBottom: ms(3),
  },
  inputValue: {
    fontSize: ms(13),
    fontFamily: AppFonts.Medium,
    color: Colors.pipeIdTextBlack,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: ms(12),
    marginBottom: ms(15),
  },
  customHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingHorizontal: ms(10),
    marginBottom: ms(10),
  },
  monthDropdown: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  monthText: {
    fontSize: ms(14),
    fontWeight: 'bold',
    marginRight: ms(5),
    color: Colors.textPrimary,
  },
});

export default BottomCalendarModal;
