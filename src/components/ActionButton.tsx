import React from 'react';
import {Pressable, Text, StyleSheet, ViewStyle} from 'react-native';
import { ms } from '../utils/Scale/Scaling';
import Colors from '../utils/Colors/Colors';

interface ActionButtonProps {
    type: 'approve' | 'reject';
    onPress: () => void;
    customStyle?: ViewStyle;
}

const ActionButton: React.FC<ActionButtonProps> = ({ type, onPress, customStyle }) => {
    const isApprove = type === 'approve';
    
    return (
        <Pressable
            style={[
                styles.container,
                isApprove ? styles.approveContainer : styles.rejectContainer,
                customStyle
            ]}
            onPress={onPress}
        >
            <Text style={[
                styles.text,
          isApprove ? styles.approveText : styles.rejectText,
            ]}>
                {isApprove ? 'Approve' : 'Reject'}
            </Text>
        </Pressable>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.white,
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: ms(5),
        paddingHorizontal: ms(20),
        borderRadius: ms(8),
        borderWidth: ms(1),
        minWidth: ms(120),
        justifyContent: 'center',
    },
    approveContainer: {
        borderColor: Colors.onlineGreen,
        backgroundColor: Colors.onlineGreen,
    },
    rejectContainer: {
        borderColor: Colors.lightRed,
        backgroundColor: Colors.white,
    },
    icon: {
        marginRight: ms(8),
    },
    text: {
        fontFamily: 'MNBold',
        fontSize: ms(15),
        textAlign: 'center',
    },
    approveText: {
        color: Colors.updateTextLightBlue,
        fontFamily:'MNMedium',
        fontSize:ms(14),
        fontWeight:'700'
    },
    rejectText: {
        color: Colors.lightRed,
        fontFamily:'MNMedium',
        fontSize:ms(14),
        fontWeight:'700'
    },
});

export default ActionButton; 