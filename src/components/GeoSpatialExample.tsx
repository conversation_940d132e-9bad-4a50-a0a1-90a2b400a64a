import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useGeoSpatialService } from '../hooks/useGeoSpatialService';

interface GeoSpatialExampleProps {
  jobCode?: string;
  boqCode?: string;
  deliverableCode?: string;
}

const GeoSpatialExample: React.FC<GeoSpatialExampleProps> = ({
  jobCode = "DEFAULT_JOB",
  boqCode = "DEFAULT_BOQ",
  deliverableCode = "DEFAULT_DELIVERABLE"
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [syncStatus, setSyncStatus] = useState<string>('default');

  const geoService = useGeoSpatialService({
    onSyncSuccess: (data) => {
      setIsLoading(false);
      setSyncStatus(data.status);
      Alert.alert('Success!', 'Geospatial sync completed successfully! GeoSpatialExample');
    },
    onSyncFailure: (data) => {
      setIsLoading(false);
      setSyncStatus(data.status);
      Alert.alert('Error', 'Geospatial sync failed. Please try again.');
    },
    onSyncStatusUpdate: (data) => {
      setSyncStatus(data.status);
      console.log('Status updated:', data.status);
    },
    onError: (error) => {
      setIsLoading(false);
      console.error('GeoSpatial Error:', error);
    }
  });

  const handleStartSync = async () => {
    try {
      setIsLoading(true);
      geoService.startGeospatialService(jobCode);
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Error', 'Failed to start geospatial service');
    }
  };

  const handleOpenGISApp = () => {
    try {
      geoService.openGISApp(jobCode, boqCode, deliverableCode);
    } catch (error) {
      Alert.alert('Error', 'Failed to open GIS application');
    }
  };

  const handleGetSyncStatus = async () => {
    try {
      const status = await geoService.getSyncStatus();
      setSyncStatus(status);
      Alert.alert('Sync Status', `Current status: ${status}`);
    } catch (error) {
      Alert.alert('Error', 'Failed to get sync status');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Synced':
        return '#4CAF50'; // Green
      case 'Sync Failed':
        return '#F44336'; // Red
      case 'default':
        return '#FF9800'; // Orange
      default:
        return '#757575'; // Gray
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>GeoSpatial Service Example</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusLabel}>Current Status:</Text>
        <Text style={[styles.statusText, { color: getStatusColor(syncStatus) }]}>
          {syncStatus}
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, isLoading && styles.disabledButton]} 
          onPress={handleStartSync}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#ffffff" />
          ) : (
            <Text style={styles.buttonText}>Start Geospatial Sync</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.button} 
          onPress={handleOpenGISApp}
        >
          <Text style={styles.buttonText}>Open GIS App</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, styles.secondaryButton]} 
          onPress={handleGetSyncStatus}
        >
          <Text style={[styles.buttonText, styles.secondaryButtonText]}>
            Get Sync Status
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>Job Code: {jobCode}</Text>
        <Text style={styles.infoText}>BOQ Code: {boqCode}</Text>
        <Text style={styles.infoText}>Deliverable Code: {deliverableCode}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
    padding: 15,
    backgroundColor: '#ffffff',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statusLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 10,
    color: '#333',
  },
  statusText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonContainer: {
    marginBottom: 30,
  },
  button: {
    backgroundColor: '#2196F3',
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  disabledButton: {
    backgroundColor: '#BDBDBD',
  },
  secondaryButton: {
    backgroundColor: '#ffffff',
    borderWidth: 2,
    borderColor: '#2196F3',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#2196F3',
  },
  infoContainer: {
    backgroundColor: '#ffffff',
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 5,
    color: '#666',
  },
});

export default GeoSpatialExample; 