import React, { useCallback, useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal } from 'react-native';
import { Calendar, LocaleConfig } from 'react-native-calendars';
import Colors from '../../utils/Colors/Colors';
import Strings from '../../utils/Strings/Strings';
import BottomPopup from '../BottomPopup';
import ButtonComponent from '../ButtonComponent';
import { ms } from '../../utils/Scale/Scaling';
import { AppFonts } from '../Fonts';
import { useTranslation } from 'react-i18next';
import ArrowDown from '../../assets/svg/calendar_down.svg';
import ArrowLeft from '../../assets/svg/calendar_left.svg';
import ArrowRight from '../../assets/svg/calendar_Right.svg';

type Props = {
  visible: boolean;
  fromDateTitle: string;
  toDateTitle?: string;
  onClose: () => void;
  selectedFromDate: string;
  seelctedToDate?: string;
  showFromDate?: boolean;
  showToDate?: boolean;
  showBottomButton?: boolean;
  onApply: (from: string, to: string) => void;
};

interface DateObject {
  year: number;
  month: number;
  day: number;
  dateString: string;
}

const BottomCalendarModal: React.FC<Props> = ({
  visible,
  onClose,
  selectedFromDate,
  seelctedToDate,
  fromDateTitle,
  toDateTitle = 'SyncData.todate',
  showFromDate = true,
  showToDate = false,
  showBottomButton,
  onApply,
}) => {
  const { t } = useTranslation();
  
  // State for selected dates
  const [fromDate, setFromDate] = useState<DateObject | null>(null);
  const [toDate, setToDate] = useState<DateObject | null>(null);
  
  // State for calendar navigation
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [calendarDate, setCalendarDate] = useState(new Date().toISOString().split('T')[0]);
  
  // State for UI
  const [selecting, setSelecting] = useState<'from' | 'to'>('from');
  const [showMonthPicker, setShowMonthPicker] = useState(false);

  // Initialize dates when modal opens
  useEffect(() => {
    if (visible) {
      let initialFromDate: DateObject | null = null;
      let initialToDate: DateObject | null = null;
      let initialCalendarDate = new Date().toISOString().split('T')[0];

      // Set from date
      if (selectedFromDate) {
        // Check if the date is in DD/MM/YYYY format
        if (selectedFromDate.includes('/')) {
          const [day, month, year] = selectedFromDate.split('/').map(Number);
          initialFromDate = {
            year: year,
            month: month, // Already 1-12 format
            day: day,
            dateString: `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`,
          };
          initialCalendarDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
          setCurrentMonth(month - 1); // Convert 1-12 to 0-11 for calendar
          setCurrentYear(year);
        } else {
          // Handle ISO format or other formats
          const from = new Date(selectedFromDate);
          initialFromDate = {
            year: from.getFullYear(),
            month: from.getMonth() + 1,
            day: from.getDate(),
            dateString: selectedFromDate,
          };
          initialCalendarDate = selectedFromDate;
          setCurrentMonth(from.getMonth());
          setCurrentYear(from.getFullYear());
        }
      } else {
        const now = new Date();
        const dateStr = now.toISOString().split('T')[0];
        initialFromDate = {
          year: now.getFullYear(),
          month: now.getMonth() + 1,
          day: now.getDate(),
          dateString: dateStr,
        };
        initialCalendarDate = dateStr;
        setCurrentMonth(now.getMonth());
        setCurrentYear(now.getFullYear());
      }

      // Set to date if needed
      if (showToDate && seelctedToDate) {
        // Check if the date is in DD/MM/YYYY format
        if (seelctedToDate.includes('/')) {
          const [day, month, year] = seelctedToDate.split('/').map(Number);
          initialToDate = {
            year: year,
            month: month, // Already 1-12 format
            day: day,
            dateString: `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`,
          };
        } else {
          // Handle ISO format or other formats
          const to = new Date(seelctedToDate);
          initialToDate = {
            year: to.getFullYear(),
            month: to.getMonth() + 1,
            day: to.getDate(),
            dateString: seelctedToDate,
          };
        }
      } else if (showToDate) {
        // If showing to date but no date selected, set it to same as from date
        initialToDate = initialFromDate;
      }

      setFromDate(initialFromDate);
      setToDate(initialToDate);
      setCalendarDate(initialCalendarDate);
    }
  }, [visible, selectedFromDate, seelctedToDate, showToDate]);

  // Handle day selection
  const handleDayPress = useCallback((day: DateObject) => {
    // The calendar library provides month in 0-11 format, convert to 1-12
    const selectedDate: DateObject = {
      year: day.year,
      month: day.month + 1, // Convert 0-11 to 1-12
      day: day.day,
      dateString: day.dateString,
    };
    
    if (selecting === 'from') {
      setFromDate(selectedDate);
      // If to date is not set and we're showing both dates, set to date to same as from date
      if (showToDate && !toDate) {
        setToDate(selectedDate);
      }
    } else {
      setToDate(selectedDate);
    }
  }, [selecting, showToDate, toDate]);

  // Handle month navigation
  const handleMonthChange = useCallback((month: number, year: number) => {
    setCurrentMonth(month);
    setCurrentYear(year);
    
    // Update calendar date to show the selected month/year
    const newDate = new Date(year, month, 1);
    const dateStr = newDate.toISOString().split('T')[0];
    setCalendarDate(dateStr);
  }, []);

  const handlePreviousMonth = useCallback(() => {
    let newMonth = currentMonth - 1;
    let newYear = currentYear;
    
    if (newMonth < 0) {
      newMonth = 11;
      newYear = currentYear - 1;
    }
    
    handleMonthChange(newMonth, newYear);
  }, [currentMonth, currentYear, handleMonthChange]);

  const handleNextMonth = useCallback(() => {
    let newMonth = currentMonth + 1;
    let newYear = currentYear;
    
    if (newMonth > 11) {
      newMonth = 0;
      newYear = currentYear + 1;
    }
    
    handleMonthChange(newMonth, newYear);
  }, [currentMonth, currentYear, handleMonthChange]);

  // Handle date field selection
  const handleFromDatePress = useCallback(() => {
    setSelecting('from');
    if (fromDate?.dateString) {
      setCalendarDate(fromDate.dateString);
      // Use the stored month and year directly instead of parsing dateString
      setCurrentMonth(fromDate.month - 1); // Convert 1-12 to 0-11 for calendar
      setCurrentYear(fromDate.year);
    }
  }, [fromDate]);

  const handleToDatePress = useCallback(() => {
    setSelecting('to');
    if (toDate?.dateString) {
      setCalendarDate(toDate.dateString);
      // Use the stored month and year directly instead of parsing dateString
      setCurrentMonth(toDate.month - 1); // Convert 1-12 to 0-11 for calendar
      setCurrentYear(toDate.year);
    }
  }, [toDate]);

  // Helper function to format date display using stored values
  const formatDateDisplay = useCallback((dateObj: DateObject | null) => {
    if (!dateObj) {
      const now = new Date();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const year = now.getFullYear();
      return `${day}/${month}/${year}`;
    }
    
    const month = String(dateObj.month).padStart(2, '0'); // Use 1-12 directly
    const day = String(dateObj.day).padStart(2, '0');
    const year = dateObj.year;
    
    return `${day}/${month}/${year}`;
  }, []);

  // Handle apply button
  const handleApply = useCallback(() => {
    console.log('=== APPLY DEBUG ===');
    console.log('From date object:', fromDate);
    console.log('To date object:', toDate);
    
    // Use the formatDateDisplay function for consistency
    const from = formatDateDisplay(fromDate);
    const to = formatDateDisplay(toDate);
    
    console.log('Formatted from date:', from);
    console.log('Formatted to date:', to);
    console.log('=== END APPLY DEBUG ===');
    
    onApply(from, to);
  }, [fromDate, toDate, onApply, formatDateDisplay]);

  // Create marked dates for calendar
  const getMarkedDates = useCallback(() => {
    const markedDates: any = {};
    
    if (fromDate?.dateString) {
      markedDates[fromDate.dateString] = { 
        selected: true, 
        selectedColor: Colors.forgotPinBlue, 
        selectedTextColor: Colors.white,
        startingDay: true
      };
    }
    
    if (toDate?.dateString && toDate.dateString !== fromDate?.dateString) {
      markedDates[toDate.dateString] = { 
        selected: true, 
        selectedColor: Colors.forgotPinBlue, 
        selectedTextColor: Colors.white,
        endingDay: true
      };
    }
    
    return markedDates;
  }, [fromDate, toDate]);

  // Set custom short day labels
  LocaleConfig.locales['en'] = {
    monthNames: [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December',
    ],
    monthNamesShort: [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec',
    ],
    dayNames: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
    dayNamesShort: ['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    today: 'Today',
  };
  LocaleConfig.defaultLocale = 'en';

  const renderHeader = () => {
    const monthName = LocaleConfig.locales['en'].monthNames[currentMonth];
    return (
      <View style={styles.headerContainer}>
        <View style={styles.headerLeft}>
          <TouchableOpacity 
            style={styles.monthSelector} 
            onPress={() => setShowMonthPicker(true)}
          >
            <Text style={styles.monthText}>{`${monthName} ${currentYear}`}</Text>
            <ArrowDown width={ms(20)} height={ms(20)} style={{ marginLeft: 4 }} />
          </TouchableOpacity>
        </View>
        <View style={styles.arrowContainer}>
          <TouchableOpacity onPress={handlePreviousMonth} style={styles.arrowButton}>
            <ArrowLeft width={ms(30)} height={ms(30)} />
          </TouchableOpacity>
          <TouchableOpacity onPress={handleNextMonth} style={styles.arrowButton}>
            <ArrowRight width={ms(30)} height={ms(30)} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderMonthPicker = () => {
    return (
      <Modal
        transparent={true}
        visible={showMonthPicker}
        animationType="slide"
        onRequestClose={() => setShowMonthPicker(false)}
      >
        <View style={styles.monthPickerContainer}>
          <View style={styles.monthPicker}>
            <Text style={styles.pickerTitle}>Select Month</Text>
            
            <View style={styles.yearSelector}>
              <TouchableOpacity onPress={() => setCurrentYear(currentYear - 1)}>
                <ArrowLeft width={24} height={24} />
              </TouchableOpacity>
              <Text style={styles.yearText}>{currentYear}</Text>
              <TouchableOpacity onPress={() => setCurrentYear(currentYear + 1)}>
                <ArrowRight width={24} height={24} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.monthGrid}>
              {LocaleConfig.locales['en'].monthNamesShort.map((month: string, index: number) => (
                <TouchableOpacity
                  key={month}
                  style={[
                    styles.monthButton,
                    currentMonth === index && styles.selectedMonth
                  ]}
                  onPress={() => {
                    handleMonthChange(index, currentYear);
                    setShowMonthPicker(false);
                  }}
                >
                  <Text style={[
                    styles.monthButtonText,
                    currentMonth === index && styles.selectedMonthText
                  ]}>
                    {month}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            
            <ButtonComponent
              title="Done"
              onPress={() => setShowMonthPicker(false)}
            />
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View>
      <BottomPopup visible={visible} onCancelPress={onClose}>
        <Text style={styles.title}>{Strings.DailyProgress.selectDate}</Text>
        <View style={styles.dividerView} />
        
        <View style={styles.inputRow}>
          {showFromDate && (
            <View style={styles.dateColomnStyle}>
              <Text style={styles.inputLabel}>{t(fromDateTitle)}</Text>
              <TouchableOpacity
                style={[
                  styles.input,
                  selecting === 'from' && styles.activeInput
                ]}
                onPress={handleFromDatePress}
              >
                <Text style={styles.inputValue}>
                  {fromDate?.dateString 
                    ? formatDateDisplay(fromDate)
                    : formatDateDisplay(null)
                  }
                </Text>
              </TouchableOpacity>
            </View>
          )}
          
          {showToDate && (
            <View style={styles.dateColomnStyle}>
              <Text style={styles.inputLabel}>{t(toDateTitle)}</Text>
              <TouchableOpacity
                style={[
                  styles.input,
                  selecting === 'to' && styles.activeInput
                ]}
                onPress={handleToDatePress}
              >
                <Text style={styles.inputValue}>
                  {toDate?.dateString 
                    ? formatDateDisplay(toDate)
                    : formatDateDisplay(null)
                  }
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        <View style={styles.calendarContainer}>
          {calendarDate && !isNaN(new Date(calendarDate).getTime()) ? (
            <Calendar
              key={`calendar-${currentMonth}-${currentYear}-${fromDate?.dateString}-${toDate?.dateString}`}
              onDayPress={handleDayPress}
              initialDate={calendarDate}
              current={calendarDate}
              markedDates={getMarkedDates()}
              renderHeader={renderHeader}
              onPressArrowLeft={handlePreviousMonth}
              onPressArrowRight={handleNextMonth}
              theme={{
                backgroundColor: 'transparent',
                calendarBackground: 'transparent',
                textSectionTitleColor: Colors.textPrimary,
                selectedDayBackgroundColor: Colors.forgotPinBlue,
                selectedDayTextColor: Colors.white,
                todayTextColor: Colors.textPrimary,
                dayTextColor: Colors.textPrimary,
                textDisabledColor: '#d9e1e8',
                dotColor: Colors.forgotPinBlue,
                selectedDotColor: Colors.white,
                arrowColor: 'transparent',
                monthTextColor: Colors.textPrimary,
                indicatorColor: Colors.textPrimary,
                textDayFontFamily: AppFonts.Medium,
                textMonthFontFamily: AppFonts.SemiBold,
                textDayHeaderFontFamily: AppFonts.SemiBold,
                textDayFontWeight: '500',
                textMonthFontWeight: '700',
                textDayHeaderFontWeight: '700',
                textDayFontSize: 16,
                textMonthFontSize: 18,
                textDayHeaderFontSize: 15,
              }}
              style={styles.calendar}
            />
          ) : (
            <Text style={{textAlign: 'center', margin: 20, color: 'red'}}>Invalid or missing date. Please select a valid date.</Text>
          )}
        </View>

        {showBottomButton && (
          <ButtonComponent
            title={t('commonStrings.apply')}
            onPress={handleApply}
          />
        )}
      </BottomPopup>
      
      {renderMonthPicker()}
    </View>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: ms(16),
    fontFamily: AppFonts.SemiBold,
    fontWeight: '700',
    marginTop: ms(30),
    marginBottom: ms(5),
    color: Colors.textPrimary,
    marginRight:'65%'
  },
  dividerView: {
    height: ms(6),
    backgroundColor: Colors.dailyProgressItemBg,
    marginHorizontal: ms(12),
    marginVertical: ms(10),
  },
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: ms(16),
    marginBottom: 16,
  },
  input: {
    borderWidth: ms(1),
    borderColor: Colors.searchBorderGrey,
    borderRadius: ms(8),
    paddingVertical: ms(10),
    paddingHorizontal: ms(12),
    backgroundColor: Colors.containerligetBlue,
    justifyContent: 'center',
  },
  activeInput: {
    borderColor: Colors.forgotPinBlue,
    borderWidth: ms(2),
  },
  dateColomnStyle: {
    flex: 1,
    marginHorizontal: ms(4),
  },
  inputLabel: {
    color: Colors.pipeIdTextBlack,
    fontSize: ms(14),
    fontFamily: AppFonts.Medium,
    marginBottom: ms(3),
    fontWeight: '500',
  },
  inputValue: {
    fontSize: ms(14),
    fontFamily: AppFonts.Medium,
    color: Colors.textPrimary,
    fontWeight: '500',
  },
  calendarContainer: {
    width: '100%',
    paddingHorizontal: 0,
    marginHorizontal: 0,
  },
  calendar: {
    width: '100%',
    alignSelf: 'stretch',
    marginBottom: ms(10),
    backgroundColor: 'transparent',
  },
  headerContainer: {
    flexDirection: 'row',
    paddingHorizontal: ms(16),
    marginBottom: ms(10),
   
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight:'50%'
  },
  monthSelector: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  monthText: {
    fontSize: ms(14),
    fontWeight: '500',
    color: Colors.textPrimary,
  },
  arrowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    // gap: ms(20),
    marginRight:25
   
    
  },
  arrowButton: {
    marginLeft: ms(10),
  },
  // Month picker styles
  monthPickerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  monthPicker: {
    backgroundColor: Colors.white,
    borderRadius: 10,
    padding: 20,
    width: '80%',
  },
  pickerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  yearSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    gap: 20,
  },
  yearText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  monthGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 10,
  },
  monthButton: {
    width: '30%',
    padding: 10,
    borderRadius: 5,
    backgroundColor: Colors.grey,
    alignItems: 'center',
  },
  selectedMonth: {
    backgroundColor: Colors.forgotPinBlue,
  },
  monthButtonText: {
    fontSize: 14,
    color: Colors.textPrimary,
  },
  selectedMonthText: {
    color: Colors.white,
  },
  doneButton: {
    marginTop: 20,
  },
  buttonRow: {
    paddingHorizontal: ms(16),
    marginBottom: ms(10),
  },
});

export default BottomCalendarModal;