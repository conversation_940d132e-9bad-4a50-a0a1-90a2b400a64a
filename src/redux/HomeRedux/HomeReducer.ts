import { CURRENT_JOB_ID, JOBS_DOWNLOADED } from "./HomeActionTypes";

interface HomeDetailsState {
  isJobsDownloaded: boolean;
  currentJobId: string;
}

const initialState: HomeDetailsState = {
  isJobsDownloaded: false,
  currentJobId: '',
};

export const HomeReducer = (state = initialState, action: any): HomeDetailsState => {
  if (action.type === JOBS_DOWNLOADED) {
    return { ...state, isJobsDownloaded: action.payload };
  }
  if (action.type === CURRENT_JOB_ID) {
    return { ...state, currentJobId: action.payload };
  }
  return state;
};
