import { call, put, takeLatest } from 'redux-saga/effects';
import { USER_MANNUAL_IMAGE_REQUEST, USER_MANNUAL_REQUEST } from './UserMannualActionTypes';
import { userMannualSuccess, userMannualFailure, userMannualImageSuccess, userMannualImageFailure } from './UserMannualActions';
import { postDataWithBodyForDownload, postDataWithBodyToken } from '../../services/ApiRequests';
import { UserMannualDownloadRequestData, UserMannualResponse } from '../../model/UserMannual/UserMannualData';
// import { Use } from 'react-native-svg'; // Removed unused import
import { API } from '../../utils/Constants/ApiConstants';
import { showErrorToast } from '../../components/CustomToast';
import { t } from 'i18next';
import PrintLog from '../../utils/Logger/PrintLog';
import BaseApiConstants from '../../utils/Constants/CommonConstant';


interface UserMannualDownloadAction {
  type: typeof USER_MANNUAL_IMAGE_REQUEST;
  payload: UserMannualDownloadRequestData;
}

// function* fetchUserMannualImageSaga(action: any): any {
//     try {
//       const requestBody = {
//         ModuleName: 'PMP',
//         Unique: action.payload, // PRGDTA_Unique_ID
//         SiteUrl: 'EIP4EPM',
//       };
//       const response = yield call(axios.post,
//         'https://ltceip4prod.azure-api.net:443/Pragatiext/DownloadImaage',
//         requestBody,
//         {
//           headers: {
//             'Content-Type': 'application/json',
//           },
//         }
//       );
//       // The response is a base64 string
//       yield put(userMannualImageSuccess(response.data));
//       console.log('UserMannual Image API response:', response.data);
//     } catch (error) {
//       yield put(userMannualImageFailure(error));
//       console.log('UserMannual Image API error:', error);
//     }
//   }

export function* handleUserMannual() {
  try {
    const params: { Type: string } = {
      Type: 'UserMannual',
    }

    const response: UserMannualResponse = yield call(
      () =>
        new Promise((resolve, reject) =>
          postDataWithBodyForDownload<string, UserMannualResponse>(
            API.userMannual,
            JSON.stringify(params),
            res => resolve(res),
            err => {
              reject(new Error(typeof err === 'string' ? err : JSON.stringify(err)));
            },
          ),
        ),
    );
    if (response?.MasterList?.UserMannual.length > 0) {
      yield put(userMannualSuccess(response));
    } else {
      PrintLog.error('UserMannual Failed', { error: t('commonStrings.noResponseReceived') });
      showErrorToast(t('commonStrings.noResponseReceived'));
      yield put(userMannualFailure(t('commonStrings.noResponseReceived')));
      return;
    }
  } catch (error: any) {
    PrintLog.error('UserMannual catch', { error });
    yield put(userMannualFailure('Error in Fetching User Mannual'));
    showErrorToast('Error in Fetching User Mannual');
  }
}
export function* handleUserMannualDownload(action: UserMannualDownloadAction) {
  try {
    const params = action.payload;
    PrintLog.debug('handleUserMannualDownload', 'Request params:', params);
    PrintLog.debug('handleUserMannualDownload', 'Request URL:', BaseApiConstants.imageUploadDownloadUrl + API.DownloadImaage);
    PrintLog.debug('handleUserMannualDownload', 'Request full object:', {
      baseUrl: BaseApiConstants.imageUploadDownloadUrl,
      urlSuffix: API.DownloadImaage,
      params,
    });
    const response: string = yield call(
      () =>
        new Promise((resolve, reject) =>
          postDataWithBodyToken<UserMannualDownloadRequestData, string>(
            BaseApiConstants.imageUploadDownloadUrl,
            API.DownloadImaage,
            params,
            res => {
              PrintLog.debug('handleUserMannualDownload', 'API Success Response:', typeof res, res && typeof res === 'string' ? res.substring(0, 100) : res);
              resolve(res);
            },
            err => {
              PrintLog.error('handleUserMannualDownload', 'API Error:', err);
              reject(new Error(typeof err === 'string' ? err : JSON.stringify(err)));
            },
          ),
        ),
    );
    if (typeof response === 'string') {
      PrintLog.debug('handleUserMannualDownload', 'Dispatching userMannualImageSuccess');
      yield put(userMannualImageSuccess(response));
    } else {
      PrintLog.error('UserMannualDownload Failed', { error: t('commonStrings.noResponseReceived') });
      showErrorToast(t('commonStrings.noResponseReceived'));
      yield put(userMannualImageFailure(t('commonStrings.noResponseReceived')));
      return;
    }
  } catch (error: any) {
    PrintLog.error('UserMannualDownload catch', { error, errorString: error?.toString?.(), stack: error?.stack });
    yield put(userMannualImageFailure('Error in Downloading User Mannual'));
    showErrorToast('Error in Downloading User Mannual');
  }
}

export default function* userMannualSaga() {
  yield takeLatest(USER_MANNUAL_REQUEST, handleUserMannual);
  yield takeLatest(USER_MANNUAL_IMAGE_REQUEST, handleUserMannualDownload);
} 


