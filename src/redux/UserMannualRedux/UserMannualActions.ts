import { UserMannualDownloadRequestData } from '../../model/UserMannual/UserMannualData';
import { USER_MANNUAL_REQUEST, USER_MANNUAL_SUCCESS, USER_MANNUAL_FAILURE, USER_MANNUAL_IMAGE_FAILURE, USER_MANNUAL_IMAGE_REQUEST, USER_MANNUAL_IMAGE_SUCCESS } from './UserMannualActionTypes';

export const userMannualRequest = () => ({
  type: USER_MANNUAL_REQUEST,
});

export const userMannualSuccess = (data: any) => ({
  type: USER_MANNUAL_SUCCESS,
  payload: data,
});

export const userMannualFailure = (error: any) => ({
  type: USER_MANNUAL_FAILURE,
  payload: error,
}); 

export const userMannualImageRequest = (data: UserMannualDownloadRequestData) => ({
    type: USER_MANNUAL_IMAGE_REQUEST,
    payload: data,
  });
  
  export const userMannualImageSuccess = (data: string) => ({
    type: USER_MANNUAL_IMAGE_SUCCESS,
    payload: data,
  });
  
  export const userMannualImageFailure = (error: any) => ({
    type: USER_MANNUAL_IMAGE_FAILURE,
    payload: error,
  });