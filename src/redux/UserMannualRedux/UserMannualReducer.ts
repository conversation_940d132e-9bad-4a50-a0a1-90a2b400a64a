import { UserMannualResponseData } from '../../model/UserMannual/UserMannualData';
import { USER_MANNUAL_REQUEST, USER_MANNUAL_SUCCESS, USER_MANNUAL_FAILURE, USER_MANNUAL_IMAGE_FAILURE, USER_MANNUAL_IMAGE_REQUEST, USER_MANNUAL_IMAGE_SUCCESS } from './UserMannualActionTypes';

// const initialState = {
//   loading: false,
//   data: null,
//   error: null,

//   loadingUserMannualPdf: false,
//   dataUserMannualPdf: null,
//   errorUserMannualPdf: null,
// };

interface UsermannualState {
  userMannualData: UserMannualResponseData | null;
  userMannualLoading: boolean;
  userMannualError: string | null;

  userMannualDownloadData: string;
  userMannualDownloading: boolean;
  userMannualDownloadError: string | null;
}

const initialState: UsermannualState = {
  userMannualData: null,
  userMannualLoading: false,
  userMannualError: null,
  userMannualDownloadData: '',
  userMannualDownloading: false,
  userMannualDownloadError: null,
};

export const UserMannualReducer = (state = initialState, action: any) => {
  switch (action.type) {
    case USER_MANNUAL_REQUEST:
      return { ...state, userMannualLoading: true, userMannualError: null };
    case USER_MANNUAL_SUCCESS:
      return { ...state, userMannualLoading: false, userMannualData: action.payload };
    case USER_MANNUAL_FAILURE:
      return { ...state, userMannualLoading: false, userMannualError: action.payload };

    case USER_MANNUAL_IMAGE_REQUEST:
      return { ...state, userMannualDownloading: true, userMannualDownloadError: null };
    case USER_MANNUAL_IMAGE_SUCCESS:
      return { ...state, userMannualDownloading: false, userMannualDownloadData: action.payload };
    case USER_MANNUAL_IMAGE_FAILURE:
      return { ...state, userMannualDownloading: false, userMannualDownloadError: action.payload };

    default:
      return state;
  }
};
