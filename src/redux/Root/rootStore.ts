// import { configureStore } from '@reduxjs/toolkit';
// import createSagaMiddleware from 'redux-saga';
// import { LoginReducer } from './../AuthRedux/Login/LoginReducer';
// import { TokenReducer } from '../AuthRedux/Token/TokenReducer';
// import { RolesReducer } from '../AuthRedux/Roles/RolesReducer';
// import { HomeReducer } from '../HomeRedux/HomeReducer';
// import rootSaga from './rootSaga';
// import wbsReducer from '../WBSDownload/WBSReducer';
// import { wbsRolesReducer } from '../RolesRedux/WBSRolesReducer';

// const sagaMiddleware = createSagaMiddleware();
// const store = configureStore({
//   reducer: {
//     auth: LoginReducer,
//     token: TokenReducer,
//     roles: RolesReducer,
//     home: HomeReducer,
//     wbs: wbsReducer,
//     wbsRoles: wbsRolesReducer,
//   },
//   middleware: getDefaultMiddleware =>
//     getDefaultMiddleware({
//       serializableCheck: false,
//       thunk: false,
//     }).concat(sagaMiddleware),
// });

// sagaMiddleware.run(rootSaga);

// export type RootState = ReturnType<typeof store.getState>;
// export type AppDispatch = typeof store.dispatch;

// export default store;



import { configureStore } from '@reduxjs/toolkit';
import createSagaMiddleware from 'redux-saga';
import rootReducer from './RootReducer'; // 
import rootSaga from './rootSaga';

const sagaMiddleware = createSagaMiddleware();

const store = configureStore({
  reducer: rootReducer, 
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: false,
      thunk: false,
    }).concat(sagaMiddleware),
});

sagaMiddleware.run(rootSaga);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;

