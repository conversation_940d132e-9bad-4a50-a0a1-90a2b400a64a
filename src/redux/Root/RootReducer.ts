import { combineReducers } from 'redux';
import { LoginReducer } from './../AuthRedux/Login/LoginReducer';
import { TokenReducer } from '../AuthRedux/Token/TokenReducer';
import { HomeReducer } from '../HomeRedux/HomeReducer';
import wbsReducer from '../WBSDownload/WBSReducer';
import { RolesReducer } from '../RolesRedux/RolesReducer';
import progressUpdateReducer from '../ProgressUpdate/ProgressUpdateReducer';
import { BookmarksReducer } from '../DailyProgressRedux/Bookmark/BookmarkReducer';
import {UserMannualReducer} from '../UserMannualRedux/UserMannualReducer';
import hindranceMapReducer from '../HindranceRedux/HindranceMapReducer';
import { ApproveReducer } from '../DailyProgressApprover/ApproveReducer';
import uploadImageReducer from '../DailyProgressApprover/UploadImageReducer';

const appReducer = combineReducers({
  auth: LoginReducer,
  token: TokenReducer,
  home: HomeReducer,
  wbs: wbsReducer,
  rolesData: RolesReducer,
  progressUpdate: progressUpdateReducer,
  bookmark: BookmarksReducer,
  userMannual: UserMannualReducer,
  hindranceMap: hindranceMapReducer,
  approveReducer: ApproveReducer,
  uploadImageReducer: uploadImageReducer,
});

const RootReducer = (state: any, action: any) => {
  if (action.type === 'LOGOUT') {
    state = undefined; // This will reset all slices to their initial state
  }
  return appReducer(state, action);
};

export type RootState = ReturnType<typeof appReducer>;

export default RootReducer;

