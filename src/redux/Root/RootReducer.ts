// import { LoginReducer } from './../AuthRedux/Login/LoginReducer';
// import { TokenReducer } from '../AuthRedux/Token/TokenReducer';
// import { RolesReducer } from '../AuthRedux/Roles/RolesReducer';
// import { HomeReducer } from '../HomeRedux/HomeReducer';
// import { combineReducers } from 'redux';
// import wbsReducer from '../WBSDownload/WBSReducer';
// import { wbsRolesReducer } from '../RolesRedux/WBSRolesReducer';

// const reducers = {
//   loginReducer: LoginReducer,
//   tokenReducer: TokenReducer,
//   rolesReducer: RolesReducer,
//   homeReducer: HomeReducer,
//   wbsReducer: wbsReducer,
//   wbsRolesReducer: wbsRolesReducer
// };

// export const RootReducer = combineReducers(reducers);

// export type RootState = ReturnType<typeof RootReducer>;

import { combineReducers } from 'redux';
import { LoginReducer } from './../AuthRedux/Login/LoginReducer';
import { TokenReducer } from '../AuthRedux/Token/TokenReducer';
import { HomeReducer } from '../HomeRedux/HomeReducer';
import wbsReducer from '../WBSDownload/WBSReducer';
import { RolesReducer } from '../RolesRedux/RolesReducer';
import progressUpdateReducer from '../ProgressUpdate/ProgressUpdateReducer';

const appReducer = combineReducers({
  auth: LoginReducer,
  token: TokenReducer,
  home: HomeReducer,
  wbs: wbsReducer,
  rolesData: RolesReducer,
  progressUpdate: progressUpdateReducer
});

const RootReducer = (state: any, action: any) => {
  if (action.type === 'LOGOUT') {
    state = undefined; // This will reset all slices to their initial state
  }
  return appReducer(state, action);
};

export type RootState = ReturnType<typeof appReducer>;

export default RootReducer;

