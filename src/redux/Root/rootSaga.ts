import { all, fork } from 'redux-saga/effects';
import loginSaga from '../AuthRedux/Login/LoginSaga';
import tokenSaga from '../AuthRedux/Token/TokenSaga';
import PrintLog from '../../utils/Logger/PrintLog';
import { wbsSaga } from '../WBSDownload/WBSSaga';
import rolesSaga from '../RolesRedux/RolesSaga';
import logoutSaga from '../AuthRedux/Logout/LogoutSaga';
import bookmarksSaga from '../DailyProgressRedux/Bookmark/BookmarkSaga';
import userMannualSaga from '../UserMannualRedux/UserMannualSaga';
import { progressUpdateRootSaga } from '../ProgressUpdate/ProgressUpdateSaga';
import { hindranceMapSaga } from '../HindranceRedux/HindranceMapSaga';
import approveSaga from '../DailyProgressApprover/ApproveSaga';

export default function* rootSaga() {
  PrintLog.debug('rootSaga', 'Running root saga');
  try {
    yield all([
      fork(loginSaga),
      fork(tokenSaga),
      fork(rolesSaga),
      fork(wbsSaga),
      fork(logoutSaga),
      fork(progressUpdateRootSaga),
      fork(bookmarksSaga),
      fork(userMannualSaga),
      fork(hindranceMapSaga),
      fork(approveSaga),
      // Add other sagas here
    ]);
  } catch (error) {
    PrintLog.debug('Saga error:', error);
  }
}
