import { all, fork } from 'redux-saga/effects';
import loginSaga from '../AuthRedux/Login/LoginSaga';
import tokenSaga from '../AuthRedux/Token/TokenSaga';
import PrintLog from '../../utils/Logger/PrintLog';
import { wbsSaga } from '../WBSDownload/WBSSaga';
import rolesSaga from '../RolesRedux/RolesSaga';
import logoutSaga from '../AuthRedux/Logout/LogoutSaga';
import { progressUpdateRootSaga } from '../ProgressUpdate/ProgressUpdateSaga';

export default function* rootSaga() {
  PrintLog.debug('rootSaga', 'Running root saga');
  try {
    yield all([
      fork(loginSaga),
      fork(tokenSaga),
      fork(rolesSaga),
      fork(wbsSaga), ,
      fork(logoutSaga),
      fork(progressUpdateRootSaga)
      // Add other sagas here
    ]);
  } catch (error) {
    PrintLog.debug('Saga error:', error);
  }
}
