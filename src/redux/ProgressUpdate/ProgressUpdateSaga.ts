import { call, put, takeLatest } from 'redux-saga/effects';
import { AxiosResponse } from 'axios';
import { PROGRESS_UPDATE_REQUEST } from './ProgressUpdateActionTypes';
import { ProgressUpdateSuccess, ProgressUpdateFailure } from './ProgressUpdateActions';
import { postDataWithBodyForDownload } from '../../services/ApiRequests';
import PrintLog from '../../utils/Logger/PrintLog';
import Config from '../../config/Config';
import { API } from '../../utils/Constants/ApiConstants';
import { ProgressUpdateRequestBody, ProgressUpdateResponseData } from '../../model/DailyProgress/DailyProgressData';
import { t } from 'i18next';

// API function for progress update
const progressUpdateAPI = async (payload: ProgressUpdateRequestBody): Promise<ProgressUpdateResponseData> => {
    console.log('ProgressUpdateRequest', payload);
   
    return new Promise((resolve, reject) => {
        postDataWithBodyForDownload(
            API.ProgressUpdateInsert,
            payload,
            (response: ProgressUpdateResponseData) => {
                resolve(response);
            },
            (error: any) => {
                reject(error);
            }
        );
    });
};

// Saga for progress update
function* progressUpdateSaga(action: { type: string; payload: ProgressUpdateRequestBody }) {
    try {
        const response: ProgressUpdateResponseData = yield call(progressUpdateAPI, action.payload);
        if (response) {
            yield put(ProgressUpdateSuccess(response));
            action.payload.cb({ success: true, data: response });
        } else {
            yield put(ProgressUpdateFailure(t('commonStrings.internalServerError')));
            action.payload.cb({ success: false, data: null });
        }
    } catch (error: any) {
        const errorMessage = error?.response?.data?.message || error?.message || 'Network error occurred';
        yield put(ProgressUpdateFailure(errorMessage));
        action.payload.cb({ success: false, data: null });
    }
}

// Root saga
export function* progressUpdateRootSaga() {
    yield takeLatest(PROGRESS_UPDATE_REQUEST, progressUpdateSaga);
}
