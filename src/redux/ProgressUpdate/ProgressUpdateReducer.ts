import { PROGRESS_UPDATE_REQUEST, PROGRESS_UPDATE_SUCCESS, PROGRESS_UPDATE_FAILURE } from './ProgressUpdateActionTypes';

interface ProgressUpdateState {
    loading: boolean;
    error: string | null;
    response: any | null;
}

const initialState: ProgressUpdateState = {
    loading: false,
    error: null,
    response: null,
};

const progressUpdateReducer = (state = initialState, action: any) => {
    switch (action.type) {
        case PROGRESS_UPDATE_REQUEST:
            return {
                ...state,
                loading: true,
                error: null,
            };
        case PROGRESS_UPDATE_SUCCESS:
            return {
                ...state,
                loading: false,
                error: null,
                response: action.payload,
            };
        case PROGRESS_UPDATE_FAILURE:
            return {
                ...state,
                loading: false,
                error: action.payload,
            };
        default:
            return state;
    }
};

export default progressUpdateReducer;