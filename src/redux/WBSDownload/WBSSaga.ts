import { call, put, takeLatest } from 'redux-saga/effects';
import { wbsRequest, wbsSuccess, wbsFailure, getPendingApproverWbsRequest, wbsPendingApprovalSuccess } from './WBSActions';
import { postDataWithBodyForDownload } from '../../services/ApiRequests';
import { WbsRequestData, WbsJobResponseData, WbsJobItem, GetWbsApproverProps, WbsPendingApprovalModal, WBSPendingApprovalProps } from '../../model/Wbs/WbsData';
import { WBS_APPROVER_REQUEST, WBS_REQUEST } from './WBSActionTypes';
import { API } from '../../utils/Constants/ApiConstants';
import { StoreWbsHierarchyData } from '../../database/WBSDataInsert/StoreWbsHierarchyData';
import { StoreWbsDetailsData } from '../../database/WBSDataInsert/StoreWbsDetailsData';
import { StoreWbsGISDetailsData } from '../../database/WBSDataInsert/StoreWbsGISDetailsData';
import { StoreProgressConsolidatedData } from '../../database/WBSDataInsert/StoreProgressConsolidatedData';
import { StoreProgressEngineerData } from '../../database/WBSDataInsert/StoreProgressEngineerData';
import { StoreViewLastUpdateBQIT } from '../../database/WBSDataInsert/StoreViewLastUpdateBQITData';
import { StoreViewLastUpdateGISData } from '../../database/WBSDataInsert/StoreViewLastUpdateGISData';
import { StoreBookMarkListData } from '../../database/WBSDataInsert/StoreBookMarkListData';
import { StoreLatLongHierarchyData } from '../../database/WBSDataInsert/StoreLatLongHierarchyData';
import { StoreWbsTaskData } from '../../database/WBSDataInsert/StoreWbsTaskData';
import { setSelectedJobs } from '../../utils/Storage/Storage';
import { t } from 'i18next';
import { customAlertWithOK } from '../../components/CustomAlert';
import { StorePendingApproval } from '../../database/WBSDataInsert/StorePendingApprovalData';
import { InteractionManager } from 'react-native';

const WBS_TYPES = [
  'WBS',
  'Task',
  'Details',
  'GISDetails',
  'ProgressConsolidated',
  'ProgressDetails_Enginner',
  'ViewLastUpdateBQITFromTodate',
  'ViewLastUpdateGISFromTodate'
] as const;

const MASTER_LIST_TYPES = ['BookMarkList', 'LatLongHierarchy'] as const;

function* fetchMasterListData(userId: string, objJoblist: WbsJobItem[]): Generator<any, any, any> {
  try {
    const responses = yield Promise.all(
      MASTER_LIST_TYPES.map(type =>
        new Promise((resolve, reject) => {
          postDataWithBodyForDownload(
            API.masterList,
            {
              JobWorkLists: objJoblist,
              Uid: userId,
              Type: type
            },
            resolve,
            reject
          );
        })
      )
    );

    const masterListResponses = MASTER_LIST_TYPES.reduce((acc, type, index) => {
      const response = responses[index];
      if (!response) {
        throw new Error(t('commonStrings.noResponseReceived'));
      }
      acc[type] = response;
      return acc;
    }, {} as Record<string, any>);


    const bookMarkLinkedData = masterListResponses?.BookMarkList?.MasterList?.BookMarkLinked;
    const latLongHierarchyData = masterListResponses?.LatLongHierarchy?.MasterList?.LatLongHierarchy;


    yield Promise.all([
      bookMarkLinkedData && Array.isArray(bookMarkLinkedData)
        ? StoreBookMarkListData(bookMarkLinkedData)
        : Promise.resolve(),
      latLongHierarchyData && Array.isArray(latLongHierarchyData)
        ? StoreLatLongHierarchyData(latLongHierarchyData)
        : Promise.resolve()
    ]);

    return masterListResponses;
  } catch (error) {
    yield put(wbsFailure(t('commonStrings.wbsDownloadFailed')));
    return null;
  }
}

const showNoDataAlert = (isLogin: boolean = false) => {
  !isLogin && customAlertWithOK(
    t('commonStrings.alertTitle'),
    t('commonStrings.NoDetailsFound'),
    [{ text: t('commonStrings.ok') }],
    false
  );
};


// Utility function to introduce delays
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Process large arrays in manageable chunks
async function processInChunks<T>(
  array: T[],
  processChunk: (chunk: T[]) => Promise<void>,
  chunkSize: number = 500
): Promise<void> {
  for (let i = 0; i < array.length; i += chunkSize) {
    const chunk = array.slice(i, i + chunkSize);
    await processChunk(chunk);
    await delay(50); // Brief pause between chunks
  }
}

// Store data chunks based on type
async function storeChunkData(type: string, chunk: any[], objJoblist: any): Promise<void> {
  try {
    switch (type) {
      case 'WBS':
        await StoreWbsHierarchyData(chunk, objJoblist);
        break;
      case 'Task':
        await StoreWbsTaskData(chunk, objJoblist);
        break;
      case 'Details':
        await StoreWbsDetailsData(chunk, objJoblist);
        break;
      case 'GISDetails':
        await StoreWbsGISDetailsData(chunk, objJoblist);
        break;
      case 'ProgressConsolidated':
        await StoreProgressConsolidatedData(chunk, objJoblist);
        break;
      case 'ProgressDetails_Enginner':
        await StoreProgressEngineerData(chunk, objJoblist);
        break;
      case 'ViewLastUpdateBQITFromTodate':
        await StoreViewLastUpdateBQIT(chunk);
        break;
      case 'ViewLastUpdateGISFromTodate':
        await StoreViewLastUpdateGISData(chunk);
        break;
      default:
        console.warn(`Unknown type: ${type}`);
    }
  } catch (error) {
    console.error(`Error storing ${type} chunk:`, error);
    throw error;
  }
}

// Map WBS types to their response keys
function getResponseKey(type: string): string {
  const typeMap: Record<string, string> = {
    'WBS': 'WbsHierarchyOutput',
    'Task': 'WbsTaskOutput',
    'Details': 'WbsDetailsOutput',
    'GISDetails': 'WbsGISDetailsOutput',
    'ProgressConsolidated': 'ProgressConsolidatedOutput',
    'ProgressDetails_Enginner': 'DeliverableProgressDetailsEnginneerOutput',
    'ViewLastUpdateBQITFromTodate': 'ViewLastUpdateBQITFromTodateOutput',
    'ViewLastUpdateGISFromTodate': 'ViewLastUpdateGISFromTodateOutput'
  };
  return typeMap[type] || '';
}

// Validate API responses
function validateResponse(type: string, response: any): boolean {
  const responseKey = getResponseKey(type);
  if (!response || !response[responseKey]) return false;


  return Array.isArray(response[responseKey]) &&
    (response[responseKey].length > 0 ||
      type.includes('ViewLastUpdate'));
}

// Process individual API call with chunked handling
function processApiCall(
  endpoint: string,
  requestBody: any,
  type: string,
  objJoblist: any
): Promise<any> {
  return new Promise((resolve, reject) => {
    postDataWithBodyForDownload(
      endpoint,
      requestBody,
      async (response) => {
        try {
          if (!response) {
            reject(new Error('No response received'));
            return;
          }

          const responseKey = getResponseKey(type);
          const dataArray = response[responseKey];

          if (dataArray?.length > 0) {
            await processInChunks(
              dataArray,
              async (chunk) => {
                await storeChunkData(type, chunk, objJoblist);
              },
              dataArray.length > 1000 ? 500 : dataArray.length
            );
          }
          resolve(response);
        } catch (error) {
          reject(error);
        }
      },
      (error) => {
        error?.response?.status >= 500 ? reject(error) : resolve(null);
      }
    );
  });
}

// Main saga function
function* fetchWbsJob(action: ReturnType<typeof wbsRequest>): Generator<any, void, any> {
  try {
    const { userId, objJoblist, Fromdate, Todate, selectedJob, isLogin } = action.payload;

    const allResponses: WbsJobResponseData = {
      WbsHierarchyOutput: null,
      WbsTaskOutput: null,
      WbsDetailsOutput: null,
      WbsGISDetailsOutput: null,
      ProgressConsolidatedOutput: null,
      DeliverableProgressDetailsEnginneerOutput: null,
      ViewLastUpdateBQITFromTodateOutput: null,
      ViewLastUpdateGISFromTodateOutput: null
    };

    // Process API calls sequentially
    for (const type of WBS_TYPES) {
      try {
        const requestBody = {
          userId,
          type,
          objJoblist,
          ...(type.includes('ViewLastUpdate') ? { Fromdate, Todate } : {})
        };

        const response = yield call(processApiCall, API.WBSDownload, requestBody, type, objJoblist);


        if (validateResponse(type, response)) {
          const responseKey = getResponseKey(type);
          allResponses[responseKey] = response[responseKey];
        } else if (!isLogin) {
          yield put(wbsFailure(t('commonStrings.invalidWbsResponse')));
          action.payload.cb({ success: false });
          return;
        }

        yield call(delay, 500); // Throttle API calls
      } catch (error) {
        console.error(`API call failed for ${type}:`, error);
        if (!isLogin) {
          yield put(wbsFailure('WBS download failed'));
          action.payload.cb({ success: false });
          return;
        }
      }
    }

    // Only proceed if we got some valid responses
    if (Object.values(allResponses).some(Boolean)) {
      const masterListData = yield* fetchMasterListData(userId.toString(), objJoblist);
      yield put(wbsSuccess({ ...allResponses, masterList: masterListData }));

      if (selectedJob && selectedJob?.length > 0) {
        setSelectedJobs(selectedJob);
      }
      action.payload.cb({ success: true });
    } else {
      showNoDataAlert();
      yield put(wbsFailure('No valid responses received'));
    }
  } catch (error) {
    console.error('Error in fetchWbsJob:', error);
    yield put(wbsFailure('WBS processing failed'));
    action.payload.cb({ success: false });
  }
}


function* getPendingApproverWbsSaga(
  action: ReturnType<typeof getPendingApproverWbsRequest>
): Generator<any, void, any> {
  try {
    const { jobCode, type, objJoblist, selectedJob, cb } = action.payload;

    const requestBody = { jobCode, type };

    const response1: WBSPendingApprovalProps = yield call(() =>
      new Promise((resolve, reject) => {
        postDataWithBodyForDownload<GetWbsApproverProps, WBSPendingApprovalProps>(
          API.getPendingAppovals,
          requestBody,
          res => res ? resolve(res) : reject(new Error(t('commonStrings.noResponseReceived'))),
          err => err?.response?.status >= 500 ? reject(err) : resolve(null)
        );
      })
    );

    const response2: WBSPendingApprovalProps = yield call(() =>
      new Promise((resolve, reject) => {
        postDataWithBodyForDownload<GetWbsApproverProps, WBSPendingApprovalProps>(
          API.getNodeApprovals,
          requestBody,
          res => res ? resolve(res) : reject(new Error(t('commonStrings.noResponseReceived'))),
          err => err?.response?.status >= 500 ? reject(err) : resolve(null)
        );
      })
    );

    const combinedTable = [
      ...(response1?.Table || []),
      ...(response2?.Table || [])
    ];

    StorePendingApproval(combinedTable, objJoblist);

    if (combinedTable.length > 0) {
      if (selectedJob && selectedJob.length > 0) {
        setSelectedJobs(selectedJob);
      }
      yield put(wbsPendingApprovalSuccess(combinedTable));

      // ✅ Call success callback
      if (cb) {
        cb({ success: true });
      }
    } else {
      showNoDataAlert();
      yield put(wbsFailure(t('commonStrings.noResponseReceived')));

      // ❌ Call failure callback
      if (cb) {
        cb({ success: false });
      }
    }

  } catch (error) {
    console.error('getPendingApproverWbsSaga error:', error);
    yield put(wbsFailure(t('commonStrings.wbsDownloadFailed')));

    // ❌ Call failure callback
    if (action.payload.cb) {
      action.payload.cb({ success: false });
    }
  }
}



export function* wbsSaga() {
  yield takeLatest(WBS_REQUEST, fetchWbsJob);
  yield takeLatest(WBS_APPROVER_REQUEST, getPendingApproverWbsSaga);
}