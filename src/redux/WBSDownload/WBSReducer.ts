import {
    WBS_REQUEST,
    WBS_SUCCESS,
    WBS_FAILURE,
    WBS_PENDING_APPROVER_REQUEST,
    WBS_PENDING_APPROVAL_SUCCESS,
    WBS_PENDING_APPROVAL_FAILURE,
} from './WBSActionTypes';
import { WbsJobResponseData, WbsPendingApprovalModal } from '../../model/Wbs/WbsData';

interface WbsState {
    isLoading: boolean;
    isPendingLoading: boolean;
    error: string | null;
    response: WbsJobResponseData | null;
    pendingApprovalResponse: WbsPendingApprovalModal[];
    pendingApprovalError: string | null;
}

const initialState: WbsState = {
    isLoading: false,
    isPendingLoading: false,
    error: null,
    response: null,
    pendingApprovalResponse: [],
    pendingApprovalError: null,
};

const wbsReducer = (state = initialState, action: any): WbsState => {
    switch (action.type) {
        case WBS_REQUEST:
            return {
                ...state,
                isLoading: true,
                error: null,
            };
        case WBS_SUCCESS:
            return {
                ...state,
                isLoading: false,
                response: action.payload,
                error: null,
            };
        case WBS_FAILURE:
            return {
                ...state,
                isLoading: false,
                error: action.payload,
            };
        case WBS_PENDING_APPROVER_REQUEST:
            return {
                ...state,
                isPendingLoading: true,
                pendingApprovalError: null
            };
        case WBS_PENDING_APPROVAL_SUCCESS:
            return {
                ...state,
                isPendingLoading: false,
                pendingApprovalResponse: action.payload,
            };
        case WBS_PENDING_APPROVAL_FAILURE:
            return {
                ...state,
                isPendingLoading: false,
                pendingApprovalError: null,
            };
        default:
            return state;
    }
};

export default wbsReducer;
