import { WBS_REQUEST, WBS_SUCCESS, WBS_FAILURE, WBS_APPROVER_REQUEST as WBS_PENDING_APPROVER_REQUEST, WBS_PENDING_APPROVAL_SUCCESS, WBS_PENDING_APPROVAL_FAILURE } from './WBSActionTypes';
import { WbsRequestData, WbsJobResponseData, GetWbsApproverProps, WbsPendingApprovalModal } from '../../model/Wbs/WbsData';

export const wbsRequest = (payload: WbsRequestData) => ({
    type: WBS_REQUEST,
    payload
});

export const getPendingApproverWbsRequest = (payload: GetWbsApproverProps) => ({
    type: WBS_PENDING_APPROVER_REQUEST,
    payload
});

export const wbsSuccess = (payload: WbsJobResponseData) => ({
    type: WBS_SUCCESS,
    payload
});

export const wbsFailure = (payload: string) => ({
    type: WBS_FAILURE,
    payload
});

export const fetchWBSDataSuccess = (payload: WbsJobResponseData) => ({
    type: WBS_SUCCESS,
    payload
});

export const fetchWBSDataFailure = (payload: string) => ({
    type: WBS_FAILURE,
    payload
}); 

export const wbsPendingApprovalSuccess = (payload: WbsPendingApprovalModal[]) => ({
    type: WBS_PENDING_APPROVAL_SUCCESS,
    payload
});

export const wbsPendingApprovalFailure = (payload: string) => ({
    type: WBS_PENDING_APPROVAL_FAILURE,
    payload
});
