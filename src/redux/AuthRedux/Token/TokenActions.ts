
import { TokenResponse } from '../../../model/Auth/TokenModel';
import {
  VALIDATE_TOKEN_REQUEST,
  VALIDATE_TOKEN_SUCCESS,
  VALIDATE_TOKEN_FAILURE,
} from './TokenActionTypes';

export const validateTokenRequest = () => ({
  type: VALIDATE_TOKEN_REQUEST,
});

export const validateTokenSuccess = (response: TokenResponse) => ({
  type: VALIDATE_TOKEN_SUCCESS,
  payload: response,
});

export const validateTokenFailure = (error: string) => ({
  type: VALIDATE_TOKEN_FAILURE,
  payload: error,
}); 