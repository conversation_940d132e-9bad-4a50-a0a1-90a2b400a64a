import { call, put, takeLatest } from 'redux-saga/effects';
import { validateTokenSuccess, validateTokenFailure } from './TokenActions';
import { VALIDATE_TOKEN_REQUEST } from './TokenActionTypes';
import { postDataWithBodyToken } from '../../../services/ApiRequests';
import { API } from '../../../utils/Constants/ApiConstants';
import PrintLog from '../../../utils/Logger/PrintLog';
import { TokenModel, TokenResponse } from '../../../model/Auth/TokenModel';
import Strings from '../../../utils/Strings/Strings';
import { getToken, removeToken, saveToken } from './../../../utils/DataStorage/Storage';
import Config from '../../../config/Config';
import { showErrorToast } from '../../../components/CustomToast';
import { t } from 'i18next';
import BaseApiConstants from '../../../utils/Constants/CommonConstant';

export function* handleValidateToken() {
  try {
    const tokenModel: TokenModel = {
      ClientID: BaseApiConstants.clientID,
      SecretKey: BaseApiConstants.secretKey,
      CompanyCode: BaseApiConstants.companyCode,
      isipcheck: 'N',
    };
    const response: TokenResponse = yield call(
      () =>
        new Promise((resolve, reject) =>
          postDataWithBodyToken<TokenModel, TokenResponse>(
            BaseApiConstants.defaultGenerateToken,
            API.tokenEndpoint,
            tokenModel,
            res => {
              PrintLog.debug('Token Validation Response', { response: res });
              // Handle string response
              if (typeof res === 'string') {
                resolve({
                  statusCode: 200,
                  message: res,
                  token: res
                });
              } else {
                resolve(res);
              }
            },
            err => {
              PrintLog.error('Token Validation Error', { error: err });
              reject(new Error(typeof err === 'string' ? err : JSON.stringify(err)));
            },
          ),
        ),
    );

    if (!response) {
      PrintLog.error('Token Validation Failed', { error: t('commonStrings.noResponseReceived') });
      showErrorToast(t('commonStrings.noResponseReceived'));
      yield put(validateTokenFailure(t('commonStrings.noResponseReceived')));
      return;
    }

    // Handle string response from API
    const responseMessage = typeof response === 'string' ? response : response.message;
    const statusCode = typeof response === 'string' ? 200 : response.statusCode;

    if (statusCode === 200 && responseMessage === Strings.validationStrings.invalidToken) {
      PrintLog.error('Token Validation Failed', { error: responseMessage });
      removeToken();
      showErrorToast(Strings.validationStrings.invalidToken);
      yield put(validateTokenFailure(Strings.validationStrings.invalidToken));
      return;
    }

    // Handle valid token response
    if (statusCode === 200 && responseMessage.includes('valid')) {
      PrintLog.debug('Token Validation Success with valid token', { token: responseMessage });
      const storedToken = getToken();
      PrintLog.debug('storedToken: ', storedToken, ' -- Config.APPLICATION_TOKEN: ', Config.APPLICATION_TOKEN);
      if (storedToken) {
        yield put(validateTokenSuccess({
          statusCode: 200,
          message: 'Token valid',
          token: storedToken,
        }));
      } else {
        // No token found in storage..
        yield put(validateTokenSuccess({
          statusCode: 200,
          message: 'Token valid',
          token: Config.APPLICATION_TOKEN!,
        }));
        saveToken(Config.APPLICATION_TOKEN!);
      }

      return;
    }

    PrintLog.debug('Token Validation Success', { response });
    if (statusCode === 200 && response.token) {
      saveToken(response.token);
      yield put(validateTokenSuccess({
        statusCode,
        message: response.message,
        token: response.token,
      }));
      return;
    }
  } catch (error) {
    PrintLog.error('Token Validation Exception', { error });
    removeToken();
    showErrorToast(t('commonStrings.internalServerError'));
    yield put(validateTokenFailure('Token validation failed'));
  }
}

export default function* tokenSaga() {
  yield takeLatest(VALIDATE_TOKEN_REQUEST, handleValidateToken);
} 
