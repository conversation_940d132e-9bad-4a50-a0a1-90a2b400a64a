import {
  VAL<PERSON>ATE_TOKEN_REQUEST,
  VALIDATE_TOKEN_SUCCESS,
  VALIDATE_TOKEN_FAILURE,
  VALIDATE_TOKEN_LOGOUT,
} from './TokenActionTypes';

interface TokenState {
  token: string | null;
  loading: boolean;
  error: string | null;
}

const initialState: TokenState = {
  token: null,
  loading: false,
  error: null,
};

export const TokenReducer = (state = initialState, action: any): TokenState => {
  switch (action.type) {
    case VALIDATE_TOKEN_REQUEST:
      return { ...state, loading: true, error: null };

    case VALIDATE_TOKEN_SUCCESS:
      return {
        ...state,
        loading: false,
        token: action.payload.token,
        error: null,
      };

    case VALIDATE_TOKEN_FAILURE:
      return {
        ...state,
        loading: false,
        token: null,
        error: action.payload,
      };
    case VALIDATE_TOKEN_LOGOUT:
      // Reset state on logout
      return initialState;

    default:
      return state;
  }
}; 