import { call, put, takeLatest } from 'redux-saga/effects';
import { loginFailure, loginSuccess } from './LoginActions';
import { LOGIN_REQUEST } from './LoginActionTypes';
import { postDataWithBodyForAuth } from '../../../services/ApiRequests';
import { API } from '../../../utils/Constants/ApiConstants';
import PrintLog from '../../../utils/Logger/PrintLog';
import { customAlertWithOK } from '../../../components/CustomAlert';
import { LoginRequestData } from '../../../model/Auth/LoginData';
import { decryptString } from '../../../utils/Crypto/Decryption';
import { t } from 'i18next';

interface LoginAction {
  type: typeof LOGIN_REQUEST;
  payload: LoginRequestData;
}

function postDataWithAuthPromise(params: LoginRequestData) {
  console.log("params",params)
  return new Promise<string | object>((resolve, reject) => {
    postDataWithBodyForAuth(
      API.login,
      params,
      res => resolve(res),
      err => {
        const error = typeof err === 'string'
          ? new Error(err)
          : new Error(JSON.stringify(err));
        reject(error);
      }
    );
  });
}

export function* handleLogin(action: LoginAction) {
  try {
    const params = action.payload;

    const response: string | object = yield call(postDataWithAuthPromise, params);

    PrintLog.debug('Type of response:', typeof response, 'Response:', response);

    if (!response) {
      PrintLog.error('Login Validation Failed', { error: 'No response received' });
      customAlertWithOK(
        t('commonStrings.alertTitle'),
        t('commonStrings.noResponseReceived'),
        [{ text: t('commonStrings.ok') }],
        false,
      );
      yield put(loginFailure(t('commonStrings.noResponseReceived')));
      return;
    }

    if (typeof response === 'string') {
      const decrypted = decryptString(response);
      const parsed = JSON.parse(decrypted);
      const userData = parsed[0];
      yield put(loginSuccess(userData));
    } else if (typeof response === 'object') {
      const errorData = Array.isArray(response) ? response[0] : response;
      yield put(loginFailure(errorData?.Message ?? 'Login failed'));
    } else {
      throw new Error('Unexpected response format');
    }

  } catch (error: any) {
    PrintLog.error('handleLogin catch', { error });
    yield put(loginFailure('Login failed'));
    customAlertWithOK(
      t('commonStrings.alertTitle'),
      t('commonStrings.internalServerError'),
      [{ text: t('commonStrings.ok') }],
      false,
    );
  }
}

export default function* loginSaga() {
  yield takeLatest(LOGIN_REQUEST, handleLogin);
}
