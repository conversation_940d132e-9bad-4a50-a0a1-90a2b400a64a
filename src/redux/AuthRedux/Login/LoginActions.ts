import {
  LoginRequestData,
  LoginResponseItem,
} from '../../../model/Auth/LoginData';
import { LOGIN_REQUEST, LOGIN_SUCCESS, LOGIN_FAILURE } from './LoginActionTypes';

export const loginRequest = (payload: LoginRequestData) => ({
  type: LOGIN_REQUEST,
  payload,
});

export const loginSuccess = (response: LoginResponseItem) => ({
  type: LOGIN_SUCCESS,
  payload: response,
});

export const loginFailure = (error: string) => ({
  type: LOGIN_FAILURE,
  payload: error,
});
