import { put, call, takeLatest } from 'redux-saga/effects';
import { logoutAction } from './LogoutAction';
import { LOGOUT_REQUEST } from './LogoutActionTypes';


export function* handleLogout() {
    try {
        yield put(logoutAction());
        // (Optional) Navigate to Login
        // yield put(NavigationActions.navigate({ name: 'Login' }));
    } catch (error) {
        console.error('Logout error:', error);
    }
}
export default function* logoutSaga() {
    yield takeLatest(LOGOUT_REQUEST, handleLogout);
}

