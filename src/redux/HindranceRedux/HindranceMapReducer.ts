import { SET_HINDRANCE_MAP_DATA, CLEAR_HINDRANCE_MAP_DATA, UPDATE_HINDRANCE_MAP_PROGRESS, GET_HINDRANCE_DETAILS_SUCCESS, GET_HINDRANCE_DETAILS_FAILURE, GET_HINDRANCE_DETAILS } from './HindranceMapActionTypes';
import { HindranceMapData, HindranceMapItem } from './HindranceMapActions';

interface HindranceMapState {
  mapData: HindranceMapData | null;
  hasMapData: boolean;
  hindranceDetails: any;
  hindranceMapItems: HindranceMapItem[];
  isLoading: boolean;
  error: string | null;
}

const initialState: HindranceMapState = {
  mapData: null,
  hasMapData: false,
  hindranceDetails: null,
  hindranceMapItems: [],
  isLoading: false,
  error: null,
};

const hindranceMapReducer = (state = initialState, action: any): HindranceMapState => {
  switch (action.type) {
    case SET_HINDRANCE_MAP_DATA:
      return {
        ...state,
        mapData: action.payload,
        hasMapData: action.payload.hasMapData,
      };
    
    case CLEAR_HINDRANCE_MAP_DATA:
      return {
        ...state,
        mapData: null,
        hasMapData: false,
        hindranceMapItems: [],
        error: null,
      };
    
    case UPDATE_HINDRANCE_MAP_PROGRESS:
      if (state.mapData) {
        return {
          ...state,
          mapData: {
            ...state.mapData,
            progressLat: action.payload.progressLat,
            progressLng: action.payload.progressLng,
          },
        };
      }
      return state;

    case GET_HINDRANCE_DETAILS:
      return {
        ...state,
        hindranceDetails: null,
      };

    case GET_HINDRANCE_DETAILS_SUCCESS:
      return {
        ...state,
        hindranceDetails: action.payload,
      };

    case GET_HINDRANCE_DETAILS_FAILURE:
      return {
        ...state,
        hindranceDetails: null,
      };

    default:
      return state;
  }
};

export default hindranceMapReducer; 