import { takeLatest, call, put } from 'redux-saga/effects';
import { HINDRANCE_UPDATE_REQUEST, GET_HINDRANCE_DETAILS } from './HindranceMapActionTypes';
import { hindranceUpdateSuccess, hindranceUpdateFailure, getHindranceDetailsFailure, getHindranceDetailsSuccess } from './HindranceMapActions';
import { postDataWithBodyForDownload } from '../../services/ApiRequests';
import { API } from '../../utils/Constants/ApiConstants';
import PrintLog from '../../utils/Logger/PrintLog';

/**
 * Saga to handle hindrance update requests
 * @param action - The action containing payload and callback
 */
function* handleHindranceUpdate(action: any): Generator<any, void, any> {
  try {
    PrintLog.debug('handleHindranceUpdate', 'Starting hindrance update request', action.payload);
    
    const response = yield call(() =>
      new Promise((resolve, reject) =>
        postDataWithBodyForDownload(
          API.insertHindranceUpdate,
          action.payload,
          res => resolve(res),
          err => reject(err)
        )
      )
    );
    yield put(hindranceUpdateSuccess(response));
    
    if (action.cb) {
      action.cb({ success: true, data: response });
    }
  } catch (error: any) {
    PrintLog.error('handleHindranceUpdate', 'Hindrance update failed', error);
    yield put(hindranceUpdateFailure(error));
    
    if (action.cb) {
      action.cb({ success: false, error });
    }
  }
}

/**
 * Saga to handle getting hindrance details
 * @param action - The action containing job code and type
 */
function* handleGetHindranceDetails(action: any): Generator<any, void, any> {
  try {
    const { jobCode, type } = action.payload;
    PrintLog.debug('handleGetHindranceDetails', 'Getting hindrance details', { jobCode, type });
    
    const params = {
      jobCode: jobCode,
      type: type
    };
    
    const response = yield call(() =>
      new Promise((resolve, reject) =>
        postDataWithBodyForDownload(
          API.getHindranceGapDetails,
          params,
          res => resolve(res),
          err => reject(err)
        )
      )
    );
    
    console.log('Hindrance details fetched successfully', response);
    
    if (response) {
      yield put(getHindranceDetailsSuccess(response?.Table));
    } else {
      throw new Error('No response received from API');
    }
  } catch (error: any) {
    PrintLog.error('handleGetHindranceDetails', 'Failed to get hindrance details', error);
    yield put(getHindranceDetailsFailure(error));
    
    if (action.cb) {
      action.cb({ success: false, error });
    }
  }
}

export function* hindranceMapSaga(): Generator<any, void, any> {
  yield takeLatest('HINDRANCE_UPDATE_REQUEST', handleHindranceUpdate);
  yield takeLatest(GET_HINDRANCE_DETAILS, handleGetHindranceDetails);
} 