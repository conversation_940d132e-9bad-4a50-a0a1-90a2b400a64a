import { SET_HINDRANCE_MAP_DATA, CLEAR_HINDRANCE_MAP_DATA, UPDATE_HINDRANCE_MAP_PROGRESS, GET_HINDRANCE_DETAILS, GET_HINDRANCE_DETAILS_SUCCESS, GET_HINDRANCE_DETAILS_FAILURE } from './HindranceMapActionTypes';

export interface HindranceMapData {
    jobCode: string;
    UID: string;
    GapLength: number;
    Latitude_Map: number;
    Longitude_Map: number;
    GisPhoto: any;
    date: string;
    Open_Active: string;
    Remarks: string;
    type: string;
    Classification_Type_Detail_Code: string;
    Latitude_End: number;
    Longitude_End: string;
    Start_End_Node: string;
    Gap_Reason: string;
}

export interface HindranceMapItem {
  id: string;
  name: string;
  details: string;
  progressQty: string;
  manDays: string;
  date: string;
  latitude: number;
  longitude: number;
  Open_Active: string;
  New_Gap: string;
  Progress_Update: string;
  GAP_Length: number;
  GapLength_Updated: number;
  Remarks: string;
  LGD_Latitude_End: number | null;
  LGD_Longitude_End: number | null;
  LGD_Start_End_Node: string | null;
  LGD_Gap_Reason: string | null;
  Classification_Type_Detail_Code: string | null;
  Classification_Type_Detail_Description: string | null;
  // Additional fields from API response
  Jobcode: string;
  Serialno: number | null;
  Photo: any[];
  MinDate: string | null;
}

interface hindranceGapDetailsProps {
  jobCode: string;
  type: string;
}

// Existing action creators
export const getHindranceDetails = (data: hindranceGapDetailsProps) => ({
  type: GET_HINDRANCE_DETAILS,
  payload: data
});

export const getHindranceDetailsSuccess = (response: hindranceGapDetailsProps) => ({
  type: GET_HINDRANCE_DETAILS_SUCCESS,
  payload: response
});

export const getHindranceDetailsFailure = (error: any) => ({
  type: GET_HINDRANCE_DETAILS_FAILURE,
  payload: error
});

export const setHindranceMapData = (data: HindranceMapData) => ({
  type: SET_HINDRANCE_MAP_DATA,
  payload: data,
});

export const clearHindranceMapData = () => ({
  type: CLEAR_HINDRANCE_MAP_DATA,
});

export const updateHindranceMapProgress = (progressLat: number, progressLng: number) => ({
  type: UPDATE_HINDRANCE_MAP_PROGRESS,
  payload: { progressLat, progressLng },
});

export const hindranceUpdateRequest = (
  payload: any,
  cb?: (result: { success: boolean; data?: any; error?: any }) => void
) => ({
  type: 'HINDRANCE_UPDATE_REQUEST',
  payload,
  cb,
});

export const hindranceUpdateSuccess = (response: any) => ({
  type: 'HINDRANCE_UPDATE_SUCCESS',
  payload: response,
});

export const hindranceUpdateFailure = (error: any) => ({
  type: 'HINDRANCE_UPDATE_FAILURE',
  payload: error,
}); 