import { ApproveResponseData } from '../../model/DailyProgressApprover/ApproveData';
import { APPROVE_REQUEST, APPROVE_SUCCESS, APPROVE_FAILURE } from './ApproveActionTypes';

interface ApproveState {
  response: ApproveResponseData | null;
  loading: boolean;
  error: string | null;
}

const initialState: ApproveState = {
  response: null,
  loading: false,
  error: null,
};

export const ApproveReducer = (state = initialState, action: any): ApproveState => {
  switch (action.type) {
    case APPROVE_REQUEST:
      return { ...state, loading: true, error: null };
    case APPROVE_SUCCESS:
      return { ...state, loading: false, response: action.payload };
    case APPROVE_FAILURE:
      return { ...state, loading: false, error: action.payload };
    default:
      return state;
  }
}; 