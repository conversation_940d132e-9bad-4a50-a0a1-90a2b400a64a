import {
  UPLOAD_IMAGE_REQUEST,
  UPLOAD_IMAGE_SUCCESS,
  UPLOAD_IMAGE_FAILURE,
} from './UploadImageActionTypes';

interface UploadImageState {
  loading: boolean;
  uploadedIds: string[];
  error: string | null;
}

const initialState: UploadImageState = {
  loading: false,
  uploadedIds: [],
  error: null,
};

const uploadImageReducer = (state = initialState, action: any): UploadImageState => {
  switch (action.type) {
    case UPLOAD_IMAGE_REQUEST:
      return { ...state, loading: true, error: null };
    case UPLOAD_IMAGE_SUCCESS:
      return { 
        ...state, 
        loading: false, 
        uploadedIds: [...state.uploadedIds, action.payload],
        error: null 
      };
    case UPLOAD_IMAGE_FAILURE:
      return { ...state, loading: false, error: action.payload };
    default:
      return state;
  }
};

export default uploadImageReducer; 