import { ApproveRequestData, ApproveResponseData } from '../../model/DailyProgressApprover/ApproveData';
import { APPROVE_REQUEST, APPROVE_SUCCESS, APPROVE_FAILURE } from './ApproveActionTypes';

export const approveRequest = (payload: ApproveRequestData) => ({
  type: APPROVE_REQUEST,
  payload,
});

export const approveSuccess = (response: ApproveResponseData) => ({
  type: APPROVE_SUCCESS,
  payload: response,
});

export const approveFailure = (error: string) => ({
  type: APPROVE_FAILURE,
  payload: error,
}); 