import {
  UPLOAD_IMAGE_REQUEST,
  UPLOAD_IMAGE_SUCCESS,
  UPLOAD_IMAGE_FAILURE,
} from './UploadImageActionTypes';

export const uploadImageRequest = (payload: any) => ({
  type: UPLOAD_IMAGE_REQUEST,
  payload,
});

export const uploadImageSuccess = (response: any) => ({
  type: UPLOAD_IMAGE_SUCCESS,
  response,
});

export const uploadImageFailure = (error: any) => ({
  type: UPLOAD_IMAGE_FAILURE,
  error,
}); 