import { call, put, takeLatest } from 'redux-saga/effects';
import { UPLOAD_IMAGE_REQUEST } from './UploadImageActionTypes';
import { uploadImageSuccess, uploadImageFailure } from './UploadImageActions';
import { postDataWithBodyForImageUpload } from '../../services/ApiRequests';

function* uploadImageSaga(action: any) {
  try {
    const urlSuffix = 'UploadImaage';
    const response = yield call(() =>
      new Promise((resolve, reject) =>
        postDataWithBodyForImageUpload(
          urlSuffix,
          action.payload,
          res => resolve(res),
          err => reject(err)
        )
      )
    );
    yield put(uploadImageSuccess(response));
  } catch (error) {
    yield put(uploadImageFailure(error));
  }
}

export default function* watchUploadImage() {
  yield takeLatest(UPLOAD_IMAGE_REQUEST, uploadImageSaga);
} 