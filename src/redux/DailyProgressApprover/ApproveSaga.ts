import { call, put, takeLatest } from 'redux-saga/effects';
import { approveSuccess, approveFailure } from './ApproveActions';
import { APPROVE_REQUEST } from './ApproveActionTypes';
import { postDataWithBodyForDownload } from '../../services/ApiRequests';
import { API } from '../../utils/Constants/ApiConstants';
// import Config from 'react-native-config';

function* handleApprove(action: any) {
  try {
    const params = action.payload;
    console.log('handleApprove - Request Payload:', params);

    // Compose the full endpoint
    const url = `${API.ProgressUpdateInsert}`;

    // Wrap the API call in a Promise for proper error handling
    const response = yield call(
      () =>
        new Promise((resolve, reject) =>
          postDataWithBodyForDownload(
            url,
            params,
            res => {
              console.log('handleApprove - API Success Response:', res);
              resolve(res);
            },
            err => {
              console.error('handleApprove - API Error:', err);
              reject(new Error(typeof err === 'string' ? err : JSON.stringify(err)));
            }
          )
        )
    );

    // if (response && response.StatusCode === 200) {
    //   console.log('handleApprove - Approve Success:', response);
    //   yield put(approveSuccess(response));
    // } else {
    //   console.error('handleApprove - Approve Failed:', response);
    //   yield put(approveFailure(response?.Message || 'Approve failed'));
    // }
    if (
      response &&
      Array.isArray(response.Table) &&
      response.Table.length > 0 &&
      response.Table[0].Msg === 'OK'
    ) {
      console.log('handleApprove - Approve Success:', response);
      yield put(approveSuccess(response));
    } else {
      console.log('handleApprove - Approve FAilure:', response);
      yield put(approveFailure(response?.Table?.[0]?.Msg || 'Approve failed'));
    }
  } catch (error: any) {
    console.error('handleApprove - Exception Caught:', error);
    yield put(approveFailure(error?.message || 'Approve failed'));
  }
}

export default function* approveSaga() {
  yield takeLatest(APPROVE_REQUEST, handleApprove);
}
