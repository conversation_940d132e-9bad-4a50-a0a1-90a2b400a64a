import { RolesResponse } from '../../model/Roles/RolesData';
import { ROLES_REQUEST, ROLES_SUCCESS, ROLES_FAILURE } from './RolesActionTypes';

interface RolesState {
    roles: RolesResponse | null;
    loading: boolean;
    rolesError: string | null;
}

const initialState: RolesState = {
    roles: null,
    loading: true,
    rolesError: null,
};

export const RolesReducer = (state = initialState, action: any): RolesState => {
    switch (action.type) {
        case ROLES_REQUEST:
            return { ...state, loading: true, rolesError: null };

        case ROLES_SUCCESS:
            return { ...state, loading: false, roles: action.payload };

        case ROLES_FAILURE:
            return { ...state, loading: false, rolesError: action.payload };

        default:
            return state;
    }
}; 