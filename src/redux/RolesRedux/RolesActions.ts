import { RolesRequest, RolesResponse } from '../../model/Roles/RolesData';
import { ROLES_REQUEST, ROLES_SUCCESS, ROLES_FAILURE } from './RolesActionTypes';

export const rolesRequest = (payload: RolesRequest) => ({
    type: ROLES_REQUEST,
    payload,
});

export const rolesSuccess = (response: RolesResponse) => ({
    type: ROLES_SUCCESS,
    payload: response,
});

export const rolesFailure = (error: string) => ({
    type: ROLES_FAILURE,
    payload: error,
}); 