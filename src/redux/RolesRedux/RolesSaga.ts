import { call, put, takeLatest } from 'redux-saga/effects';
import { rolesFailure, rolesSuccess } from './RolesActions';
import { ROLES_REQUEST } from './RolesActionTypes';
import { postDataWithBodyForDownload } from '../../services/ApiRequests';
import PrintLog from '../../utils/Logger/PrintLog';
import { RolesRequest, RolesResponse } from '../../model/Roles/RolesData';
import { API } from '../../utils/Constants/ApiConstants';
import { t } from 'i18next';
import { showErrorToast } from '../../components/CustomToast';
import { removeUserRolesInfo } from '../../utils/DataStorage/Storage';

interface RolesAction {
    type: typeof ROLES_REQUEST;
    payload: RolesRequest;
}

export function* handleRoles(action: RolesAction) {
    try {
        const params = action.payload;
        const requestBody = {
            User_ID: params.User_ID,
            RoleType: params.RoleType
        };
        const response: RolesResponse = yield call(
            () =>
                new Promise((resolve, reject) =>
                    postDataWithBodyForDownload<RolesRequest, RolesResponse>(
                        API.roles,
                        requestBody,
                        res => resolve(res),
                        err => reject(err),
                    ),
                ),
        );
        if (response?.RolesList.length > 0) {
            yield put(rolesSuccess(response));
            params.cb({success: true, data: response && response?.RolesList || []});
        } else {
            yield put(rolesFailure('Failed to fetch jobs'));
            showErrorToast(t('commonStrings.noRolesFound'));
            params.cb({success: false});
        }
    } catch (error: any) {
        PrintLog.error('handleRoles catch', { error });
        yield put(rolesFailure('Failed to fetch jobs'));
        showErrorToast(t('commonStrings.noRolesFound'));
        action.payload.cb({success: false});
    }
}

export default function* rolesSaga() {
    yield takeLatest(ROLES_REQUEST, handleRoles);
} 