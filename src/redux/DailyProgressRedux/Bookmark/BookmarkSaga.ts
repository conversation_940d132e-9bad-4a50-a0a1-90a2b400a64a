import { t } from 'i18next';
import { showErrorToast } from '../../../components/CustomToast';
import { BookmarksRequestData, BookmarksResponseData } from '../../../model/DailyProgress/BookmarksData';
import { postDataWithBodyForDownload } from '../../../services/ApiRequests';
import { API } from '../../../utils/Constants/ApiConstants';
import { BOOKMARK_REQUEST } from './BookmarkActionTypes';
import { bookmarkFailure, bookmarkRequest, bookmarkSuccess } from './BookmarkActions';
import { call, put, takeLatest } from 'redux-saga/effects';
import PrintLog from '../../../utils/Logger/PrintLog';

interface BookmarkAction {
    type: typeof BOOKMARK_REQUEST;
    payload: BookmarksRequestData;
}
export function* handleBookmarksRequest(action: BookmarkAction) {
    try {
        const params = action.payload;
    console.log('handleBookmarksRequest -- params: ', params);
        const response: BookmarksResponseData = yield call(
            () =>
                new Promise((resolve, reject) =>
                    postDataWithBodyForDownload<BookmarksRequestData, BookmarksResponseData>(
                        API.masterList,
                        params,
                        res => resolve(res),
                        err => reject(err),
                    ),
                ),
        );
        if (!!response) {
            yield put(bookmarkSuccess(response));
        } else {
            yield put(bookmarkFailure('commonStrings.noResponseReceived'));
            showErrorToast(t('commonStrings.noResponseReceived'));
        }
    } catch (error: any) {
        PrintLog.error('handleBookmarksRequest catch', { error });
        yield put(bookmarkFailure(error));
        showErrorToast(error);
    }
}

export default function* bookmarksSaga() {
    yield takeLatest(BOOKMARK_REQUEST, handleBookmarksRequest);
} 