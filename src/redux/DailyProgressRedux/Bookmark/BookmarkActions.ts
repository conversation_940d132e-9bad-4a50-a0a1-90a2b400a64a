import { BookmarksRequestData, BookmarksResponseData } from "../../../model/DailyProgress/BookmarksData";
import { BOOKMARK_FAILURE, BOOKMARK_REQUEST, BOOKMARK_SUCCESS } from "./BookmarkActionTypes";

export const bookmarkRequest = (payload: BookmarksRequestData) => ({
    type: BOOKMARK_REQUEST,
    payload,
});

export const bookmarkSuccess = (response: BookmarksResponseData) => ({
    type: BOOKMARK_SUCCESS,
    payload: response,
});

export const bookmarkFailure = (error: string) => ({
    type: BOOKMARK_FAILURE,
    payload: error,
}); 

export const clearBookmarkData = () => ({
    type: 'CLEAR_BOOKMARK_DATA',
});