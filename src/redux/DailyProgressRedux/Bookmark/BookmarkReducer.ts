import { BookmarksResponseData } from "../../../model/DailyProgress/BookmarksData";
import { BOOKMARK_FAILURE, BOOKMARK_REQUEST, BOOKMARK_SUCCESS } from "./BookmarkActionTypes";

interface BookmarksState {
    bookmarksData: BookmarksResponseData | null;
    bookmarksLoading: boolean;
    bookmarksError: string | null;
}

const initialState: BookmarksState = {
    bookmarksData: null,
    bookmarksLoading: false,
    bookmarksError: null,
};

export const BookmarksReducer = (state = initialState, action: any): BookmarksState => {
    switch (action.type) {
        case BOOKMARK_REQUEST:
            return { ...state, bookmarksLoading: true, bookmarksData: null, bookmarksError: null };

        case BOOKMARK_SUCCESS:
            return { ...state, bookmarksLoading: false, bookmarksData: action.payload };

        case BOOKMARK_FAILURE:
            return { ...state, bookmarksLoading: false, bookmarksError: action.payload };

        case 'CLEAR_BOOKMARK_DATA':
            return { ...state, bookmarksData: null, bookmarksLoading: false };
        default:
            return state;
    }
}; 