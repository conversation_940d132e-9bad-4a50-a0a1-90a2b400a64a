import {
  saveBookmarkToLocalDB,
  getPendingBookmarks,
  updateBookmarkStatus,
  UnsyncedBookmarkData
} from '../../../database/ProgressUpdate/BookmarkUpdateDBData';
import { database } from '../../../database/index';
import { Q } from '@nozbe/watermelondb';
import BookMarkList from '../../../database/model/BookmarkList';
import UnsyncedBookmarkList from '../../../database/model/UnsyncedBookmarkList';

// Mock dependencies
jest.mock('../../../database/index', () => ({
  database: {
    write: jest.fn(),
    get: jest.fn(),
  },
}));

jest.mock('@nozbe/watermelondb', () => ({
  Q: {
    where: jest.fn(),
  },
}));

jest.mock('../../../database/model/BookmarkList');
jest.mock('../../../database/model/UnsyncedBookmarkList');

describe('BookmarkUpdateDBData', () => {
  const mockDatabase = database as jest.Mocked<typeof database>;
  const mockQ = Q as jest.Mocked<typeof Q>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup console spies
    jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
    
    // Setup default mock implementations
    mockQ.where.mockImplementation((field: string, value: any) => ({ field, value, type: 'where' }));
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('UnsyncedBookmarkData Interface', () => {
    it('should create a valid UnsyncedBookmarkData object', () => {
      const bookmarkData: UnsyncedBookmarkData = {
        jobCode: 'JOB001',
        wbsCode: 'WBS001',
        userId: 12345,
        hierarchyLevel: 'Level 1',
        isActive: 'Y',
        jobDescription: 'Test Job Description',
        ilCode: 'IL001',
        ilDescription: 'IL Description',
        wpCode: 'WP001',
        wpDescription: 'WP Description',
        childWorkCode: 'CWC001',
        childWorkDescription: 'Child Work Description',
        deliverableCode: 1001,
        deliverableCodeDesc: 'Deliverable Description',
        etCode: 'ET001',
        type: 'Bookmark',
        wpCodeForSync: 'WP_SYNC_001'
      };

      expect(bookmarkData.jobCode).toBe('JOB001');
      expect(bookmarkData.wbsCode).toBe('WBS001');
      expect(bookmarkData.userId).toBe(12345);
      expect(bookmarkData.hierarchyLevel).toBe('Level 1');
      expect(bookmarkData.isActive).toBe('Y');
      expect(bookmarkData.jobDescription).toBe('Test Job Description');
      expect(bookmarkData.ilCode).toBe('IL001');
      expect(bookmarkData.ilDescription).toBe('IL Description');
      expect(bookmarkData.wpCode).toBe('WP001');
      expect(bookmarkData.wpDescription).toBe('WP Description');
      expect(bookmarkData.childWorkCode).toBe('CWC001');
      expect(bookmarkData.childWorkDescription).toBe('Child Work Description');
      expect(bookmarkData.deliverableCode).toBe(1001);
      expect(bookmarkData.deliverableCodeDesc).toBe('Deliverable Description');
      expect(bookmarkData.etCode).toBe('ET001');
      expect(bookmarkData.type).toBe('Bookmark');
      expect(bookmarkData.wpCodeForSync).toBe('WP_SYNC_001');
    });

    it('should validate all required properties exist', () => {
      const bookmarkData: UnsyncedBookmarkData = {
        jobCode: 'JOB002',
        wbsCode: 'WBS002',
        userId: 54321,
        hierarchyLevel: 'Level 2',
        isActive: 'N',
        jobDescription: 'Another Job',
        ilCode: 'IL002',
        ilDescription: 'Another IL Description',
        wpCode: 'WP002',
        wpDescription: 'Another WP Description',
        childWorkCode: 'CWC002',
        childWorkDescription: 'Another Child Work',
        deliverableCode: 2002,
        deliverableCodeDesc: 'Another Deliverable',
        etCode: 'ET002',
        type: 'Task',
        wpCodeForSync: 'WP_SYNC_002'
      };

      expect(bookmarkData).toHaveProperty('jobCode');
      expect(bookmarkData).toHaveProperty('wbsCode');
      expect(bookmarkData).toHaveProperty('userId');
      expect(bookmarkData).toHaveProperty('hierarchyLevel');
      expect(bookmarkData).toHaveProperty('isActive');
      expect(bookmarkData).toHaveProperty('jobDescription');
      expect(bookmarkData).toHaveProperty('ilCode');
      expect(bookmarkData).toHaveProperty('ilDescription');
      expect(bookmarkData).toHaveProperty('wpCode');
      expect(bookmarkData).toHaveProperty('wpDescription');
      expect(bookmarkData).toHaveProperty('childWorkCode');
      expect(bookmarkData).toHaveProperty('childWorkDescription');
      expect(bookmarkData).toHaveProperty('deliverableCode');
      expect(bookmarkData).toHaveProperty('deliverableCodeDesc');
      expect(bookmarkData).toHaveProperty('etCode');
      expect(bookmarkData).toHaveProperty('type');
      expect(bookmarkData).toHaveProperty('wpCodeForSync');
    });

    it('should handle empty string values', () => {
      const bookmarkData: UnsyncedBookmarkData = {
        jobCode: '',
        wbsCode: '',
        userId: 0,
        hierarchyLevel: '',
        isActive: '',
        jobDescription: '',
        ilCode: '',
        ilDescription: '',
        wpCode: '',
        wpDescription: '',
        childWorkCode: '',
        childWorkDescription: '',
        deliverableCode: 0,
        deliverableCodeDesc: '',
        etCode: '',
        type: '',
        wpCodeForSync: ''
      };

      expect(typeof bookmarkData.jobCode).toBe('string');
      expect(typeof bookmarkData.wbsCode).toBe('string');
      expect(typeof bookmarkData.userId).toBe('number');
      expect(typeof bookmarkData.hierarchyLevel).toBe('string');
      expect(typeof bookmarkData.isActive).toBe('string');
      expect(typeof bookmarkData.deliverableCode).toBe('number');
    });
  });

  describe('saveBookmarkToLocalDB', () => {
    const mockBookmarkData: UnsyncedBookmarkData = {
      jobCode: 'JOB001',
      wbsCode: 'WBS001',
      userId: 12345,
      hierarchyLevel: 'Level 1',
      isActive: 'Y',
      jobDescription: 'Test Job Description',
      ilCode: 'IL001',
      ilDescription: 'IL Description',
      wpCode: 'WP001',
      wpDescription: 'WP Description',
      childWorkCode: 'CWC001',
      childWorkDescription: 'Child Work Description',
      deliverableCode: 1001,
      deliverableCodeDesc: 'Deliverable Description',
      etCode: 'ET001',
      type: 'Bookmark',
      wpCodeForSync: 'WP_SYNC_001'
    };

    it('should save bookmark to local database successfully', async () => {
      const mockCreate = jest.fn((callback) => {
        const mockRecord = {} as UnsyncedBookmarkList;
        callback(mockRecord);
        return mockRecord;
      });

      const mockCollection = {
        create: mockCreate
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);
      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      const result = await saveBookmarkToLocalDB(mockBookmarkData);

      expect(mockDatabase.write).toHaveBeenCalledTimes(1);
      expect(mockDatabase.get).toHaveBeenCalledWith('UnsyncedBookmarkList');
      expect(mockCreate).toHaveBeenCalledTimes(1);
      expect(result).toBe(true);
      expect(console.log).toHaveBeenCalledWith('Bookmark saved to local database for offline sync');
    });

    it('should assign all bookmark data properties to record', async () => {
      let capturedRecord: any = null;
      
      const mockCreate = jest.fn((callback) => {
        const mockRecord = {} as UnsyncedBookmarkList;
        callback(mockRecord);
        capturedRecord = mockRecord;
        return mockRecord;
      });

      const mockCollection = {
        create: mockCreate
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);
      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await saveBookmarkToLocalDB(mockBookmarkData);

      expect(capturedRecord.jobCode).toBe('JOB001');
      expect(capturedRecord.wbsCode).toBe('WBS001');
      expect(capturedRecord.userId).toBe(12345);
      expect(capturedRecord.hierarchyLevel).toBe(''); // Note: hardcoded to empty string in implementation
      expect(capturedRecord.isActive).toBe('Y');
      expect(capturedRecord.jobDescription).toBe('Test Job Description');
      expect(capturedRecord.ilCode).toBe('IL001');
      expect(capturedRecord.ilDescription).toBe('IL Description');
      expect(capturedRecord.wpCode).toBe('WP001');
      expect(capturedRecord.wpDescription).toBe('WP Description');
      expect(capturedRecord.childWorkCode).toBe('CWC001');
      expect(capturedRecord.childWorkDescription).toBe('Child Work Description');
      expect(capturedRecord.deliverableCode).toBe(1001);
      expect(capturedRecord.deliverableCodeDesc).toBe('Deliverable Description');
      expect(capturedRecord.etCode).toBe('ET001');
      expect(capturedRecord.type).toBe('Bookmark');
      expect(capturedRecord.wpCodeForSync).toBe('WP_SYNC_001');
    });

    it('should handle database errors gracefully', async () => {
      mockDatabase.write.mockRejectedValue(new Error('Database write error'));

      const result = await saveBookmarkToLocalDB(mockBookmarkData);

      expect(result).toBe(false);
      expect(console.error).toHaveBeenCalledWith('Error saving bookmark to local DB:', expect.any(Error));
    });

    it('should handle collection creation errors', async () => {
      const mockCreate = jest.fn(() => {
        throw new Error('Collection create error');
      });

      const mockCollection = {
        create: mockCreate
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);
      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      const result = await saveBookmarkToLocalDB(mockBookmarkData);

      expect(result).toBe(false);
      expect(console.error).toHaveBeenCalledWith('Error saving bookmark to local DB:', expect.any(Error));
    });

    it('should save bookmark with minimal data', async () => {
      const minimalBookmarkData: UnsyncedBookmarkData = {
        jobCode: 'MIN001',
        wbsCode: 'WBS_MIN',
        userId: 1,
        hierarchyLevel: '',
        isActive: 'N',
        jobDescription: '',
        ilCode: '',
        ilDescription: '',
        wpCode: '',
        wpDescription: '',
        childWorkCode: '',
        childWorkDescription: '',
        deliverableCode: 0,
        deliverableCodeDesc: '',
        etCode: '',
        type: '',
        wpCodeForSync: ''
      };

      const mockCreate = jest.fn((callback) => {
        const mockRecord = {} as UnsyncedBookmarkList;
        callback(mockRecord);
        return mockRecord;
      });

      const mockCollection = {
        create: mockCreate
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);
      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      const result = await saveBookmarkToLocalDB(minimalBookmarkData);

      expect(result).toBe(true);
    });
  });

  describe('getPendingBookmarks', () => {
    it('should fetch pending bookmarks successfully', async () => {
      const mockBookmarks = [
        {
          id: '1',
          _raw: {
            PRCGB_Job_Code: 'JOB001',
            PRCGB_WBS_Code: 'WBS001',
            PRCGB_IsActive: 'Y'
          }
        },
        {
          id: '2',
          _raw: {
            PRCGB_Job_Code: 'JOB002',
            PRCGB_WBS_Code: 'WBS002',
            PRCGB_IsActive: 'Y'
          }
        }
      ];

      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue(mockBookmarks)
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await getPendingBookmarks();

      expect(mockDatabase.get).toHaveBeenCalledWith('BookMarkList');
      expect(mockQuery.query).toHaveBeenCalled();
      expect(mockQuery.fetch).toHaveBeenCalled();
      expect(result).toEqual(mockBookmarks);
      expect(result).toHaveLength(2);
    });

    it('should return empty array when no pending bookmarks found', async () => {
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([])
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await getPendingBookmarks();

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('should handle database errors gracefully', async () => {
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockRejectedValue(new Error('Database fetch error'))
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await getPendingBookmarks();

      expect(result).toEqual([]);
      expect(console.error).toHaveBeenCalledWith('Error fetching pending bookmarks:', expect.any(Error));
    });

    it('should use correct query parameters', async () => {
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([])
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      await getPendingBookmarks();

      expect(mockQ.where).toHaveBeenCalledWith('PRCGB_IsActive', 'Y');
      expect(mockQuery.query).toHaveBeenCalledWith({ field: 'PRCGB_IsActive', value: 'Y', type: 'where' });
    });

    it('should handle large datasets', async () => {
      const largeBookmarksList = Array.from({ length: 1000 }, (_, index) => ({
        id: `bookmark_${index + 1}`,
        _raw: {
          PRCGB_Job_Code: `JOB${String(index + 1).padStart(3, '0')}`,
          PRCGB_WBS_Code: `WBS${String(index + 1).padStart(3, '0')}`,
          PRCGB_IsActive: 'Y'
        }
      }));

      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue(largeBookmarksList)
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await getPendingBookmarks();

      expect(result).toHaveLength(1000);
      expect(result[0].id).toBe('bookmark_1');
      expect(result[999].id).toBe('bookmark_1000');
    });
  });

  describe('updateBookmarkStatus', () => {
    const mockBookmarkId = 'bookmark_123';

    it('should update bookmark status to active successfully', async () => {
      const mockBookmark = {
        id: mockBookmarkId,
        update: jest.fn(),
        isActive: 'N'
      } as unknown as BookMarkList;

      const mockCollection = {
        find: jest.fn().mockResolvedValue(mockBookmark)
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);
      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await updateBookmarkStatus(mockBookmarkId, true);

      expect(mockDatabase.write).toHaveBeenCalledTimes(1);
      expect(mockDatabase.get).toHaveBeenCalledWith('BookMarkList');
      expect(mockCollection.find).toHaveBeenCalledWith(mockBookmarkId);
      expect(mockBookmark.update).toHaveBeenCalledTimes(1);
      expect(console.log).toHaveBeenCalledWith(`Bookmark ${mockBookmarkId} status updated to Y`);
    });

    it('should update bookmark status to inactive successfully', async () => {
      const mockBookmark = {
        id: mockBookmarkId,
        update: jest.fn(),
        isActive: 'Y'
      } as unknown as BookMarkList;

      const mockCollection = {
        find: jest.fn().mockResolvedValue(mockBookmark)
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);
      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await updateBookmarkStatus(mockBookmarkId, false);

      expect(mockBookmark.update).toHaveBeenCalledTimes(1);
      expect(console.log).toHaveBeenCalledWith(`Bookmark ${mockBookmarkId} status updated to N`);
    });

    it('should properly set isActive property in update callback', async () => {
      let capturedUpdateCallback: ((record: any) => void) | null = null;
      
      const mockBookmark = {
        id: mockBookmarkId,
        update: jest.fn((callback) => {
          capturedUpdateCallback = callback;
        }),
        isActive: 'N'
      } as unknown as BookMarkList;

      const mockCollection = {
        find: jest.fn().mockResolvedValue(mockBookmark)
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);
      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await updateBookmarkStatus(mockBookmarkId, true);

      expect(capturedUpdateCallback).toBeDefined();
      
      // Test the update callback
      const mockRecord = { isActive: 'N' };
      capturedUpdateCallback!(mockRecord);
      expect(mockRecord.isActive).toBe('Y');
    });

    it('should handle case when bookmark is not found', async () => {
      const mockCollection = {
        find: jest.fn().mockResolvedValue(null)
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);
      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await updateBookmarkStatus(mockBookmarkId, true);

      expect(mockCollection.find).toHaveBeenCalledWith(mockBookmarkId);
      // Should not call update when bookmark is not found
      expect(console.log).not.toHaveBeenCalledWith(expect.stringContaining('status updated'));
    });

    it('should handle database write errors gracefully', async () => {
      mockDatabase.write.mockRejectedValue(new Error('Database write error'));

      await updateBookmarkStatus(mockBookmarkId, true);

      expect(console.error).toHaveBeenCalledWith('Error updating bookmark status:', expect.any(Error));
    });

    it('should handle find operation errors', async () => {
      const mockCollection = {
        find: jest.fn().mockRejectedValue(new Error('Find operation error'))
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);
      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await updateBookmarkStatus(mockBookmarkId, true);

      expect(console.error).toHaveBeenCalledWith('Error updating bookmark status:', expect.any(Error));
    });

    it('should handle update operation errors', async () => {
      const mockBookmark = {
        id: mockBookmarkId,
        update: jest.fn().mockRejectedValue(new Error('Update operation error')),
        isActive: 'N'
      } as unknown as BookMarkList;

      const mockCollection = {
        find: jest.fn().mockResolvedValue(mockBookmark)
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);
      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await updateBookmarkStatus(mockBookmarkId, true);

      expect(console.error).toHaveBeenCalledWith('Error updating bookmark status:', expect.any(Error));
    });

    it('should handle empty bookmark ID', async () => {
      const mockCollection = {
        find: jest.fn().mockRejectedValue(new Error('Invalid ID'))
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);
      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await updateBookmarkStatus('', true);

      expect(console.error).toHaveBeenCalledWith('Error updating bookmark status:', expect.any(Error));
    });

    it('should handle concurrent update operations', async () => {
      const mockBookmark1 = {
        id: 'bookmark_1',
        update: jest.fn(),
        isActive: 'N'
      } as unknown as BookMarkList;

      const mockBookmark2 = {
        id: 'bookmark_2',
        update: jest.fn(),
        isActive: 'Y'
      } as unknown as BookMarkList;

      const mockCollection = {
        find: jest.fn()
          .mockResolvedValueOnce(mockBookmark1)
          .mockResolvedValueOnce(mockBookmark2)
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);
      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await Promise.all([
        updateBookmarkStatus('bookmark_1', true),
        updateBookmarkStatus('bookmark_2', false)
      ]);

      expect(mockBookmark1.update).toHaveBeenCalledTimes(1);
      expect(mockBookmark2.update).toHaveBeenCalledTimes(1);
      expect(console.log).toHaveBeenCalledWith('Bookmark bookmark_1 status updated to Y');
      expect(console.log).toHaveBeenCalledWith('Bookmark bookmark_2 status updated to N');
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete bookmark lifecycle', async () => {
      const bookmarkData: UnsyncedBookmarkData = {
        jobCode: 'JOB_LIFECYCLE',
        wbsCode: 'WBS_LIFECYCLE',
        userId: 99999,
        hierarchyLevel: 'Test Level',
        isActive: 'Y',
        jobDescription: 'Lifecycle Test Job',
        ilCode: 'IL_LIFECYCLE',
        ilDescription: 'Lifecycle IL',
        wpCode: 'WP_LIFECYCLE',
        wpDescription: 'Lifecycle WP',
        childWorkCode: 'CWC_LIFECYCLE',
        childWorkDescription: 'Lifecycle Child Work',
        deliverableCode: 9999,
        deliverableCodeDesc: 'Lifecycle Deliverable',
        etCode: 'ET_LIFECYCLE',
        type: 'Lifecycle',
        wpCodeForSync: 'WP_SYNC_LIFECYCLE'
      };

      // Mock save operation
      const mockCreateForSave = jest.fn((callback) => {
        const mockRecord = {} as UnsyncedBookmarkList;
        callback(mockRecord);
        return mockRecord;
      });

      const mockSaveCollection = {
        create: mockCreateForSave
      };

      // Mock get pending operation
      const mockPendingBookmarks = [
        {
          id: 'lifecycle_bookmark_1',
          _raw: {
            PRCGB_Job_Code: 'JOB_LIFECYCLE',
            PRCGB_WBS_Code: 'WBS_LIFECYCLE',
            PRCGB_IsActive: 'Y'
          }
        }
      ];

      const mockQueryForPending = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue(mockPendingBookmarks)
      };

      // Mock update operation
      const mockBookmarkForUpdate = {
        id: 'lifecycle_bookmark_1',
        update: jest.fn(),
        isActive: 'Y'
      } as unknown as BookMarkList;

      const mockUpdateCollection = {
        find: jest.fn().mockResolvedValue(mockBookmarkForUpdate)
      };

      mockDatabase.get
        .mockReturnValueOnce(mockSaveCollection as any) // saveBookmarkToLocalDB
        .mockReturnValueOnce(mockQueryForPending as any) // getPendingBookmarks
        .mockReturnValueOnce(mockUpdateCollection as any); // updateBookmarkStatus

      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      // Execute lifecycle operations
      const saveResult = await saveBookmarkToLocalDB(bookmarkData);
      const pendingBookmarks = await getPendingBookmarks();
      await updateBookmarkStatus('lifecycle_bookmark_1', false);

      expect(saveResult).toBe(true);
      expect(pendingBookmarks).toHaveLength(1);
      expect(mockBookmarkForUpdate.update).toHaveBeenCalled();
    });

    it('should handle database connection failures', async () => {
      mockDatabase.write.mockRejectedValue(new Error('Database connection failed'));
      mockDatabase.get.mockImplementation(() => {
        throw new Error('Database connection failed');
      });

      const bookmarkData: UnsyncedBookmarkData = {
        jobCode: 'FAIL_TEST',
        wbsCode: 'WBS_FAIL',
        userId: 1,
        hierarchyLevel: '',
        isActive: 'Y',
        jobDescription: '',
        ilCode: '',
        ilDescription: '',
        wpCode: '',
        wpDescription: '',
        childWorkCode: '',
        childWorkDescription: '',
        deliverableCode: 0,
        deliverableCodeDesc: '',
        etCode: '',
        type: '',
        wpCodeForSync: ''
      };

      const saveResult = await saveBookmarkToLocalDB(bookmarkData);
      const pendingResult = await getPendingBookmarks();

      expect(saveResult).toBe(false);
      expect(pendingResult).toEqual([]);
      expect(console.error).toHaveBeenCalledTimes(2);
    });
  });
}); 