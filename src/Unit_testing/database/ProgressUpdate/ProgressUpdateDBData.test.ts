import {
  InsertProgressUpdateDetails,
  logInputDetails,
  logParentItems
} from '../../../database/ProgressUpdate/ProgressUpdateDBData';
import { getUserInfo } from '../../../utils/DataStorage/Storage';
import { database } from '../../../database/index';

// Mock dependencies
jest.mock('../../../utils/DataStorage/Storage', () => ({
  getUserInfo: jest.fn(),
}));

jest.mock('../../../database/index', () => ({
  database: {
    get: jest.fn(),
    write: jest.fn(),
  },
}));

describe('ProgressUpdateDBData', () => {
  const mockDatabase = database as jest.Mocked<typeof database>;
  const mockGetUserInfo = getUserInfo as jest.MockedFunction<typeof getUserInfo>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup console spies
    jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
    
    // Setup default user info
    mockGetUserInfo.mockReturnValue({
      UID: 12345,
      UserName: 'testuser',
      Department: 'Engineering',
      EMailid: '<EMAIL>',
      MobileNo: '1234567890'
    } as any);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('InsertProgressUpdateDetails', () => {
    const mockInputData = {
      inputDetails: {
        JobCode: 'JOB001',
        WBS: 'WBS001',
        Task: 'TASK001',
        PlanedQty: 100.5,
        PlanedSDate: '2024-01-01',
        PlanedEDate: '2024-12-31',
        PlanedLabour: 50,
        ActualQty: 75.25,
        ActualSDate: '2024-01-15',
        ActualEDate: '2024-11-30',
        ActualLabour: 45,
        LastUpdatedDate: '2024-06-15',
        LastUpdatedQty: 80,
        LastUpdatedLabour: 48,
        Remarks: 'Work in progress',
        TaskType: 'Construction',
        Min_Productivity_Range: 10,
        Max_Productivity_Range: 20,
        Scope: 100,
        MonthwisePlanQty: 25,
        Et_Code: 1001,
        DeliverableType_Et_Code: 'DET001',
        DET_Assigned_Scope_Quantity: 100,
        DET_Year_Month: 202401,
        TDD_Is_Engineer_Target_Mandatory: 'Y',
        Full_Desc: 'Full Description',
        ManDays: 5,
        date: '2024-06-15',
        Latitude: 12.9716,
        Longitude: 77.5946,
        TotalQuantity: 100,
        WBS_Description: 'WBS Description',
        WBS_Custom_Description: 'Custom WBS Description',
        Task_Description: 'Task Description',
        Task_Custom_Description: 'Custom Task Description',
        Image_ID: 'IMG001',
        Image_URL: 'https://example.com/image.jpg',
        UOM: 'Meters',
        NodeId: 'NODE001',
        ReferanceNodeId: 'REF_NODE001',
        From_Length: 0,
        Progress_Length: 25,
        Total_Length: 100,
        Distance_From_Center: 50,
        Alignment: 'NORTH',
        IS_Completed: 'N',
        Is_Hindrance: 'N'
      },
      wbsPath: 'ROOT/WBS001',
      selectedItem: { id: '1', name: 'Selected Item' },
      parentItems: [
        {
          WBS_Code: 'WBS001',
          WBS_Description: 'Parent WBS 1',
          ET_Code: 'ET001'
        },
        {
          WBS_Code: 'WBS002',
          WBS_Description: 'Parent WBS 2',
          ET_Code: 'ET002'
        }
      ]
    };

    it('should insert progress update details successfully', async () => {
      const mockInputDetailRecord = { id: 'input_detail_123', Wbs: 'WBS001' };
      const mockExistingRecords = [];
      const mockExistingPendingRecords = [];

      const mockInputDetailCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue(mockExistingRecords),
        create: jest.fn().mockResolvedValue(mockInputDetailRecord)
      };

      const mockParentItemsCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'parent_item_456' })
      };

      const mockPendingApprovalCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue(mockExistingPendingRecords),
        create: jest.fn().mockResolvedValue({ id: 'pending_approval_789' })
      };

      mockDatabase.get
        .mockReturnValueOnce(mockInputDetailCollection as any)
        .mockReturnValueOnce(mockParentItemsCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any);

      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await InsertProgressUpdateDetails(mockInputData);

      expect(mockDatabase.write).toHaveBeenCalledTimes(2);
      expect(mockInputDetailCollection.create).toHaveBeenCalledTimes(1);
      expect(mockParentItemsCollection.create).toHaveBeenCalledTimes(2);
      expect(mockPendingApprovalCollection.create).toHaveBeenCalledTimes(1);
    });

    it('should handle existing InputDetails record by deleting it first', async () => {
      const mockExistingRecord = {
        id: 'existing_123',
        Wbs: 'WBS001',
        destroyPermanently: jest.fn().mockResolvedValue(undefined)
      };
      const mockExistingRecords = [mockExistingRecord];
      
      const mockInputDetailRecord = { id: 'input_detail_456', Wbs: 'WBS001' };

      const mockInputDetailCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue(mockExistingRecords),
        create: jest.fn().mockResolvedValue(mockInputDetailRecord)
      };

      const mockParentItemsCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'parent_item_456' })
      };

      const mockPendingApprovalCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'pending_approval_789' })
      };

      mockDatabase.get
        .mockReturnValueOnce(mockInputDetailCollection as any)
        .mockReturnValueOnce(mockParentItemsCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any);

      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await InsertProgressUpdateDetails(mockInputData);

      expect(mockExistingRecord.destroyPermanently).toHaveBeenCalledTimes(1);
      expect(mockInputDetailCollection.create).toHaveBeenCalledTimes(1);
    });

    it('should delete related ParentItems before deleting InputDetails', async () => {
      const mockExistingInputRecord = {
        id: 'existing_input_123',
        Wbs: 'WBS001',
        destroyPermanently: jest.fn().mockResolvedValue(undefined)
      };

      const mockRelatedParentItem1 = {
        id: 'parent_1',
        Input_Detail_Id: 'existing_input_123',
        destroyPermanently: jest.fn().mockResolvedValue(undefined)
      };

      const mockRelatedParentItem2 = {
        id: 'parent_2',
        Input_Detail_Id: 'existing_input_123',
        destroyPermanently: jest.fn().mockResolvedValue(undefined)
      };

      const mockUnrelatedParentItem = {
        id: 'parent_3',
        Input_Detail_Id: 'different_id',
        destroyPermanently: jest.fn().mockResolvedValue(undefined)
      };

      const mockInputDetailCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([mockExistingInputRecord]),
        create: jest.fn().mockResolvedValue({ id: 'new_input_456' })
      };

      const mockParentItemsCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn()
          .mockResolvedValueOnce([mockRelatedParentItem1, mockRelatedParentItem2, mockUnrelatedParentItem])
          .mockResolvedValueOnce([]),
        create: jest.fn().mockResolvedValue({ id: 'new_parent_item' })
      };

      const mockPendingApprovalCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'pending_approval_789' })
      };

      mockDatabase.get
        .mockReturnValueOnce(mockInputDetailCollection as any)
        .mockReturnValueOnce(mockParentItemsCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any);

      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await InsertProgressUpdateDetails(mockInputData);

      expect(mockRelatedParentItem1.destroyPermanently).toHaveBeenCalledTimes(1);
      expect(mockRelatedParentItem2.destroyPermanently).toHaveBeenCalledTimes(1);
      expect(mockUnrelatedParentItem.destroyPermanently).not.toHaveBeenCalled();
      expect(mockExistingInputRecord.destroyPermanently).toHaveBeenCalledTimes(1);
    });

    it('should handle existing PendingForApprovalBQIT record by deleting it first', async () => {
      const mockExistingPendingRecord = {
        id: 'existing_pending_123',
        wbs: 'WBS001',
        destroyPermanently: jest.fn().mockResolvedValue(undefined)
      };

      const mockInputDetailCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'input_detail_456' })
      };

      const mockParentItemsCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'parent_item_456' })
      };

      const mockPendingApprovalCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([mockExistingPendingRecord]),
        create: jest.fn().mockResolvedValue({ id: 'pending_approval_789' })
      };

      mockDatabase.get
        .mockReturnValueOnce(mockInputDetailCollection as any)
        .mockReturnValueOnce(mockParentItemsCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any);

      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await InsertProgressUpdateDetails(mockInputData);

      expect(mockExistingPendingRecord.destroyPermanently).toHaveBeenCalledTimes(1);
      expect(mockPendingApprovalCollection.create).toHaveBeenCalledTimes(1);
    });

    it('should set all InputDetails properties correctly', async () => {
      let capturedInputDetailRecord: any = null;

      const mockInputDetailCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockImplementation((callback) => {
          const record = {};
          callback(record);
          capturedInputDetailRecord = record;
          return { id: 'input_detail_456' };
        })
      };

      const mockParentItemsCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'parent_item_456' })
      };

      const mockPendingApprovalCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'pending_approval_789' })
      };

      mockDatabase.get
        .mockReturnValueOnce(mockInputDetailCollection as any)
        .mockReturnValueOnce(mockParentItemsCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any);

      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await InsertProgressUpdateDetails(mockInputData);

      const d = mockInputData.inputDetails;
      expect(capturedInputDetailRecord.Job_Code).toBe(d.JobCode);
      expect(capturedInputDetailRecord.Wbs).toBe(d.WBS);
      expect(capturedInputDetailRecord.Task).toBe(d.Task);
      expect(capturedInputDetailRecord.Planned_Qty).toBe(d.PlanedQty);
      expect(capturedInputDetailRecord.Planned_SDate).toBe(d.PlanedSDate);
      expect(capturedInputDetailRecord.Planned_EDate).toBe(d.PlanedEDate);
      expect(capturedInputDetailRecord.Planned_Labour).toBe(d.PlanedLabour);
      expect(capturedInputDetailRecord.Actual_Qty).toBe(d.ActualQty);
      expect(capturedInputDetailRecord.Actual_SDate).toBe(d.ActualSDate);
      expect(capturedInputDetailRecord.Actual_EDate).toBe(d.ActualEDate);
      expect(capturedInputDetailRecord.Actual_Labour).toBe(d.ActualLabour);
      expect(capturedInputDetailRecord.Last_Updated_Date).toBe(d.LastUpdatedDate);
      expect(capturedInputDetailRecord.Last_Updated_Qty).toBe(d.LastUpdatedQty);
      expect(capturedInputDetailRecord.Last_Updated_Labour).toBe(d.LastUpdatedLabour);
      expect(capturedInputDetailRecord.Remarks).toBe(d.Remarks);
      expect(capturedInputDetailRecord.Task_Type).toBe(d.TaskType);
      expect(capturedInputDetailRecord.Min_Productivity_Range).toBe(d.Min_Productivity_Range);
      expect(capturedInputDetailRecord.Max_Productivity_Range).toBe(d.Max_Productivity_Range);
      expect(capturedInputDetailRecord.Scope).toBe(d.Scope);
      expect(capturedInputDetailRecord.Monthwise_Plan_Qty).toBe(d.MonthwisePlanQty);
      expect(capturedInputDetailRecord.Et_Code).toBe(d.Et_Code);
      expect(capturedInputDetailRecord.Deliverable_Type_Et_Code).toBe(d.DeliverableType_Et_Code);
      expect(capturedInputDetailRecord.Det_Assigned_Scope_Quantity).toBe(d.DET_Assigned_Scope_Quantity);
      expect(capturedInputDetailRecord.Det_Year_Month).toBe(d.DET_Year_Month);
      expect(capturedInputDetailRecord.Tdd_Is_Engineer_Target_Mandatory).toBe(d.TDD_Is_Engineer_Target_Mandatory);
      expect(capturedInputDetailRecord.Full_Desc).toBe(d.Full_Desc);
      expect(capturedInputDetailRecord.Wbs_Path).toBe(mockInputData.wbsPath);
      expect(capturedInputDetailRecord.Selected_Item).toBe(JSON.stringify(mockInputData.selectedItem));
      expect(capturedInputDetailRecord.Man_Days).toBe(d.ManDays);
      expect(capturedInputDetailRecord.Date).toBe(d.date);
    });

    it('should set all ParentItems properties correctly', async () => {
      const capturedParentRecords: any[] = [];

      const mockInputDetailRecord = { id: 'input_detail_456' };

      const mockInputDetailCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue(mockInputDetailRecord)
      };

      const mockParentItemsCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockImplementation((callback) => {
          const record = {};
          callback(record);
          capturedParentRecords.push(record);
          return { id: `parent_item_${capturedParentRecords.length}` };
        })
      };

      const mockPendingApprovalCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'pending_approval_789' })
      };

      mockDatabase.get
        .mockReturnValueOnce(mockInputDetailCollection as any)
        .mockReturnValueOnce(mockParentItemsCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any);

      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await InsertProgressUpdateDetails(mockInputData);

      expect(capturedParentRecords).toHaveLength(2);
      
      expect(capturedParentRecords[0].Wbs_Code).toBe('WBS001');
      expect(capturedParentRecords[0].Wbs_Description).toBe('Parent WBS 1');
      expect(capturedParentRecords[0].Et_Code).toBe('ET001');
      expect(capturedParentRecords[0].Input_Detail_Id).toBe('input_detail_456');

      expect(capturedParentRecords[1].Wbs_Code).toBe('WBS002');
      expect(capturedParentRecords[1].Wbs_Description).toBe('Parent WBS 2');
      expect(capturedParentRecords[1].Et_Code).toBe('ET002');
      expect(capturedParentRecords[1].Input_Detail_Id).toBe('input_detail_456');
    });

    it('should set all PendingForApprovalBQIT properties correctly', async () => {
      let capturedPendingRecord: any = null;

      const mockInputDetailCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'input_detail_456' })
      };

      const mockParentItemsCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'parent_item_456' })
      };

      const mockPendingApprovalCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockImplementation((callback) => {
          const record = {};
          callback(record);
          capturedPendingRecord = record;
          return { id: 'pending_approval_789' };
        })
      };

      mockDatabase.get
        .mockReturnValueOnce(mockInputDetailCollection as any)
        .mockReturnValueOnce(mockParentItemsCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any);

      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await InsertProgressUpdateDetails(mockInputData);

      const d = mockInputData.inputDetails;
      const user = mockGetUserInfo();

      expect(capturedPendingRecord.job).toBe(d.JobCode);
      expect(capturedPendingRecord.wbs).toBe(d.WBS);
      expect(capturedPendingRecord.task).toBe(d.Task);
      expect(capturedPendingRecord.tDate).toBe(d.date);
      expect(capturedPendingRecord.userId).toBe(user?.UID);
      expect(capturedPendingRecord.userName).toBe(user?.UserName);
      expect(capturedPendingRecord.quantity).toBe(d.PlanedQty);
      expect(capturedPendingRecord.manpower).toBe(d.ManDays);
      expect(capturedPendingRecord.remarks).toBe(d.Remarks);
      expect(capturedPendingRecord.taskType).toBe(d.TaskType);
      expect(capturedPendingRecord.latitude).toBe(d.Latitude);
      expect(capturedPendingRecord.longitude).toBe(d.Longitude);
      expect(capturedPendingRecord.totalQuantity).toBe(d.TotalQuantity);
      expect(capturedPendingRecord.actualQuantity).toBe(d.ActualQty);
      expect(capturedPendingRecord.wbsDescription).toBe(d.WBS_Description);
      expect(capturedPendingRecord.wbsCustomDescription).toBe(d.WBS_Custom_Description);
      expect(capturedPendingRecord.taskDescription).toBe(d.Task_Description);
      expect(capturedPendingRecord.taskCustomDescription).toBe(d.Task_Custom_Description);
      expect(capturedPendingRecord.imageId).toBe(d.Image_ID);
      expect(capturedPendingRecord.imageUrl).toBe(d.Image_URL);
      expect(capturedPendingRecord.uom).toBe(d.UOM);
      expect(capturedPendingRecord.nodeId).toBe(d.NodeId == undefined? null:d.NodeId);
      expect(capturedPendingRecord.referanceNodeId).toBe(d.ReferanceNodeId);
      expect(capturedPendingRecord.progressLength).toBe(d.Progress_Length);
      expect(capturedPendingRecord.totalLength).toBe(d.Total_Length);
      expect(capturedPendingRecord.distanceFromCenter).toBe(d.Distance_From_Center);
      expect(capturedPendingRecord.alignment).toBe(d.Alignment);
      expect(capturedPendingRecord.isCompleted).toBe(d.IS_Completed);
      expect(capturedPendingRecord.isHindrance).toBe(d.Is_Hindrance);
    });

    it('should use default values for missing optional fields in PendingForApprovalBQIT', async () => {
      const dataWithMissingFields = {
        ...mockInputData,
        inputDetails: {
          ...mockInputData.inputDetails,
          Latitude: undefined,
          Longitude: undefined,
          Image_ID: undefined,
          Image_URL: undefined,
          UOM: undefined,
          NodeId: undefined,
          ReferanceNodeId: undefined,
          From_Length: undefined,
          Progress_Length: undefined,
          Total_Length: undefined,
          Distance_From_Center: undefined,
          Alignment: undefined,
          IS_Completed: undefined,
          Is_Hindrance: undefined
        }
      };

      let capturedPendingRecord: any = null;

      const mockInputDetailCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'input_detail_456' })
      };

      const mockParentItemsCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'parent_item_456' })
      };

      const mockPendingApprovalCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockImplementation((callback) => {
          const record = {};
          callback(record);
          capturedPendingRecord = record;
          return { id: 'pending_approval_789' };
        })
      };

      mockDatabase.get
        .mockReturnValueOnce(mockInputDetailCollection as any)
        .mockReturnValueOnce(mockParentItemsCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any);

      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await InsertProgressUpdateDetails(dataWithMissingFields);

      expect(capturedPendingRecord.latitude).toBe(13.02042); // Default value
      expect(capturedPendingRecord.longitude).toBe(80.17819); // Default value
      expect(capturedPendingRecord.imageId).toBeNull();
      expect(capturedPendingRecord.imageUrl).toBeNull();
      expect(capturedPendingRecord.uom).toBe('');
      expect(capturedPendingRecord.nodeId).toBeNull();
      expect(capturedPendingRecord.referanceNodeId).toBeNull();
      expect(capturedPendingRecord.fromLength).toBeNull();
      expect(capturedPendingRecord.progressLength).toBeNull();
      expect(capturedPendingRecord.totalLength).toBeNull();
      expect(capturedPendingRecord.distanceFromCenter).toBeNull();
      expect(capturedPendingRecord.alignment).toBeNull();
      expect(capturedPendingRecord.isCompleted).toBeNull();
      expect(capturedPendingRecord.isHindrance).toBeNull();
    });

    it('should handle empty parentItems array', async () => {
      const dataWithNoParents = {
        ...mockInputData,
        parentItems: []
      };

      const mockInputDetailCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'input_detail_456' })
      };

      const mockParentItemsCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'parent_item_456' })
      };

      const mockPendingApprovalCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'pending_approval_789' })
      };

      mockDatabase.get
        .mockReturnValueOnce(mockInputDetailCollection as any)
        .mockReturnValueOnce(mockParentItemsCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any);

      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await InsertProgressUpdateDetails(dataWithNoParents);

      expect(mockParentItemsCollection.create).not.toHaveBeenCalled();
      expect(mockInputDetailCollection.create).toHaveBeenCalledTimes(1);
      expect(mockPendingApprovalCollection.create).toHaveBeenCalledTimes(1);
    });

    it('should handle null user info', async () => {
      mockGetUserInfo.mockReturnValue(null);

      let capturedPendingRecord: any = null;

      const mockInputDetailCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'input_detail_456' })
      };

      const mockParentItemsCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'parent_item_456' })
      };

      const mockPendingApprovalCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockImplementation((callback) => {
          const record = {};
          callback(record);
          capturedPendingRecord = record;
          return { id: 'pending_approval_789' };
        })
      };

      mockDatabase.get
        .mockReturnValueOnce(mockInputDetailCollection as any)
        .mockReturnValueOnce(mockParentItemsCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any);

      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await InsertProgressUpdateDetails(mockInputData);

      expect(capturedPendingRecord.userId).toBeUndefined();
      expect(capturedPendingRecord.userName).toBeUndefined();
    });
  });

  describe('logInputDetails', () => {
    it('should log InputDetails records when they exist', async () => {
      const mockRecords = [
        { id: '1', Wbs: 'WBS001', Task: 'TASK001' },
        { id: '2', Wbs: 'WBS002', Task: 'TASK002' }
      ];

      const mockCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue(mockRecords)
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);

      await logInputDetails();

      expect(mockDatabase.get).toHaveBeenCalledWith('InputDetails');
      expect(console.log).toHaveBeenCalledWith('Fetching InputDetails...');
      expect(console.log).toHaveBeenCalledWith('InputDetails records:', mockRecords);
    });

    it('should log message when no records found', async () => {
      const mockCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([])
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);

      await logInputDetails();

      expect(console.log).toHaveBeenCalledWith('Fetching InputDetails...');
      expect(console.log).toHaveBeenCalledWith('No records found in InputDetails');
    });

    it('should handle database errors gracefully', async () => {
      const mockCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockRejectedValue(new Error('Database fetch error'))
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);

      await logInputDetails();

      expect(console.log).toHaveBeenCalledWith('Fetching InputDetails...');
      expect(console.error).toHaveBeenCalledWith('Error fetching InputDetails:', expect.any(Error));
    });

    it('should handle large datasets', async () => {
      const largeRecordSet = Array.from({ length: 1000 }, (_, index) => ({
        id: `record_${index + 1}`,
        Wbs: `WBS${String(index + 1).padStart(3, '0')}`,
        Task: `TASK${String(index + 1).padStart(3, '0')}`
      }));

      const mockCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue(largeRecordSet)
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);

      await logInputDetails();

      expect(console.log).toHaveBeenCalledWith('InputDetails records:', largeRecordSet);
    });
  });

  describe('logParentItems', () => {
    it('should log ParentItems records', async () => {
      const mockParentRecords = [
        { id: '1', Wbs_Code: 'WBS001', Wbs_Description: 'Parent WBS 1' },
        { id: '2', Wbs_Code: 'WBS002', Wbs_Description: 'Parent WBS 2' }
      ];

      const mockCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue(mockParentRecords)
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);

      await logParentItems();

      expect(mockDatabase.get).toHaveBeenCalledWith('ParentItems');
      expect(console.log).toHaveBeenCalledWith('All ParentItems:', mockParentRecords);
    });

    it('should log empty array when no parent items exist', async () => {
      const mockCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([])
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);

      await logParentItems();

      expect(console.log).toHaveBeenCalledWith('All ParentItems:', []);
    });

    it('should handle database errors', async () => {
      const mockCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockRejectedValue(new Error('Database error'))
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);

      // Since logParentItems doesn't have error handling, it should throw
      await expect(logParentItems()).rejects.toThrow('Database error');
    });

    it('should handle null/undefined records', async () => {
      const mockParentRecords = [
        null,
        { id: '1', Wbs_Code: 'WBS001', Wbs_Description: 'Parent WBS 1' },
        undefined,
        { id: '2', Wbs_Code: 'WBS002', Wbs_Description: 'Parent WBS 2' }
      ];

      const mockCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue(mockParentRecords)
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);

      await logParentItems();

      expect(console.log).toHaveBeenCalledWith('All ParentItems:', mockParentRecords);
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete workflow with logging', async () => {
      const mockInputDetailRecord = { id: 'input_detail_123', Wbs: 'WBS001' };
      const mockParentRecords = [
        { id: 'parent_1', Wbs_Code: 'WBS001', Wbs_Description: 'Parent WBS 1' }
      ];

      // Setup for InsertProgressUpdateDetails
      const mockInputDetailCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue(mockInputDetailRecord)
      };

      const mockParentItemsCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'parent_item_456' })
      };

      const mockPendingApprovalCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue({ id: 'pending_approval_789' })
      };

      mockDatabase.get
        .mockReturnValueOnce(mockInputDetailCollection as any)
        .mockReturnValueOnce(mockParentItemsCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any)
        .mockReturnValueOnce(mockInputDetailCollection as any) // For logInputDetails
        .mockReturnValueOnce(mockParentItemsCollection as any); // For logParentItems

      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      // Execute workflow
      const mockInputData = {
        inputDetails: {
          JobCode: 'JOB001',
          WBS: 'WBS001',
          Task: 'Task 1',
          PlanedQty: 100,
          PlanedSDate: '2024-01-01',
          PlanedEDate: '2024-01-31',
          PlanedLabour: 10,
          ActualQty: 90,
          ActualSDate: '2024-01-02',
          ActualEDate: '2024-01-30',
          ActualLabour: 9,
          LastUpdatedDate: '2024-01-30',
          LastUpdatedQty: 90,
          LastUpdatedLabour: 9,
          Remarks: 'Test remarks',
          TaskType: 'TypeA',
          Min_Productivity_Range: 5,
          Max_Productivity_Range: 15,
          Scope: 'Test Scope',
          MonthwisePlanQty: 100,
          Et_Code: 'ET001'
        },
        wbsPath: 'WBS001/Task 1',
        selectedItem: { id: 'input_detail_123' },
        parentItems: [
          {
            WBS_Code: 'WBS001',
            WBS_Description: 'Parent WBS 1',
            ET_Code: 'ET001'
          }
        ]
      };
      await InsertProgressUpdateDetails(mockInputData);
      await logInputDetails();
      await logParentItems();

      expect(mockDatabase.write).toHaveBeenCalledTimes(2);
      expect(console.log).toHaveBeenCalledWith('Fetching InputDetails...');
      expect(console.log).toHaveBeenCalledWith('All ParentItems:', []);
    });

    it('should handle concurrent operations', async () => {
      const mockCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([])
      };

      mockDatabase.get.mockReturnValue(mockCollection as any);

      await Promise.all([
        logInputDetails(),
        logParentItems()
      ]);

      expect(mockDatabase.get).toHaveBeenCalledWith('InputDetails');
      expect(mockDatabase.get).toHaveBeenCalledWith('ParentItems');
    });

    it('should maintain data consistency across operations', async () => {
      const consistentData = {
        inputDetails: {
          JobCode: 'CONSISTENT_JOB',
          WBS: 'CONSISTENT_WBS'
        },
        wbsPath: 'CONSISTENT/PATH',
        selectedItem: { id: 'consistent_1' },
        parentItems: [
          {
            WBS_Code: 'CONSISTENT_WBS_PARENT',
            WBS_Description: 'Consistent Parent WBS',
            ET_Code: 'CONSISTENT_ET'
          }
        ]
      };

      let capturedInputRecord: any = null;
      let capturedParentRecord: any = null;
      let capturedPendingRecord: any = null;

      const mockInputDetailRecord = { id: 'consistent_input_123' };

      const mockInputDetailCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockImplementation((callback) => {
          const record = {};
          callback(record);
          capturedInputRecord = record;
          return mockInputDetailRecord;
        })
      };

      const mockParentItemsCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockImplementation((callback) => {
          const record = {};
          callback(record);
          capturedParentRecord = record;
          return { id: 'consistent_parent_456' };
        })
      };

      const mockPendingApprovalCollection = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockImplementation((callback) => {
          const record = {};
          callback(record);
          capturedPendingRecord = record;
          return { id: 'consistent_pending_789' };
        })
      };

      mockDatabase.get
        .mockReturnValueOnce(mockInputDetailCollection as any)
        .mockReturnValueOnce(mockParentItemsCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any)
        .mockReturnValueOnce(mockPendingApprovalCollection as any);

      mockDatabase.write.mockImplementation(async (callback) => {
        await callback();
      });

      await InsertProgressUpdateDetails(consistentData);

      expect(capturedInputRecord.Job_Code).toBe('CONSISTENT_JOB');
      expect(capturedInputRecord.Wbs).toBe('CONSISTENT_WBS');
      expect(capturedParentRecord.Wbs_Code).toBe('CONSISTENT_WBS_PARENT');
      expect(capturedParentRecord.Input_Detail_Id).toBe('consistent_input_123');
      expect(capturedPendingRecord.job).toBe('CONSISTENT_JOB');
      expect(capturedPendingRecord.wbs).toBe('CONSISTENT_WBS');
    });
  });
}); 