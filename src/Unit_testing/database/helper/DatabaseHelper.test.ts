import {
  loadLatLongHierarchyData,
  getAllWBSItemsWithPathAllLevels,
  buildWBSCodePath
} from '../../../database/helper/DatabaseHelper';
import { WBSItem } from '../../../model/DailyProgress/DailyProgressData';
import { database } from '../../../database/index';
import { Q } from '@nozbe/watermelondb';

// Mock dependencies
jest.mock('../../../database/index', () => ({
  database: {
    get: jest.fn(),
  },
}));

jest.mock('@nozbe/watermelondb', () => ({
  Q: {
    where: jest.fn(),
    notEq: jest.fn(),
  },
}));

describe('DatabaseHelper', () => {
  const mockDatabase = database as jest.Mocked<typeof database>;
  const mockQ = Q as jest.Mocked<typeof Q>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mock implementations
    mockQ.where.mockImplementation((field: string, value: any) => ({ field, value, type: 'where' }));
    mockQ.notEq.mockImplementation((value: any) => ({ value, type: 'notEq' }));
  });

  describe('loadLatLongHierarchyData', () => {
    it('should load lat long hierarchy data successfully', async () => {
      const mockRecord = {
        id: '1',
        _raw: {
          PRCLLH_Job_Code: 'JOB001',
          PRCLLH_WBS_Code: 'WBS001',
          PRCLLH_Latitude: 12.9716,
          PRCLLH_Longitude: 77.5946,
          PRCLLH_Hierarchy_Level: 1,
          PRCLLH_IsActive: 'Y',
          JOB_DESC: 'Test Job Description',
          IL: 'IL001',
          IL_DESC: 'IL Description',
          WP: 'WP001',
          WP_DESC: 'WP Description',
          SWP: 'SWP001',
          CWP_DESC: 'CWP Description',
          DELIVERABLE_CODE: 'DEL001',
          DELIVERABLE_CODE_DESC: 'Deliverable Description'
        }
      };

      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([mockRecord])
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await loadLatLongHierarchyData('JOB001');

      expect(mockDatabase.get).toHaveBeenCalledWith('LatLongHierarchy');
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        id: '1',
        jobCode: 'JOB001',
        wbsCode: 'WBS001',
        latitude: 12.9716,
        longitude: 77.5946,
        hierarchyLevel: 1,
        isActive: 'Y',
        jobDesc: 'Test Job Description',
        il: 'IL001',
        ilDesc: 'IL Description',
        wp: 'WP001',
        wpDesc: 'WP Description',
        swp: 'SWP001',
        cwpDesc: 'CWP Description',
        deliverableCode: 'DEL001',
        deliverableCodeDesc: 'Deliverable Description'
      });
    });

    it('should load lat long hierarchy data with bookmarks filter', async () => {
      const mockBookmarksList: WBSItem[] = [
        {
          id: '1',
          entity_Code: 'ENT001',
          job_Code: 'JOB001',
          entity_Type: 'WBS',
          leaf_Node_Tag: 'Y',
          entity_Description: 'Entity Description',
          parent_WBS_Code: 'WBS001',
          et_Code: 'ET001'
        },
        {
          id: '2',
          entity_Code: 'ENT002',
          job_Code: 'JOB001',
          entity_Type: 'Task',
          leaf_Node_Tag: 'Y',
          entity_Description: 'Task Description',
          parent_WBS_Code: 'WBS002',
          et_Code: 'ET002'
        }
      ];

      const mockRecord = {
        id: '1',
        _raw: {
          PRCLLH_Job_Code: 'JOB001',
          PRCLLH_WBS_Code: 'WBS001',
          PRCLLH_Latitude: 12.9716,
          PRCLLH_Longitude: 77.5946,
          PRCLLH_Hierarchy_Level: 1,
          PRCLLH_IsActive: 'Y',
          JOB_DESC: 'Test Job',
          IL: 'IL001',
          IL_DESC: 'IL Description',
          WP: 'WP001',
          WP_DESC: 'WP Description',
          SWP: 'SWP001',
          CWP_DESC: 'CWP Description',
          DELIVERABLE_CODE: 'DEL001',
          DELIVERABLE_CODE_DESC: 'Deliverable Description'
        }
      };

      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([mockRecord])
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await loadLatLongHierarchyData('JOB001', 'Bookmarks', mockBookmarksList);

      expect(mockDatabase.get).toHaveBeenCalledWith('LatLongHierarchy');
      expect(mockQuery.query).toHaveBeenCalled();
      expect(result).toHaveLength(1);
      expect(result[0].jobCode).toBe('JOB001');
      expect(result[0].wbsCode).toBe('WBS001');
    });

    it('should return empty array when no data found', async () => {
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([])
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await loadLatLongHierarchyData('JOB999');

      expect(result).toEqual([]);
    });

    it('should handle errors gracefully', async () => {
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockRejectedValue(new Error('Database error'))
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await loadLatLongHierarchyData('JOB001');

      expect(consoleSpy).toHaveBeenCalledWith('Error loading LatLongHierarchy data:', expect.any(Error));
      expect(result).toEqual([]);

      consoleSpy.mockRestore();
    });

    it('should handle bookmarks with undefined parent_WBS_Code', async () => {
      const mockBookmarksList: WBSItem[] = [
        {
          id: '1',
          entity_Code: 'ENT001',
          job_Code: 'JOB001',
          entity_Type: 'WBS',
          leaf_Node_Tag: 'Y',
          entity_Description: 'Entity Description',
          parent_WBS_Code: undefined,
          et_Code: 'ET001'
        }
      ];

      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([])
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await loadLatLongHierarchyData('JOB001', 'Bookmarks', mockBookmarksList);

      expect(result).toEqual([]);
    });

    it('should handle empty bookmarks list', async () => {
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([])
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await loadLatLongHierarchyData('JOB001', 'Bookmarks', []);

      expect(result).toEqual([]);
    });

    it('should handle undefined bookmarks list', async () => {
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([])
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await loadLatLongHierarchyData('JOB001', 'Bookmarks', undefined);

      expect(result).toEqual([]);
    });
  });

  describe('getAllWBSItemsWithPathAllLevels', () => {
    const mockEntityConstants = {
      WBSJob: 'WBSJob',
      WBSTask: 'WBSTask',
      Y: 'Y',
      N: 'N'
    };

    it('should return empty array when no root items found', async () => {
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([])
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await getAllWBSItemsWithPathAllLevels('JOB001', mockDatabase, mockEntityConstants);

      expect(result).toEqual([]);
    });

    it('should traverse WBS hierarchy with leaf nodes', async () => {
      const mockRootItem = {
        id: '1',
        _raw: {
          WBS_Code: 'WBS001',
          Job_Code: 'JOB001',
          entity_Type: 'WBSJob',
          Leaf_Node_Tag: 'Y',
          WBS_Description: 'Root WBS',
          Parent_WBS_Code: 'JOB001',
          ET_Code: 'ET001'
        }
      };

      const mockTask = {
        id: 'task1',
        taskCode: 'TASK001',
        jobCode: 'JOB001',
        taskDescription: 'Test Task',
        parentWbs: 'WBS001',
        gisTag: 'GIS001 ',
        etCode: 'ET001 '
      };

      let queryCallCount = 0;
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockImplementation(() => {
          queryCallCount++;
          if (queryCallCount === 1) {
            // First call - root items
            return Promise.resolve([mockRootItem]);
          } else if (queryCallCount === 2) {
            // Second call - tasks under root
            return Promise.resolve([mockTask]);
          } else if (queryCallCount === 3) {
            // Third call - subtasks
            return Promise.resolve([]);
          } else {
            // Further calls - no more tasks
            return Promise.resolve([]);
          }
        })
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await getAllWBSItemsWithPathAllLevels('JOB001', mockDatabase, mockEntityConstants);

      expect(mockDatabase.get).toHaveBeenCalled();
      expect(result).toHaveLength(1);
      expect(result[0].entity_Code).toBe('TASK001');
      expect(result[0].fullPath).toBe('Root WBS / Test Task');
    });

    it('should traverse WBS hierarchy with non-leaf nodes', async () => {
      const mockRootItem = {
        id: '1',
        _raw: {
          WBS_Code: 'WBS001',
          Job_Code: 'JOB001',
          entity_Type: 'WBSJob',
          Leaf_Node_Tag: 'N',
          WBS_Description: 'Root WBS',
          Parent_WBS_Code: 'JOB001',
          ET_Code: 'ET001'
        }
      };

      const mockChildItem = {
        id: '2',
        _raw: {
          WBS_Code: 'WBS002',
          Job_Code: 'JOB001',
          entity_Type: 'WBSJob',
          Leaf_Node_Tag: 'Y',
          WBS_Description: 'Child WBS',
          Parent_WBS_Code: 'WBS001',
          ET_Code: 'ET002'
        }
      };

      let queryCallCount = 0;
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockImplementation(() => {
          queryCallCount++;
          if (queryCallCount === 1) {
            // First call - root items
            return Promise.resolve([mockRootItem]);
          } else if (queryCallCount === 2) {
            // Second call - child WBS items
            return Promise.resolve([mockChildItem]);
          } else if (queryCallCount === 3) {
            // Third call - tasks under child WBS
            return Promise.resolve([]);
          } else {
            // Further calls - no more items
            return Promise.resolve([]);
          }
        })
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await getAllWBSItemsWithPathAllLevels('JOB001', mockDatabase, mockEntityConstants);

      expect(result).toHaveLength(1);
      expect(result[0].entity_Code).toBe('WBS002');
      expect(result[0].fullPath).toBe('Root WBS / Child WBS');
    });

    it('should handle WBS task entity type', async () => {
      const mockRootItem = {
        id: '1',
        _raw: {
          WBS_Code: 'TASK001',
          Job_Code: 'JOB001',
          entity_Type: 'WBSTask',
          Leaf_Node_Tag: 'Y',
          WBS_Description: 'Root Task',
          Parent_WBS_Code: 'JOB001',
          ET_Code: 'ET001'
        }
      };

      let queryCallCount = 0;
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockImplementation(() => {
          queryCallCount++;
          if (queryCallCount === 1) {
            // First call - root items
            return Promise.resolve([mockRootItem]);
          } else {
            // Subsequent calls - no tasks found
            return Promise.resolve([]);
          }
        })
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await getAllWBSItemsWithPathAllLevels('JOB001', mockDatabase, mockEntityConstants);

      expect(result).toHaveLength(1);
      expect(result[0].entity_Code).toBe('TASK001');
      expect(result[0].fullPath).toBe('Root Task');
    });

    it('should handle tasks with subtasks (hardcoded Parent_Task)', async () => {
      const mockRootItem = {
        id: '1',
        _raw: {
          WBS_Code: 'WBS001',
          Job_Code: 'JOB001',
          entity_Type: 'WBSJob',
          Leaf_Node_Tag: 'Y',
          WBS_Description: 'Root WBS',
          Parent_WBS_Code: 'JOB001',
          ET_Code: 'ET001'
        }
      };

      const mockTask = {
        id: 'task1',
        taskCode: 'TASK001',
        jobCode: 'JOB001',
        taskDescription: 'Test Task',
        parentWbs: 'WBS001',
        gisTag: 'GIS001 ',
        etCode: 'ET001 ',
        parentTask: '3115'
      };

      const mockSubTask = {
        id: 'subtask1',
        taskCode: 'SUBTASK001',
        jobCode: 'JOB001',
        taskDescription: 'Sub Task',
        parentWbs: 'WBS001',
        gisTag: 'GIS002 ',
        etCode: 'ET002 '
      };

      let queryCallCount = 0;
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockImplementation(() => {
          queryCallCount++;
          if (queryCallCount === 1) {
            // First call - root items
            return Promise.resolve([mockRootItem]);
          } else if (queryCallCount === 2) {
            // Second call - tasks under root
            return Promise.resolve([mockTask]);
          } else if (queryCallCount === 3) {
            // Third call - subtasks (hardcoded query for Parent_Task = '3115')
            return Promise.resolve([mockSubTask]);
          } else {
            // Further calls - no more tasks
            return Promise.resolve([]);
          }
        })
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await getAllWBSItemsWithPathAllLevels('JOB001', mockDatabase, mockEntityConstants);

      expect(result).toHaveLength(1);
      expect(result[0].entity_Code).toBe('TASK001');
    });

    it('should handle complex nested hierarchy', async () => {
      const mockRootItem = {
        id: '1',
        _raw: {
          WBS_Code: 'WBS001',
          Job_Code: 'JOB001',
          entity_Type: 'WBSJob',
          Leaf_Node_Tag: 'N',
          WBS_Description: 'Level 1',
          Parent_WBS_Code: 'JOB001',
          ET_Code: 'ET001'
        }
      };

      const mockLevel2Item = {
        id: '2',
        _raw: {
          WBS_Code: 'WBS002',
          Job_Code: 'JOB001',
          entity_Type: 'WBSJob',
          Leaf_Node_Tag: 'N',
          WBS_Description: 'Level 2',
          Parent_WBS_Code: 'WBS001',
          ET_Code: 'ET002'
        }
      };

      const mockLevel3Item = {
        id: '3',
        _raw: {
          WBS_Code: 'WBS003',
          Job_Code: 'JOB001',
          entity_Type: 'WBSJob',
          Leaf_Node_Tag: 'Y',
          WBS_Description: 'Level 3',
          Parent_WBS_Code: 'WBS002',
          ET_Code: 'ET003'
        }
      };

      let queryCallCount = 0;
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockImplementation(() => {
          queryCallCount++;
          if (queryCallCount === 1) {
            return Promise.resolve([mockRootItem]);
          } else if (queryCallCount === 2) {
            return Promise.resolve([mockLevel2Item]);
          } else if (queryCallCount === 3) {
            return Promise.resolve([mockLevel3Item]);
          } else {
            return Promise.resolve([]);
          }
        })
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await getAllWBSItemsWithPathAllLevels('JOB001', mockDatabase, mockEntityConstants);

      expect(result).toHaveLength(1);
      expect(result[0].fullPath).toBe('Level 1 / Level 2 / Level 3');
    });

    it('should handle unknown entity types gracefully', async () => {
      const mockRootItem = {
        id: '1',
        _raw: {
          WBS_Code: 'GIS001',
          Job_Code: 'JOB001',
          entity_Type: 'GISNode',
          Leaf_Node_Tag: 'Y',
          WBS_Description: 'GIS Node',
          Parent_WBS_Code: 'JOB001',
          ET_Code: 'ET001'
        }
      };

      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([mockRootItem])
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await getAllWBSItemsWithPathAllLevels('JOB001', mockDatabase, mockEntityConstants);

      // Should stop traversal for unknown entity types
      expect(result).toEqual([]);
    });
  });

  describe('buildWBSCodePath', () => {
    const mockItem: WBSItem = {
      id: '1',
      entity_Code: 'WBS003',
      job_Code: 'JOB001',
      entity_Type: 'WBS',
      leaf_Node_Tag: 'Y',
      entity_Description: 'Level 3 WBS',
      parent_WBS_Code: 'WBS002',
      et_Code: 'ET003'
    };

    it('should build WBS code path successfully', async () => {
      const mockParent1 = {
        id: '1',
        _raw: {
          WBS_Code: 'WBS002',
          Job_Code: 'JOB001',
          Parent_WBS_Code: 'WBS001'
        }
      };

      const mockParent2 = {
        id: '2',
        _raw: {
          WBS_Code: 'WBS001',
          Job_Code: 'JOB001',
          Parent_WBS_Code: 'JOB001'
        }
      };

      const mockParent3 = {
        id: '3',
        _raw: {
          WBS_Code: 'JOB001',
          Job_Code: 'JOB001',
          Parent_WBS_Code: null
        }
      };

      let queryCallCount = 0;
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockImplementation(() => {
          queryCallCount++;
          if (queryCallCount === 1) {
            return Promise.resolve([mockParent1]); // WBS002
          } else if (queryCallCount === 2) {
            return Promise.resolve([mockParent2]); // WBS001
          } else if (queryCallCount === 3) {
            return Promise.resolve([mockParent3]); // JOB001
          } else {
            return Promise.resolve([]); // No more parents
          }
        })
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await buildWBSCodePath(mockItem);

      expect(result).toBe('JOB001~WBS001~WBS002~WBS003');
    });

    it('should handle item with no parent', async () => {
      const itemWithoutParent: WBSItem = {
        id: '1',
        entity_Code: 'ROOT001',
        job_Code: 'JOB001',
        entity_Type: 'WBS',
        leaf_Node_Tag: 'Y',
        entity_Description: 'Root Item',
        parent_WBS_Code: null,
        et_Code: 'ET001'
      };

      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([])
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await buildWBSCodePath(itemWithoutParent);

      expect(result).toBe('ROOT001');
    });

    it('should handle item with undefined parent', async () => {
      const itemWithUndefinedParent: WBSItem = {
        id: '1',
        entity_Code: 'ROOT002',
        job_Code: 'JOB001',
        entity_Type: 'WBS',
        leaf_Node_Tag: 'Y',
        entity_Description: 'Root Item',
        parent_WBS_Code: undefined,
        et_Code: 'ET001'
      };

      const result = await buildWBSCodePath(itemWithUndefinedParent);

      expect(result).toBe('ROOT002');
    });

    it('should handle database errors gracefully', async () => {
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockRejectedValue(new Error('Database error'))
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await buildWBSCodePath(mockItem);

      expect(consoleSpy).toHaveBeenCalledWith('Error building WBS code path:', expect.any(Error));
      expect(result).toBe('');

      consoleSpy.mockRestore();
    });

    it('should handle missing parent in database', async () => {
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([]) // No parent found
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await buildWBSCodePath(mockItem);

      expect(result).toBe('WBS003');
    });

    it('should build path with single parent', async () => {
      const mockParent = {
        id: '1',
        _raw: {
          WBS_Code: 'WBS001',
          Job_Code: 'JOB001',
          Parent_WBS_Code: null
        }
      };

      let queryCallCount = 0;
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockImplementation(() => {
          queryCallCount++;
          if (queryCallCount === 1) {
            return Promise.resolve([mockParent]); // Parent found
          } else {
            return Promise.resolve([]); // No more parents
          }
        })
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await buildWBSCodePath(mockItem);

      expect(result).toBe('WBS001~WBS003');
    });

    it('should handle complex nested hierarchy path', async () => {
      const deepItem: WBSItem = {
        id: '1',
        entity_Code: 'WBS005',
        job_Code: 'JOB001',
        entity_Type: 'WBS',
        leaf_Node_Tag: 'Y',
        entity_Description: 'Deep Level WBS',
        parent_WBS_Code: 'WBS004',
        et_Code: 'ET005'
      };

      const mockParents = [
        { id: '1', _raw: { WBS_Code: 'WBS004', Job_Code: 'JOB001', Parent_WBS_Code: 'WBS003' } },
        { id: '2', _raw: { WBS_Code: 'WBS003', Job_Code: 'JOB001', Parent_WBS_Code: 'WBS002' } },
        { id: '3', _raw: { WBS_Code: 'WBS002', Job_Code: 'JOB001', Parent_WBS_Code: 'WBS001' } },
        { id: '4', _raw: { WBS_Code: 'WBS001', Job_Code: 'JOB001', Parent_WBS_Code: 'ROOT' } },
        { id: '5', _raw: { WBS_Code: 'ROOT', Job_Code: 'JOB001', Parent_WBS_Code: null } }
      ];

      let queryCallCount = 0;
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockImplementation(() => {
          queryCallCount++;
          if (queryCallCount <= mockParents.length) {
            return Promise.resolve([mockParents[queryCallCount - 1]]);
          } else {
            return Promise.resolve([]);
          }
        })
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await buildWBSCodePath(deepItem);

      expect(result).toBe('ROOT~WBS001~WBS002~WBS003~WBS004~WBS005');
    });
  });

  describe('Integration Tests', () => {
    it('should handle concurrent database operations', async () => {
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([])
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const promises = [
        loadLatLongHierarchyData('JOB001'),
        loadLatLongHierarchyData('JOB002'),
        buildWBSCodePath({
          id: '1',
          entity_Code: 'WBS001',
          job_Code: 'JOB001',
          entity_Type: 'WBS',
          leaf_Node_Tag: 'Y',
          entity_Description: 'Test WBS',
          parent_WBS_Code: null,
          et_Code: 'ET001'
        })
      ];

      const results = await Promise.all(promises);

      expect(results[0]).toEqual([]);
      expect(results[1]).toEqual([]);
      expect(results[2]).toBe('WBS001');
    });

    it('should handle edge case with empty job code', async () => {
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockResolvedValue([])
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const result = await loadLatLongHierarchyData('');

      expect(result).toEqual([]);
    });

    it('should maintain data consistency across operations', async () => {
      const testItem: WBSItem = {
        id: '1',
        entity_Code: 'WBS001',
        job_Code: 'JOB001',
        entity_Type: 'WBS',
        leaf_Node_Tag: 'Y',
        entity_Description: 'Consistent Test WBS',
        parent_WBS_Code: 'ROOT',
        et_Code: 'ET001'
      };

      const mockLatLongRecord = {
        id: '1',
        _raw: {
          PRCLLH_Job_Code: 'JOB001',
          PRCLLH_WBS_Code: 'WBS001',
          PRCLLH_Latitude: 12.9716,
          PRCLLH_Longitude: 77.5946,
          PRCLLH_Hierarchy_Level: 1,
          PRCLLH_IsActive: 'Y',
          JOB_DESC: 'Test Job',
          IL: 'IL001',
          IL_DESC: 'IL Description',
          WP: 'WP001',
          WP_DESC: 'WP Description',
          SWP: 'SWP001',
          CWP_DESC: 'CWP Description',
          DELIVERABLE_CODE: 'DEL001',
          DELIVERABLE_CODE_DESC: 'Deliverable Description'
        }
      };

      const mockParent = {
        id: '1',
        _raw: {
          WBS_Code: 'ROOT',
          Job_Code: 'JOB001',
          Parent_WBS_Code: null
        }
      };

      let callType = '';
      const mockQuery = {
        query: jest.fn().mockReturnThis(),
        fetch: jest.fn().mockImplementation(() => {
          if (mockDatabase.get.mock.calls[mockDatabase.get.mock.calls.length - 1][0] === 'LatLongHierarchy') {
            return Promise.resolve([mockLatLongRecord]);
          } else {
            return Promise.resolve([mockParent]);
          }
        })
      };

      mockDatabase.get.mockReturnValue(mockQuery as any);

      const [latLongResult, pathResult] = await Promise.all([
        loadLatLongHierarchyData('JOB001'),
        buildWBSCodePath(testItem)
      ]);

      expect(latLongResult[0].jobCode).toBe('JOB001');
      expect(latLongResult[0].wbsCode).toBe('WBS001');
      expect(pathResult).toBe('ROOT~WBS001');
    });
  });
}); 