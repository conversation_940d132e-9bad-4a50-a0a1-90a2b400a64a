import { StoreWbsHierarchyData } from '../../../../src/database/WBSDataInsert/StoreWbsHierarchyData';
import { database } from '../../../../src/database';

// Mock the entire database module
jest.mock('../../../../src/database', () => ({
  database: {
    write: jest.fn(async (callback) => {
      // Call the callback to simulate the write operation
      return await callback();
    }),
    batch: jest.fn().mockResolvedValue(undefined),
    collections: {
      get: jest.fn(() => ({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue([]), // Return empty array for existing records
        })),
        prepareCreate: jest.fn((callback) => {
          const mockRecord = {};
          callback(mockRecord);
          return mockRecord;
        }),
      })),
    },
  },
}));

describe('StoreWbsHierarchyData', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should process WBS hierarchy data without errors', async () => {
        const mockData = [
            { 
                Job_Code: 'JC1', 
                WBS_Code: 'WBS1', 
                WBS_Description: 'Description 1', 
                Parent_WBS_Code: 'P1', 
                ET_Code: 'ET1', 
                Leaf_Node_Tag: 'L1', 
                Sort_Order: 1, 
                Is_Active: 'Y',
                entity_type: 'WBSJob'
            },
            { 
                Job_Code: 'JC1', 
                WBS_Code: 'WBS2', 
                WBS_Description: 'Description 2', 
                Parent_WBS_Code: 'P2', 
                ET_Code: 'ET2', 
                Leaf_Node_Tag: 'L2', 
                Sort_Order: 2, 
                Is_Active: 'N',
                entity_type: 'WBSJob'
            },
        ];
        const mockJobList = [{ jobCode: 'JC1', jobDescription: 'Job 1' }];

        // This should not throw any errors
        await expect(StoreWbsHierarchyData(mockData, mockJobList)).resolves.not.toThrow();
        
        // Verify that database operations were attempted
        expect(database.write).toHaveBeenCalled();
        expect(database.collections.get).toHaveBeenCalledWith('WBSJob');
    });

    it('should handle empty data arrays', async () => {
        const mockData: any[] = [];
        const mockJobList: any[] = [];

        await expect(StoreWbsHierarchyData(mockData, mockJobList)).resolves.not.toThrow();
    });
}); 