// Mock react-native dimensions at the module level
const mockDimensions = {
  get: jest.fn(() => ({ width: 375, height: 812 }))
};

jest.mock('react-native', () => ({
  Dimensions: mockDimensions,
}));

describe('Scaling Utilities', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Reset to default iPhone X dimensions
        mockDimensions.get.mockReturnValue({ width: 375, height: 812 });
    });
    
    it('should return the correct screen dimensions and scale values for base dimensions', () => {
        // Set base iPhone X dimensions (375x812)
        mockDimensions.get.mockReturnValue({ width: 375, height: 812 });

        // Clear module cache to force re-evaluation
        jest.resetModules();
        const {
            width,
            height,
            scale,
            verticalScale,
            ms,
            moderateVerticalScale,
        } = require('../../../src/utils/Scale/Scaling');

        expect(width).toBe(375);
        expect(height).toBe(812);
        expect(scale(100)).toBe(100); // 375/375 * 100 = 100
        expect(verticalScale(100)).toBe(100); // 812/812 * 100 = 100
        expect(ms(100)).toBe(100); // 100 + (100-100) * 0.5 = 100
        expect(moderateVerticalScale(100)).toBe(100); // 100 + (100-100) * 0.5 = 100
    });

    it('should handle scaling calculations correctly', () => {
        // Test that scaling functions exist and return numbers
        const {
            scale,
            verticalScale,
            ms,
            moderateVerticalScale,
        } = require('../../../src/utils/Scale/Scaling');
        
        expect(typeof scale(100)).toBe('number');
        expect(typeof verticalScale(100)).toBe('number');
        expect(typeof ms(100)).toBe('number');
        expect(typeof moderateVerticalScale(100)).toBe('number');
        
        // Test basic functionality
        expect(scale(0)).toBe(0);
        expect(verticalScale(0)).toBe(0);
        expect(ms(0)).toBe(0);
        expect(moderateVerticalScale(0)).toBe(0);
    });

    it('should use moderate scaling factors correctly', () => {
        const { ms, moderateVerticalScale } = require('../../../src/utils/Scale/Scaling');
        
        // Test with different factors
        expect(typeof ms(100, 0)).toBe('number');
        expect(typeof ms(100, 1)).toBe('number');
        expect(typeof moderateVerticalScale(100, 0)).toBe('number');
        expect(typeof moderateVerticalScale(100, 1)).toBe('number');
        
        // With factor 0, should return original size
        expect(ms(100, 0)).toBe(100);
        expect(moderateVerticalScale(100, 0)).toBe(100);
    });

    it('should export all required scaling functions', () => {
        const scalingModule = require('../../../src/utils/Scale/Scaling');
        
        expect(scalingModule).toHaveProperty('width');
        expect(scalingModule).toHaveProperty('height');
        expect(scalingModule).toHaveProperty('scale');
        expect(scalingModule).toHaveProperty('verticalScale');
        expect(scalingModule).toHaveProperty('ms');
        expect(scalingModule).toHaveProperty('moderateVerticalScale');
        
        expect(typeof scalingModule.scale).toBe('function');
        expect(typeof scalingModule.verticalScale).toBe('function');
        expect(typeof scalingModule.ms).toBe('function');
        expect(typeof scalingModule.moderateVerticalScale).toBe('function');
    });

    it('should handle positive input values', () => {
        const { scale, verticalScale, ms, moderateVerticalScale } = require('../../../src/utils/Scale/Scaling');
        
        // Test with various positive values
        const testValues = [1, 10, 50, 100, 200];
        
        testValues.forEach(value => {
            expect(scale(value)).toBeGreaterThanOrEqual(0);
            expect(verticalScale(value)).toBeGreaterThanOrEqual(0);
            expect(ms(value)).toBeGreaterThanOrEqual(0);
            expect(moderateVerticalScale(value)).toBeGreaterThanOrEqual(0);
        });
    });
}); 