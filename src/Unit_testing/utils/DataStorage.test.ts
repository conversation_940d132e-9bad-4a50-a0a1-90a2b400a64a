import { LoginResponseItem } from '../../../src/model/Auth/LoginData';
import { RolesResponseData } from '../../../src/model/Auth/RolesData';

jest.mock('react-native-mmkv', () => {
    let storage = new Map<string, any>();
    return {
      MMKV: jest.fn().mockImplementation(() => ({
        set: jest.fn((key, value) => {
          storage.set(key, value);
        }),
        getString: jest.fn((key) => {
          return storage.get(key);
        }),
        delete: jest.fn((key) => {
          storage.delete(key);
        }),
        clearAll: jest.fn(() => {
          storage.clear();
        }),
      })),
    };
  });

describe('Storage Utilities', () => {

    beforeEach(() => {
        jest.resetModules();
    });

    describe('Token Storage', () => {
        it('should save and get a token', () => {
            const { saveToken, getToken, storage } = require('../../../src/utils/DataStorage/Storage');
            const token = 'test-token';
            saveToken(token);
            expect(getToken()).toBe(token);
        });

        it('should remove a token', () => {
            const { saveToken, getToken, removeToken, storage } = require('../../../src/utils/DataStorage/Storage');
            const token = 'test-token';
            saveToken(token);
            removeToken();
            expect(getToken()).toBeUndefined();
        });
    });

    describe('User Info Storage', () => {
        it('should save and get user info', () => {
            const { saveUserInfo, getUserInfo, storage } = require('../../../src/utils/DataStorage/Storage');
            const userInfo: LoginResponseItem = { UserID: '1', UserName: 'Test User' };
            saveUserInfo(userInfo);
            expect(getUserInfo()).toEqual(userInfo);
        });

        it('should remove user info', () => {
            const { saveUserInfo, getUserInfo, removeUserInfo, storage } = require('../../../src/utils/DataStorage/Storage');
            const userInfo: LoginResponseItem = { UserID: '1', UserName: 'Test User' };
            saveUserInfo(userInfo);
            removeUserInfo();
            expect(getUserInfo()).toBeNull();
        });
    });

    describe('User Roles Info Storage', () => {
        it('should save and get user roles info', () => {
            const { saveUserRolesInfo, getUserRolesInfo, storage } = require('../../../src/utils/DataStorage/Storage');
            const userRolesInfo: RolesResponseData = { roles: ['admin', 'user'] };
            saveUserRolesInfo(userRolesInfo);
            expect(getUserRolesInfo()).toEqual(userRolesInfo);
        });

        it('should remove user roles info', () => {
            const { saveUserRolesInfo, getUserRolesInfo, removeUserRolesInfo, storage } = require('../../../src/utils/DataStorage/Storage');
            const userRolesInfo: RolesResponseData = { roles: ['admin', 'user'] };
            saveUserRolesInfo(userRolesInfo);
            removeUserRolesInfo();
            expect(getUserRolesInfo()).toBeNull();
        });
    });

    describe('User Last Logged In Date Storage', () => {
        it('should set and get user last logged in date', () => {
            const { setUserLastLoggedInDate, getUserLastLoggedInDate, storage } = require('../../../src/utils/DataStorage/Storage');
            const date = new Date().toISOString();
            setUserLastLoggedInDate(date);
            expect(getUserLastLoggedInDate()).toBe(date);
        });
    });

    describe('clearAllData', () => {
        it('should clear all data from storage', () => {
            const { clearAllData, storage } = require('../../../src/utils/DataStorage/Storage');
            clearAllData();
            expect(storage.clearAll).toHaveBeenCalled();
        });
    });
}); 