import PrintLog from '../../../src/utils/Logger/PrintLog';

describe('PrintLog Utility', () => {

    const originalDev = __DEV__;
    let consoleDebugSpy: jest.SpyInstance;
    let consoleErrorSpy: jest.SpyInstance;

    beforeEach(() => {
        consoleDebugSpy = jest.spyOn(console, 'debug').mockImplementation(() => {});
        consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    });

    afterEach(() => {
        // @ts-ignore
        global.__DEV__ = originalDev;
        consoleDebugSpy.mockRestore();
        consoleErrorSpy.mockRestore();
    });

    describe('debug', () => {
        it('should call console.debug in development', () => {
            // @ts-ignore
            global.__DEV__ = true;
            PrintLog.debug('TestTag', 'Test message', { data: 'test' });
            expect(consoleDebugSpy).toHaveBeenCalledWith('[DEBUG] TestTag:', 'Test message', { data: 'test' });
        });

        it('should not call console.debug in production', () => {
            // @ts-ignore
            global.__DEV__ = false;
            PrintLog.debug('TestTag', 'Test message');
            expect(consoleDebugSpy).not.toHaveBeenCalled();
        });
    });

    describe('error', () => {
        it('should call console.error', () => {
            PrintLog.error('TestTag', 'Test error message', new Error('test error'));
            expect(consoleErrorSpy).toHaveBeenCalledWith('[ERROR] TestTag:', 'Test error message', new Error('test error'));
        });
    });
}); 