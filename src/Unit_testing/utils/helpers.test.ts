import { FormatDate, FormatMonthDate } from '../../../src/utils/helpers';

describe('Date Formatting Utilities', () => {
    describe('FormatDate', () => {
        it('should format a valid ISO string correctly', () => {
            const isoString = '2025-06-19T14:45:00.000Z';
            const expected = {
                shortDate: '19/06/25',
                fullDate: '19/06/2025',
                time: '08:15 PM', // Note: This depends on the test environment's timezone.
            };
            const result = FormatDate(isoString);
            // We will only check date part, as time is timezone sensitive
            expect(result.shortDate).toEqual(expected.shortDate);
            expect(result.fullDate).toEqual(expected.fullDate);
        });

        it('should handle an invalid date string', () => {
            const isoString = 'invalid-date';
            const expected = {
                shortDate: null,
                fullDate: null,
                time: null,
            };
            expect(FormatDate(isoString)).toEqual(expected);
        });

        it('should handle another valid ISO string', () => {
            const isoString = '2023-01-01T00:00:00.000Z';
            const result = FormatDate(isoString);
            expect(result.shortDate).toEqual('01/01/23');
            expect(result.fullDate).toEqual('01/01/2023');
        });
    });

    describe('FormatMonthDate', () => {
        it('should format a valid ISO string correctly with month name', () => {
            const isoString = '2025-06-19T14:45:00.000Z';
            const expected = {
                shortDate: '19/Jun/25',
                fullDate: '19/Jun/2025',
                time: '08:15 PM', // Note: This depends on the test environment's timezone.
            };
            const result = FormatMonthDate(isoString);
            // We will only check date part, as time is timezone sensitive
            expect(result.shortDate).toEqual(expected.shortDate);
            expect(result.fullDate).toEqual(expected.fullDate);
        });

        it('should handle an invalid date string', () => {
            const isoString = 'invalid-date';
            const expected = {
                shortDate: null,
                fullDate: null,
                time: null,
            };
            expect(FormatMonthDate(isoString)).toEqual(expected);
        });

        it('should handle another valid ISO string', () => {
            const isoString = '2023-01-01T00:00:00.000Z';
            const result = FormatMonthDate(isoString);
            expect(result.shortDate).toEqual('01/Jan/23');
            expect(result.fullDate).toEqual('01/Jan/2023');
        });
    });
}); 