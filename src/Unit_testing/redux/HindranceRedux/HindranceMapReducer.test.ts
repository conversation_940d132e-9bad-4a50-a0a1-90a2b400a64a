import hindranceMapReducer from '../../../../src/redux/HindranceRedux/HindranceMapReducer';
import * as types from '../../../../src/redux/HindranceRedux/HindranceMapActionTypes';
import { HindranceMapData } from '../../../../src/redux/HindranceRedux/HindranceMapActions';

describe('HindranceMap reducer', () => {
    const initialState = {
        mapData: null,
        hasMapData: false,
        hindranceDetails: null,
        hindranceMapItems: [],
        isLoading: false,
        error: null,
    };

    it('should return the initial state', () => {
        expect(hindranceMapReducer(undefined, {})).toEqual(initialState);
    });

    it('should handle SET_HINDRANCE_MAP_DATA', () => {
        const payload: HindranceMapData & { hasMapData: boolean } = {
            jobCode: "JC-01",
            UID: "UID-01",
            GapLength: 10.5,
            Latitude_Map: 12.9716,
            Longitude_Map: 77.5946,
            GisPhoto: null,
            date: "2024-07-29",
            Open_Active: "Active",
            Remarks: "Test remarks",
            type: "Test type",
            Classification_Type_Detail_Code: "C-01",
            Latitude_End: 12.9720,
            Longitude_End: "77.5950",
            Start_End_Node: "Node-01",
            Gap_Reason: "Reason-01",
            hasMapData: true,
        };
        const expectedState = {
            ...initialState,
            mapData: payload,
            hasMapData: true,
        };
        expect(hindranceMapReducer(initialState, { type: types.SET_HINDRANCE_MAP_DATA, payload })).toEqual(expectedState);
    });

    it('should handle CLEAR_HINDRANCE_MAP_DATA', () => {
        const currentState = {
            ...initialState,
            mapData: {} as HindranceMapData,
            hasMapData: true,
            hindranceMapItems: [{} as any],
        };
        const expectedState = {
            ...initialState,
            mapData: null,
            hasMapData: false,
            hindranceMapItems: [],
            error: null,
        };
        expect(hindranceMapReducer(currentState, { type: types.CLEAR_HINDRANCE_MAP_DATA })).toEqual(expectedState);
    });

    it('should handle UPDATE_HINDRANCE_MAP_PROGRESS', () => {
        const currentState = {
            ...initialState,
            mapData: {
                jobCode: "JC-01",
                UID: "UID-01",
                GapLength: 10.5,
                Latitude_Map: 12.9716,
                Longitude_Map: 77.5946,
                GisPhoto: null,
                date: "2024-07-29",
                Open_Active: "Active",
                Remarks: "Test remarks",
                type: "Test type",
                Classification_Type_Detail_Code: "C-01",
                Latitude_End: 12.9720,
                Longitude_End: "77.5950",
                Start_End_Node: "Node-01",
                Gap_Reason: "Reason-01"
            },
        };
        const payload = { progressLat: 12.9720, progressLng: 77.5950 };
        const expectedState = {
            ...currentState,
            mapData: {
                ...currentState.mapData,
                progressLat: payload.progressLat,
                progressLng: payload.progressLng,
            },
        };
        expect(hindranceMapReducer(currentState, { type: types.UPDATE_HINDRANCE_MAP_PROGRESS, payload })).toEqual(expectedState);
    });

    it('should handle GET_HINDRANCE_DETAILS', () => {
        const expectedState = {
            ...initialState,
            hindranceDetails: null,
        };
        expect(hindranceMapReducer(initialState, { type: types.GET_HINDRANCE_DETAILS })).toEqual(expectedState);
    });

    it('should handle GET_HINDRANCE_DETAILS_SUCCESS', () => {
        const payload = { id: '1', name: 'Hindrance 1' };
        const expectedState = {
            ...initialState,
            hindranceDetails: payload,
        };
        expect(hindranceMapReducer(initialState, { type: types.GET_HINDRANCE_DETAILS_SUCCESS, payload })).toEqual(expectedState);
    });

    it('should handle GET_HINDRANCE_DETAILS_FAILURE', () => {
        const currentState = {
            ...initialState,
            hindranceDetails: { id: '1', name: 'Hindrance 1' },
        };
        const expectedState = {
            ...currentState,
            hindranceDetails: null,
        };
        expect(hindranceMapReducer(currentState, { type: types.GET_HINDRANCE_DETAILS_FAILURE })).toEqual(expectedState);
    });
}); 