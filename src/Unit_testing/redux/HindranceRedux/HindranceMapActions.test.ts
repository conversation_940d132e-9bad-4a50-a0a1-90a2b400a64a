import * as actions from '../../../../src/redux/HindranceRedux/HindranceMapActions';
import * as types from '../../../../src/redux/HindranceRedux/HindranceMapActionTypes';
import { HindranceMapData } from '../../../../src/redux/HindranceRedux/HindranceMapActions';

describe('HindranceMap actions', () => {
    it('should create an action to get hindrance details', () => {
        const data = { jobCode: '123', type: 'some_type' };
        const expectedAction = {
            type: types.GET_HINDRANCE_DETAILS,
            payload: data,
        };
        expect(actions.getHindranceDetails(data)).toEqual(expectedAction);
    });

    it('should create an action for successful hindrance details fetch', () => {
        const response = { jobCode: '123', type: 'some_type' };
        const expectedAction = {
            type: types.GET_HINDRANCE_DETAILS_SUCCESS,
            payload: response,
        };
        expect(actions.getHindranceDetailsSuccess(response)).toEqual(expectedAction);
    });

    it('should create an action for failed hindrance details fetch', () => {
        const error = 'Error message';
        const expectedAction = {
            type: types.GET_HINDRANCE_DETAILS_FAILURE,
            payload: error,
        };
        expect(actions.getHindranceDetailsFailure(error)).toEqual(expectedAction);
    });

    it('should create an action to set hindrance map data', () => {
        const data: HindranceMapData = {
            jobCode: "JC-01",
            UID: "UID-01",
            GapLength: 10.5,
            Latitude_Map: 12.9716,
            Longitude_Map: 77.5946,
            GisPhoto: null,
            date: "2024-07-29",
            Open_Active: "Active",
            Remarks: "Test remarks",
            type: "Test type",
            Classification_Type_Detail_Code: "C-01",
            Latitude_End: 12.9720,
            Longitude_End: "77.5950",
            Start_End_Node: "Node-01",
            Gap_Reason: "Reason-01"
        };
        const expectedAction = {
            type: types.SET_HINDRANCE_MAP_DATA,
            payload: data,
        };
        expect(actions.setHindranceMapData(data)).toEqual(expectedAction);
    });

    it('should create an action to clear hindrance map data', () => {
        const expectedAction = {
            type: types.CLEAR_HINDRANCE_MAP_DATA,
        };
        expect(actions.clearHindranceMapData()).toEqual(expectedAction);
    });

    it('should create an action to update hindrance map progress', () => {
        const progressLat = 12.9716;
        const progressLng = 77.5946;
        const expectedAction = {
            type: types.UPDATE_HINDRANCE_MAP_PROGRESS,
            payload: { progressLat, progressLng },
        };
        expect(actions.updateHindranceMapProgress(progressLat, progressLng)).toEqual(expectedAction);
    });

    it('should create an action for hindrance update request', () => {
        const payload = { id: '123', status: 'resolved' };
        const cb = jest.fn();
        const expectedAction = {
            type: 'HINDRANCE_UPDATE_REQUEST',
            payload,
            cb,
        };
        expect(actions.hindranceUpdateRequest(payload, cb)).toEqual(expectedAction);
    });

    it('should create an action for successful hindrance update', () => {
        const response = { message: 'Update successful' };
        const expectedAction = {
            type: 'HINDRANCE_UPDATE_SUCCESS',
            payload: response,
        };
        expect(actions.hindranceUpdateSuccess(response)).toEqual(expectedAction);
    });

    it('should create an action for failed hindrance update', () => {
        const error = 'Error message';
        const expectedAction = {
            type: 'HINDRANCE_UPDATE_FAILURE',
            payload: error,
        };
        expect(actions.hindranceUpdateFailure(error)).toEqual(expectedAction);
    });
}); 