import { ApproveReducer } from '../../../../src/redux/DailyProgressApprover/ApproveReducer';
import * as types from '../../../../src/redux/DailyProgressApprover/ApproveActionTypes';
import { ApproveResponseData } from '../../../../src/model/DailyProgressApprover/ApproveData';

describe('Approve reducer', () => {
    const initialState = {
        response: null,
        loading: false,
        error: null,
    };

    it('should return the initial state', () => {
        expect(ApproveReducer(undefined, {})).toEqual(initialState);
    });

    it('should handle APPROVE_REQUEST', () => {
        const expectedState = {
            ...initialState,
            loading: true,
            error: null,
        };
        expect(ApproveReducer(initialState, { type: types.APPROVE_REQUEST })).toEqual(expectedState);
    });

    it('should handle APPROVE_SUCCESS', () => {
        const payload: ApproveResponseData = {
            message: 'Success',
            status: 200,
        };
        const expectedState = {
            ...initialState,
            loading: false,
            response: payload,
        };
        expect(ApproveReducer(initialState, { type: types.APPROVE_SUCCESS, payload })).toEqual(expectedState);
    });

    it('should handle APPROVE_FAILURE', () => {
        const payload = 'Error message';
        const expectedState = {
            ...initialState,
            loading: false,
            error: payload,
        };
        expect(ApproveReducer(initialState, { type: types.APPROVE_FAILURE, payload })).toEqual(expectedState);
    });
}); 