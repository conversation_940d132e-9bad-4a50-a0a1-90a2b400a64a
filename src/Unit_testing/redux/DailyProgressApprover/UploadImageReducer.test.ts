import uploadImageReducer from '../../../../src/redux/DailyProgressApprover/UploadImageReducer';
import * as types from '../../../../src/redux/DailyProgressApprover/UploadImageActionTypes';

describe('UploadImage reducer', () => {
    const initialState = {
        loading: false,
        uploadedIds: [],
        error: null,
    };

    it('should return the initial state', () => {
        expect(uploadImageReducer(undefined, {})).toEqual(initialState);
    });

    it('should handle UPLOAD_IMAGE_REQUEST', () => {
        const expectedState = {
            ...initialState,
            loading: true,
            error: null,
        };
        expect(uploadImageReducer(initialState, { type: types.UPLOAD_IMAGE_REQUEST })).toEqual(expectedState);
    });

    it('should handle UPLOAD_IMAGE_SUCCESS', () => {
        const payload = 'image_id_123';
        const expectedState = {
            ...initialState,
            loading: false,
            uploadedIds: [payload],
            error: null,
        };
        expect(uploadImageReducer(initialState, { type: types.UPLOAD_IMAGE_SUCCESS, payload })).toEqual(expectedState);
    });

    it('should handle UPLOAD_IMAGE_FAILURE', () => {
        const payload = 'Error message';
        const expectedState = {
            ...initialState,
            loading: false,
            error: payload,
        };
        expect(uploadImageReducer(initialState, { type: types.UPLOAD_IMAGE_FAILURE, payload })).toEqual(expectedState);
    });
}); 