import * as actions from '../../../../src/redux/DailyProgressApprover/ApproveActions';
import * as types from '../../../../src/redux/DailyProgressApprover/ApproveActionTypes';
import { ApproveRequestData, ApproveResponseData } from '../../../../src/model/DailyProgressApprover/ApproveData';

describe('Approve actions', () => {
    it('should create an action for approve request', () => {
        const payload: ApproveRequestData = {
            "Jobid": "1",
            "userid": "1",
            "WBSid": "1",
            "Text": "Approved"
        };
        const expectedAction = {
            type: types.APPROVE_REQUEST,
            payload,
        };
        expect(actions.approveRequest(payload)).toEqual(expectedAction);
    });

    it('should create an action for approve success', () => {
        const response: ApproveResponseData = {
            message: 'Success',
            status: 200,
        };
        const expectedAction = {
            type: types.APPROVE_SUCCESS,
            payload: response,
        };
        expect(actions.approveSuccess(response)).toEqual(expectedAction);
    });

    it('should create an action for approve failure', () => {
        const error = 'Error message';
        const expectedAction = {
            type: types.APPROVE_FAILURE,
            payload: error,
        };
        expect(actions.approveFailure(error)).toEqual(expectedAction);
    });
}); 