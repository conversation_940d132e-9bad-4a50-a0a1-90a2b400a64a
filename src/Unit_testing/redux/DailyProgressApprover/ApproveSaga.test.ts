import { runSaga } from 'redux-saga';
import { takeLatest } from 'redux-saga/effects';
import * as api from '../../../../src/services/ApiRequests';
import approveSaga, { handleApprove } from '../../../../src/redux/DailyProgressApprover/ApproveSaga';
import * as actions from '../../../../src/redux/DailyProgressApprover/ApproveActions';
import * as types from '../../../../src/redux/DailyProgressApprover/ApproveActionTypes';

jest.mock('../../../../src/services/ApiRequests', () => ({
    postDataWithBodyForDownload: jest.fn(),
}));

const mockedApi = api as jest.Mocked<typeof api>;

describe('Approve Saga', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('handleApprove', () => {
        it('should handle successful approve', async () => {
            const dispatched: any[] = [];
            const payload = { Jobid: '1', userid: '1', WBSid: '1', Text: 'Approved' };
            const action = { type: types.APPROVE_REQUEST, payload };
            const response = { Table: [{ Msg: 'OK' }] };

            (mockedApi.postDataWithBodyForDownload as jest.Mock).mockImplementationOnce((_url, _body, success) => {
                success(response);
            });

            await runSaga({
                dispatch: (event) => dispatched.push(event),
            }, handleApprove, action).toPromise();

            expect(dispatched).toEqual([actions.approveSuccess(response)]);
        });

        it('should handle failed approve', async () => {
            const dispatched: any[] = [];
            const payload = { Jobid: '1', userid: '1', WBSid: '1', Text: 'Approved' };
            const action = { type: types.APPROVE_REQUEST, payload };
            const error = new Error('API Error');

            (mockedApi.postDataWithBodyForDownload as jest.Mock).mockImplementationOnce((_url, _body, _success, failure) => {
                failure(error);
            });

            await runSaga({
                dispatch: (event) => dispatched.push(event),
            }, handleApprove, action).toPromise();

            expect(dispatched).toEqual([actions.approveFailure(error.message)]);
        });

        it('should handle approve failure from response', async () => {
            const dispatched: any[] = [];
            const payload = { Jobid: '1', userid: '1', WBSid: '1', Text: 'Approved' };
            const action = { type: types.APPROVE_REQUEST, payload };
            const response = { Table: [{ Msg: 'Error' }] };

            (mockedApi.postDataWithBodyForDownload as jest.Mock).mockImplementationOnce((_url, _body, success) => {
                success(response);
            });

            await runSaga({
                dispatch: (event) => dispatched.push(event),
            }, handleApprove, action).toPromise();

            expect(dispatched).toEqual([actions.approveFailure(response.Table[0].Msg)]);
        });
    });

    describe('approveSaga (watcher)', () => {
        it('should watch for APPROVE_REQUEST action', () => {
            const gen = approveSaga();
            expect(gen.next().value).toEqual(takeLatest(types.APPROVE_REQUEST, handleApprove));
            expect(gen.next().done).toBe(true);
        });
    });
}); 