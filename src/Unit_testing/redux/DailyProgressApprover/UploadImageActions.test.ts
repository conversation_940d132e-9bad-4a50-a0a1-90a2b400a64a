import * as actions from '../../../../src/redux/DailyProgressApprover/UploadImageActions';
import * as types from '../../../../src/redux/DailyProgressApprover/UploadImageActionTypes';

describe('UploadImage actions', () => {
    it('should create an action for upload image request', () => {
        const payload = { file: 'test.jpg' };
        const expectedAction = {
            type: types.UPLOAD_IMAGE_REQUEST,
            payload,
        };
        expect(actions.uploadImageRequest(payload)).toEqual(expectedAction);
    });

    it('should create an action for upload image success', () => {
        const response = { url: 'http://example.com/test.jpg' };
        const expectedAction = {
            type: types.UPLOAD_IMAGE_SUCCESS,
            response,
        };
        expect(actions.uploadImageSuccess(response)).toEqual(expectedAction);
    });

    it('should create an action for upload image failure', () => {
        const error = 'Error message';
        const expectedAction = {
            type: types.UPLOAD_IMAGE_FAILURE,
            error,
        };
        expect(actions.uploadImageFailure(error)).toEqual(expectedAction);
    });
}); 