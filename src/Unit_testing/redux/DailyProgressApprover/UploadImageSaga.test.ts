import { runSaga } from 'redux-saga';
import { takeLatest } from 'redux-saga/effects';
import * as api from '../../../../src/services/ApiRequests';
import watchUploadImage, { uploadImageSaga } from '../../../../src/redux/DailyProgressApprover/UploadImageSaga';
import * as actions from '../../../../src/redux/DailyProgressApprover/UploadImageActions';
import * as types from '../../../../src/redux/DailyProgressApprover/UploadImageActionTypes';

jest.mock('../../../../src/services/ApiRequests', () => ({
    postDataWithBodyForImageUpload: jest.fn(),
}));

const mockedApi = api as jest.Mocked<typeof api>;

describe('UploadImage Saga', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('uploadImageSaga', () => {
        it('should handle successful image upload', async () => {
            const dispatched: any[] = [];
            const payload = { file: 'test.jpg' };
            const action = { type: types.UPLOAD_IMAGE_REQUEST, payload };
            const response = { url: 'http://example.com/test.jpg' };

            (mockedApi.postDataWithBodyForImageUpload as jest.Mock).mockImplementationOnce((_url, _body, success) => {
                success(response);
            });

            await runSaga({
                dispatch: (event) => dispatched.push(event),
            }, uploadImageSaga, action).toPromise();

            expect(dispatched).toEqual([actions.uploadImageSuccess(response)]);
        });

        it('should handle failed image upload', async () => {
            const dispatched: any[] = [];
            const payload = { file: 'test.jpg' };
            const action = { type: types.UPLOAD_IMAGE_REQUEST, payload };
            const error = new Error('API Error');

            (mockedApi.postDataWithBodyForImageUpload as jest.Mock).mockImplementationOnce((_url, _body, _success, failure) => {
                failure(error);
            });

            await runSaga({
                dispatch: (event) => dispatched.push(event),
            }, uploadImageSaga, action).toPromise();

            expect(dispatched).toEqual([actions.uploadImageFailure(error)]);
        });
    });

    describe('watchUploadImage (watcher)', () => {
        it('should watch for UPLOAD_IMAGE_REQUEST action', () => {
            const gen = watchUploadImage();
            expect(gen.next().value).toEqual(takeLatest(types.UPLOAD_IMAGE_REQUEST, uploadImageSaga));
            expect(gen.next().done).toBe(true);
        });
    });
}); 