import * as actions from '../../../../../src/redux/DailyProgressRedux/Bookmark/BookmarkActions';
import * as types from '../../../../../src/redux/DailyProgressRedux/Bookmark/BookmarkActionTypes';
import { BookmarksRequestData, BookmarksResponseData } from '../../../../../src/model/DailyProgress/BookmarksData';

describe('Bookmark actions', () => {
    it('should create an action for bookmark request', () => {
        const payload: BookmarksRequestData = {
            userId: '1',
            jobId: '1',
        };
        const expectedAction = {
            type: types.BOOKMARK_REQUEST,
            payload,
        };
        expect(actions.bookmarkRequest(payload)).toEqual(expectedAction);
    });

    it('should create an action for bookmark success', () => {
        const response: BookmarksResponseData = {
            message: 'Success',
            status: 200,
        };
        const expectedAction = {
            type: types.BOOKMARK_SUCCESS,
            payload: response,
        };
        expect(actions.bookmarkSuccess(response)).toEqual(expectedAction);
    });

    it('should create an action for bookmark failure', () => {
        const error = 'Error message';
        const expectedAction = {
            type: types.BOOKMARK_FAILURE,
            payload: error,
        };
        expect(actions.bookmarkFailure(error)).toEqual(expectedAction);
    });

    it('should create an action to clear bookmark data', () => {
        const expectedAction = {
            type: 'CLEAR_BOOKMARK_DATA',
        };
        expect(actions.clearBookmarkData()).toEqual(expectedAction);
    });
}); 