import { BookmarksReducer } from '../../../../../src/redux/DailyProgressRedux/Bookmark/BookmarkReducer';
import * as types from '../../../../../src/redux/DailyProgressRedux/Bookmark/BookmarkActionTypes';
import { BookmarksResponseData } from '../../../../../src/model/DailyProgress/BookmarksData';

describe('BookmarksReducer', () => {
    const initialState = {
        bookmarksData: null,
        bookmarksLoading: false,
        bookmarksError: null,
    };

    it('should return the initial state', () => {
        expect(BookmarksReducer(undefined, {})).toEqual(initialState);
    });

    it('should handle BOOKMARK_REQUEST', () => {
        const expectedState = {
            ...initialState,
            bookmarksLoading: true,
        };
        expect(BookmarksReducer(initialState, { type: types.BOOKMARK_REQUEST })).toEqual(expectedState);
    });

    it('should handle BOOKMARK_SUCCESS', () => {
        const payload: BookmarksResponseData = {
            message: 'Success',
            status: 200,
        };
        const expectedState = {
            ...initialState,
            bookmarksLoading: false,
            bookmarksData: payload,
        };
        expect(BookmarksReducer(initialState, { type: types.BOOKMARK_SUCCESS, payload })).toEqual(expectedState);
    });

    it('should handle BOOKMARK_FAILURE', () => {
        const payload = 'Error message';
        const expectedState = {
            ...initialState,
            bookmarksLoading: false,
            bookmarksError: payload,
        };
        expect(BookmarksReducer(initialState, { type: types.BOOKMARK_FAILURE, payload })).toEqual(expectedState);
    });

    it('should handle CLEAR_BOOKMARK_DATA', () => {
        const currentState = {
            ...initialState,
            bookmarksData: { message: 'some data', status: 200 }
        };
        const expectedState = {
            ...currentState,
            bookmarksData: null,
            bookmarksLoading: false,
        };
        expect(BookmarksReducer(currentState, { type: 'CLEAR_BOOKMARK_DATA' })).toEqual(expectedState);
    });
}); 