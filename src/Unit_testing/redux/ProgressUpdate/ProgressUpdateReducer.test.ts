import progressUpdateReducer from '../../../redux/ProgressUpdate/ProgressUpdateReducer';
import {
  PROGRESS_UPDATE_REQUEST,
  PROGRESS_UPDATE_SUCCESS,
  PROGRESS_UPDATE_FAILURE,
} from '../../../redux/ProgressUpdate/ProgressUpdateActionTypes';
import { ProgressUpdateResponseData } from '../../../model/DailyProgress/DailyProgressData';

describe('ProgressUpdate Reducer', () => {
  const initialState = {
    loading: false,
    error: null,
    response: null,
  };

  describe('Initial State', () => {
    it('should return initial state when no action is passed', () => {
      const result = progressUpdateReducer(undefined, {} as any);
      expect(result).toEqual(initialState);
    });

    it('should have correct initial state structure', () => {
      const result = progressUpdateReducer(undefined, {} as any);
      expect(result).toHaveProperty('loading');
      expect(result).toHaveProperty('error');
      expect(result).toHaveProperty('response');
    });

    it('should have loading false initially', () => {
      const result = progressUpdateReducer(undefined, {} as any);
      expect(result.loading).toBe(false);
    });

    it('should have null error initially', () => {
      const result = progressUpdateReducer(undefined, {} as any);
      expect(result.error).toBeNull();
    });

    it('should have null response initially', () => {
      const result = progressUpdateReducer(undefined, {} as any);
      expect(result.response).toBeNull();
    });
  });

  describe('PROGRESS_UPDATE_REQUEST', () => {
    it('should handle PROGRESS_UPDATE_REQUEST action', () => {
      const action = {
        type: PROGRESS_UPDATE_REQUEST,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.loading).toBe(true);
      expect(result.error).toBeNull();
      expect(result.response).toBeNull();
    });

    it('should reset error on progress update request', () => {
      const stateWithError = {
        ...initialState,
        error: 'Previous error',
      };
      const action = {
        type: PROGRESS_UPDATE_REQUEST,
      };
      const result = progressUpdateReducer(stateWithError, action);
      expect(result.error).toBeNull();
    });

    it('should preserve existing response during request', () => {
      const existingResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Previous update',
        data: { id: 123 },
      };
      const stateWithResponse = {
        ...initialState,
        response: existingResponse,
        loading: false,
      };
      const action = {
        type: PROGRESS_UPDATE_REQUEST,
      };
      const result = progressUpdateReducer(stateWithResponse, action);
      expect(result.response).toEqual(existingResponse);
      expect(result.loading).toBe(true);
    });

    it('should maintain immutability', () => {
      const action = {
        type: PROGRESS_UPDATE_REQUEST,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result).not.toBe(initialState);
    });
  });

  describe('PROGRESS_UPDATE_SUCCESS', () => {
    const mockSuccessResponse: ProgressUpdateResponseData = {
      success: true,
      message: 'Progress updated successfully',
      data: { id: 123, status: 'completed' },
    };

    it('should handle PROGRESS_UPDATE_SUCCESS action', () => {
      const action = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: mockSuccessResponse,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.loading).toBe(false);
      expect(result.error).toBeNull();
      expect(result.response).toEqual(mockSuccessResponse);
    });

    it('should update response from payload', () => {
      const action = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: mockSuccessResponse,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.response).toEqual(mockSuccessResponse);
      expect(result.response?.success).toBe(true);
      expect(result.response?.message).toBe('Progress updated successfully');
    });

    it('should clear loading state on success', () => {
      const loadingState = {
        ...initialState,
        loading: true,
      };
      const action = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: mockSuccessResponse,
      };
      const result = progressUpdateReducer(loadingState, action);
      expect(result.loading).toBe(false);
    });

    it('should clear error on success', () => {
      const errorState = {
        ...initialState,
        error: 'Previous error',
      };
      const action = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: mockSuccessResponse,
      };
      const result = progressUpdateReducer(errorState, action);
      expect(result.error).toBeNull();
    });

    it('should handle success with minimal response data', () => {
      const minimalResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Updated',
      };
      const action = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: minimalResponse,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.response).toEqual(minimalResponse);
      expect(result.response?.data).toBeUndefined();
    });

    it('should handle success with complex response data', () => {
      const complexResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Complex update completed',
        data: {
          progress: {
            total: 100,
            completed: 75,
            remaining: 25,
          },
          attachments: ['file1.jpg', 'file2.pdf'],
          timestamp: '2024-01-15T10:30:00Z',
        },
      };
      const action = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: complexResponse,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.response?.data.progress.completed).toBe(75);
      expect(result.response?.data.attachments).toHaveLength(2);
    });

    it('should handle success with null data', () => {
      const nullDataResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Success with null data',
        data: null,
      };
      const action = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: nullDataResponse,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.response?.data).toBeNull();
    });

    it('should replace previous response on new success', () => {
      const previousResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Previous update',
        data: { id: 999 },
      };
      const stateWithPreviousResponse = {
        ...initialState,
        response: previousResponse,
        loading: false,
      };
      const action = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: mockSuccessResponse,
      };
      const result = progressUpdateReducer(stateWithPreviousResponse, action);
      expect(result.response).toEqual(mockSuccessResponse);
      expect(result.response?.data.id).toBe(123);
    });

    it('should maintain immutability', () => {
      const action = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: mockSuccessResponse,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result).not.toBe(initialState);
      expect(result.response).toBe(mockSuccessResponse); // Direct assignment
    });
  });

  describe('PROGRESS_UPDATE_FAILURE', () => {
    it('should handle PROGRESS_UPDATE_FAILURE action', () => {
      const errorMessage = 'Failed to update progress';
      const action = {
        type: PROGRESS_UPDATE_FAILURE,
        payload: errorMessage,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.loading).toBe(false);
      expect(result.error).toBe(errorMessage);
      expect(result.response).toBeNull();
    });

    it('should clear loading state on failure', () => {
      const loadingState = {
        ...initialState,
        loading: true,
      };
      const action = {
        type: PROGRESS_UPDATE_FAILURE,
        payload: 'Error message',
      };
      const result = progressUpdateReducer(loadingState, action);
      expect(result.loading).toBe(false);
    });

    it('should preserve existing response on failure', () => {
      const existingResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Previous success',
        data: { id: 123 },
      };
      const stateWithResponse = {
        ...initialState,
        response: existingResponse,
      };
      const action = {
        type: PROGRESS_UPDATE_FAILURE,
        payload: 'Network error',
      };
      const result = progressUpdateReducer(stateWithResponse, action);
      expect(result.response).toEqual(existingResponse);
    });

    it('should handle empty error message', () => {
      const action = {
        type: PROGRESS_UPDATE_FAILURE,
        payload: '',
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.error).toBe('');
    });

    it('should handle null error message', () => {
      const action = {
        type: PROGRESS_UPDATE_FAILURE,
        payload: null,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.error).toBeNull();
    });

    it('should handle undefined error message', () => {
      const action = {
        type: PROGRESS_UPDATE_FAILURE,
        payload: undefined,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.error).toBeUndefined();
    });

    it('should handle network error message', () => {
      const networkError = 'Network request failed - timeout';
      const action = {
        type: PROGRESS_UPDATE_FAILURE,
        payload: networkError,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.error).toBe(networkError);
    });

    it('should handle validation error message', () => {
      const validationError = 'Invalid progress data format';
      const action = {
        type: PROGRESS_UPDATE_FAILURE,
        payload: validationError,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.error).toBe(validationError);
    });

    it('should handle server error message', () => {
      const serverError = 'Internal server error - 500';
      const action = {
        type: PROGRESS_UPDATE_FAILURE,
        payload: serverError,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.error).toBe(serverError);
    });

    it('should replace previous error with new error', () => {
      const stateWithError = {
        ...initialState,
        error: 'Previous error',
      };
      const newError = 'New error occurred';
      const action = {
        type: PROGRESS_UPDATE_FAILURE,
        payload: newError,
      };
      const result = progressUpdateReducer(stateWithError, action);
      expect(result.error).toBe(newError);
    });

    it('should maintain immutability', () => {
      const action = {
        type: PROGRESS_UPDATE_FAILURE,
        payload: 'Error',
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result).not.toBe(initialState);
    });
  });

  describe('Unknown Actions', () => {
    it('should return current state for unknown action types', () => {
      const currentState = {
        loading: true,
        error: 'Some error',
        response: {
          success: true,
          message: 'Previous response',
          data: { id: 123 },
        },
      };
      const unknownAction = {
        type: 'UNKNOWN_ACTION',
        payload: 'some payload',
      };
      const result = progressUpdateReducer(currentState, unknownAction);
      expect(result).toEqual(currentState);
    });

    it('should return current state reference for unknown actions', () => {
      const currentState = {
        loading: false,
        error: 'Some error',
        response: null,
      };
      const unknownAction = {
        type: 'RANDOM_ACTION',
      };
      const result = progressUpdateReducer(currentState, unknownAction);
      expect(result).toBe(currentState);
    });

    it('should handle action without type', () => {
      const currentState = {
        loading: true,
        error: null,
        response: null,
      };
      const invalidAction = {} as any;
      const result = progressUpdateReducer(currentState, invalidAction);
      expect(result).toBe(currentState);
    });

    it('should handle action with undefined type', () => {
      const currentState = {
        loading: false,
        error: 'Error',
        response: null,
      };
      const undefinedAction = { type: undefined };
      const result = progressUpdateReducer(currentState, undefinedAction);
      expect(result).toBe(currentState);
    });
  });

  describe('State Transitions', () => {
    it('should handle complete success flow', () => {
      const mockResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Progress updated successfully',
        data: { id: 123 },
      };

      let state = progressUpdateReducer(initialState, {
        type: PROGRESS_UPDATE_REQUEST,
      });
      expect(state.loading).toBe(true);
      expect(state.error).toBeNull();

      state = progressUpdateReducer(state, {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: mockResponse,
      });
      expect(state.loading).toBe(false);
      expect(state.response).toEqual(mockResponse);
      expect(state.error).toBeNull();
    });

    it('should handle complete failure flow', () => {
      let state = progressUpdateReducer(initialState, {
        type: PROGRESS_UPDATE_REQUEST,
      });
      expect(state.loading).toBe(true);
      expect(state.error).toBeNull();

      state = progressUpdateReducer(state, {
        type: PROGRESS_UPDATE_FAILURE,
        payload: 'Network error',
      });
      expect(state.loading).toBe(false);
      expect(state.response).toBeNull();
      expect(state.error).toBe('Network error');
    });

    it('should handle retry after failure', () => {
      let state = progressUpdateReducer(initialState, {
        type: PROGRESS_UPDATE_FAILURE,
        payload: 'First error',
      });
      expect(state.error).toBe('First error');

      state = progressUpdateReducer(state, {
        type: PROGRESS_UPDATE_REQUEST,
      });
      expect(state.loading).toBe(true);
      expect(state.error).toBeNull();
    });

    it('should handle success after previous failure', () => {
      const mockResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Recovery success',
        data: { id: 123 },
      };

      let state = progressUpdateReducer(initialState, {
        type: PROGRESS_UPDATE_FAILURE,
        payload: 'Network error',
      });
      expect(state.error).toBe('Network error');

      state = progressUpdateReducer(state, {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: mockResponse,
      });
      expect(state.loading).toBe(false);
      expect(state.response).toEqual(mockResponse);
      expect(state.error).toBeNull();
    });
  });

  describe('Type Safety', () => {
    it('should handle action with correct payload type', () => {
      const mockResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Progress updated successfully',
        data: { id: 123 },
      };
      const action = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: mockResponse,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.response).toEqual(mockResponse);
    });

    it('should handle action with missing payload properties', () => {
      const action = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: {
          success: true,
          // Missing message property
        } as any,
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result.response?.success).toBe(true);
    });
  });

  describe('State Immutability', () => {
    it('should not mutate the original state', () => {
      const originalState = {
        loading: false,
        error: null,
        response: null,
      };
      const action = {
        type: PROGRESS_UPDATE_REQUEST,
      };

      const newState = progressUpdateReducer(originalState, action);

      expect(newState).not.toBe(originalState);
      expect(originalState).toEqual({
        loading: false,
        error: null,
        response: null,
      });
    });

    it('should create new state object for each action', () => {
      const mockResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Success',
        data: { id: 123 },
      };

      const action1 = {
        type: PROGRESS_UPDATE_REQUEST,
      };
      const action2 = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: mockResponse,
      };

      const state1 = progressUpdateReducer(initialState, action1);
      const state2 = progressUpdateReducer(state1, action2);

      expect(state1).not.toBe(initialState);
      expect(state2).not.toBe(state1);
      expect(state2).not.toBe(initialState);
    });

    it('should preserve immutability with complex state', () => {
      const complexState = {
        loading: true,
        error: 'Some error',
        response: {
          success: true,
          message: 'Previous response',
          data: { id: 123, nested: { value: 'test' } },
        },
      };
      const action = {
        type: PROGRESS_UPDATE_REQUEST,
      };

      const newState = progressUpdateReducer(complexState, action);

      expect(newState).not.toBe(complexState);
      expect(complexState.response?.data.nested.value).toBe('test');
      expect(complexState.loading).toBe(true);
    });
  });

  describe('State Structure', () => {
    it('should maintain correct state structure after actions', () => {
      const requestAction = {
        type: PROGRESS_UPDATE_REQUEST,
      };
      const successAction = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: {
          success: true,
          message: 'Success',
        },
      };

      let state = progressUpdateReducer(initialState, requestAction);
      expect(Object.keys(state)).toEqual(['loading', 'error', 'response']);

      state = progressUpdateReducer(state, successAction);
      expect(Object.keys(state)).toEqual(['loading', 'error', 'response']);
    });

    it('should not add extra properties to state', () => {
      const action = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: {
          success: true,
          message: 'Success',
        },
        extraProperty: 'should be ignored',
      };
      const result = progressUpdateReducer(initialState, action);
      expect(result).not.toHaveProperty('extraProperty');
    });
  });
}); 