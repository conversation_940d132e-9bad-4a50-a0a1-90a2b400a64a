import {
  ProgressUpdateRequest,
  ProgressUpdateSuccess,
  ProgressUpdateFailure,
} from '../../../redux/ProgressUpdate/ProgressUpdateActions';
import {
  PROGRESS_UPDATE_REQUEST,
  PROGRESS_UPDATE_SUCCESS,
  PROGRESS_UPDATE_FAILURE,
} from '../../../redux/ProgressUpdate/ProgressUpdateActionTypes';
import {
  ProgressUpdateRequestBody,
  ProgressUpdateResponseData,
  ActualList,
  Attachment,
} from '../../../model/DailyProgress/DailyProgressData';

describe('ProgressUpdate Actions', () => {
  const mockCallback = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('ProgressUpdateRequest', () => {
    const mockActualList: ActualList[] = [
      {
        WBS: 'WBS001',
        TaskCode: 'TASK001',
        ADate: '2024-01-15',
        Quantity: '100',
        Manpower: '5',
        Remarks: 'Progress completed',
        Tasktype: 'Construction',
        Is_Approved: 'Y',
        Latitude: 12.9716,
        Longitude: 77.5946,
      },
    ];

    const mockAttachments: Attachment[] = [
      {
        WBS: 'WBS001',
        TaskCode: 'TASK001',
        ADate: '2024-01-15',
        Tasktype: 'Construction',
        SiteUrl: 'https://example.com/file1.jpg',
        Unique: 'unique-id-123',
      },
    ];

    const mockProgressUpdateRequest: ProgressUpdateRequestBody = {
      jobCode: 'JOB001',
      UID: 'user123',
      Type: 'daily',
      Notification_Desc: 'Daily progress update',
      Quantity: 100,
      uOM: 'meters',
      manPower: 5,
      ActualList: mockActualList,
      Attachments: mockAttachments,
      cb: mockCallback,
    };

    it('should create ProgressUpdateRequest action', () => {
      const expectedAction = {
        type: PROGRESS_UPDATE_REQUEST,
        payload: mockProgressUpdateRequest,
      };
      expect(ProgressUpdateRequest(mockProgressUpdateRequest)).toEqual(expectedAction);
    });

    it('should return action with correct type', () => {
      const action = ProgressUpdateRequest(mockProgressUpdateRequest);
      expect(action.type).toBe(PROGRESS_UPDATE_REQUEST);
    });

    it('should include request payload', () => {
      const action = ProgressUpdateRequest(mockProgressUpdateRequest);
      expect(action.payload).toEqual(mockProgressUpdateRequest);
      expect(action.payload.jobCode).toBe('JOB001');
      expect(action.payload.UID).toBe('user123');
      expect(action.payload.cb).toBe(mockCallback);
    });

    it('should handle request with different job codes', () => {
      const differentRequest: ProgressUpdateRequestBody = {
        ...mockProgressUpdateRequest,
        jobCode: 'JOB002',
        UID: 'user456',
      };
      const action = ProgressUpdateRequest(differentRequest);
      expect(action.payload.jobCode).toBe('JOB002');
      expect(action.payload.UID).toBe('user456');
    });

    it('should handle request with empty ActualList', () => {
      const emptyActualListRequest: ProgressUpdateRequestBody = {
        ...mockProgressUpdateRequest,
        ActualList: [],
      };
      const action = ProgressUpdateRequest(emptyActualListRequest);
      expect(action.payload.ActualList).toHaveLength(0);
    });

    it('should handle request with empty Attachments', () => {
      const emptyAttachmentsRequest: ProgressUpdateRequestBody = {
        ...mockProgressUpdateRequest,
        Attachments: [],
      };
      const action = ProgressUpdateRequest(emptyAttachmentsRequest);
      expect(action.payload.Attachments).toHaveLength(0);
    });

    it('should handle request with multiple ActualList items', () => {
      const multipleActualList: ActualList[] = [
        ...mockActualList,
        {
          WBS: 'WBS002',
          TaskCode: 'TASK002',
          ADate: '2024-01-16',
          Quantity: '50',
          Manpower: '3',
          Remarks: 'Second task progress',
          Tasktype: 'Installation',
          Is_Approved: 'N',
          Latitude: 12.9726,
          Longitude: 77.5956,
        },
      ];
      const multipleItemsRequest: ProgressUpdateRequestBody = {
        ...mockProgressUpdateRequest,
        ActualList: multipleActualList,
      };
      const action = ProgressUpdateRequest(multipleItemsRequest);
      expect(action.payload.ActualList).toHaveLength(2);
    });

    it('should handle request with zero quantity', () => {
      const zeroQuantityRequest: ProgressUpdateRequestBody = {
        ...mockProgressUpdateRequest,
        Quantity: 0,
      };
      const action = ProgressUpdateRequest(zeroQuantityRequest);
      expect(action.payload.Quantity).toBe(0);
    });

    it('should handle request with special characters in description', () => {
      const specialCharsRequest: ProgressUpdateRequestBody = {
        ...mockProgressUpdateRequest,
        Notification_Desc: 'Progress with special chars: @#$%^&*()',
      };
      const action = ProgressUpdateRequest(specialCharsRequest);
      expect(action.payload.Notification_Desc).toBe('Progress with special chars: @#$%^&*()');
    });

    it('should handle different types', () => {
      const types = ['daily', 'weekly', 'monthly', 'milestone'];
      types.forEach(type => {
        const request: ProgressUpdateRequestBody = {
          ...mockProgressUpdateRequest,
          Type: type,
        };
        const action = ProgressUpdateRequest(request);
        expect(action.payload.Type).toBe(type);
      });
    });

    it('should handle different UOMs', () => {
      const uoms = ['meters', 'feet', 'cubic meters', 'pieces', 'hours'];
      uoms.forEach(uom => {
        const request: ProgressUpdateRequestBody = {
          ...mockProgressUpdateRequest,
          uOM: uom,
        };
        const action = ProgressUpdateRequest(request);
        expect(action.payload.uOM).toBe(uom);
      });
    });

    it('should create consistent action structure', () => {
      const action = ProgressUpdateRequest(mockProgressUpdateRequest);
      expect(action).toHaveProperty('type');
      expect(action).toHaveProperty('payload');
      expect(Object.keys(action)).toEqual(['type', 'payload']);
    });

    it('should be a pure function', () => {
      const action1 = ProgressUpdateRequest(mockProgressUpdateRequest);
      const action2 = ProgressUpdateRequest(mockProgressUpdateRequest);
      expect(action1).toEqual(action2);
    });
  });

  describe('ProgressUpdateSuccess', () => {
    const mockSuccessResponse: ProgressUpdateResponseData = {
      success: true,
      message: 'Progress updated successfully',
      data: { id: 123, status: 'completed' },
    };

    it('should create ProgressUpdateSuccess action', () => {
      const expectedAction = {
        type: PROGRESS_UPDATE_SUCCESS,
        payload: mockSuccessResponse,
      };
      expect(ProgressUpdateSuccess(mockSuccessResponse)).toEqual(expectedAction);
    });

    it('should return action with correct type', () => {
      const action = ProgressUpdateSuccess(mockSuccessResponse);
      expect(action.type).toBe(PROGRESS_UPDATE_SUCCESS);
    });

    it('should include response payload', () => {
      const action = ProgressUpdateSuccess(mockSuccessResponse);
      expect(action.payload).toEqual(mockSuccessResponse);
      expect(action.payload.success).toBe(true);
      expect(action.payload.message).toBe('Progress updated successfully');
    });

    it('should handle success response with no data', () => {
      const noDataResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Updated successfully',
      };
      const action = ProgressUpdateSuccess(noDataResponse);
      expect(action.payload.data).toBeUndefined();
    });

    it('should handle success response with complex data', () => {
      const complexDataResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Complex update completed',
        data: {
          progress: {
            total: 100,
            completed: 75,
            remaining: 25,
          },
          attachments: ['file1.jpg', 'file2.pdf'],
          timestamp: '2024-01-15T10:30:00Z',
        },
      };
      const action = ProgressUpdateSuccess(complexDataResponse);
      expect(action.payload.data.progress.completed).toBe(75);
      expect(action.payload.data.attachments).toHaveLength(2);
    });

    it('should handle success response with empty message', () => {
      const emptyMessageResponse: ProgressUpdateResponseData = {
        success: true,
        message: '',
      };
      const action = ProgressUpdateSuccess(emptyMessageResponse);
      expect(action.payload.message).toBe('');
    });

    it('should handle success response with null data', () => {
      const nullDataResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Success with null data',
        data: null,
      };
      const action = ProgressUpdateSuccess(nullDataResponse);
      expect(action.payload.data).toBeNull();
    });

    it('should create consistent action structure', () => {
      const action = ProgressUpdateSuccess(mockSuccessResponse);
      expect(action).toHaveProperty('type');
      expect(action).toHaveProperty('payload');
      expect(Object.keys(action)).toEqual(['type', 'payload']);
    });
  });

  describe('ProgressUpdateFailure', () => {
    it('should create ProgressUpdateFailure action with error message', () => {
      const errorMessage = 'Failed to update progress';
      const expectedAction = {
        type: PROGRESS_UPDATE_FAILURE,
        payload: errorMessage,
      };
      expect(ProgressUpdateFailure(errorMessage)).toEqual(expectedAction);
    });

    it('should return action with correct type', () => {
      const action = ProgressUpdateFailure('Error message');
      expect(action.type).toBe(PROGRESS_UPDATE_FAILURE);
    });

    it('should include error message in payload', () => {
      const errorMessage = 'Network error occurred';
      const action = ProgressUpdateFailure(errorMessage);
      expect(action.payload).toBe(errorMessage);
    });

    it('should handle empty error message', () => {
      const action = ProgressUpdateFailure('');
      expect(action.payload).toBe('');
    });

    it('should handle network error message', () => {
      const networkError = 'Network request failed - timeout';
      const action = ProgressUpdateFailure(networkError);
      expect(action.payload).toBe(networkError);
    });

    it('should handle validation error message', () => {
      const validationError = 'Invalid progress data format';
      const action = ProgressUpdateFailure(validationError);
      expect(action.payload).toBe(validationError);
    });

    it('should handle server error message', () => {
      const serverError = 'Internal server error - 500';
      const action = ProgressUpdateFailure(serverError);
      expect(action.payload).toBe(serverError);
    });

    it('should handle authentication error message', () => {
      const authError = 'Authentication failed - invalid token';
      const action = ProgressUpdateFailure(authError);
      expect(action.payload).toBe(authError);
    });

    it('should handle long error message', () => {
      const longError = 'A'.repeat(1000) + ' - progress update error';
      const action = ProgressUpdateFailure(longError);
      expect(action.payload).toBe(longError);
    });

    it('should handle error message with special characters', () => {
      const specialError = 'Error: @#$%^&*()_+{}[]|\\:";\'<>?,./`~';
      const action = ProgressUpdateFailure(specialError);
      expect(action.payload).toBe(specialError);
    });

    it('should create consistent action structure', () => {
      const action = ProgressUpdateFailure('Test error');
      expect(action).toHaveProperty('type');
      expect(action).toHaveProperty('payload');
      expect(Object.keys(action)).toEqual(['type', 'payload']);
    });
  });

  describe('Action Type Constants', () => {
    it('should have correct action type constants', () => {
      expect(PROGRESS_UPDATE_REQUEST).toBe('PROGRESS_UPDATE_REQUEST');
      expect(PROGRESS_UPDATE_SUCCESS).toBe('PROGRESS_UPDATE_SUCCESS');
      expect(PROGRESS_UPDATE_FAILURE).toBe('PROGRESS_UPDATE_FAILURE');
    });

    it('should have different action type constants', () => {
      expect(PROGRESS_UPDATE_REQUEST).not.toBe(PROGRESS_UPDATE_SUCCESS);
      expect(PROGRESS_UPDATE_SUCCESS).not.toBe(PROGRESS_UPDATE_FAILURE);
      expect(PROGRESS_UPDATE_FAILURE).not.toBe(PROGRESS_UPDATE_REQUEST);
    });
  });

  describe('Action Creators Integration', () => {
    it('should create different action types for different creators', () => {
      const requestAction = ProgressUpdateRequest({
        jobCode: 'JOB001',
        UID: 'user123',
        Type: 'daily',
        Notification_Desc: 'Test progress',
        Quantity: 100,
        uOM: 'meters',
        manPower: 5,
        ActualList: [],
        Attachments: [],
        cb: mockCallback,
      });
      const successAction = ProgressUpdateSuccess({
        success: true,
        message: 'Success',
      });
      const failureAction = ProgressUpdateFailure('Error');

      expect(requestAction.type).not.toBe(successAction.type);
      expect(successAction.type).not.toBe(failureAction.type);
      expect(failureAction.type).not.toBe(requestAction.type);
    });

    it('should maintain consistent action structure', () => {
      const requestAction = ProgressUpdateRequest({
        jobCode: 'JOB001',
        UID: 'user123',
        Type: 'daily',
        Notification_Desc: 'Test progress',
        Quantity: 100,
        uOM: 'meters',
        manPower: 5,
        ActualList: [],
        Attachments: [],
        cb: mockCallback,
      });
      const successAction = ProgressUpdateSuccess({
        success: true,
        message: 'Success',
      });
      const failureAction = ProgressUpdateFailure('Error');

      expect(requestAction).toHaveProperty('type');
      expect(requestAction).toHaveProperty('payload');
      expect(successAction).toHaveProperty('type');
      expect(successAction).toHaveProperty('payload');
      expect(failureAction).toHaveProperty('type');
      expect(failureAction).toHaveProperty('payload');
    });
  });

  describe('Action Creator Behavior', () => {
    it('should be pure functions', () => {
      const request = {
        jobCode: 'JOB001',
        UID: 'user123',
        Type: 'daily',
        Notification_Desc: 'Test progress',
        Quantity: 100,
        uOM: 'meters',
        manPower: 5,
        ActualList: [],
        Attachments: [],
        cb: mockCallback,
      };
      const response = { success: true, message: 'Success' };
      const error = 'Test error';

      const request1 = ProgressUpdateRequest(request);
      const request2 = ProgressUpdateRequest(request);
      const success1 = ProgressUpdateSuccess(response);
      const success2 = ProgressUpdateSuccess(response);
      const failure1 = ProgressUpdateFailure(error);
      const failure2 = ProgressUpdateFailure(error);

      expect(request1).toEqual(request2);
      expect(success1).toEqual(success2);
      expect(failure1).toEqual(failure2);
    });

    it('should not modify any external state', () => {
      const originalRequest = PROGRESS_UPDATE_REQUEST;
      const originalSuccess = PROGRESS_UPDATE_SUCCESS;
      const originalFailure = PROGRESS_UPDATE_FAILURE;

      ProgressUpdateRequest({
        jobCode: 'JOB001',
        UID: 'user123',
        Type: 'daily',
        Notification_Desc: 'Test progress',
        Quantity: 100,
        uOM: 'meters',
        manPower: 5,
        ActualList: [],
        Attachments: [],
        cb: mockCallback,
      });
      ProgressUpdateSuccess({ success: true, message: 'Success' });
      ProgressUpdateFailure('Error');

      expect(PROGRESS_UPDATE_REQUEST).toBe(originalRequest);
      expect(PROGRESS_UPDATE_SUCCESS).toBe(originalSuccess);
      expect(PROGRESS_UPDATE_FAILURE).toBe(originalFailure);
    });
  });

  describe('Type Safety', () => {
    it('should handle action with correct payload types', () => {
      const request: ProgressUpdateRequestBody = {
        jobCode: 'JOB001',
        UID: 'user123',
        Type: 'daily',
        Notification_Desc: 'Test progress',
        Quantity: 100,
        uOM: 'meters',
        manPower: 5,
        ActualList: [],
        Attachments: [],
        cb: mockCallback,
      };
      const response: ProgressUpdateResponseData = {
        success: true,
        message: 'Success',
      };

      const requestAction = ProgressUpdateRequest(request);
      const successAction = ProgressUpdateSuccess(response);
      const failureAction = ProgressUpdateFailure('Error');

      expect(requestAction.payload).toEqual(request);
      expect(successAction.payload).toEqual(response);
      expect(failureAction.payload).toBe('Error');
    });
  });
}); 