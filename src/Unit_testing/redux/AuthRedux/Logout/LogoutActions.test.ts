import {
  logoutRequest,
  logoutAction,
} from '../../../../redux/AuthRedux/Logout/LogoutAction';
import {
  LOGOUT,
  LOGOUT_REQUEST,
} from '../../../../redux/AuthRedux/Logout/LogoutActionTypes';

describe('Logout Actions', () => {
  describe('logoutRequest', () => {
    it('should create logoutRequest action', () => {
      const expectedAction = {
        type: LOGOUT_REQUEST,
      };
      expect(logoutRequest()).toEqual(expectedAction);
    });

    it('should return action with correct type', () => {
      const action = logoutRequest();
      expect(action.type).toBe(LOGOUT_REQUEST);
    });

    it('should not have payload for logout request action', () => {
      const action = logoutRequest();
      expect(action).not.toHaveProperty('payload');
    });

    it('should create consistent action on multiple calls', () => {
      const action1 = logoutRequest();
      const action2 = logoutRequest();
      expect(action1).toEqual(action2);
    });

    it('should have only type property', () => {
      const action = logoutRequest();
      const actionKeys = Object.keys(action);
      expect(actionKeys).toEqual(['type']);
    });
  });

  describe('logoutAction', () => {
    it('should create logoutAction action', () => {
      const expectedAction = {
        type: LOGOUT,
      };
      expect(logoutAction()).toEqual(expectedAction);
    });

    it('should return action with correct type', () => {
      const action = logoutAction();
      expect(action.type).toBe(LOGOUT);
    });

    it('should not have payload for logout action', () => {
      const action = logoutAction();
      expect(action).not.toHaveProperty('payload');
    });

    it('should create consistent action on multiple calls', () => {
      const action1 = logoutAction();
      const action2 = logoutAction();
      expect(action1).toEqual(action2);
    });

    it('should have only type property', () => {
      const action = logoutAction();
      const actionKeys = Object.keys(action);
      expect(actionKeys).toEqual(['type']);
    });
  });

  describe('Action Type Constants', () => {
    it('should have correct action type constants', () => {
      expect(LOGOUT).toBe('LOGOUT');
      expect(LOGOUT_REQUEST).toBe('LOGOUT_REQUEST');
    });

    it('should have different action type constants', () => {
      expect(LOGOUT).not.toBe(LOGOUT_REQUEST);
    });
  });

  describe('Action Creators Comparison', () => {
    it('should create different action types for different creators', () => {
      const requestAction = logoutRequest();
      const logoutActionResult = logoutAction();

      expect(requestAction.type).not.toBe(logoutActionResult.type);
    });

    it('should maintain consistent action structure', () => {
      const requestAction = logoutRequest();
      const logoutActionResult = logoutAction();

      expect(requestAction).toHaveProperty('type');
      expect(logoutActionResult).toHaveProperty('type');
    });

    it('should both return objects with type property', () => {
      const requestAction = logoutRequest();
      const logoutActionResult = logoutAction();

      expect(typeof requestAction).toBe('object');
      expect(typeof logoutActionResult).toBe('object');
      expect(typeof requestAction.type).toBe('string');
      expect(typeof logoutActionResult.type).toBe('string');
    });
  });

  describe('Action Creator Behavior', () => {
    it('should be pure functions', () => {
      // Pure functions should return the same output for the same input
      const request1 = logoutRequest();
      const request2 = logoutRequest();
      const logout1 = logoutAction();
      const logout2 = logoutAction();

      expect(request1).toEqual(request2);
      expect(logout1).toEqual(logout2);
    });

    it('should not modify any external state', () => {
      const originalLogout = LOGOUT;
      const originalLogoutRequest = LOGOUT_REQUEST;

      logoutRequest();
      logoutAction();

      expect(LOGOUT).toBe(originalLogout);
      expect(LOGOUT_REQUEST).toBe(originalLogoutRequest);
    });

    it('should create new objects on each call', () => {
      const request1 = logoutRequest();
      const request2 = logoutRequest();
      const logout1 = logoutAction();
      const logout2 = logoutAction();

      expect(request1).not.toBe(request2);
      expect(logout1).not.toBe(logout2);
    });

    it('should handle rapid successive calls', () => {
      const actions = [];
      for (let i = 0; i < 100; i++) {
        actions.push(logoutRequest());
        actions.push(logoutAction());
      }

      // All logout requests should be equal
      const logoutRequests = actions.filter(action => action.type === LOGOUT_REQUEST);
      expect(logoutRequests).toHaveLength(100);
      logoutRequests.forEach(action => {
        expect(action).toEqual({ type: LOGOUT_REQUEST });
      });

      // All logout actions should be equal
      const logoutActions = actions.filter(action => action.type === LOGOUT);
      expect(logoutActions).toHaveLength(100);
      logoutActions.forEach(action => {
        expect(action).toEqual({ type: LOGOUT });
      });
    });
  });

  describe('Function Properties', () => {
    it('should be functions', () => {
      expect(typeof logoutRequest).toBe('function');
      expect(typeof logoutAction).toBe('function');
    });

    it('should be callable without arguments', () => {
      expect(() => logoutRequest()).not.toThrow();
      expect(() => logoutAction()).not.toThrow();
    });

    it('should ignore extra arguments', () => {
      const action1 = logoutRequest();
      const action2 = logoutRequest('extra', 'arguments', 123);
      const action3 = logoutAction();
      const action4 = logoutAction('extra', 'arguments', 123);

      expect(action1).toEqual(action2);
      expect(action3).toEqual(action4);
    });
  });

  describe('Action Object Structure', () => {
    it('should create actions with valid structure', () => {
      const requestAction = logoutRequest();
      const logoutActionResult = logoutAction();

      expect(requestAction).toMatchObject({
        type: expect.any(String),
      });
      expect(logoutActionResult).toMatchObject({
        type: expect.any(String),
      });
    });

    it('should create actions that can be serialized', () => {
      const requestAction = logoutRequest();
      const logoutActionResult = logoutAction();

      expect(() => JSON.stringify(requestAction)).not.toThrow();
      expect(() => JSON.stringify(logoutActionResult)).not.toThrow();

      const serializedRequest = JSON.stringify(requestAction);
      const serializedLogout = JSON.stringify(logoutActionResult);

      expect(JSON.parse(serializedRequest)).toEqual(requestAction);
      expect(JSON.parse(serializedLogout)).toEqual(logoutActionResult);
    });
  });

  describe('Integration with Action Types', () => {
    it('should use action type constants correctly', () => {
      const requestAction = logoutRequest();
      const logoutActionResult = logoutAction();

      expect(requestAction.type).toBe(LOGOUT_REQUEST);
      expect(logoutActionResult.type).toBe(LOGOUT);
    });

    it('should maintain type consistency with constants', () => {
      // If constants change, actions should reflect the change
      const requestAction = logoutRequest();
      const logoutActionResult = logoutAction();

      expect(requestAction.type).toEqual(LOGOUT_REQUEST);
      expect(logoutActionResult.type).toEqual(LOGOUT);
    });
  });
}); 