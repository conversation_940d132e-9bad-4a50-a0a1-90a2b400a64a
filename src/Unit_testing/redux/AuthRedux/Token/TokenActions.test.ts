import {
  validateTokenRequest,
  validateTokenSuccess,
  validateTokenFailure,
} from '../../../../redux/AuthRedux/Token/TokenActions';
import {
  VALIDATE_TOKEN_REQUEST,
  VALIDATE_TOKEN_SUCCESS,
  VALIDATE_TOKEN_FAILURE,
} from '../../../../redux/AuthRedux/Token/TokenActionTypes';
import { TokenResponse } from '../../../../model/Auth/TokenModel';

describe('Token Actions', () => {
  describe('validateTokenRequest', () => {
    it('should create validateTokenRequest action', () => {
      const expectedAction = {
        type: VALIDATE_TOKEN_REQUEST,
      };
      expect(validateTokenRequest()).toEqual(expectedAction);
    });

    it('should return action with correct type', () => {
      const action = validateTokenRequest();
      expect(action.type).toBe(VALIDATE_TOKEN_REQUEST);
    });

    it('should not have payload for request action', () => {
      const action = validateTokenRequest();
      expect(action).not.toHaveProperty('payload');
    });
  });

  describe('validateTokenSuccess', () => {
    const mockTokenResponse: TokenResponse = {
      statusCode: 200,
      message: 'Token validated successfully',
      token: 'mock-jwt-token-12345',
    };

    it('should create validateTokenSuccess action with token response', () => {
      const expectedAction = {
        type: VALIDATE_TOKEN_SUCCESS,
        payload: mockTokenResponse,
      };
      expect(validateTokenSuccess(mockTokenResponse)).toEqual(expectedAction);
    });

    it('should return action with correct type', () => {
      const action = validateTokenSuccess(mockTokenResponse);
      expect(action.type).toBe(VALIDATE_TOKEN_SUCCESS);
    });

    it('should include token response in payload', () => {
      const action = validateTokenSuccess(mockTokenResponse);
      expect(action.payload).toEqual(mockTokenResponse);
      expect(action.payload.token).toBe('mock-jwt-token-12345');
    });

    it('should handle token response with different status codes', () => {
      const tokenResponse: TokenResponse = {
        statusCode: 201,
        message: 'Token refreshed',
        token: 'new-token-67890',
      };
      const action = validateTokenSuccess(tokenResponse);
      expect(action.payload.statusCode).toBe(201);
      expect(action.payload.token).toBe('new-token-67890');
    });

    it('should handle token response with empty token', () => {
      const tokenResponse: TokenResponse = {
        statusCode: 200,
        message: 'Token validation successful',
        token: '',
      };
      const action = validateTokenSuccess(tokenResponse);
      expect(action.payload.token).toBe('');
    });

    it('should handle token response with long token', () => {
      const longToken = 'a'.repeat(1000);
      const tokenResponse: TokenResponse = {
        statusCode: 200,
        message: 'Token validated',
        token: longToken,
      };
      const action = validateTokenSuccess(tokenResponse);
      expect(action.payload.token).toBe(longToken);
    });

    it('should handle token response with special characters in message', () => {
      const tokenResponse: TokenResponse = {
        statusCode: 200,
        message: 'Token validated with special chars: @#$%^&*()_+{}[]|\\:";\'<>?,./`~',
        token: 'token-123',
      };
      const action = validateTokenSuccess(tokenResponse);
      expect(action.payload.message).toContain('special chars');
    });
  });

  describe('validateTokenFailure', () => {
    it('should create validateTokenFailure action with error message', () => {
      const errorMessage = 'Token validation failed';
      const expectedAction = {
        type: VALIDATE_TOKEN_FAILURE,
        payload: errorMessage,
      };
      expect(validateTokenFailure(errorMessage)).toEqual(expectedAction);
    });

    it('should return action with correct type', () => {
      const action = validateTokenFailure('Error message');
      expect(action.type).toBe(VALIDATE_TOKEN_FAILURE);
    });

    it('should include error message in payload', () => {
      const errorMessage = 'Token expired';
      const action = validateTokenFailure(errorMessage);
      expect(action.payload).toBe(errorMessage);
    });

    it('should handle empty error message', () => {
      const action = validateTokenFailure('');
      expect(action.payload).toBe('');
    });

    it('should handle null error message', () => {
      const action = validateTokenFailure(null as any);
      expect(action.payload).toBeNull();
    });

    it('should handle undefined error message', () => {
      const action = validateTokenFailure(undefined as any);
      expect(action.payload).toBeUndefined();
    });

    it('should handle network error message', () => {
      const networkError = 'Network request failed';
      const action = validateTokenFailure(networkError);
      expect(action.payload).toBe(networkError);
    });

    it('should handle authentication error message', () => {
      const authError = 'Authentication failed - invalid credentials';
      const action = validateTokenFailure(authError);
      expect(action.payload).toBe(authError);
    });

    it('should handle server error message', () => {
      const serverError = 'Internal server error - 500';
      const action = validateTokenFailure(serverError);
      expect(action.payload).toBe(serverError);
    });

    it('should handle long error message', () => {
      const longError = 'A'.repeat(500) + ' - error occurred';
      const action = validateTokenFailure(longError);
      expect(action.payload).toBe(longError);
    });

    it('should handle error message with special characters', () => {
      const specialError = 'Error: @#$%^&*()_+{}[]|\\:";\'<>?,./`~';
      const action = validateTokenFailure(specialError);
      expect(action.payload).toBe(specialError);
    });
  });

  describe('Action Type Constants', () => {
    it('should have correct action type constants', () => {
      expect(VALIDATE_TOKEN_REQUEST).toBe('VALIDATE_TOKEN_REQUEST');
      expect(VALIDATE_TOKEN_SUCCESS).toBe('VALIDATE_TOKEN_SUCCESS');
      expect(VALIDATE_TOKEN_FAILURE).toBe('VALIDATE_TOKEN_FAILURE');
    });
  });

  describe('Action Creators Integration', () => {
    it('should create different action types for different scenarios', () => {
      const requestAction = validateTokenRequest();
      const successAction = validateTokenSuccess({
        statusCode: 200,
        message: 'Success',
        token: 'token',
      });
      const failureAction = validateTokenFailure('Error');

      expect(requestAction.type).not.toBe(successAction.type);
      expect(successAction.type).not.toBe(failureAction.type);
      expect(failureAction.type).not.toBe(requestAction.type);
    });

    it('should maintain consistent action structure', () => {
      const requestAction = validateTokenRequest();
      const successAction = validateTokenSuccess({
        statusCode: 200,
        message: 'Success',
        token: 'token',
      });
      const failureAction = validateTokenFailure('Error');

      expect(requestAction).toHaveProperty('type');
      expect(successAction).toHaveProperty('type');
      expect(successAction).toHaveProperty('payload');
      expect(failureAction).toHaveProperty('type');
      expect(failureAction).toHaveProperty('payload');
    });
  });
}); 