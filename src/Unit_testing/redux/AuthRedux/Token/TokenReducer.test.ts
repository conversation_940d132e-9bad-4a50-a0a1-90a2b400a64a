import { TokenReducer } from '../../../../redux/AuthRedux/Token/TokenReducer';
import {
  VALIDATE_TOKEN_REQUEST,
  VALIDATE_TOKEN_SUCCESS,
  VALIDATE_TOKEN_FAILURE,
  VALIDATE_TOKEN_LOGOUT,
} from '../../../../redux/AuthRedux/Token/TokenActionTypes';
import { TokenResponse } from '../../../../model/Auth/TokenModel';

describe('Token Reducer', () => {
  const initialState = {
    token: null,
    loading: false,
    error: null,
  };

  describe('Initial State', () => {
    it('should return initial state when no action is passed', () => {
      const result = TokenReducer(undefined, {} as any);
      expect(result).toEqual(initialState);
    });

    it('should have correct initial state structure', () => {
      const result = TokenReducer(undefined, {} as any);
      expect(result).toHaveProperty('token');
      expect(result).toHaveProperty('loading');
      expect(result).toHaveProperty('error');
    });

    it('should have null token initially', () => {
      const result = TokenReducer(undefined, {} as any);
      expect(result.token).toBeNull();
    });

    it('should have loading false initially', () => {
      const result = TokenReducer(undefined, {} as any);
      expect(result.loading).toBe(false);
    });

    it('should have null error initially', () => {
      const result = TokenReducer(undefined, {} as any);
      expect(result.error).toBeNull();
    });
  });

  describe('VALIDATE_TOKEN_REQUEST', () => {
    it('should handle VALIDATE_TOKEN_REQUEST action', () => {
      const action = {
        type: VALIDATE_TOKEN_REQUEST,
      };
      const result = TokenReducer(initialState, action);
      expect(result.loading).toBe(true);
      expect(result.error).toBeNull();
      expect(result.token).toBeNull();
    });

    it('should reset error on token request', () => {
      const stateWithError = {
        ...initialState,
        error: 'Previous error',
      };
      const action = {
        type: VALIDATE_TOKEN_REQUEST,
      };
      const result = TokenReducer(stateWithError, action);
      expect(result.error).toBeNull();
    });

    it('should preserve existing token during request', () => {
      const stateWithToken = {
        ...initialState,
        token: 'existing-token',
      };
      const action = {
        type: VALIDATE_TOKEN_REQUEST,
      };
      const result = TokenReducer(stateWithToken, action);
      expect(result.token).toBe('existing-token');
    });

    it('should maintain immutability', () => {
      const action = {
        type: VALIDATE_TOKEN_REQUEST,
      };
      const result = TokenReducer(initialState, action);
      expect(result).not.toBe(initialState);
    });
  });

  describe('VALIDATE_TOKEN_SUCCESS', () => {
    const mockTokenResponse: TokenResponse = {
      statusCode: 200,
      message: 'Token validated successfully',
      token: 'mock-jwt-token-12345',
    };

    it('should handle VALIDATE_TOKEN_SUCCESS action', () => {
      const action = {
        type: VALIDATE_TOKEN_SUCCESS,
        payload: mockTokenResponse,
      };
      const result = TokenReducer(initialState, action);
      expect(result.loading).toBe(false);
      expect(result.error).toBeNull();
      expect(result.token).toBe('mock-jwt-token-12345');
    });

    it('should update token from payload', () => {
      const action = {
        type: VALIDATE_TOKEN_SUCCESS,
        payload: mockTokenResponse,
      };
      const result = TokenReducer(initialState, action);
      expect(result.token).toBe(mockTokenResponse.token);
    });

    it('should clear loading state on success', () => {
      const loadingState = {
        ...initialState,
        loading: true,
      };
      const action = {
        type: VALIDATE_TOKEN_SUCCESS,
        payload: mockTokenResponse,
      };
      const result = TokenReducer(loadingState, action);
      expect(result.loading).toBe(false);
    });

    it('should clear error on success', () => {
      const errorState = {
        ...initialState,
        error: 'Previous error',
      };
      const action = {
        type: VALIDATE_TOKEN_SUCCESS,
        payload: mockTokenResponse,
      };
      const result = TokenReducer(errorState, action);
      expect(result.error).toBeNull();
    });

    it('should handle empty token in success', () => {
      const emptyTokenResponse: TokenResponse = {
        statusCode: 200,
        message: 'Success',
        token: '',
      };
      const action = {
        type: VALIDATE_TOKEN_SUCCESS,
        payload: emptyTokenResponse,
      };
      const result = TokenReducer(initialState, action);
      expect(result.token).toBe('');
    });

    it('should handle different status codes', () => {
      const differentStatusResponse: TokenResponse = {
        statusCode: 201,
        message: 'Token refreshed',
        token: 'new-token',
      };
      const action = {
        type: VALIDATE_TOKEN_SUCCESS,
        payload: differentStatusResponse,
      };
      const result = TokenReducer(initialState, action);
      expect(result.token).toBe('new-token');
    });

    it('should maintain immutability', () => {
      const action = {
        type: VALIDATE_TOKEN_SUCCESS,
        payload: mockTokenResponse,
      };
      const result = TokenReducer(initialState, action);
      expect(result).not.toBe(initialState);
    });
  });

  describe('VALIDATE_TOKEN_FAILURE', () => {
    it('should handle VALIDATE_TOKEN_FAILURE action', () => {
      const errorMessage = 'Token validation failed';
      const action = {
        type: VALIDATE_TOKEN_FAILURE,
        payload: errorMessage,
      };
      const result = TokenReducer(initialState, action);
      expect(result.loading).toBe(false);
      expect(result.error).toBe(errorMessage);
      expect(result.token).toBeNull();
    });

    it('should clear token on failure', () => {
      const stateWithToken = {
        ...initialState,
        token: 'existing-token',
      };
      const action = {
        type: VALIDATE_TOKEN_FAILURE,
        payload: 'Error message',
      };
      const result = TokenReducer(stateWithToken, action);
      expect(result.token).toBeNull();
    });

    it('should clear loading state on failure', () => {
      const loadingState = {
        ...initialState,
        loading: true,
      };
      const action = {
        type: VALIDATE_TOKEN_FAILURE,
        payload: 'Error message',
      };
      const result = TokenReducer(loadingState, action);
      expect(result.loading).toBe(false);
    });

    it('should handle empty error message', () => {
      const action = {
        type: VALIDATE_TOKEN_FAILURE,
        payload: '',
      };
      const result = TokenReducer(initialState, action);
      expect(result.error).toBe('');
    });

    it('should handle null error message', () => {
      const action = {
        type: VALIDATE_TOKEN_FAILURE,
        payload: null,
      };
      const result = TokenReducer(initialState, action);
      expect(result.error).toBeNull();
    });

    it('should handle network error message', () => {
      const networkError = 'Network request failed';
      const action = {
        type: VALIDATE_TOKEN_FAILURE,
        payload: networkError,
      };
      const result = TokenReducer(initialState, action);
      expect(result.error).toBe(networkError);
    });

    it('should maintain immutability', () => {
      const action = {
        type: VALIDATE_TOKEN_FAILURE,
        payload: 'Error',
      };
      const result = TokenReducer(initialState, action);
      expect(result).not.toBe(initialState);
    });
  });

  describe('VALIDATE_TOKEN_LOGOUT', () => {
    it('should handle VALIDATE_TOKEN_LOGOUT action', () => {
      const stateWithData = {
        token: 'existing-token',
        loading: false,
        error: 'some error',
      };
      const action = {
        type: VALIDATE_TOKEN_LOGOUT,
      };
      const result = TokenReducer(stateWithData, action);
      expect(result).toEqual(initialState);
    });

    it('should reset to initial state on logout', () => {
      const stateWithData = {
        token: 'some-token',
        loading: true,
        error: 'some error',
      };
      const action = {
        type: VALIDATE_TOKEN_LOGOUT,
      };
      const result = TokenReducer(stateWithData, action);
      expect(result.token).toBeNull();
      expect(result.loading).toBe(false);
      expect(result.error).toBeNull();
    });

    it('should handle logout from loading state', () => {
      const loadingState = {
        ...initialState,
        loading: true,
      };
      const action = {
        type: VALIDATE_TOKEN_LOGOUT,
      };
      const result = TokenReducer(loadingState, action);
      expect(result.loading).toBe(false);
    });

    it('should handle logout from error state', () => {
      const errorState = {
        ...initialState,
        error: 'Token expired',
      };
      const action = {
        type: VALIDATE_TOKEN_LOGOUT,
      };
      const result = TokenReducer(errorState, action);
      expect(result.error).toBeNull();
    });

    it('should maintain immutability', () => {
      const stateWithData = {
        token: 'token',
        loading: false,
        error: null,
      };
      const action = {
        type: VALIDATE_TOKEN_LOGOUT,
      };
      const result = TokenReducer(stateWithData, action);
      expect(result).not.toBe(stateWithData);
    });
  });

  describe('Unknown Actions', () => {
    it('should return current state for unknown action types', () => {
      const currentState = {
        token: 'existing-token',
        loading: true,
        error: 'some error',
      };
      const unknownAction = {
        type: 'UNKNOWN_ACTION',
        payload: 'some payload',
      };
      const result = TokenReducer(currentState, unknownAction);
      expect(result).toEqual(currentState);
    });

    it('should return current state reference for unknown actions', () => {
      const currentState = {
        token: 'existing-token',
        loading: false,
        error: null,
      };
      const unknownAction = {
        type: 'RANDOM_ACTION',
      };
      const result = TokenReducer(currentState, unknownAction);
      expect(result).toBe(currentState);
    });

    it('should handle action without type', () => {
      const currentState = {
        token: 'token',
        loading: false,
        error: null,
      };
      const invalidAction = {} as any;
      const result = TokenReducer(currentState, invalidAction);
      expect(result).toBe(currentState);
    });
  });

  describe('State Transitions', () => {
    it('should handle complete success flow', () => {
      let state = TokenReducer(initialState, {
        type: VALIDATE_TOKEN_REQUEST,
      });
      expect(state.loading).toBe(true);

      state = TokenReducer(state, {
        type: VALIDATE_TOKEN_SUCCESS,
        payload: {
          statusCode: 200,
          message: 'Success',
          token: 'new-token',
        },
      });
      expect(state.loading).toBe(false);
      expect(state.token).toBe('new-token');
      expect(state.error).toBeNull();
    });

    it('should handle complete failure flow', () => {
      let state = TokenReducer(initialState, {
        type: VALIDATE_TOKEN_REQUEST,
      });
      expect(state.loading).toBe(true);

      state = TokenReducer(state, {
        type: VALIDATE_TOKEN_FAILURE,
        payload: 'Validation failed',
      });
      expect(state.loading).toBe(false);
      expect(state.token).toBeNull();
      expect(state.error).toBe('Validation failed');
    });

    it('should handle logout after success', () => {
      let state = TokenReducer(initialState, {
        type: VALIDATE_TOKEN_SUCCESS,
        payload: {
          statusCode: 200,
          message: 'Success',
          token: 'auth-token',
        },
      });
      expect(state.token).toBe('auth-token');

      state = TokenReducer(state, {
        type: VALIDATE_TOKEN_LOGOUT,
      });
      expect(state).toEqual(initialState);
    });
  });

  describe('Type Safety', () => {
    it('should handle action with correct payload type', () => {
      const action = {
        type: VALIDATE_TOKEN_SUCCESS,
        payload: {
          statusCode: 200,
          message: 'Success',
          token: 'typed-token',
        } as TokenResponse,
      };
      const result = TokenReducer(initialState, action);
      expect(result.token).toBe('typed-token');
    });

    it('should handle action with missing payload properties', () => {
      const action = {
        type: VALIDATE_TOKEN_SUCCESS,
        payload: {
          token: 'partial-token',
        } as any,
      };
      const result = TokenReducer(initialState, action);
      expect(result.token).toBe('partial-token');
    });
  });
}); 