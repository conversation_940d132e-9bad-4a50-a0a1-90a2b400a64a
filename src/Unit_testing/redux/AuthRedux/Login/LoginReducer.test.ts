import { LoginReducer } from '../../../../redux/AuthRedux/Login/LoginReducer';
import { loginRequest, loginSuccess, loginFailure } from '../../../../redux/AuthRedux/Login/LoginActions';
import { LOGIN_REQUEST, LOGIN_SUCCESS, LOGIN_FAILURE, LOGIN_LOGOUT } from '../../../../redux/AuthRedux/Login/LoginActionTypes';
import { LoginRequestData, LoginResponseItem } from '../../../../model/Auth/LoginData';

describe('LoginReducer', () => {
  const initialState = {
    user: null,
    loading: false,
    error: null,
  };

  describe('Initial State', () => {
    it('should return the initial state', () => {
      // Act
      const state = LoginReducer(undefined, { type: 'UNKNOWN_ACTION' });

      // Assert
      expect(state).toEqual(initialState);
    });
  });

  describe('LOGIN_REQUEST', () => {
    it('should handle LOGIN_REQUEST action', () => {
      // Arrange
      const payload: LoginRequestData = {
        UserName: 'testuser',
        Password: 'testpass123',
        Token: 'test-token',
        CompanyCode: 1,
        intAppCode: 1,
        isipcheck: 'Y',
        strDeviceUniqueCode: 'device123'
      };
      const action = loginRequest(payload);

      // Act
      const newState = LoginReducer(initialState, action);

      // Assert
      expect(newState).toEqual({
        ...initialState,
        loading: true,
        error: null,
      });
    });

    it('should handle LOGIN_REQUEST when there is an existing error', () => {
      // Arrange
      const stateWithError = {
        ...initialState,
        error: 'Previous error',
      };
      const payload: LoginRequestData = {
        UserName: 'testuser',
        Password: 'testpass123',
        Token: 'test-token',
        CompanyCode: 1,
        intAppCode: 1,
        isipcheck: 'Y',
        strDeviceUniqueCode: 'device123'
      };
      const action = loginRequest(payload);

      // Act
      const newState = LoginReducer(stateWithError, action);

      // Assert
      expect(newState).toEqual({
        ...stateWithError,
        loading: true,
        error: null, // Should clear the previous error
      });
    });

    it('should handle LOGIN_REQUEST when user is already logged in', () => {
      // Arrange
      const stateWithUser = {
        ...initialState,
        user: {
          UID: 123,
          UserName: 'existing-user',
          Status: 'Success',
          StatusCode: 200,
          Message: 'Already logged in',
          intAppCode: 1,
          strDeviceUniqueCode: 'device123',
          CompanyCode: 1,
          PSNumber: 'PS001',
          Department: 'Engineering',
          EMailid: '<EMAIL>',
          MobileNo: '1234567890',
          ISDCode: 91,
          RegTag: 'REG123',
          EmpDetails: null
        } as LoginResponseItem,
      };
      const payload: LoginRequestData = {
        UserName: 'newuser',
        Password: 'newpass123',
        Token: 'new-token',
        CompanyCode: 1,
        intAppCode: 1,
        isipcheck: 'Y',
        strDeviceUniqueCode: 'device456'
      };
      const action = loginRequest(payload);

      // Act
      const newState = LoginReducer(stateWithUser, action);

      // Assert
      expect(newState).toEqual({
        ...stateWithUser,
        loading: true,
        error: null,
      });
    });
  });

  describe('LOGIN_SUCCESS', () => {
    it('should handle LOGIN_SUCCESS action', () => {
      // Arrange
      const loadingState = {
        ...initialState,
        loading: true,
      };
      const user: LoginResponseItem = {
        Status: 'Success',
        StatusCode: 200,
        Message: 'Login successful',
        UID: 12345,
        intAppCode: 1,
        strDeviceUniqueCode: 'device123',
        CompanyCode: 1,
        PSNumber: 'PS001',
        UserName: 'testuser',
        Department: 'Engineering',
        EMailid: '<EMAIL>',
        MobileNo: '1234567890',
        ISDCode: 91,
        RegTag: 'REG123',
        EmpDetails: {
          firstName: 'John',
          lastName: 'Doe',
          designation: 'Engineer'
        }
      };
      const action = loginSuccess(user);

      // Act
      const newState = LoginReducer(loadingState, action);

      // Assert
      expect(newState).toEqual({
        user: user,
        loading: false,
        error: null,
      });
    });

    it('should handle LOGIN_SUCCESS with minimal user data', () => {
      // Arrange
      const loadingState = {
        ...initialState,
        loading: true,
      };
      const user: LoginResponseItem = {
        Status: 'Success',
        StatusCode: 200,
        Message: 'Login successful',
        UID: 1,
        intAppCode: 1,
        strDeviceUniqueCode: 'device1',
        CompanyCode: 1,
        PSNumber: '',
        UserName: 'user1',
        Department: '',
        EMailid: '',
        MobileNo: '',
        ISDCode: 0,
        RegTag: '',
        EmpDetails: null
      };
      const action = loginSuccess(user);

      // Act
      const newState = LoginReducer(loadingState, action);

      // Assert
      expect(newState).toEqual({
        user: user,
        loading: false,
        error: null,
      });
    });

    it('should handle LOGIN_SUCCESS when there was a previous error', () => {
      // Arrange
      const stateWithError = {
        ...initialState,
        loading: true,
        error: 'Previous login error',
      };
      const user: LoginResponseItem = {
        Status: 'Success',
        StatusCode: 200,
        Message: 'Login successful',
        UID: 12345,
        intAppCode: 1,
        strDeviceUniqueCode: 'device123',
        CompanyCode: 1,
        PSNumber: 'PS001',
        UserName: 'testuser',
        Department: 'Engineering',
        EMailid: '<EMAIL>',
        MobileNo: '1234567890',
        ISDCode: 91,
        RegTag: 'REG123',
        EmpDetails: null
      };
      const action = loginSuccess(user);

      // Act
      const newState = LoginReducer(stateWithError, action);

      // Assert
      expect(newState).toEqual({
        user: user,
        loading: false,
        error: 'Previous login error', // Error is not cleared in actual implementation
      });
    });
  });

  describe('LOGIN_FAILURE', () => {
    it('should handle LOGIN_FAILURE action', () => {
      // Arrange
      const loadingState = {
        ...initialState,
        loading: true,
      };
      const error = 'Invalid credentials';
      const action = loginFailure(error);

      // Act
      const newState = LoginReducer(loadingState, action);

      // Assert
      expect(newState).toEqual({
        user: null,
        loading: false,
        error: error,
      });
    });

    it('should handle LOGIN_FAILURE with detailed error message', () => {
      // Arrange
      const loadingState = {
        ...initialState,
        loading: true,
      };
      const error = 'Authentication failed: Username or password is incorrect. Please try again.';
      const action = loginFailure(error);

      // Act
      const newState = LoginReducer(loadingState, action);

      // Assert
      expect(newState).toEqual({
        user: null,
        loading: false,
        error: error,
      });
    });

    it('should handle LOGIN_FAILURE with empty error message', () => {
      // Arrange
      const loadingState = {
        ...initialState,
        loading: true,
      };
      const error = '';
      const action = loginFailure(error);

      // Act
      const newState = LoginReducer(loadingState, action);

      // Assert
      expect(newState).toEqual({
        user: null,
        loading: false,
        error: error,
      });
    });

    it('should handle LOGIN_FAILURE when user was previously logged in', () => {
      // Arrange
      const stateWithUser = {
        user: {
          UID: 123,
          UserName: 'existing-user',
          Status: 'Success',
          StatusCode: 200,
          Message: 'Previously logged in',
          intAppCode: 1,
          strDeviceUniqueCode: 'device123',
          CompanyCode: 1,
          PSNumber: 'PS001',
          Department: 'Engineering',
          EMailid: '<EMAIL>',
          MobileNo: '1234567890',
          ISDCode: 91,
          RegTag: 'REG123',
          EmpDetails: null
        } as LoginResponseItem,
        loading: true,
        error: null,
      };
      const error = 'Network error';
      const action = loginFailure(error);

      // Act
      const newState = LoginReducer(stateWithUser, action);

      // Assert
      expect(newState).toEqual({
        user: stateWithUser.user, // User is not cleared in actual implementation
        loading: false,
        error: error,
      });
    });
  });

  describe('LOGIN_LOGOUT', () => {
    it('should handle LOGIN_LOGOUT action', () => {
      // Arrange
      const loggedInState = {
        user: {
          UID: 123,
          UserName: 'testuser',
          Status: 'Success',
          StatusCode: 200,
          Message: 'Logged in',
          intAppCode: 1,
          strDeviceUniqueCode: 'device123',
          CompanyCode: 1,
          PSNumber: 'PS001',
          Department: 'Engineering',
          EMailid: '<EMAIL>',
          MobileNo: '1234567890',
          ISDCode: 91,
          RegTag: 'REG123',
          EmpDetails: null
        } as LoginResponseItem,
        loading: false,
        error: null,
      };
      const action = { type: LOGIN_LOGOUT };

      // Act
      const newState = LoginReducer(loggedInState, action);

      // Assert
      expect(newState).toEqual(initialState);
    });

    it('should handle LOGIN_LOGOUT when there is an error', () => {
      // Arrange
      const stateWithError = {
        user: {
          UID: 123,
          UserName: 'testuser',
          Status: 'Success',
          StatusCode: 200,
          Message: 'Logged in',
          intAppCode: 1,
          strDeviceUniqueCode: 'device123',
          CompanyCode: 1,
          PSNumber: 'PS001',
          Department: 'Engineering',
          EMailid: '<EMAIL>',
          MobileNo: '1234567890',
          ISDCode: 91,
          RegTag: 'REG123',
          EmpDetails: null
        } as LoginResponseItem,
        loading: false,
        error: 'Some error',
      };
      const action = { type: LOGIN_LOGOUT };

      // Act
      const newState = LoginReducer(stateWithError, action);

      // Assert
      expect(newState).toEqual(initialState);
    });

    it('should handle LOGIN_LOGOUT when loading is true', () => {
      // Arrange
      const loadingState = {
        user: {
          UID: 123,
          UserName: 'testuser',
          Status: 'Success',
          StatusCode: 200,
          Message: 'Logged in',
          intAppCode: 1,
          strDeviceUniqueCode: 'device123',
          CompanyCode: 1,
          PSNumber: 'PS001',
          Department: 'Engineering',
          EMailid: '<EMAIL>',
          MobileNo: '1234567890',
          ISDCode: 91,
          RegTag: 'REG123',
          EmpDetails: null
        } as LoginResponseItem,
        loading: true,
        error: null,
      };
      const action = { type: LOGIN_LOGOUT };

      // Act
      const newState = LoginReducer(loadingState, action);

      // Assert
      expect(newState).toEqual(initialState);
    });
  });

  describe('Unknown Actions', () => {
    it('should return the current state for unknown actions', () => {
      // Arrange
      const currentState = {
        user: {
          UID: 123,
          UserName: 'testuser',
          Status: 'Success',
          StatusCode: 200,
          Message: 'Logged in',
          intAppCode: 1,
          strDeviceUniqueCode: 'device123',
          CompanyCode: 1,
          PSNumber: 'PS001',
          Department: 'Engineering',
          EMailid: '<EMAIL>',
          MobileNo: '1234567890',
          ISDCode: 91,
          RegTag: 'REG123',
          EmpDetails: null
        } as LoginResponseItem,
        loading: false,
        error: null,
      };
      const action = { type: 'UNKNOWN_ACTION', payload: 'some data' };

      // Act
      const newState = LoginReducer(currentState, action);

      // Assert
      expect(newState).toBe(currentState);
    });

    it('should handle action with undefined type', () => {
      // Arrange
      const currentState = {
        user: null,
        loading: true,
        error: 'Some error',
      };
      const action = { type: undefined };

      // Act
      const newState = LoginReducer(currentState, action);

      // Assert
      expect(newState).toBe(currentState);
    });
  });

  describe('State Immutability', () => {
    it('should not mutate the original state', () => {
      // Arrange
      const originalState = {
        user: null,
        loading: false,
        error: null,
      };
      const action = { type: LOGIN_REQUEST, payload: {} };

      // Act
      const newState = LoginReducer(originalState, action);

      // Assert
      expect(newState).not.toBe(originalState);
      expect(originalState).toEqual({
        user: null,
        loading: false,
        error: null,
      });
    });
  });
});

// Export for use in other tests
export { LoginReducer }; 