import { loginRequest, loginSuccess, loginFailure } from '../../../../redux/AuthRedux/Login/LoginActions';
import { LOGIN_REQUEST, LOGIN_SUCCESS, LOGIN_FAILURE } from '../../../../redux/AuthRedux/Login/LoginActionTypes';
import { LoginRequestData, LoginResponseItem } from '../../../../model/Auth/LoginData';

describe('Login Actions', () => {
  describe('loginRequest', () => {
    it('should create a login request action', () => {
      // Arrange
      const payload: LoginRequestData = {
        UserName: 'testuser',
        Password: 'testpass123',
        Token: 'test-token',
        CompanyCode: 1,
        intAppCode: 1,
        isipcheck: 'Y',
        strDeviceUniqueCode: 'device123'
      };

      // Act
      const action = loginRequest(payload);

      // Assert
      expect(action).toEqual({
        type: LOGIN_REQUEST,
        payload: payload
      });
    });

    it('should handle empty payload', () => {
      // Arrange
      const payload: LoginRequestData = {
        UserName: '',
        Password: '',
        Token: '',
        CompanyCode: 0,
        intAppCode: 0,
        isipcheck: '',
        strDeviceUniqueCode: ''
      };

      // Act
      const action = loginRequest(payload);

      // Assert
      expect(action).toEqual({
        type: LOGIN_REQUEST,
        payload: payload
      });
    });

    it('should handle special characters in credentials', () => {
      // Arrange
      const payload: LoginRequestData = {
        UserName: '<EMAIL>',
        Password: 'P@ssw0rd!@#',
        Token: 'token-with-special-chars!',
        CompanyCode: 999,
        intAppCode: 123,
        isipcheck: 'N',
        strDeviceUniqueCode: 'device-123-456'
      };

      // Act
      const action = loginRequest(payload);

      // Assert
      expect(action).toEqual({
        type: LOGIN_REQUEST,
        payload: payload
      });
    });
  });

  describe('loginSuccess', () => {
    it('should create a login success action', () => {
      // Arrange
      const response: LoginResponseItem = {
        Status: 'Success',
        StatusCode: 200,
        Message: 'Login successful',
        UID: 12345,
        intAppCode: 1,
        strDeviceUniqueCode: 'device123',
        CompanyCode: 1,
        PSNumber: 'PS001',
        UserName: 'testuser',
        Department: 'Engineering',
        EMailid: '<EMAIL>',
        MobileNo: '1234567890',
        ISDCode: 91,
        RegTag: 'REG123',
        EmpDetails: {
          firstName: 'John',
          lastName: 'Doe',
          designation: 'Engineer'
        }
      };

      // Act
      const action = loginSuccess(response);

      // Assert
      expect(action).toEqual({
        type: LOGIN_SUCCESS,
        payload: response
      });
    });

    it('should handle minimal user data', () => {
      // Arrange
      const response: LoginResponseItem = {
        Status: 'Success',
        StatusCode: 200,
        Message: 'Login successful',
        UID: 1,
        intAppCode: 1,
        strDeviceUniqueCode: 'device1',
        CompanyCode: 1,
        PSNumber: '',
        UserName: 'user1',
        Department: '',
        EMailid: '',
        MobileNo: '',
        ISDCode: 0,
        RegTag: '',
        EmpDetails: null
      };

      // Act
      const action = loginSuccess(response);

      // Assert
      expect(action).toEqual({
        type: LOGIN_SUCCESS,
        payload: response
      });
    });

    it('should handle complete user profile', () => {
      // Arrange
      const response: LoginResponseItem = {
        Status: 'Success',
        StatusCode: 200,
        Message: 'Login successful',
        UID: 99999,
        intAppCode: 5,
        strDeviceUniqueCode: 'premium-device-xyz',
        CompanyCode: 100,
        PSNumber: 'PS999',
        UserName: 'admin.user',
        Department: 'Information Technology',
        EMailid: '<EMAIL>',
        MobileNo: '+************',
        ISDCode: 91,
        RegTag: 'ADMIN_REG',
        EmpDetails: {
          firstName: 'Admin',
          lastName: 'User',
          designation: 'System Administrator',
          role: 'Admin',
          permissions: ['read', 'write', 'delete', 'admin']
        }
      };

      // Act
      const action = loginSuccess(response);

      // Assert
      expect(action).toEqual({
        type: LOGIN_SUCCESS,
        payload: response
      });
    });
  });

  describe('loginFailure', () => {
    it('should create a login failure action', () => {
      // Arrange
      const error = 'Invalid credentials';

      // Act
      const action = loginFailure(error);

      // Assert
      expect(action).toEqual({
        type: LOGIN_FAILURE,
        payload: error
      });
    });

    it('should handle empty error message', () => {
      // Arrange
      const error = '';

      // Act
      const action = loginFailure(error);

      // Assert
      expect(action).toEqual({
        type: LOGIN_FAILURE,
        payload: error
      });
    });

    it('should handle detailed error message', () => {
      // Arrange
      const error = 'Authentication failed: Username or password is incorrect. Please try again.';

      // Act
      const action = loginFailure(error);

      // Assert
      expect(action).toEqual({
        type: LOGIN_FAILURE,
        payload: error
      });
    });

    it('should handle network error message', () => {
      // Arrange
      const error = 'Network error: Unable to connect to server. Please check your internet connection.';

      // Act
      const action = loginFailure(error);

      // Assert
      expect(action).toEqual({
        type: LOGIN_FAILURE,
        payload: error
      });
    });

    it('should handle server error message', () => {
      // Arrange
      const error = 'Server error: Internal server error occurred. Please try again later.';

      // Act
      const action = loginFailure(error);

      // Assert
      expect(action).toEqual({
        type: LOGIN_FAILURE,
        payload: error
      });
    });
  });

  describe('Action Type Constants', () => {
    it('should have correct action type values', () => {
      expect(LOGIN_REQUEST).toBe('LOGIN_REQUEST');
      expect(LOGIN_SUCCESS).toBe('LOGIN_SUCCESS');
      expect(LOGIN_FAILURE).toBe('LOGIN_FAILURE');
    });
  });
});

// Export for use in other tests
export { loginRequest, loginSuccess, loginFailure }; 