import * as actions from '../../../../src/redux/UserMannualRedux/UserMannualActions';
import * as types from '../../../../src/redux/UserMannualRedux/UserMannualActionTypes';
import { UserMannualDownloadRequestData } from '../../../../src/model/UserMannual/UserMannualData';

describe('UserMannual actions', () => {
    it('should create an action for user mannual request', () => {
        const expectedAction = {
            type: types.USER_MANNUAL_REQUEST,
        };
        expect(actions.userMannualRequest()).toEqual(expectedAction);
    });

    it('should create an action for user mannual success', () => {
        const data = [{ id: 1, name: 'Manual 1' }];
        const expectedAction = {
            type: types.USER_MANNUAL_SUCCESS,
            payload: data,
        };
        expect(actions.userMannualSuccess(data)).toEqual(expectedAction);
    });

    it('should create an action for user mannual failure', () => {
        const error = 'Error message';
        const expectedAction = {
            type: types.USER_MANNUAL_FAILURE,
            payload: error,
        };
        expect(actions.userMannualFailure(error)).toEqual(expectedAction);
    });

    it('should create an action for user mannual image request', () => {
        const data: UserMannualDownloadRequestData = {
            fileName: 'manual.pdf',
        };
        const expectedAction = {
            type: types.USER_MANNUAL_IMAGE_REQUEST,
            payload: data,
        };
        expect(actions.userMannualImageRequest(data)).toEqual(expectedAction);
    });

    it('should create an action for user mannual image success', () => {
        const data = 'path/to/image.jpg';
        const expectedAction = {
            type: types.USER_MANNUAL_IMAGE_SUCCESS,
            payload: data,
        };
        expect(actions.userMannualImageSuccess(data)).toEqual(expectedAction);
    });

    it('should create an action for user mannual image failure', () => {
        const error = 'Error message';
        const expectedAction = {
            type: types.USER_MANNUAL_IMAGE_FAILURE,
            payload: error,
        };
        expect(actions.userMannualImageFailure(error)).toEqual(expectedAction);
    });
}); 