import { UserMannualReducer } from '../../../../src/redux/UserMannualRedux/UserMannualReducer';
import * as types from '../../../../src/redux/UserMannualRedux/UserMannualActionTypes';

describe('UserMannualReducer', () => {
    const initialState = {
        userMannualData: null,
        userMannualLoading: false,
        userMannualError: null,
        userMannualDownloadData: '',
        userMannualDownloading: false,
        userMannualDownloadError: null,
    };

    it('should return the initial state', () => {
        expect(UserMannualReducer(undefined, {})).toEqual(initialState);
    });

    it('should handle USER_MANNUAL_REQUEST', () => {
        const expectedState = {
            ...initialState,
            userMannualLoading: true,
            userMannualError: null,
        };
        expect(UserMannualReducer(initialState, { type: types.USER_MANNUAL_REQUEST })).toEqual(expectedState);
    });

    it('should handle USER_MANNUAL_SUCCESS', () => {
        const payload = [{ id: 1, name: 'Manual 1' }];
        const expectedState = {
            ...initialState,
            userMannualLoading: false,
            userMannualData: payload,
        };
        expect(UserMannualReducer(initialState, { type: types.USER_MANNUAL_SUCCESS, payload })).toEqual(expectedState);
    });

    it('should handle USER_MANNUAL_FAILURE', () => {
        const payload = 'Error message';
        const expectedState = {
            ...initialState,
            userMannualLoading: false,
            userMannualError: payload,
        };
        expect(UserMannualReducer(initialState, { type: types.USER_MANNUAL_FAILURE, payload })).toEqual(expectedState);
    });

    it('should handle USER_MANNUAL_IMAGE_REQUEST', () => {
        const expectedState = {
            ...initialState,
            userMannualDownloading: true,
            userMannualDownloadError: null,
        };
        expect(UserMannualReducer(initialState, { type: types.USER_MANNUAL_IMAGE_REQUEST })).toEqual(expectedState);
    });

    it('should handle USER_MANNUAL_IMAGE_SUCCESS', () => {
        const payload = 'path/to/image.jpg';
        const expectedState = {
            ...initialState,
            userMannualDownloading: false,
            userMannualDownloadData: payload,
        };
        expect(UserMannualReducer(initialState, { type: types.USER_MANNUAL_IMAGE_SUCCESS, payload })).toEqual(expectedState);
    });

    it('should handle USER_MANNUAL_IMAGE_FAILURE', () => {
        const payload = 'Error message';
        const expectedState = {
            ...initialState,
            userMannualDownloading: false,
            userMannualDownloadError: payload,
        };
        expect(UserMannualReducer(initialState, { type: types.USER_MANNUAL_IMAGE_FAILURE, payload })).toEqual(expectedState);
    });
}); 