import wbsReducer from '../../../../src/redux/WBSDownload/WBSReducer';
import * as types from '../../../../src/redux/WBSDownload/WBSActionTypes';
import { WbsJobResponseData, WbsPendingApprovalModal } from '../../../../src/model/Wbs/WbsData';

describe('WBSDownload reducer', () => {
    const initialState = {
        isLoading: false,
        isPendingLoading: false,
        error: null,
        response: null,
        pendingApprovalResponse: [],
        pendingApprovalError: null,
    };

    it('should return the initial state', () => {
        expect(wbsReducer(undefined, {})).toEqual(initialState);
    });

    it('should handle WBS_REQUEST', () => {
        const expectedState = {
            ...initialState,
            isLoading: true,
            error: null,
        };
        expect(wbsReducer(initialState, { type: types.WBS_REQUEST })).toEqual(expectedState);
    });

    it('should handle WBS_SUCCESS', () => {
        const payload: WbsJobResponseData = {
            message: 'Success',
            status: 200,
            data: [],
        };
        const expectedState = {
            ...initialState,
            isLoading: false,
            response: payload,
            error: null,
        };
        expect(wbsReducer(initialState, { type: types.WBS_SUCCESS, payload })).toEqual(expectedState);
    });

    it('should handle WBS_FAILURE', () => {
        const payload = 'Error message';
        const expectedState = {
            ...initialState,
            isLoading: false,
            error: payload,
        };
        expect(wbsReducer(initialState, { type: types.WBS_FAILURE, payload })).toEqual(expectedState);
    });

    it('should handle WBS_PENDING_APPROVER_REQUEST', () => {
        const expectedState = {
            ...initialState,
            isPendingLoading: true,
            pendingApprovalError: null,
        };
        expect(wbsReducer(initialState, { type: types.WBS_PENDING_APPROVER_REQUEST })).toEqual(expectedState);
    });

    it('should handle WBS_PENDING_APPROVAL_SUCCESS', () => {
        const payload: WbsPendingApprovalModal[] = [{
            wbsId: 1,
            wbsName: "Test WBS",
            contractorName: "Contractor A",
            location: "Location A",
            subLocation: "Sub Location A",
            jobName: "Job A",
            pipeLength: 100,
            pipeSize: 12,
            startDate: "2024-01-01",
            endDate: "2024-12-31",
            status: "Pending",
            action: "Review",
        }];
        const expectedState = {
            ...initialState,
            isPendingLoading: false,
            pendingApprovalResponse: payload,
        };
        expect(wbsReducer(initialState, { type: types.WBS_PENDING_APPROVAL_SUCCESS, payload })).toEqual(expectedState);
    });

    it('should handle WBS_PENDING_APPROVAL_FAILURE', () => {
        const payload = 'Error message';
        const expectedState = {
            ...initialState,
            isPendingLoading: false,
            pendingApprovalError: null,
        };
        expect(wbsReducer(initialState, { type: types.WBS_PENDING_APPROVAL_FAILURE, payload })).toEqual(expectedState);
    });
}); 