import { runSaga } from 'redux-saga';
import { takeLatest } from 'redux-saga/effects';
import * as api from '../../../../src/services/ApiRequests';
import * as wbsSaga from '../../../../src/redux/WBSDownload/WBSSaga';
import * as wbsActions from '../../../../src/redux/WBSDownload/WBSActions';
import * as db from '../../../../src/database/WBSDataInsert/StoreWbsHierarchyData';
import { WBS_REQUEST } from '../../../../src/redux/WBSDownload/WBSActionTypes';
import { wbsRequest } from '../../../../src/redux/WBSDownload/WBSActions';
import { WbsRequestData } from '../../../../src/model/Wbs/WbsData';

jest.mock('../../../../src/services/ApiRequests');
jest.mock('../../../../src/database/WBSDataInsert/StoreWbsHierarchyData');
jest.mock('../../../../src/database/WBSDataInsert/StoreWbsDetailsData');
jest.mock('../../../../src/database/WBSDataInsert/StoreWbsGISDetailsData');
jest.mock('../../../../src/database/WBSDataInsert/StoreProgressConsolidatedData');
jest.mock('../../../../src/database/WBSDataInsert/StoreProgressEngineerData');
jest.mock('../../../../src/database/WBSDataInsert/StoreViewLastUpdateBQITData');
jest.mock('../../../../src/database/WBSDataInsert/StoreViewLastUpdateGISData');
jest.mock('../../../../src/database/WBSDataInsert/StoreBookMarkListData');
jest.mock('../../../../src/database/WBSDataInsert/StoreLatLongHierarchyData');
jest.mock('../../../../src/database/WBSDataInsert/StoreWbsTaskData');
jest.mock('../../../../src/utils/Storage/Storage');
jest.mock('../../../../src/components/CustomAlert');
jest.mock('../../../../src/database/WBSDataInsert/StorePendingApprovalData');
jest.mock('../../../../src/database/WBSDataInsert/StoreEpragatiGisPipesData');

describe('WBSDownload Saga', () => {
    const mockedApi = api as jest.Mocked<any>;

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should watch for WBS_REQUEST action', () => {
        const gen = wbsSaga.watchWbsRequest();
        expect(gen.next().value).toEqual(takeLatest(WBS_REQUEST, wbsSaga.fetchWbsJob));
        expect(gen.next().done).toBe(true);
    });

    it('should handle successful WBS download', async () => {
        const dispatched: any[] = [];
        const payload: WbsRequestData = {
            userId: 'testUser',
            objJoblist: [{
                "jobId": 2,
                "jobName": "JC-2",
                "wbsId": 1,
                "wbsName": "Wbs-1"
            }],
            Fromdate: '2024-01-01',
            Todate: '2024-01-31',
            selectedJob: {
                "jobId": 2,
                "jobName": "JC-2",
                "wbsId": 1,
                "wbsName": "Wbs-1"
            },
            isLogin: false,
        };

        const mockResponse = {
            WbsHierarchyOutput: [{ id: 1, name: 'WBS root' }],
            WbsTaskOutput: [{ id: 1, name: 'Task 1' }],
            WbsDetailsOutput: [{ id: 1, name: 'Detail 1' }],
            WbsGISDetailsOutput: [{ id: 1, name: 'GIS Detail 1' }],
            ProgressConsolidatedOutput: [{ id: 1, name: 'Progress 1' }],
            DeliverableProgressDetailsEnginneerOutput: [{ id: 1, name: 'Engineer Progress 1' }],
            ViewLastUpdateBQITFromTodateOutput: [{ id: 1, name: 'BQIT Update 1' }],
            ViewLastUpdateGISFromTodateOutput: [{ id: 1, name: 'GIS Update 1' }],
        };

        mockedApi.postDataWithBodyForDownload.mockImplementation((_url: any, _body: any, success: (arg0: any) => void, _failure: any) => {
            success(mockResponse);
        });

        const action = wbsRequest(payload);

        await runSaga({
            dispatch: (event) => dispatched.push(event),
            getState: () => ({}),
        }, wbsSaga.fetchWbsJob, action).toPromise();

        expect(dispatched).toHaveLength(1);
    });

    it('should handle WBS download failure', async () => {
        const dispatched: any[] = [];
        const payload: WbsRequestData = {
            userId: 'testUser',
            objJoblist: [],
            Fromdate: '2024-01-01',
            Todate: '2024-01-31',
            selectedJob: null,
            isLogin: false,
        };
        const error = new Error('API Error');

        mockedApi.postDataWithBodyForDownload.mockImplementation((_url: any, _body: any, _success: any, failure: (arg0: { response: { status: number; }; }) => void) => {
            failure({ response: { status: 500 } });
        });

        const action = wbsRequest(payload);

        await runSaga({
            dispatch: (event) => dispatched.push(event),
            getState: () => ({}),
        }, wbsSaga.fetchWbsJob, action).toPromise();

        expect(dispatched).toHaveLength(1);
    });
}); 