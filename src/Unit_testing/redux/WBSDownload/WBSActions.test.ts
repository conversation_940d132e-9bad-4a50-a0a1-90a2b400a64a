import * as actions from '../../../../src/redux/WBSDownload/WBSActions';
import * as types from '../../../../src/redux/WBSDownload/WBSActionTypes';
import { WbsRequestData, GetWbsApproverProps, WbsJobResponseData, WbsPendingApprovalModal } from '../../../../src/model/Wbs/WbsData';

describe('WBSDownload actions', () => {
    it('should create an action for WBS request', () => {
        const payload: WbsRequestData = {
            userId: 'testUser',
            isContractor: true,
            wbsId: 123,
        };
        const expectedAction = {
            type: types.WBS_REQUEST,
            payload,
        };
        expect(actions.wbsRequest(payload)).toEqual(expectedAction);
    });

    it('should create an action for get pending approver WBS request', () => {
        const payload: GetWbsApproverProps = {
            userId: 'testUser',
        };
        const expectedAction = {
            type: types.WBS_APPROVER_REQUEST,
            payload,
        };
        expect(actions.getPendingApproverWbsRequest(payload)).toEqual(expectedAction);
    });

    it('should create an action for WBS success', () => {
        const payload: WbsJobResponseData = {
            message: 'Success',
            status: 200,
            data: [],
        };
        const expectedAction = {
            type: types.WBS_SUCCESS,
            payload,
        };
        expect(actions.wbsSuccess(payload)).toEqual(expectedAction);
    });

    it('should create an action for WBS failure', () => {
        const payload = 'Error message';
        const expectedAction = {
            type: types.WBS_FAILURE,
            payload,
        };
        expect(actions.wbsFailure(payload)).toEqual(expectedAction);
    });

    it('should create an action for fetch WBS data success', () => {
        const payload: WbsJobResponseData = {
            message: 'Success',
            status: 200,
            data: [],
        };
        const expectedAction = {
            type: types.WBS_SUCCESS,
            payload,
        };
        expect(actions.fetchWBSDataSuccess(payload)).toEqual(expectedAction);
    });

    it('should create an action for fetch WBS data failure', () => {
        const payload = 'Error message';
        const expectedAction = {
            type: types.WBS_FAILURE,
            payload,
        };
        expect(actions.fetchWBSDataFailure(payload)).toEqual(expectedAction);
    });

    it('should create an action for WBS pending approval success', () => {
        const payload: WbsPendingApprovalModal[] = [{
            wbsId: 1,
            wbsName: "Test WBS",
            contractorName: "Contractor A",
            location: "Location A",
            subLocation: "Sub Location A",
            jobName: "Job A",
            pipeLength: 100,
            pipeSize: 12,
            startDate: "2024-01-01",
            endDate: "2024-12-31",
            status: "Pending",
            action: "Review",
        }];
        const expectedAction = {
            type: types.WBS_PENDING_APPROVAL_SUCCESS,
            payload,
        };
        expect(actions.wbsPendingApprovalSuccess(payload)).toEqual(expectedAction);
    });

    it('should create an action for WBS pending approval failure', () => {
        const payload = 'Error message';
        const expectedAction = {
            type: types.WBS_PENDING_APPROVAL_FAILURE,
            payload,
        };
        expect(actions.wbsPendingApprovalFailure(payload)).toEqual(expectedAction);
    });
}); 