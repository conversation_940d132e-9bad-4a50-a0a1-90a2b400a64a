import {
  setJobsDownloaded,
  setSelectedCurrentJobId,
} from '../../../redux/HomeRedux/HomeActions';
import {
  JOBS_DOWNLOADED,
  CURRENT_JOB_ID,
} from '../../../redux/HomeRedux/HomeActionTypes';

describe('Home Actions', () => {
  describe('setJobsDownloaded', () => {
    it('should create setJobsDownloaded action with true payload', () => {
      const isJobsDownloaded = true;
      const expectedAction = {
        type: JOBS_DOWNLOADED,
        payload: isJobsDownloaded,
      };
      expect(setJobsDownloaded(isJobsDownloaded)).toEqual(expectedAction);
    });

    it('should create setJobsDownloaded action with false payload', () => {
      const isJobsDownloaded = false;
      const expectedAction = {
        type: JOBS_DOWNLOADED,
        payload: isJobsDownloaded,
      };
      expect(setJobsDownloaded(isJobsDownloaded)).toEqual(expectedAction);
    });

    it('should return action with correct type', () => {
      const action = setJobsDownloaded(true);
      expect(action.type).toBe(JOBS_DOWNLOADED);
    });

    it('should include boolean payload', () => {
      const action = setJobsDownloaded(true);
      expect(action.payload).toBe(true);
      expect(typeof action.payload).toBe('boolean');
    });

    it('should handle boolean false correctly', () => {
      const action = setJobsDownloaded(false);
      expect(action.payload).toBe(false);
      expect(typeof action.payload).toBe('boolean');
    });

    it('should handle null payload as boolean', () => {
      const action = setJobsDownloaded(null as any);
      expect(action.payload).toBeNull();
    });

    it('should handle undefined payload as boolean', () => {
      const action = setJobsDownloaded(undefined as any);
      expect(action.payload).toBeUndefined();
    });

    it('should handle non-boolean payload', () => {
      const action = setJobsDownloaded('true' as any);
      expect(action.payload).toBe('true');
    });

    it('should create consistent action structure', () => {
      const action = setJobsDownloaded(true);
      expect(action).toHaveProperty('type');
      expect(action).toHaveProperty('payload');
      expect(Object.keys(action)).toEqual(['type', 'payload']);
    });

    it('should be a pure function', () => {
      const action1 = setJobsDownloaded(true);
      const action2 = setJobsDownloaded(true);
      expect(action1).toEqual(action2);
    });

    it('should create different actions for different payloads', () => {
      const action1 = setJobsDownloaded(true);
      const action2 = setJobsDownloaded(false);
      expect(action1.payload).not.toBe(action2.payload);
    });
  });

  describe('setSelectedCurrentJobId', () => {
    it('should create setSelectedCurrentJobId action with string payload', () => {
      const jobId = 'job-123';
      const expectedAction = {
        type: CURRENT_JOB_ID,
        payload: jobId,
      };
      expect(setSelectedCurrentJobId(jobId)).toEqual(expectedAction);
    });

    it('should return action with correct type', () => {
      const action = setSelectedCurrentJobId('job-456');
      expect(action.type).toBe(CURRENT_JOB_ID);
    });

    it('should include string payload', () => {
      const jobId = 'job-789';
      const action = setSelectedCurrentJobId(jobId);
      expect(action.payload).toBe(jobId);
      expect(typeof action.payload).toBe('string');
    });

    it('should handle empty string payload', () => {
      const action = setSelectedCurrentJobId('');
      expect(action.payload).toBe('');
    });

    it('should handle long string payload', () => {
      const longJobId = 'a'.repeat(1000);
      const action = setSelectedCurrentJobId(longJobId);
      expect(action.payload).toBe(longJobId);
    });

    it('should handle special characters in job id', () => {
      const specialJobId = 'job-@#$%^&*()_+{}[]|\\:";\'<>?,./`~';
      const action = setSelectedCurrentJobId(specialJobId);
      expect(action.payload).toBe(specialJobId);
    });

    it('should handle numeric string payload', () => {
      const numericJobId = '12345';
      const action = setSelectedCurrentJobId(numericJobId);
      expect(action.payload).toBe(numericJobId);
    });

    it('should handle UUID-like string payload', () => {
      const uuidJobId = '123e4567-e89b-12d3-a456-************';
      const action = setSelectedCurrentJobId(uuidJobId);
      expect(action.payload).toBe(uuidJobId);
    });

    it('should handle null payload', () => {
      const action = setSelectedCurrentJobId(null as any);
      expect(action.payload).toBeNull();
    });

    it('should handle undefined payload', () => {
      const action = setSelectedCurrentJobId(undefined as any);
      expect(action.payload).toBeUndefined();
    });

    it('should handle non-string payload', () => {
      const action = setSelectedCurrentJobId(123 as any);
      expect(action.payload).toBe(123);
    });

    it('should create consistent action structure', () => {
      const action = setSelectedCurrentJobId('job-test');
      expect(action).toHaveProperty('type');
      expect(action).toHaveProperty('payload');
      expect(Object.keys(action)).toEqual(['type', 'payload']);
    });

    it('should be a pure function', () => {
      const jobId = 'job-test';
      const action1 = setSelectedCurrentJobId(jobId);
      const action2 = setSelectedCurrentJobId(jobId);
      expect(action1).toEqual(action2);
    });

    it('should create different actions for different payloads', () => {
      const action1 = setSelectedCurrentJobId('job-1');
      const action2 = setSelectedCurrentJobId('job-2');
      expect(action1.payload).not.toBe(action2.payload);
    });
  });

  describe('Action Type Constants', () => {
    it('should have correct action type constants', () => {
      expect(JOBS_DOWNLOADED).toBe('JOBS_DOWNLOADED');
      expect(CURRENT_JOB_ID).toBe('CURRENT_JOB_ID');
    });

    it('should have different action type constants', () => {
      expect(JOBS_DOWNLOADED).not.toBe(CURRENT_JOB_ID);
    });
  });

  describe('Action Creators Integration', () => {
    it('should create different action types for different creators', () => {
      const downloadAction = setJobsDownloaded(true);
      const jobIdAction = setSelectedCurrentJobId('job-123');

      expect(downloadAction.type).not.toBe(jobIdAction.type);
    });

    it('should maintain consistent action structure', () => {
      const downloadAction = setJobsDownloaded(true);
      const jobIdAction = setSelectedCurrentJobId('job-123');

      expect(downloadAction).toHaveProperty('type');
      expect(downloadAction).toHaveProperty('payload');
      expect(jobIdAction).toHaveProperty('type');
      expect(jobIdAction).toHaveProperty('payload');
    });

    it('should both return objects with type and payload properties', () => {
      const downloadAction = setJobsDownloaded(true);
      const jobIdAction = setSelectedCurrentJobId('job-123');

      expect(typeof downloadAction).toBe('object');
      expect(typeof jobIdAction).toBe('object');
      expect(typeof downloadAction.type).toBe('string');
      expect(typeof jobIdAction.type).toBe('string');
    });
  });

  describe('Action Creator Behavior', () => {
    it('should be pure functions', () => {
      const download1 = setJobsDownloaded(true);
      const download2 = setJobsDownloaded(true);
      const jobId1 = setSelectedCurrentJobId('job-123');
      const jobId2 = setSelectedCurrentJobId('job-123');

      expect(download1).toEqual(download2);
      expect(jobId1).toEqual(jobId2);
    });

    it('should not modify any external state', () => {
      const originalJobsDownloaded = JOBS_DOWNLOADED;
      const originalCurrentJobId = CURRENT_JOB_ID;

      setJobsDownloaded(true);
      setSelectedCurrentJobId('job-123');

      expect(JOBS_DOWNLOADED).toBe(originalJobsDownloaded);
      expect(CURRENT_JOB_ID).toBe(originalCurrentJobId);
    });

    it('should create new objects on each call', () => {
      const download1 = setJobsDownloaded(true);
      const download2 = setJobsDownloaded(true);
      const jobId1 = setSelectedCurrentJobId('job-123');
      const jobId2 = setSelectedCurrentJobId('job-123');

      expect(download1).not.toBe(download2);
      expect(jobId1).not.toBe(jobId2);
    });

    it('should handle rapid successive calls', () => {
      const actions = [];
      for (let i = 0; i < 100; i++) {
        actions.push(setJobsDownloaded(true));
        actions.push(setSelectedCurrentJobId('job-' + i));
      }

      const downloadActions = actions.filter(action => action.type === JOBS_DOWNLOADED);
      const jobIdActions = actions.filter(action => action.type === CURRENT_JOB_ID);

      expect(downloadActions).toHaveLength(100);
      expect(jobIdActions).toHaveLength(100);
    });
  });

  describe('Function Properties', () => {
    it('should be functions', () => {
      expect(typeof setJobsDownloaded).toBe('function');
      expect(typeof setSelectedCurrentJobId).toBe('function');
    });

    it('should be callable with one argument', () => {
      expect(() => setJobsDownloaded(true)).not.toThrow();
      expect(() => setSelectedCurrentJobId('job-123')).not.toThrow();
    });

    it('should ignore extra arguments', () => {
      const action1 = setJobsDownloaded(true);
      const action2 = setJobsDownloaded(true, 'extra', 'arguments');
      const action3 = setSelectedCurrentJobId('job-123');
      const action4 = setSelectedCurrentJobId('job-123', 'extra', 'arguments');

      expect(action1).toEqual(action2);
      expect(action3).toEqual(action4);
    });
  });

  describe('Action Object Structure', () => {
    it('should create actions with valid structure', () => {
      const downloadAction = setJobsDownloaded(true);
      const jobIdAction = setSelectedCurrentJobId('job-123');

      expect(downloadAction).toMatchObject({
        type: expect.any(String),
        payload: expect.any(Boolean),
      });
      expect(jobIdAction).toMatchObject({
        type: expect.any(String),
        payload: expect.any(String),
      });
    });

    it('should create actions that can be serialized', () => {
      const downloadAction = setJobsDownloaded(true);
      const jobIdAction = setSelectedCurrentJobId('job-123');

      expect(() => JSON.stringify(downloadAction)).not.toThrow();
      expect(() => JSON.stringify(jobIdAction)).not.toThrow();

      const serializedDownload = JSON.stringify(downloadAction);
      const serializedJobId = JSON.stringify(jobIdAction);

      expect(JSON.parse(serializedDownload)).toEqual(downloadAction);
      expect(JSON.parse(serializedJobId)).toEqual(jobIdAction);
    });
  });

  describe('Integration with Action Types', () => {
    it('should use action type constants correctly', () => {
      const downloadAction = setJobsDownloaded(true);
      const jobIdAction = setSelectedCurrentJobId('job-123');

      expect(downloadAction.type).toBe(JOBS_DOWNLOADED);
      expect(jobIdAction.type).toBe(CURRENT_JOB_ID);
    });

    it('should maintain type consistency with constants', () => {
      const downloadAction = setJobsDownloaded(true);
      const jobIdAction = setSelectedCurrentJobId('job-123');

      expect(downloadAction.type).toEqual(JOBS_DOWNLOADED);
      expect(jobIdAction.type).toEqual(CURRENT_JOB_ID);
    });
  });
}); 