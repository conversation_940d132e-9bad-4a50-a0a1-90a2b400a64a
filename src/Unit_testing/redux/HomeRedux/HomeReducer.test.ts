import { HomeReducer } from '../../../redux/HomeRedux/HomeReducer';
import {
  JOBS_DOWNLOADED,
  CURRENT_JOB_ID,
} from '../../../redux/HomeRedux/HomeActionTypes';

describe('Home Reducer', () => {
  const initialState = {
    isJobsDownloaded: false,
    currentJobId: '',
  };

  describe('Initial State', () => {
    it('should return initial state when no action is passed', () => {
      const result = HomeReducer(undefined, {} as any);
      expect(result).toEqual(initialState);
    });

    it('should have correct initial state structure', () => {
      const result = HomeReducer(undefined, {} as any);
      expect(result).toHaveProperty('isJobsDownloaded');
      expect(result).toHaveProperty('currentJobId');
    });

    it('should have isJobsDownloaded as false initially', () => {
      const result = HomeReducer(undefined, {} as any);
      expect(result.isJobsDownloaded).toBe(false);
    });

    it('should have currentJobId as empty string initially', () => {
      const result = HomeReducer(undefined, {} as any);
      expect(result.currentJobId).toBe('');
    });
  });

  describe('JOBS_DOWNLOADED', () => {
    it('should handle JOBS_DOWNLOADED action with true payload', () => {
      const action = {
        type: JOBS_DOWNLOADED,
        payload: true,
      };
      const result = HomeReducer(initialState, action);
      expect(result.isJobsDownloaded).toBe(true);
      expect(result.currentJobId).toBe('');
    });

    it('should handle JOBS_DOWNLOADED action with false payload', () => {
      const action = {
        type: JOBS_DOWNLOADED,
        payload: false,
      };
      const result = HomeReducer(initialState, action);
      expect(result.isJobsDownloaded).toBe(false);
      expect(result.currentJobId).toBe('');
    });

    it('should update isJobsDownloaded from false to true', () => {
      const action = {
        type: JOBS_DOWNLOADED,
        payload: true,
      };
      const result = HomeReducer(initialState, action);
      expect(result.isJobsDownloaded).toBe(true);
    });

    it('should update isJobsDownloaded from true to false', () => {
      const stateWithJobsDownloaded = {
        ...initialState,
        isJobsDownloaded: true,
      };
      const action = {
        type: JOBS_DOWNLOADED,
        payload: false,
      };
      const result = HomeReducer(stateWithJobsDownloaded, action);
      expect(result.isJobsDownloaded).toBe(false);
    });

    it('should preserve currentJobId when updating isJobsDownloaded', () => {
      const stateWithJobId = {
        ...initialState,
        currentJobId: 'job-123',
      };
      const action = {
        type: JOBS_DOWNLOADED,
        payload: true,
      };
      const result = HomeReducer(stateWithJobId, action);
      expect(result.currentJobId).toBe('job-123');
      expect(result.isJobsDownloaded).toBe(true);
    });

    it('should handle null payload', () => {
      const action = {
        type: JOBS_DOWNLOADED,
        payload: null,
      };
      const result = HomeReducer(initialState, action);
      expect(result.isJobsDownloaded).toBeNull();
    });

    it('should handle undefined payload', () => {
      const action = {
        type: JOBS_DOWNLOADED,
        payload: undefined,
      };
      const result = HomeReducer(initialState, action);
      expect(result.isJobsDownloaded).toBeUndefined();
    });

    it('should handle non-boolean payload', () => {
      const action = {
        type: JOBS_DOWNLOADED,
        payload: 'true',
      };
      const result = HomeReducer(initialState, action);
      expect(result.isJobsDownloaded).toBe('true');
    });

    it('should maintain immutability', () => {
      const action = {
        type: JOBS_DOWNLOADED,
        payload: true,
      };
      const result = HomeReducer(initialState, action);
      expect(result).not.toBe(initialState);
    });
  });

  describe('CURRENT_JOB_ID', () => {
    it('should handle CURRENT_JOB_ID action with string payload', () => {
      const jobId = 'job-123';
      const action = {
        type: CURRENT_JOB_ID,
        payload: jobId,
      };
      const result = HomeReducer(initialState, action);
      expect(result.currentJobId).toBe(jobId);
      expect(result.isJobsDownloaded).toBe(false);
    });

    it('should update currentJobId from empty string to job id', () => {
      const jobId = 'job-456';
      const action = {
        type: CURRENT_JOB_ID,
        payload: jobId,
      };
      const result = HomeReducer(initialState, action);
      expect(result.currentJobId).toBe(jobId);
    });

    it('should update currentJobId from one job id to another', () => {
      const stateWithJobId = {
        ...initialState,
        currentJobId: 'job-old',
      };
      const newJobId = 'job-new';
      const action = {
        type: CURRENT_JOB_ID,
        payload: newJobId,
      };
      const result = HomeReducer(stateWithJobId, action);
      expect(result.currentJobId).toBe(newJobId);
    });

    it('should preserve isJobsDownloaded when updating currentJobId', () => {
      const stateWithJobsDownloaded = {
        ...initialState,
        isJobsDownloaded: true,
      };
      const jobId = 'job-789';
      const action = {
        type: CURRENT_JOB_ID,
        payload: jobId,
      };
      const result = HomeReducer(stateWithJobsDownloaded, action);
      expect(result.isJobsDownloaded).toBe(true);
      expect(result.currentJobId).toBe(jobId);
    });

    it('should handle empty string payload', () => {
      const action = {
        type: CURRENT_JOB_ID,
        payload: '',
      };
      const result = HomeReducer(initialState, action);
      expect(result.currentJobId).toBe('');
    });

    it('should handle long string payload', () => {
      const longJobId = 'a'.repeat(1000);
      const action = {
        type: CURRENT_JOB_ID,
        payload: longJobId,
      };
      const result = HomeReducer(initialState, action);
      expect(result.currentJobId).toBe(longJobId);
    });

    it('should handle special characters in job id', () => {
      const specialJobId = 'job-@#$%^&*()_+{}[]|\\:";\'<>?,./`~';
      const action = {
        type: CURRENT_JOB_ID,
        payload: specialJobId,
      };
      const result = HomeReducer(initialState, action);
      expect(result.currentJobId).toBe(specialJobId);
    });

    it('should handle UUID-like string payload', () => {
      const uuidJobId = '123e4567-e89b-12d3-a456-************';
      const action = {
        type: CURRENT_JOB_ID,
        payload: uuidJobId,
      };
      const result = HomeReducer(initialState, action);
      expect(result.currentJobId).toBe(uuidJobId);
    });

    it('should handle null payload', () => {
      const action = {
        type: CURRENT_JOB_ID,
        payload: null,
      };
      const result = HomeReducer(initialState, action);
      expect(result.currentJobId).toBeNull();
    });

    it('should handle undefined payload', () => {
      const action = {
        type: CURRENT_JOB_ID,
        payload: undefined,
      };
      const result = HomeReducer(initialState, action);
      expect(result.currentJobId).toBeUndefined();
    });

    it('should handle non-string payload', () => {
      const action = {
        type: CURRENT_JOB_ID,
        payload: 123,
      };
      const result = HomeReducer(initialState, action);
      expect(result.currentJobId).toBe(123);
    });

    it('should maintain immutability', () => {
      const action = {
        type: CURRENT_JOB_ID,
        payload: 'job-123',
      };
      const result = HomeReducer(initialState, action);
      expect(result).not.toBe(initialState);
    });
  });

  describe('Unknown Actions', () => {
    it('should return current state for unknown action types', () => {
      const currentState = {
        isJobsDownloaded: true,
        currentJobId: 'job-123',
      };
      const unknownAction = {
        type: 'UNKNOWN_ACTION',
        payload: 'some payload',
      };
      const result = HomeReducer(currentState, unknownAction);
      expect(result).toEqual(currentState);
    });

    it('should return current state reference for unknown actions', () => {
      const currentState = {
        isJobsDownloaded: true,
        currentJobId: 'job-123',
      };
      const unknownAction = {
        type: 'RANDOM_ACTION',
      };
      const result = HomeReducer(currentState, unknownAction);
      expect(result).toBe(currentState);
    });

    it('should handle action without type', () => {
      const currentState = {
        isJobsDownloaded: true,
        currentJobId: 'job-123',
      };
      const invalidAction = {} as any;
      const result = HomeReducer(currentState, invalidAction);
      expect(result).toBe(currentState);
    });

    it('should handle action with undefined type', () => {
      const currentState = {
        isJobsDownloaded: true,
        currentJobId: 'job-123',
      };
      const undefinedAction = { type: undefined };
      const result = HomeReducer(currentState, undefinedAction);
      expect(result).toBe(currentState);
    });
  });

  describe('State Transitions', () => {
    it('should handle multiple actions in sequence', () => {
      let state = HomeReducer(initialState, {
        type: JOBS_DOWNLOADED,
        payload: true,
      });
      expect(state.isJobsDownloaded).toBe(true);

      state = HomeReducer(state, {
        type: CURRENT_JOB_ID,
        payload: 'job-123',
      });
      expect(state.currentJobId).toBe('job-123');
      expect(state.isJobsDownloaded).toBe(true);
    });

    it('should handle alternating actions', () => {
      let state = HomeReducer(initialState, {
        type: CURRENT_JOB_ID,
        payload: 'job-first',
      });
      expect(state.currentJobId).toBe('job-first');

      state = HomeReducer(state, {
        type: JOBS_DOWNLOADED,
        payload: true,
      });
      expect(state.isJobsDownloaded).toBe(true);
      expect(state.currentJobId).toBe('job-first');

      state = HomeReducer(state, {
        type: CURRENT_JOB_ID,
        payload: 'job-second',
      });
      expect(state.currentJobId).toBe('job-second');
      expect(state.isJobsDownloaded).toBe(true);
    });

    it('should handle resetting jobs downloaded', () => {
      let state = HomeReducer(initialState, {
        type: JOBS_DOWNLOADED,
        payload: true,
      });
      expect(state.isJobsDownloaded).toBe(true);

      state = HomeReducer(state, {
        type: JOBS_DOWNLOADED,
        payload: false,
      });
      expect(state.isJobsDownloaded).toBe(false);
    });

    it('should handle clearing job id', () => {
      let state = HomeReducer(initialState, {
        type: CURRENT_JOB_ID,
        payload: 'job-123',
      });
      expect(state.currentJobId).toBe('job-123');

      state = HomeReducer(state, {
        type: CURRENT_JOB_ID,
        payload: '',
      });
      expect(state.currentJobId).toBe('');
    });
  });

  describe('Type Safety', () => {
    it('should handle action with correct types', () => {
      const downloadAction = {
        type: JOBS_DOWNLOADED,
        payload: true,
      };
      const jobIdAction = {
        type: CURRENT_JOB_ID,
        payload: 'job-123',
      };

      const result1 = HomeReducer(initialState, downloadAction);
      const result2 = HomeReducer(result1, jobIdAction);

      expect(result2.isJobsDownloaded).toBe(true);
      expect(result2.currentJobId).toBe('job-123');
    });

    it('should handle action with missing payload', () => {
      const actionWithoutPayload = {
        type: JOBS_DOWNLOADED,
      };
      const result = HomeReducer(initialState, actionWithoutPayload);
      expect(result.isJobsDownloaded).toBeUndefined();
    });
  });

  describe('State Immutability', () => {
    it('should not mutate the original state', () => {
      const originalState = {
        isJobsDownloaded: false,
        currentJobId: '',
      };
      const action = {
        type: JOBS_DOWNLOADED,
        payload: true,
      };

      const newState = HomeReducer(originalState, action);

      expect(newState).not.toBe(originalState);
      expect(originalState).toEqual({
        isJobsDownloaded: false,
        currentJobId: '',
      });
    });

    it('should create new state object for each action', () => {
      const action1 = {
        type: JOBS_DOWNLOADED,
        payload: true,
      };
      const action2 = {
        type: CURRENT_JOB_ID,
        payload: 'job-123',
      };

      const state1 = HomeReducer(initialState, action1);
      const state2 = HomeReducer(state1, action2);

      expect(state1).not.toBe(initialState);
      expect(state2).not.toBe(state1);
      expect(state2).not.toBe(initialState);
    });

    it('should preserve immutability with complex state', () => {
      const complexState = {
        isJobsDownloaded: true,
        currentJobId: 'job-complex',
      };
      const action = {
        type: JOBS_DOWNLOADED,
        payload: false,
      };

      const newState = HomeReducer(complexState, action);

      expect(newState).not.toBe(complexState);
      expect(complexState.isJobsDownloaded).toBe(true);
      expect(complexState.currentJobId).toBe('job-complex');
    });
  });

  describe('State Structure', () => {
    it('should maintain correct state structure after actions', () => {
      const downloadAction = {
        type: JOBS_DOWNLOADED,
        payload: true,
      };
      const jobIdAction = {
        type: CURRENT_JOB_ID,
        payload: 'job-123',
      };

      let state = HomeReducer(initialState, downloadAction);
      expect(Object.keys(state)).toEqual(['isJobsDownloaded', 'currentJobId']);

      state = HomeReducer(state, jobIdAction);
      expect(Object.keys(state)).toEqual(['isJobsDownloaded', 'currentJobId']);
    });

    it('should not add extra properties to state', () => {
      const action = {
        type: JOBS_DOWNLOADED,
        payload: true,
        extraProperty: 'should be ignored',
      };
      const result = HomeReducer(initialState, action);
      expect(result).not.toHaveProperty('extraProperty');
    });
  });
}); 