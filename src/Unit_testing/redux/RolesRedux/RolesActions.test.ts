import {
  rolesRequest,
  rolesSuccess,
  rolesFailure,
} from '../../../redux/RolesRedux/RolesActions';
import {
  ROLES_REQUEST,
  ROLES_SUCCESS,
  ROLES_FAILURE,
} from '../../../redux/RolesRedux/RolesActionTypes';
import { RolesRequest, RolesResponse, RoleItem } from '../../../model/Roles/RolesData';

describe('Roles Actions', () => {
  const mockCallback = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rolesRequest', () => {
    const mockRolesRequest: RolesRequest = {
      User_ID: '12345',
      RoleType: 'admin',
      cb: mockCallback,
    };

    it('should create rolesRequest action', () => {
      const expectedAction = {
        type: ROLES_REQUEST,
        payload: mockRolesRequest,
      };
      expect(rolesRequest(mockRolesRequest)).toEqual(expectedAction);
    });

    it('should return action with correct type', () => {
      const action = rolesRequest(mockRolesRequest);
      expect(action.type).toBe(ROLES_REQUEST);
    });

    it('should include request payload', () => {
      const action = rolesRequest(mockRolesRequest);
      expect(action.payload).toEqual(mockRolesRequest);
      expect(action.payload.User_ID).toBe('12345');
      expect(action.payload.RoleType).toBe('admin');
      expect(action.payload.cb).toBe(mockCallback);
    });

    it('should handle request with different User_ID', () => {
      const differentRequest: RolesRequest = {
        User_ID: '67890',
        RoleType: 'user',
        cb: mockCallback,
      };
      const action = rolesRequest(differentRequest);
      expect(action.payload.User_ID).toBe('67890');
      expect(action.payload.RoleType).toBe('user');
    });

    it('should handle request with empty User_ID', () => {
      const emptyUserRequest: RolesRequest = {
        User_ID: '',
        RoleType: 'admin',
        cb: mockCallback,
      };
      const action = rolesRequest(emptyUserRequest);
      expect(action.payload.User_ID).toBe('');
    });

    it('should handle request with special characters in User_ID', () => {
      const specialCharRequest: RolesRequest = {
        User_ID: 'user@#$%^&*()',
        RoleType: 'admin',
        cb: mockCallback,
      };
      const action = rolesRequest(specialCharRequest);
      expect(action.payload.User_ID).toBe('user@#$%^&*()');
    });

    it('should handle request with different role types', () => {
      const roleTypes = ['admin', 'user', 'manager', 'supervisor', 'guest'];
      roleTypes.forEach(roleType => {
        const request: RolesRequest = {
          User_ID: '12345',
          RoleType: roleType,
          cb: mockCallback,
        };
        const action = rolesRequest(request);
        expect(action.payload.RoleType).toBe(roleType);
      });
    });

    it('should handle request with null callback', () => {
      const requestWithNullCb: RolesRequest = {
        User_ID: '12345',
        RoleType: 'admin',
        cb: null as any,
      };
      const action = rolesRequest(requestWithNullCb);
      expect(action.payload.cb).toBeNull();
    });

    it('should handle request with undefined callback', () => {
      const requestWithUndefinedCb: RolesRequest = {
        User_ID: '12345',
        RoleType: 'admin',
        cb: undefined as any,
      };
      const action = rolesRequest(requestWithUndefinedCb);
      expect(action.payload.cb).toBeUndefined();
    });

    it('should create consistent action structure', () => {
      const action = rolesRequest(mockRolesRequest);
      expect(action).toHaveProperty('type');
      expect(action).toHaveProperty('payload');
      expect(Object.keys(action)).toEqual(['type', 'payload']);
    });

    it('should be a pure function', () => {
      const action1 = rolesRequest(mockRolesRequest);
      const action2 = rolesRequest(mockRolesRequest);
      expect(action1).toEqual(action2);
    });
  });

  describe('rolesSuccess', () => {
    const mockRoleItem: RoleItem = {
      User_ID: 12345,
      User_Full_Name: 'John Doe',
      Login_Name: 'john.doe',
      Jobcode: 'JOB001',
      JobDesc: 'Software Engineer',
      FRCode: 1001,
      Functional_ROLE: 'Developer',
    };

    const mockRolesResponse: RolesResponse = {
      RolesList: [mockRoleItem],
    };

    it('should create rolesSuccess action with roles response', () => {
      const expectedAction = {
        type: ROLES_SUCCESS,
        payload: mockRolesResponse,
      };
      expect(rolesSuccess(mockRolesResponse)).toEqual(expectedAction);
    });

    it('should return action with correct type', () => {
      const action = rolesSuccess(mockRolesResponse);
      expect(action.type).toBe(ROLES_SUCCESS);
    });

    it('should include roles response in payload', () => {
      const action = rolesSuccess(mockRolesResponse);
      expect(action.payload).toEqual(mockRolesResponse);
      expect(action.payload.RolesList).toHaveLength(1);
      expect(action.payload.RolesList[0]).toEqual(mockRoleItem);
    });

    it('should handle response with multiple roles', () => {
      const multipleRolesResponse: RolesResponse = {
        RolesList: [
          mockRoleItem,
          {
            User_ID: 67890,
            User_Full_Name: 'Jane Smith',
            Login_Name: 'jane.smith',
            Jobcode: 'JOB002',
            JobDesc: 'Project Manager',
            FRCode: 1002,
            Functional_ROLE: 'Manager',
          },
        ],
      };
      const action = rolesSuccess(multipleRolesResponse);
      expect(action.payload.RolesList).toHaveLength(2);
    });

    it('should handle response with empty roles list', () => {
      const emptyRolesResponse: RolesResponse = {
        RolesList: [],
      };
      const action = rolesSuccess(emptyRolesResponse);
      expect(action.payload.RolesList).toHaveLength(0);
    });

    it('should handle response with role containing special characters', () => {
      const specialRoleItem: RoleItem = {
        User_ID: 12345,
        User_Full_Name: 'João José-Martinez',
        Login_Name: 'joão.josé',
        Jobcode: 'JOB@#$',
        JobDesc: 'Software Engineer & Team Lead',
        FRCode: 1001,
        Functional_ROLE: 'Senior Developer/Architect',
      };
      const specialRolesResponse: RolesResponse = {
        RolesList: [specialRoleItem],
      };
      const action = rolesSuccess(specialRolesResponse);
      expect(action.payload.RolesList[0].User_Full_Name).toBe('João José-Martinez');
    });

    it('should handle response with large role list', () => {
      const largeRolesList: RoleItem[] = Array.from({ length: 100 }, (_, index) => ({
        User_ID: 10000 + index,
        User_Full_Name: `User ${index}`,
        Login_Name: `user${index}`,
        Jobcode: `JOB${index.toString().padStart(3, '0')}`,
        JobDesc: `Job Description ${index}`,
        FRCode: 2000 + index,
        Functional_ROLE: `Role ${index}`,
      }));
      const largeRolesResponse: RolesResponse = {
        RolesList: largeRolesList,
      };
      const action = rolesSuccess(largeRolesResponse);
      expect(action.payload.RolesList).toHaveLength(100);
    });

    it('should maintain reference to payload', () => {
      const action = rolesSuccess(mockRolesResponse);
      expect(action.payload).toBe(mockRolesResponse); // Action creators use direct assignment
    });

    it('should create consistent action structure', () => {
      const action = rolesSuccess(mockRolesResponse);
      expect(action).toHaveProperty('type');
      expect(action).toHaveProperty('payload');
      expect(Object.keys(action)).toEqual(['type', 'payload']);
    });
  });

  describe('rolesFailure', () => {
    it('should create rolesFailure action with error message', () => {
      const errorMessage = 'Failed to fetch roles';
      const expectedAction = {
        type: ROLES_FAILURE,
        payload: errorMessage,
      };
      expect(rolesFailure(errorMessage)).toEqual(expectedAction);
    });

    it('should return action with correct type', () => {
      const action = rolesFailure('Error message');
      expect(action.type).toBe(ROLES_FAILURE);
    });

    it('should include error message in payload', () => {
      const errorMessage = 'Network error occurred';
      const action = rolesFailure(errorMessage);
      expect(action.payload).toBe(errorMessage);
    });

    it('should handle empty error message', () => {
      const action = rolesFailure('');
      expect(action.payload).toBe('');
    });

    it('should handle null error message', () => {
      const action = rolesFailure(null as any);
      expect(action.payload).toBeNull();
    });

    it('should handle undefined error message', () => {
      const action = rolesFailure(undefined as any);
      expect(action.payload).toBeUndefined();
    });

    it('should handle network error message', () => {
      const networkError = 'Network request failed - timeout';
      const action = rolesFailure(networkError);
      expect(action.payload).toBe(networkError);
    });

    it('should handle authentication error message', () => {
      const authError = 'Authentication failed - invalid token';
      const action = rolesFailure(authError);
      expect(action.payload).toBe(authError);
    });

    it('should handle server error message', () => {
      const serverError = 'Internal server error - 500';
      const action = rolesFailure(serverError);
      expect(action.payload).toBe(serverError);
    });

    it('should handle validation error message', () => {
      const validationError = 'Invalid User_ID format';
      const action = rolesFailure(validationError);
      expect(action.payload).toBe(validationError);
    });

    it('should handle long error message', () => {
      const longError = 'A'.repeat(1000) + ' - roles fetch error';
      const action = rolesFailure(longError);
      expect(action.payload).toBe(longError);
    });

    it('should handle error message with special characters', () => {
      const specialError = 'Error: @#$%^&*()_+{}[]|\\:";\'<>?,./`~';
      const action = rolesFailure(specialError);
      expect(action.payload).toBe(specialError);
    });

    it('should create consistent action structure', () => {
      const action = rolesFailure('Test error');
      expect(action).toHaveProperty('type');
      expect(action).toHaveProperty('payload');
      expect(Object.keys(action)).toEqual(['type', 'payload']);
    });
  });

  describe('Action Type Constants', () => {
    it('should have correct action type constants', () => {
      expect(ROLES_REQUEST).toBe('ROLES_REQUEST');
      expect(ROLES_SUCCESS).toBe('ROLES_SUCCESS');
      expect(ROLES_FAILURE).toBe('ROLES_FAILURE');
    });

    it('should have different action type constants', () => {
      expect(ROLES_REQUEST).not.toBe(ROLES_SUCCESS);
      expect(ROLES_SUCCESS).not.toBe(ROLES_FAILURE);
      expect(ROLES_FAILURE).not.toBe(ROLES_REQUEST);
    });
  });

  describe('Action Creators Integration', () => {
    it('should create different action types for different creators', () => {
      const requestAction = rolesRequest({
        User_ID: '123',
        RoleType: 'admin',
        cb: mockCallback,
      });
      const successAction = rolesSuccess({
        RolesList: [],
      });
      const failureAction = rolesFailure('Error');

      expect(requestAction.type).not.toBe(successAction.type);
      expect(successAction.type).not.toBe(failureAction.type);
      expect(failureAction.type).not.toBe(requestAction.type);
    });

    it('should maintain consistent action structure', () => {
      const requestAction = rolesRequest({
        User_ID: '123',
        RoleType: 'admin',
        cb: mockCallback,
      });
      const successAction = rolesSuccess({
        RolesList: [],
      });
      const failureAction = rolesFailure('Error');

      expect(requestAction).toHaveProperty('type');
      expect(requestAction).toHaveProperty('payload');
      expect(successAction).toHaveProperty('type');
      expect(successAction).toHaveProperty('payload');
      expect(failureAction).toHaveProperty('type');
      expect(failureAction).toHaveProperty('payload');
    });
  });

  describe('Action Creator Behavior', () => {
    it('should be pure functions', () => {
      const request = {
        User_ID: '123',
        RoleType: 'admin',
        cb: mockCallback,
      };
      const response = { RolesList: [] };
      const error = 'Test error';

      const request1 = rolesRequest(request);
      const request2 = rolesRequest(request);
      const success1 = rolesSuccess(response);
      const success2 = rolesSuccess(response);
      const failure1 = rolesFailure(error);
      const failure2 = rolesFailure(error);

      expect(request1).toEqual(request2);
      expect(success1).toEqual(success2);
      expect(failure1).toEqual(failure2);
    });

    it('should not modify any external state', () => {
      const originalRequest = ROLES_REQUEST;
      const originalSuccess = ROLES_SUCCESS;
      const originalFailure = ROLES_FAILURE;

      rolesRequest({ User_ID: '123', RoleType: 'admin', cb: mockCallback });
      rolesSuccess({ RolesList: [] });
      rolesFailure('Error');

      expect(ROLES_REQUEST).toBe(originalRequest);
      expect(ROLES_SUCCESS).toBe(originalSuccess);
      expect(ROLES_FAILURE).toBe(originalFailure);
    });

    it('should create new objects on each call', () => {
      const request = {
        User_ID: '123',
        RoleType: 'admin',
        cb: mockCallback,
      };

      const action1 = rolesRequest(request);
      const action2 = rolesRequest(request);

      expect(action1).not.toBe(action2);
      expect(action1.payload).toBe(action2.payload); // Same reference for same input
    });
  });

  describe('Type Safety', () => {
    it('should handle action with correct payload types', () => {
      const request: RolesRequest = {
        User_ID: '123',
        RoleType: 'admin',
        cb: mockCallback,
      };
      const response: RolesResponse = {
        RolesList: [],
      };

      const requestAction = rolesRequest(request);
      const successAction = rolesSuccess(response);
      const failureAction = rolesFailure('Error');

      expect(requestAction.payload).toEqual(request);
      expect(successAction.payload).toEqual(response);
      expect(failureAction.payload).toBe('Error');
    });
  });
}); 