import { takeLatest } from 'redux-saga/effects';
import { default as rolesSaga } from '../../../redux/RolesRedux/RolesSaga';
import { ROLES_REQUEST } from '../../../redux/RolesRedux/RolesActionTypes';

// Mock dependencies
jest.mock('../../../services/ApiRequests');
jest.mock('../../../utils/Logger/PrintLog');
jest.mock('../../../components/CustomToast');
jest.mock('../../../utils/DataStorage/Storage');
jest.mock('i18next', () => ({
  t: jest.fn((key) => key),
}));

describe('Roles Saga', () => {
  describe('rolesSaga watcher', () => {
    it('should watch for ROLES_REQUEST actions', () => {
      const generator = rolesSaga();
      const next = generator.next();
      
      expect(next.value).toEqual(takeLatest(ROLES_REQUEST, expect.any(Function)));
      expect(next.done).toBe(false);
    });

    it('should complete the saga generator', () => {
      const generator = rolesSaga();
      generator.next(); // takeLatest call
      const next = generator.next();
      
      expect(next.done).toBe(true);
    });
  });

  describe('Saga Integration', () => {
    it('should export the default saga correctly', () => {
      expect(rolesSaga).toBeDefined();
      expect(typeof rolesSaga).toBe('function');
    });

    it('should be a generator function', () => {
      const generator = rolesSaga();
      expect(generator).toHaveProperty('next');
      expect(generator).toHaveProperty('return');
      expect(generator).toHaveProperty('throw');
    });
  });
}); 