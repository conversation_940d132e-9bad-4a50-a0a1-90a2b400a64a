import { RolesReducer } from '../../../redux/RolesRedux/RolesReducer';
import {
  ROLES_REQUEST,
  ROLES_SUCCESS,
  ROLES_FAILURE,
} from '../../../redux/RolesRedux/RolesActionTypes';
import { RolesResponse, RoleItem } from '../../../model/Roles/RolesData';

describe('Roles Reducer', () => {
  const initialState = {
    roles: null,
    loading: true,
    rolesError: null,
  };

  describe('Initial State', () => {
    it('should return initial state when no action is passed', () => {
      const result = RolesReducer(undefined, {} as any);
      expect(result).toEqual(initialState);
    });

    it('should have correct initial state structure', () => {
      const result = RolesReducer(undefined, {} as any);
      expect(result).toHaveProperty('roles');
      expect(result).toHaveProperty('loading');
      expect(result).toHaveProperty('rolesError');
    });

    it('should have null roles initially', () => {
      const result = RolesReducer(undefined, {} as any);
      expect(result.roles).toBeNull();
    });

    it('should have loading true initially', () => {
      const result = RolesReducer(undefined, {} as any);
      expect(result.loading).toBe(true);
    });

    it('should have null rolesError initially', () => {
      const result = RolesReducer(undefined, {} as any);
      expect(result.rolesError).toBeNull();
    });
  });

  describe('ROLES_REQUEST', () => {
    it('should handle ROLES_REQUEST action', () => {
      const action = {
        type: ROLES_REQUEST,
      };
      const result = RolesReducer(initialState, action);
      expect(result.loading).toBe(true);
      expect(result.rolesError).toBeNull();
      expect(result.roles).toBeNull();
    });

    it('should reset error on roles request', () => {
      const stateWithError = {
        ...initialState,
        rolesError: 'Previous error',
      };
      const action = {
        type: ROLES_REQUEST,
      };
      const result = RolesReducer(stateWithError, action);
      expect(result.rolesError).toBeNull();
    });

    it('should preserve existing roles during request', () => {
      const existingRoles: RolesResponse = {
        RolesList: [
          {
            User_ID: 123,
            User_Full_Name: 'John Doe',
            Login_Name: 'john.doe',
            Jobcode: 'JOB001',
            JobDesc: 'Software Engineer',
            FRCode: 1001,
            Functional_ROLE: 'Developer',
          },
        ],
      };
      const stateWithRoles = {
        ...initialState,
        roles: existingRoles,
        loading: false,
      };
      const action = {
        type: ROLES_REQUEST,
      };
      const result = RolesReducer(stateWithRoles, action);
      expect(result.roles).toEqual(existingRoles);
      expect(result.loading).toBe(true);
    });

    it('should maintain immutability', () => {
      const action = {
        type: ROLES_REQUEST,
      };
      const result = RolesReducer(initialState, action);
      expect(result).not.toBe(initialState);
    });
  });

  describe('ROLES_SUCCESS', () => {
    const mockRoleItem: RoleItem = {
      User_ID: 12345,
      User_Full_Name: 'John Doe',
      Login_Name: 'john.doe',
      Jobcode: 'JOB001',
      JobDesc: 'Software Engineer',
      FRCode: 1001,
      Functional_ROLE: 'Developer',
    };

    const mockRolesResponse: RolesResponse = {
      RolesList: [mockRoleItem],
    };

    it('should handle ROLES_SUCCESS action', () => {
      const action = {
        type: ROLES_SUCCESS,
        payload: mockRolesResponse,
      };
      const result = RolesReducer(initialState, action);
      expect(result.loading).toBe(false);
      expect(result.rolesError).toBeNull();
      expect(result.roles).toEqual(mockRolesResponse);
    });

    it('should update roles from payload', () => {
      const action = {
        type: ROLES_SUCCESS,
        payload: mockRolesResponse,
      };
      const result = RolesReducer(initialState, action);
      expect(result.roles).toEqual(mockRolesResponse);
      expect(result.roles?.RolesList).toHaveLength(1);
      expect(result.roles?.RolesList[0]).toEqual(mockRoleItem);
    });

    it('should clear loading state on success', () => {
      const loadingState = {
        ...initialState,
        loading: true,
      };
      const action = {
        type: ROLES_SUCCESS,
        payload: mockRolesResponse,
      };
      const result = RolesReducer(loadingState, action);
      expect(result.loading).toBe(false);
    });

    it('should preserve error on success', () => {
      const errorState = {
        ...initialState,
        rolesError: 'Previous error',
      };
      const action = {
        type: ROLES_SUCCESS,
        payload: mockRolesResponse,
      };
      const result = RolesReducer(errorState, action);
      expect(result.rolesError).toBe('Previous error'); // Error is not cleared in actual implementation
    });

    it('should handle success with empty roles list', () => {
      const emptyRolesResponse: RolesResponse = {
        RolesList: [],
      };
      const action = {
        type: ROLES_SUCCESS,
        payload: emptyRolesResponse,
      };
      const result = RolesReducer(initialState, action);
      expect(result.roles?.RolesList).toHaveLength(0);
    });

    it('should handle success with multiple roles', () => {
      const multipleRolesResponse: RolesResponse = {
        RolesList: [
          mockRoleItem,
          {
            User_ID: 67890,
            User_Full_Name: 'Jane Smith',
            Login_Name: 'jane.smith',
            Jobcode: 'JOB002',
            JobDesc: 'Project Manager',
            FRCode: 1002,
            Functional_ROLE: 'Manager',
          },
        ],
      };
      const action = {
        type: ROLES_SUCCESS,
        payload: multipleRolesResponse,
      };
      const result = RolesReducer(initialState, action);
      expect(result.roles?.RolesList).toHaveLength(2);
    });

    it('should handle success with large roles list', () => {
      const largeRolesList: RoleItem[] = Array.from({ length: 100 }, (_, index) => ({
        User_ID: 10000 + index,
        User_Full_Name: `User ${index}`,
        Login_Name: `user${index}`,
        Jobcode: `JOB${index.toString().padStart(3, '0')}`,
        JobDesc: `Job Description ${index}`,
        FRCode: 2000 + index,
        Functional_ROLE: `Role ${index}`,
      }));
      const largeRolesResponse: RolesResponse = {
        RolesList: largeRolesList,
      };
      const action = {
        type: ROLES_SUCCESS,
        payload: largeRolesResponse,
      };
      const result = RolesReducer(initialState, action);
      expect(result.roles?.RolesList).toHaveLength(100);
    });

    it('should replace previous roles on new success', () => {
      const previousRoles: RolesResponse = {
        RolesList: [
          {
            User_ID: 999,
            User_Full_Name: 'Old User',
            Login_Name: 'old.user',
            Jobcode: 'OLD001',
            JobDesc: 'Old Job',
            FRCode: 999,
            Functional_ROLE: 'Old Role',
          },
        ],
      };
      const stateWithPreviousRoles = {
        ...initialState,
        roles: previousRoles,
        loading: false,
      };
      const action = {
        type: ROLES_SUCCESS,
        payload: mockRolesResponse,
      };
      const result = RolesReducer(stateWithPreviousRoles, action);
      expect(result.roles).toEqual(mockRolesResponse);
      expect(result.roles?.RolesList[0].User_Full_Name).toBe('John Doe');
    });

    it('should maintain immutability', () => {
      const action = {
        type: ROLES_SUCCESS,
        payload: mockRolesResponse,
      };
      const result = RolesReducer(initialState, action);
      expect(result).not.toBe(initialState);
      expect(result.roles).toBe(mockRolesResponse); // Actual implementation uses direct assignment
    });
  });

  describe('ROLES_FAILURE', () => {
    it('should handle ROLES_FAILURE action', () => {
      const errorMessage = 'Failed to fetch roles';
      const action = {
        type: ROLES_FAILURE,
        payload: errorMessage,
      };
      const result = RolesReducer(initialState, action);
      expect(result.loading).toBe(false);
      expect(result.rolesError).toBe(errorMessage);
      expect(result.roles).toBeNull();
    });

    it('should clear loading state on failure', () => {
      const loadingState = {
        ...initialState,
        loading: true,
      };
      const action = {
        type: ROLES_FAILURE,
        payload: 'Error message',
      };
      const result = RolesReducer(loadingState, action);
      expect(result.loading).toBe(false);
    });

    it('should preserve existing roles on failure', () => {
      const existingRoles: RolesResponse = {
        RolesList: [
          {
            User_ID: 123,
            User_Full_Name: 'John Doe',
            Login_Name: 'john.doe',
            Jobcode: 'JOB001',
            JobDesc: 'Software Engineer',
            FRCode: 1001,
            Functional_ROLE: 'Developer',
          },
        ],
      };
      const stateWithRoles = {
        ...initialState,
        roles: existingRoles,
      };
      const action = {
        type: ROLES_FAILURE,
        payload: 'Network error',
      };
      const result = RolesReducer(stateWithRoles, action);
      expect(result.roles).toEqual(existingRoles);
    });

    it('should handle empty error message', () => {
      const action = {
        type: ROLES_FAILURE,
        payload: '',
      };
      const result = RolesReducer(initialState, action);
      expect(result.rolesError).toBe('');
    });

    it('should handle null error message', () => {
      const action = {
        type: ROLES_FAILURE,
        payload: null,
      };
      const result = RolesReducer(initialState, action);
      expect(result.rolesError).toBeNull();
    });

    it('should handle undefined error message', () => {
      const action = {
        type: ROLES_FAILURE,
        payload: undefined,
      };
      const result = RolesReducer(initialState, action);
      expect(result.rolesError).toBeUndefined();
    });

    it('should handle network error message', () => {
      const networkError = 'Network request failed - timeout';
      const action = {
        type: ROLES_FAILURE,
        payload: networkError,
      };
      const result = RolesReducer(initialState, action);
      expect(result.rolesError).toBe(networkError);
    });

    it('should handle authentication error message', () => {
      const authError = 'Authentication failed - invalid token';
      const action = {
        type: ROLES_FAILURE,
        payload: authError,
      };
      const result = RolesReducer(initialState, action);
      expect(result.rolesError).toBe(authError);
    });

    it('should handle server error message', () => {
      const serverError = 'Internal server error - 500';
      const action = {
        type: ROLES_FAILURE,
        payload: serverError,
      };
      const result = RolesReducer(initialState, action);
      expect(result.rolesError).toBe(serverError);
    });

    it('should replace previous error with new error', () => {
      const stateWithError = {
        ...initialState,
        rolesError: 'Previous error',
      };
      const newError = 'New error occurred';
      const action = {
        type: ROLES_FAILURE,
        payload: newError,
      };
      const result = RolesReducer(stateWithError, action);
      expect(result.rolesError).toBe(newError);
    });

    it('should maintain immutability', () => {
      const action = {
        type: ROLES_FAILURE,
        payload: 'Error',
      };
      const result = RolesReducer(initialState, action);
      expect(result).not.toBe(initialState);
    });
  });

  describe('Unknown Actions', () => {
    it('should return current state for unknown action types', () => {
      const currentState = {
        roles: {
          RolesList: [
            {
              User_ID: 123,
              User_Full_Name: 'John Doe',
              Login_Name: 'john.doe',
              Jobcode: 'JOB001',
              JobDesc: 'Software Engineer',
              FRCode: 1001,
              Functional_ROLE: 'Developer',
            },
          ],
        },
        loading: false,
        rolesError: null,
      };
      const unknownAction = {
        type: 'UNKNOWN_ACTION',
        payload: 'some payload',
      };
      const result = RolesReducer(currentState, unknownAction);
      expect(result).toEqual(currentState);
    });

    it('should return current state reference for unknown actions', () => {
      const currentState = {
        roles: null,
        loading: false,
        rolesError: 'Some error',
      };
      const unknownAction = {
        type: 'RANDOM_ACTION',
      };
      const result = RolesReducer(currentState, unknownAction);
      expect(result).toBe(currentState);
    });

    it('should handle action without type', () => {
      const currentState = {
        roles: null,
        loading: true,
        rolesError: null,
      };
      const invalidAction = {} as any;
      const result = RolesReducer(currentState, invalidAction);
      expect(result).toBe(currentState);
    });

    it('should handle action with undefined type', () => {
      const currentState = {
        roles: null,
        loading: false,
        rolesError: 'Error',
      };
      const undefinedAction = { type: undefined };
      const result = RolesReducer(currentState, undefinedAction);
      expect(result).toBe(currentState);
    });
  });

  describe('State Transitions', () => {
    it('should handle complete success flow', () => {
      const mockRoles: RolesResponse = {
        RolesList: [
          {
            User_ID: 123,
            User_Full_Name: 'John Doe',
            Login_Name: 'john.doe',
            Jobcode: 'JOB001',
            JobDesc: 'Software Engineer',
            FRCode: 1001,
            Functional_ROLE: 'Developer',
          },
        ],
      };

      let state = RolesReducer(initialState, {
        type: ROLES_REQUEST,
      });
      expect(state.loading).toBe(true);
      expect(state.rolesError).toBeNull();

      state = RolesReducer(state, {
        type: ROLES_SUCCESS,
        payload: mockRoles,
      });
      expect(state.loading).toBe(false);
      expect(state.roles).toEqual(mockRoles);
      expect(state.rolesError).toBeNull();
    });

    it('should handle complete failure flow', () => {
      let state = RolesReducer(initialState, {
        type: ROLES_REQUEST,
      });
      expect(state.loading).toBe(true);
      expect(state.rolesError).toBeNull();

      state = RolesReducer(state, {
        type: ROLES_FAILURE,
        payload: 'Network error',
      });
      expect(state.loading).toBe(false);
      expect(state.roles).toBeNull();
      expect(state.rolesError).toBe('Network error');
    });

    it('should handle retry after failure', () => {
      let state = RolesReducer(initialState, {
        type: ROLES_FAILURE,
        payload: 'First error',
      });
      expect(state.rolesError).toBe('First error');

      state = RolesReducer(state, {
        type: ROLES_REQUEST,
      });
      expect(state.loading).toBe(true);
      expect(state.rolesError).toBeNull();
    });

    it('should handle success after previous failure', () => {
      const mockRoles: RolesResponse = {
        RolesList: [
          {
            User_ID: 123,
            User_Full_Name: 'John Doe',
            Login_Name: 'john.doe',
            Jobcode: 'JOB001',
            JobDesc: 'Software Engineer',
            FRCode: 1001,
            Functional_ROLE: 'Developer',
          },
        ],
      };

      let state = RolesReducer(initialState, {
        type: ROLES_FAILURE,
        payload: 'Network error',
      });
      expect(state.rolesError).toBe('Network error');

      state = RolesReducer(state, {
        type: ROLES_SUCCESS,
        payload: mockRoles,
      });
      expect(state.loading).toBe(false);
      expect(state.roles).toEqual(mockRoles);
      expect(state.rolesError).toBe('Network error'); // Error is not cleared in actual implementation
    });
  });

  describe('Type Safety', () => {
    it('should handle action with correct payload type', () => {
      const mockRoles: RolesResponse = {
        RolesList: [
          {
            User_ID: 123,
            User_Full_Name: 'John Doe',
            Login_Name: 'john.doe',
            Jobcode: 'JOB001',
            JobDesc: 'Software Engineer',
            FRCode: 1001,
            Functional_ROLE: 'Developer',
          },
        ],
      };
      const action = {
        type: ROLES_SUCCESS,
        payload: mockRoles,
      };
      const result = RolesReducer(initialState, action);
      expect(result.roles).toEqual(mockRoles);
    });

    it('should handle action with missing payload properties', () => {
      const action = {
        type: ROLES_SUCCESS,
        payload: {
          RolesList: [
            {
              User_ID: 123,
              User_Full_Name: 'John Doe',
              // Missing other properties
            } as any,
          ],
        },
      };
      const result = RolesReducer(initialState, action);
      expect(result.roles?.RolesList[0].User_ID).toBe(123);
    });
  });

  describe('State Immutability', () => {
    it('should not mutate the original state', () => {
      const originalState = {
        roles: null,
        loading: true,
        rolesError: null,
      };
      const action = {
        type: ROLES_REQUEST,
      };

      const newState = RolesReducer(originalState, action);

      expect(newState).not.toBe(originalState);
      expect(originalState).toEqual({
        roles: null,
        loading: true,
        rolesError: null,
      });
    });

    it('should create new state object for each action', () => {
      const mockRoles: RolesResponse = {
        RolesList: [
          {
            User_ID: 123,
            User_Full_Name: 'John Doe',
            Login_Name: 'john.doe',
            Jobcode: 'JOB001',
            JobDesc: 'Software Engineer',
            FRCode: 1001,
            Functional_ROLE: 'Developer',
          },
        ],
      };

      const action1 = {
        type: ROLES_REQUEST,
      };
      const action2 = {
        type: ROLES_SUCCESS,
        payload: mockRoles,
      };

      const state1 = RolesReducer(initialState, action1);
      const state2 = RolesReducer(state1, action2);

      expect(state1).not.toBe(initialState);
      expect(state2).not.toBe(state1);
      expect(state2).not.toBe(initialState);
    });

    it('should preserve immutability with complex state', () => {
      const complexState = {
        roles: {
          RolesList: [
            {
              User_ID: 123,
              User_Full_Name: 'John Doe',
              Login_Name: 'john.doe',
              Jobcode: 'JOB001',
              JobDesc: 'Software Engineer',
              FRCode: 1001,
              Functional_ROLE: 'Developer',
            },
          ],
        },
        loading: false,
        rolesError: null,
      };
      const action = {
        type: ROLES_REQUEST,
      };

      const newState = RolesReducer(complexState, action);

      expect(newState).not.toBe(complexState);
      expect(complexState.roles?.RolesList[0].User_Full_Name).toBe('John Doe');
      expect(complexState.loading).toBe(false);
    });
  });

  describe('State Structure', () => {
    it('should maintain correct state structure after actions', () => {
      const requestAction = {
        type: ROLES_REQUEST,
      };
      const successAction = {
        type: ROLES_SUCCESS,
        payload: {
          RolesList: [],
        },
      };

      let state = RolesReducer(initialState, requestAction);
      expect(Object.keys(state)).toEqual(['roles', 'loading', 'rolesError']);

      state = RolesReducer(state, successAction);
      expect(Object.keys(state)).toEqual(['roles', 'loading', 'rolesError']);
    });

    it('should not add extra properties to state', () => {
      const action = {
        type: ROLES_SUCCESS,
        payload: {
          RolesList: [],
        },
        extraProperty: 'should be ignored',
      };
      const result = RolesReducer(initialState, action);
      expect(result).not.toHaveProperty('extraProperty');
    });
  });
}); 