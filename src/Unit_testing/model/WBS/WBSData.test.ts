import {
  WbsJobItem,
  Wbs<PERSON>equestType,
  StatusCallback,
  WbsRequestData,
  GetWbsApproverProps,
  PendingApprovalRequestBody,
  WbsHierarchyItem,
  WbsTaskItem,
  WbsDetailsItem,
  WbsGISDetailsItem,
  DeliverableProgressDetailsEngineerItem,
  ProgressConsolidatedItem,
  ViewLastUpdateBQITItem,
  ViewLastUpdateGISItem,
  WbsJobResponseData,
  WbsResponseData,
  WbsPendingApprovalModal,
  WBSPendingApprovalProps,
  GisPragatiItem
} from '../../../model/WBS/WBSData';

describe('WBS - WBSData Models', () => {
  describe('WbsJobItem Interface', () => {
    it('should create a valid WbsJobItem object with all properties', () => {
      const jobItem: WbsJobItem = {
        jobCode: 'JOB001',
        jobName: 'Test Job'
      };

      expect(jobItem.jobCode).toBe('JOB001');
      expect(jobItem.jobName).toBe('Test Job');
      expect(typeof jobItem.jobCode).toBe('string');
      expect(typeof jobItem.jobName).toBe('string');
    });

    it('should create a valid WbsJobItem object with only required properties', () => {
      const jobItem: WbsJobItem = {
        jobCode: 'JOB002'
      };

      expect(jobItem.jobCode).toBe('JOB002');
      expect(jobItem.jobName).toBeUndefined();
    });

    it('should handle empty string values', () => {
      const jobItem: WbsJobItem = {
        jobCode: '',
        jobName: ''
      };

      expect(typeof jobItem.jobCode).toBe('string');
      expect(typeof jobItem.jobName).toBe('string');
      expect(jobItem.jobCode).toBe('');
      expect(jobItem.jobName).toBe('');
    });

    it('should validate required properties exist', () => {
      const jobItem: WbsJobItem = {
        jobCode: 'JOB003',
        jobName: 'Engineering Job'
      };

      expect(jobItem).toHaveProperty('jobCode');
      expect(jobItem).toHaveProperty('jobName');
    });
  });

  describe('WbsRequestType Type', () => {
    it('should accept valid WbsRequestType values', () => {
      const types: WbsRequestType[] = [
        'WBS',
        'Task',
        'Details',
        'GISDetails',
        'ProgressDetails_Enginner',
        'ProgressConsolidated',
        'ViewLastUpdateBQITFromTodateOutput',
        'ViewLastUpdateGISFromTodateOutput',
        'BookMarkList',
        'LatLongHierarchy'
      ];

      types.forEach(type => {
        expect(typeof type).toBe('string');
        expect(['WBS', 'Task', 'Details', 'GISDetails', 'ProgressDetails_Enginner', 
                'ProgressConsolidated', 'ViewLastUpdateBQITFromTodateOutput', 
                'ViewLastUpdateGISFromTodateOutput', 'BookMarkList', 'LatLongHierarchy'])
                .toContain(type);
      });
    });
  });

  describe('StatusCallback Type', () => {
    it('should create a valid status callback function', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      expect(typeof mockCallback).toBe('function');
    });

    it('should execute callback with success true', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      mockCallback({ success: true, data: { test: 'data' } });
      
      expect(mockCallback).toHaveBeenCalledWith({ success: true, data: { test: 'data' } });
    });

    it('should execute callback with success false', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      mockCallback({ success: false });
      
      expect(mockCallback).toHaveBeenCalledWith({ success: false });
    });
  });

  describe('WbsRequestData Interface', () => {
    it('should create a valid WbsRequestData object with all properties', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      const requestData: WbsRequestData = {
        userId: 'USER001',
        type: 'WBS',
        objJoblist: [{ jobCode: 'JOB001', jobName: 'Test Job' }],
        Fromdate: '2024-01-01',
        Todate: '2024-12-31',
        selectedJob: [{ id: '1', name: 'Job 1', role: 'Engineer' }],
        isLogin: true,
        cb: mockCallback
      };

      expect(requestData.userId).toBe('USER001');
      expect(requestData.type).toBe('WBS');
      expect(requestData.objJoblist).toHaveLength(1);
      expect(requestData.Fromdate).toBe('2024-01-01');
      expect(requestData.Todate).toBe('2024-12-31');
      expect(requestData.selectedJob).toHaveLength(1);
      expect(requestData.isLogin).toBe(true);
      expect(typeof requestData.cb).toBe('function');
    });

    it('should create a valid WbsRequestData object with only required properties', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      const requestData: WbsRequestData = {
        userId: 'USER002',
        objJoblist: [],
        cb: mockCallback
      };

      expect(requestData.userId).toBe('USER002');
      expect(requestData.objJoblist).toEqual([]);
      expect(typeof requestData.cb).toBe('function');
      expect(requestData.type).toBeUndefined();
      expect(requestData.Fromdate).toBeUndefined();
      expect(requestData.Todate).toBeUndefined();
    });

    it('should validate required properties exist', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      const requestData: WbsRequestData = {
        userId: 'USER003',
        objJoblist: [{ jobCode: 'JOB003' }],
        cb: mockCallback
      };

      expect(requestData).toHaveProperty('userId');
      expect(requestData).toHaveProperty('objJoblist');
      expect(requestData).toHaveProperty('cb');
    });
  });

  describe('GetWbsApproverProps Interface', () => {
    it('should create a valid GetWbsApproverProps object', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      const approverProps: GetWbsApproverProps = {
        jobCode: 'JOB001',
        type: 'WBS',
        selectedJob: { id: '1', name: 'Test Job' },
        objJoblist: [{ jobCode: 'JOB001' }],
        UID: 12345,
        cb: mockCallback
      };

      expect(approverProps.jobCode).toBe('JOB001');
      expect(approverProps.type).toBe('WBS');
      expect(approverProps.selectedJob).toEqual({ id: '1', name: 'Test Job' });
      expect(approverProps.objJoblist).toHaveLength(1);
      expect(approverProps.UID).toBe(12345);
      expect(typeof approverProps.cb).toBe('function');
    });

    it('should create a valid GetWbsApproverProps object without optional UID', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      const approverProps: GetWbsApproverProps = {
        jobCode: 'JOB002',
        type: 'Task',
        selectedJob: null,
        objJoblist: [],
        cb: mockCallback
      };

      expect(approverProps.jobCode).toBe('JOB002');
      expect(approverProps.type).toBe('Task');
      expect(approverProps.selectedJob).toBeNull();
      expect(approverProps.UID).toBeUndefined();
    });
  });

  describe('PendingApprovalRequestBody Interface', () => {
    it('should create a valid PendingApprovalRequestBody object with all properties', () => {
      const requestBody: PendingApprovalRequestBody = {
        jobCode: 'JOB001',
        type: 'WBS',
        UID: 12345
      };

      expect(requestBody.jobCode).toBe('JOB001');
      expect(requestBody.type).toBe('WBS');
      expect(requestBody.UID).toBe(12345);
    });

    it('should create a valid PendingApprovalRequestBody object without optional UID', () => {
      const requestBody: PendingApprovalRequestBody = {
        jobCode: 'JOB002',
        type: 'Task'
      };

      expect(requestBody.jobCode).toBe('JOB002');
      expect(requestBody.type).toBe('Task');
      expect(requestBody.UID).toBeUndefined();
    });
  });

  describe('WbsHierarchyItem Interface', () => {
    it('should create a valid WbsHierarchyItem object', () => {
      const hierarchyItem: WbsHierarchyItem = {
        Job_Code: 'JOB001',
        WBS_Code: 'WBS001',
        WBS_Description: 'Test WBS Description',
        Parent_WBS_Code: 'PARENT001',
        ET_Code: 'ET001',
        Leaf_Node_Tag: 'LEAF001',
        Sort_Order: 1,
        Is_Active: 'Y',
        entity_type: 'WBS'
      };

      expect(hierarchyItem.Job_Code).toBe('JOB001');
      expect(hierarchyItem.WBS_Code).toBe('WBS001');
      expect(hierarchyItem.WBS_Description).toBe('Test WBS Description');
      expect(hierarchyItem.Parent_WBS_Code).toBe('PARENT001');
      expect(hierarchyItem.ET_Code).toBe('ET001');
      expect(hierarchyItem.Leaf_Node_Tag).toBe('LEAF001');
      expect(hierarchyItem.Sort_Order).toBe(1);
      expect(hierarchyItem.Is_Active).toBe('Y');
      expect(hierarchyItem.entity_type).toBe('WBS');
    });

    it('should handle null Leaf_Node_Tag', () => {
      const hierarchyItem: WbsHierarchyItem = {
        Job_Code: 'JOB002',
        WBS_Code: 'WBS002',
        WBS_Description: 'Another WBS',
        Parent_WBS_Code: 'PARENT002',
        ET_Code: 'ET002',
        Leaf_Node_Tag: null,
        Sort_Order: 2,
        Is_Active: 'N',
        entity_type: 'Task'
      };

      expect(hierarchyItem.Leaf_Node_Tag).toBeNull();
      expect(hierarchyItem.Is_Active).toBe('N');
    });

    it('should validate all properties exist', () => {
      const hierarchyItem: WbsHierarchyItem = {
        Job_Code: 'JOB003',
        WBS_Code: 'WBS003',
        WBS_Description: 'Description',
        Parent_WBS_Code: 'PARENT003',
        ET_Code: 'ET003',
        Leaf_Node_Tag: 'LEAF003',
        Sort_Order: 3,
        Is_Active: 'Y',
        entity_type: 'WBS'
      };

      expect(hierarchyItem).toHaveProperty('Job_Code');
      expect(hierarchyItem).toHaveProperty('WBS_Code');
      expect(hierarchyItem).toHaveProperty('WBS_Description');
      expect(hierarchyItem).toHaveProperty('Parent_WBS_Code');
      expect(hierarchyItem).toHaveProperty('ET_Code');
      expect(hierarchyItem).toHaveProperty('Leaf_Node_Tag');
      expect(hierarchyItem).toHaveProperty('Sort_Order');
      expect(hierarchyItem).toHaveProperty('Is_Active');
      expect(hierarchyItem).toHaveProperty('entity_type');
    });
  });

  describe('WbsTaskItem Interface', () => {
    it('should create a valid WbsTaskItem object', () => {
      const taskItem: WbsTaskItem = {
        Job_Code: 'JOB001',
        Task_Code: 'TASK001',
        Task_Description: 'Test Task Description',
        Parent_Task_Code: 'PARENT_TASK001',
        ET_Code: 'ET001',
        UOM_Symbol: 'M',
        Is_Active: 'Y',
        Gis_Tag: 'GIS001',
        Parent_WBS: 'WBS001',
        Parent_Task: 'PARENT001',
        entity_Type: 'Task'
      };

      expect(taskItem.Job_Code).toBe('JOB001');
      expect(taskItem.Task_Code).toBe('TASK001');
      expect(taskItem.Task_Description).toBe('Test Task Description');
      expect(taskItem.Parent_Task_Code).toBe('PARENT_TASK001');
      expect(taskItem.ET_Code).toBe('ET001');
      expect(taskItem.UOM_Symbol).toBe('M');
      expect(taskItem.Is_Active).toBe('Y');
      expect(taskItem.Gis_Tag).toBe('GIS001');
      expect(taskItem.Parent_WBS).toBe('WBS001');
      expect(taskItem.Parent_Task).toBe('PARENT001');
      expect(taskItem.entity_Type).toBe('Task');
    });

    it('should handle empty string values', () => {
      const taskItem: WbsTaskItem = {
        Job_Code: '',
        Task_Code: '',
        Task_Description: '',
        Parent_Task_Code: '',
        ET_Code: '',
        UOM_Symbol: '',
        Is_Active: '',
        Gis_Tag: '',
        Parent_WBS: '',
        Parent_Task: '',
        entity_Type: ''
      };

      expect(typeof taskItem.Job_Code).toBe('string');
      expect(typeof taskItem.Task_Code).toBe('string');
      expect(typeof taskItem.Task_Description).toBe('string');
    });
  });

  describe('WbsDetailsItem Interface', () => {
    it('should create a valid WbsDetailsItem object', () => {
      const detailsItem: WbsDetailsItem = {
        JobCode: 'JOB001',
        WBS: 'WBS001',
        Task: 'TASK001',
        PlanedQty: 100.5,
        PlanedSDate: '2024-01-01',
        PlanedEDate: '2024-12-31',
        PlanedLabour: 50,
        ActualQty: 75.25,
        ActualSDate: '2024-01-15',
        ActualEDate: '2024-11-30',
        ActualLabour: 45,
        LastUpdatedDate: '2024-06-15',
        LastUpdatedQty: 80,
        LastUpdatedLabour: 48,
        Remarks: 'Work in progress',
        TaskType: 'Construction',
        Min_Productivity_Range: 10,
        Max_Productivity_Range: 20,
        Scope: 100,
        MonthwisePlanQty: 25,
        Et_Code: 1001,
        DeliverableType_Et_Code: 'DET001',
        DET_Assigned_Scope_Quantity: 100,
        DET_Year_Month: 202401,
        TDD_Is_Engineer_Target_Mandatory: 'Y'
      };

      expect(detailsItem.JobCode).toBe('JOB001');
      expect(detailsItem.PlanedQty).toBe(100.5);
      expect(detailsItem.ActualQty).toBe(75.25);
      expect(detailsItem.ActualSDate).toBe('2024-01-15');
      expect(detailsItem.Et_Code).toBe(1001);
      expect(detailsItem.TDD_Is_Engineer_Target_Mandatory).toBe('Y');
    });

    it('should handle null ActualSDate and ActualEDate', () => {
      const detailsItem: WbsDetailsItem = {
        JobCode: 'JOB002',
        WBS: 'WBS002',
        Task: 'TASK002',
        PlanedQty: 200,
        PlanedSDate: '2024-02-01',
        PlanedEDate: '2024-12-31',
        PlanedLabour: 100,
        ActualQty: 0,
        ActualSDate: null,
        ActualEDate: null,
        ActualLabour: 0,
        LastUpdatedDate: '2024-06-15',
        LastUpdatedQty: 0,
        LastUpdatedLabour: 0,
        Remarks: '',
        TaskType: 'Planning',
        Min_Productivity_Range: 5,
        Max_Productivity_Range: 15,
        Scope: 200,
        MonthwisePlanQty: 50,
        Et_Code: 1002,
        DeliverableType_Et_Code: 'DET002',
        DET_Assigned_Scope_Quantity: 200,
        DET_Year_Month: 202402,
        TDD_Is_Engineer_Target_Mandatory: 'N'
      };

      expect(detailsItem.ActualSDate).toBeNull();
      expect(detailsItem.ActualEDate).toBeNull();
      expect(detailsItem.TDD_Is_Engineer_Target_Mandatory).toBe('N');
    });
  });

  describe('WbsGISDetailsItem Interface', () => {
    it('should create a valid WbsGISDetailsItem object', () => {
      const gisDetailsItem: WbsGISDetailsItem = {
        JobCode: 'JOB001',
        WBS: 'WBS001',
        Task: 'TASK001',
        Deliverable: 'DELIV001',
        Remarks: 'GIS remarks',
        Boq: 'BOQ001',
        ActualDate: '2024-06-15',
        NodeId: 'NODE001',
        NodeRefCode: 12345,
        FromLength: 0,
        ToLength: 100,
        TotalLength: 100,
        Manpower: 5,
        DistanceFCenter: 50,
        Alignment: 'NORTH',
        TaskType: 'Installation',
        Scope: 100,
        MonthwisePlanQty: 25,
        Et_Code: 1001,
        DeliverableType_Et_Code: 'DET001',
        Is_Hindrance: 'N',
        DET_Assigned_Scope_Quantity: 100,
        DET_Year_Month: 202406,
        TDD_Is_Engineer_Target_Mandatory: 'Y',
        PlanedSDate: '2024-01-01',
        PlanedEDate: '2024-12-31',
        ActualSDate: '2024-01-15',
        ActualEDate: '2024-11-30'
      };

      expect(gisDetailsItem.NodeId).toBe('NODE001');
      expect(gisDetailsItem.NodeRefCode).toBe(12345);
      expect(gisDetailsItem.FromLength).toBe(0);
      expect(gisDetailsItem.ToLength).toBe(100);
      expect(gisDetailsItem.TotalLength).toBe(100);
      expect(gisDetailsItem.Is_Hindrance).toBe('N');
    });

    it('should handle null values for optional fields', () => {
      const gisDetailsItem: WbsGISDetailsItem = {
        JobCode: 'JOB002',
        WBS: 'WBS002',
        Task: 'TASK002',
        Deliverable: null,
        Remarks: null,
        Boq: null,
        ActualDate: '2024-06-15',
        NodeId: 'NODE002',
        NodeRefCode: 54321,
        FromLength: 100,
        ToLength: 200,
        TotalLength: 100,
        Manpower: 3,
        DistanceFCenter: 150,
        Alignment: 'SOUTH',
        TaskType: 'Maintenance',
        Scope: 50,
        MonthwisePlanQty: 12.5,
        Et_Code: 1002,
        DeliverableType_Et_Code: 'DET002',
        Is_Hindrance: 'Y',
        DET_Assigned_Scope_Quantity: 50,
        DET_Year_Month: null,
        TDD_Is_Engineer_Target_Mandatory: 'N',
        PlanedSDate: '2024-02-01',
        PlanedEDate: '2024-11-30',
        ActualSDate: '2024-02-15',
        ActualEDate: '2024-10-30'
      };

      expect(gisDetailsItem.Deliverable).toBeNull();
      expect(gisDetailsItem.Remarks).toBeNull();
      expect(gisDetailsItem.Boq).toBeNull();
      expect(gisDetailsItem.DET_Year_Month).toBeNull();
      expect(gisDetailsItem.Is_Hindrance).toBe('Y');
    });
  });

  describe('DeliverableProgressDetailsEngineerItem Interface', () => {
    it('should create a valid DeliverableProgressDetailsEngineerItem object', () => {
      const progressItem: DeliverableProgressDetailsEngineerItem = {
        TDD_Job_Code: 'JOB001',
        TDD_WBS_Code: 'WBS001',
        TDD_Deliverable_Code: 1001,
        Delivscope: 100,
        CumProg: 75,
        CumManday: 50,
        FTMProgress: 25,
        FTMManday: 15,
        CumTargetPlanQty: 80,
        FTMCumTargetPlanQty: 20,
        TargetScope: 100
      };

      expect(progressItem.TDD_Job_Code).toBe('JOB001');
      expect(progressItem.TDD_WBS_Code).toBe('WBS001');
      expect(progressItem.TDD_Deliverable_Code).toBe(1001);
      expect(progressItem.Delivscope).toBe(100);
      expect(progressItem.CumProg).toBe(75);
      expect(progressItem.CumManday).toBe(50);
      expect(progressItem.FTMProgress).toBe(25);
      expect(progressItem.FTMManday).toBe(15);
      expect(progressItem.CumTargetPlanQty).toBe(80);
      expect(progressItem.FTMCumTargetPlanQty).toBe(20);
      expect(progressItem.TargetScope).toBe(100);
    });

    it('should handle zero values', () => {
      const progressItem: DeliverableProgressDetailsEngineerItem = {
        TDD_Job_Code: 'JOB002',
        TDD_WBS_Code: 'WBS002',
        TDD_Deliverable_Code: 1002,
        Delivscope: 0,
        CumProg: 0,
        CumManday: 0,
        FTMProgress: 0,
        FTMManday: 0,
        CumTargetPlanQty: 0,
        FTMCumTargetPlanQty: 0,
        TargetScope: 0
      };

      expect(progressItem.Delivscope).toBe(0);
      expect(progressItem.CumProg).toBe(0);
      expect(progressItem.CumManday).toBe(0);
    });
  });

  describe('ProgressConsolidatedItem Interface', () => {
    it('should create a valid ProgressConsolidatedItem object', () => {
      const consolidatedItem: ProgressConsolidatedItem = {
        TDD_Job_Code: 'JOB001',
        TDD_WBS_Code: 'WBS001',
        TDD_Deliverable_Code: 1001,
        Delivscope: 100,
        CumProg: 75,
        CumManday: 50,
        FTMProgress: 25,
        FTMManday: 15,
        ScScopeE: 90,
        ScProgress: 70,
        ScManday: 45,
        CumPlanningQuantity: 85,
        FTMPlanningQuantity: 20
      };

      expect(consolidatedItem.TDD_Job_Code).toBe('JOB001');
      expect(consolidatedItem.ScScopeE).toBe(90);
      expect(consolidatedItem.ScProgress).toBe(70);
      expect(consolidatedItem.ScManday).toBe(45);
      expect(consolidatedItem.CumPlanningQuantity).toBe(85);
      expect(consolidatedItem.FTMPlanningQuantity).toBe(20);
    });
  });

  describe('ViewLastUpdateBQITItem Interface', () => {
    it('should create a valid ViewLastUpdateBQITItem object', () => {
      const bqitItem: ViewLastUpdateBQITItem = {
        JOB: 'JOB001',
        TDP_WBS_CODE: 'WBS001',
        TDP_DELIVERABLE_CODE: 1001,
        INSERTED_BY: 12345,
        Inserted_On: '2024-06-15T10:30:00Z',
        QTY: 50,
        MAN: 5,
        MUSGD_User_ID: 67890,
        Full_Name: 'John Doe',
        Reference_ID: 'REF001',
        MUOM_Short_Description: 'Meters'
      };

      expect(bqitItem.JOB).toBe('JOB001');
      expect(bqitItem.INSERTED_BY).toBe(12345);
      expect(bqitItem.QTY).toBe(50);
      expect(bqitItem.MAN).toBe(5);
      expect(bqitItem.Full_Name).toBe('John Doe');
    });
  });

  describe('ViewLastUpdateGISItem Interface', () => {
    it('should create a valid ViewLastUpdateGISItem object', () => {
      const gisItem: ViewLastUpdateGISItem = {
        JOB: 'JOB001',
        TDP_WBS_CODE: 'WBS001',
        TDP_DELIVERABLE_CODE: 1001,
        NodeID: 'NODE001',
        INSERTED_BY: 12345,
        Inserted_On: '2024-06-15T10:30:00Z',
        QTY: 75,
        MAN: 8,
        MUSGD_User_ID: 67890,
        Full_Name: 'Jane Smith',
        Reference_ID: 'REF002',
        MUOM_Short_Description: 'Units'
      };

      expect(gisItem.NodeID).toBe('NODE001');
      expect(gisItem.QTY).toBe(75);
      expect(gisItem.MAN).toBe(8);
      expect(gisItem.Full_Name).toBe('Jane Smith');
    });
  });

  describe('WbsJobResponseData Interface', () => {
    it('should create a valid WbsJobResponseData object with all arrays populated', () => {
      const responseData: WbsJobResponseData = {
        WbsHierarchyOutput: [
          {
            Job_Code: 'JOB001',
            WBS_Code: 'WBS001',
            WBS_Description: 'Test WBS',
            Parent_WBS_Code: 'PARENT001',
            ET_Code: 'ET001',
            Leaf_Node_Tag: 'LEAF001',
            Sort_Order: 1,
            Is_Active: 'Y',
            entity_type: 'WBS'
          }
        ],
        WbsTaskOutput: [
          {
            Job_Code: 'JOB001',
            Task_Code: 'TASK001',
            Task_Description: 'Test Task',
            Parent_Task_Code: 'PARENT_TASK001',
            ET_Code: 'ET001',
            UOM_Symbol: 'M',
            Is_Active: 'Y',
            Gis_Tag: 'GIS001',
            Parent_WBS: 'WBS001',
            Parent_Task: 'PARENT001',
            entity_Type: 'Task'
          }
        ],
        WbsDetailsOutput: null,
        WbsGISDetailsOutput: null,
        ProgressConsolidatedOutput: null,
        DeliverableProgressDetailsEnginneerOutput: null,
        ViewLastUpdateBQITFromTodateOutput: null,
        ViewLastUpdateGISFromTodateOutput: null,
        GisPragatiItemOutput: null
      };

      expect(responseData.WbsHierarchyOutput).toHaveLength(1);
      expect(responseData.WbsTaskOutput).toHaveLength(1);
      expect(responseData.WbsDetailsOutput).toBeNull();
      expect(responseData.WbsGISDetailsOutput).toBeNull();
    });

    it('should create a valid WbsJobResponseData object with all nulls', () => {
      const responseData: WbsJobResponseData = {
        WbsHierarchyOutput: null,
        WbsTaskOutput: null,
        WbsDetailsOutput: null,
        WbsGISDetailsOutput: null,
        ProgressConsolidatedOutput: null,
        DeliverableProgressDetailsEnginneerOutput: null,
        ViewLastUpdateBQITFromTodateOutput: null,
        ViewLastUpdateGISFromTodateOutput: null,
        GisPragatiItemOutput: null
      };

      expect(responseData.WbsHierarchyOutput).toBeNull();
      expect(responseData.WbsTaskOutput).toBeNull();
      expect(responseData.WbsDetailsOutput).toBeNull();
    });
  });

  describe('WbsResponseData Interface', () => {
    it('should create a valid WbsResponseData object with success response', () => {
      const responseData: WbsResponseData = {
        StatusCode: 200,
        Message: 'Success',
        Data: {
          WbsHierarchyOutput: [],
          WbsTaskOutput: [],
          WbsDetailsOutput: [],
          WbsGISDetailsOutput: [],
          ProgressConsolidatedOutput: [],
          DeliverableProgressDetailsEnginneerOutput: [],
          ViewLastUpdateBQITFromTodateOutput: null,
          ViewLastUpdateGISFromTodateOutput: null,
          GisPragatiItemOutput: []
        }
      };

      expect(responseData.StatusCode).toBe(200);
      expect(responseData.Message).toBe('Success');
      expect(responseData.Data).toBeDefined();
      expect(responseData.Data?.WbsHierarchyOutput).toEqual([]);
    });

    it('should create a valid WbsResponseData object with error response', () => {
      const responseData: WbsResponseData = {
        StatusCode: 500,
        Message: 'Internal Server Error'
      };

      expect(responseData.StatusCode).toBe(500);
      expect(responseData.Message).toBe('Internal Server Error');
      expect(responseData.Data).toBeUndefined();
    });
  });

  describe('WbsPendingApprovalModal Interface', () => {
    it('should create a valid WbsPendingApprovalModal object', () => {
      const pendingApproval: WbsPendingApprovalModal = {
        JOB: 'JOB001',
        WBS: 'WBS001',
        Task: 'TASK001',
        TDate: '2024-06-15',
        UserID: 12345,
        UserName: 'John Doe',
        Quantity: 50,
        Manpower: 5,
        Remarks: 'Pending approval',
        TaskType: 'Construction',
        Latitude: 12.9716,
        Longitude: 77.5946,
        TotalQuantity: 100,
        ActualQuantity: 50,
        WBS_Description: 'WBS Description',
        WBS_Custom_Description: 'Custom WBS Description',
        Task_Description: 'Task Description',
        Task_Custom_Description: 'Custom Task Description',
        Image_ID: 'IMG001',
        Image_URL: 'https://example.com/image.jpg',
        UOM: 'Meters',
        NodeId: 'NODE001',
        ReferanceNodeId: 'REF_NODE001',
        From_Length: 0,
        Progress_Length: 25,
        Total_Length: 100,
        Distance_From_Center: 50,
        Alignment: 'NORTH',
        IS_Completed: 'N',
        Is_Hindrance: 'N'
      };

      expect(pendingApproval.JOB).toBe('JOB001');
      expect(pendingApproval.UserID).toBe(12345);
      expect(pendingApproval.Latitude).toBe(12.9716);
      expect(pendingApproval.Longitude).toBe(77.5946);
      expect(pendingApproval.IS_Completed).toBe('N');
      expect(pendingApproval.Is_Hindrance).toBe('N');
    });
  });

  describe('WBSPendingApprovalProps Interface', () => {
    it('should create a valid WBSPendingApprovalProps object', () => {
      const pendingApprovalProps: WBSPendingApprovalProps = {
        Table: [
          {
            JOB: 'JOB001',
            WBS: 'WBS001',
            Task: 'TASK001',
            TDate: '2024-06-15',
            UserID: 12345,
            UserName: 'John Doe',
            Quantity: 50,
            Manpower: 5,
            Remarks: 'Test remarks',
            TaskType: 'Construction',
            Latitude: 12.9716,
            Longitude: 77.5946,
            TotalQuantity: 100,
            ActualQuantity: 50,
            WBS_Description: 'WBS Description',
            WBS_Custom_Description: 'Custom WBS Description',
            Task_Description: 'Task Description',
            Task_Custom_Description: 'Custom Task Description',
            Image_ID: 'IMG001',
            Image_URL: 'https://example.com/image.jpg',
            UOM: 'Meters',
            NodeId: 'NODE001',
            ReferanceNodeId: 'REF_NODE001',
            From_Length: 0,
            Progress_Length: 25,
            Total_Length: 100,
            Distance_From_Center: 50,
            Alignment: 'NORTH',
            IS_Completed: 'N',
            Is_Hindrance: 'N'
          }
        ]
      };

      expect(pendingApprovalProps.Table).toHaveLength(1);
      expect(pendingApprovalProps.Table[0].JOB).toBe('JOB001');
    });

    it('should create a valid WBSPendingApprovalProps object with empty table', () => {
      const pendingApprovalProps: WBSPendingApprovalProps = {
        Table: []
      };

      expect(pendingApprovalProps.Table).toEqual([]);
      expect(pendingApprovalProps.Table).toHaveLength(0);
    });
  });

  describe('GisPragatiItem Interface', () => {
    it('should create a valid GisPragatiItem object', () => {
      const gisItem: GisPragatiItem = {
        ZONE: 'ZONE001',
        DMA: 1001,
        Sl_No: 1,
        PRAGATI_WBS_Dia: 100,
        Design_Dia: 110,
        Material: 'PVC',
        Pipe_ID: 'PIPE001',
        Start_Node_ID: 'START001',
        Stop_Node_ID: 'STOP001',
        Design_Length: 1000,
        Deliverable_Code: 2001,
        Boq_Code: 'BOQ001'
      };

      expect(gisItem.ZONE).toBe('ZONE001');
      expect(gisItem.DMA).toBe(1001);
      expect(gisItem.Sl_No).toBe(1);
      expect(gisItem.PRAGATI_WBS_Dia).toBe(100);
      expect(gisItem.Design_Dia).toBe(110);
      expect(gisItem.Material).toBe('PVC');
      expect(gisItem.Pipe_ID).toBe('PIPE001');
      expect(gisItem.Design_Length).toBe(1000);
      expect(gisItem.Deliverable_Code).toBe(2001);
    });

    it('should validate all properties exist', () => {
      const gisItem: GisPragatiItem = {
        ZONE: 'ZONE002',
        DMA: 1002,
        Sl_No: 2,
        PRAGATI_WBS_Dia: 200,
        Design_Dia: 220,
        Material: 'Steel',
        Pipe_ID: 'PIPE002',
        Start_Node_ID: 'START002',
        Stop_Node_ID: 'STOP002',
        Design_Length: 2000,
        Deliverable_Code: 2002,
        Boq_Code: 'BOQ002'
      };

      expect(gisItem).toHaveProperty('ZONE');
      expect(gisItem).toHaveProperty('DMA');
      expect(gisItem).toHaveProperty('Sl_No');
      expect(gisItem).toHaveProperty('PRAGATI_WBS_Dia');
      expect(gisItem).toHaveProperty('Design_Dia');
      expect(gisItem).toHaveProperty('Material');
      expect(gisItem).toHaveProperty('Pipe_ID');
      expect(gisItem).toHaveProperty('Start_Node_ID');
      expect(gisItem).toHaveProperty('Stop_Node_ID');
      expect(gisItem).toHaveProperty('Design_Length');
      expect(gisItem).toHaveProperty('Deliverable_Code');
      expect(gisItem).toHaveProperty('Boq_Code');
    });
  });

  describe('Type Safety and Edge Cases', () => {
    it('should handle complex nested objects', () => {
      const complexResponse: WbsResponseData = {
        StatusCode: 200,
        Message: 'Success',
        Data: {
          WbsHierarchyOutput: [
            {
              Job_Code: 'JOB001',
              WBS_Code: 'WBS001',
              WBS_Description: 'Complex WBS',
              Parent_WBS_Code: 'PARENT001',
              ET_Code: 'ET001',
              Leaf_Node_Tag: null,
              Sort_Order: 1,
              Is_Active: 'Y',
              entity_type: 'WBS'
            }
          ],
          WbsTaskOutput: null,
          WbsDetailsOutput: null,
          WbsGISDetailsOutput: null,
          ProgressConsolidatedOutput: null,
          DeliverableProgressDetailsEnginneerOutput: null,
          ViewLastUpdateBQITFromTodateOutput: null,
          ViewLastUpdateGISFromTodateOutput: null,
          GisPragatiItemOutput: null
        }
      };

      expect(complexResponse.Data?.WbsHierarchyOutput?.[0].Leaf_Node_Tag).toBeNull();
      expect(complexResponse.Data?.WbsTaskOutput).toBeNull();
    });

    it('should handle arrays with multiple items', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      const requestData: WbsRequestData = {
        userId: 'USER001',
        type: 'WBS',
        objJoblist: [
          { jobCode: 'JOB001', jobName: 'Job One' },
          { jobCode: 'JOB002', jobName: 'Job Two' },
          { jobCode: 'JOB003' }
        ],
        selectedJob: [
          { id: '1', name: 'Job 1', role: 'Engineer' },
          { id: '2', name: 'Job 2', role: 'Manager' }
        ],
        cb: mockCallback
      };

      expect(requestData.objJoblist).toHaveLength(3);
      expect(requestData.selectedJob).toHaveLength(2);
      expect(requestData.objJoblist[2].jobName).toBeUndefined();
    });

    it('should handle all WbsRequestType values correctly', () => {
      const allTypes: WbsRequestType[] = [
        'WBS',
        'Task',
        'Details',
        'GISDetails',
        'ProgressDetails_Enginner',
        'ProgressConsolidated',
        'ViewLastUpdateBQITFromTodateOutput',
        'ViewLastUpdateGISFromTodateOutput',
        'BookMarkList',
        'LatLongHierarchy'
      ];

      allTypes.forEach(type => {
        const mockCallback: StatusCallback = jest.fn();
        const requestData: WbsRequestData = {
          userId: 'USER001',
          type: type,
          objJoblist: [],
          cb: mockCallback
        };

        expect(requestData.type).toBe(type);
      });
    });
  });
}); 