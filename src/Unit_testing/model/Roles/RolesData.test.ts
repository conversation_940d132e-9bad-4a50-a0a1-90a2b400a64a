import { RoleItem, RolesResponse, RolesRequest } from '../../../model/Roles/RolesData';

// Mock the StatusCallback type since it's imported from WbsData
type StatusCallback<T = any> = (params: { success: boolean; data?: T; statusCode?: number }) => void;

describe('Roles - RolesData Models', () => {
  describe('RoleItem Interface', () => {
    it('should create a valid RoleItem object', () => {
      const roleItem: RoleItem = {
        User_ID: 12345,
        User_Full_Name: '<PERSON>',
        Login_Name: 'john.smith',
        Jobcode: 'ENG001',
        JobDesc: 'Senior Software Engineer',
        FRCode: 101,
        Functional_ROLE: 'Project Lead'
      };

      expect(roleItem.User_ID).toBe(12345);
      expect(roleItem.User_Full_Name).toBe('<PERSON>');
      expect(roleItem.Login_Name).toBe('john.smith');
      expect(roleItem.Jobcode).toBe('ENG001');
      expect(roleItem.JobDesc).toBe('Senior Software Engineer');
      expect(roleItem.FRCode).toBe(101);
      expect(roleItem.Functional_ROLE).toBe('Project Lead');
    });

    it('should handle different user types and roles', () => {
      const adminRole: RoleItem = {
        User_ID: 1,
        User_Full_Name: 'Admin User',
        Login_Name: 'admin',
        Jobcode: 'ADM001',
        JobDesc: 'System Administrator',
        FRCode: 999,
        Functional_ROLE: 'Administrator'
      };

      const engineerRole: RoleItem = {
        User_ID: 2,
        User_Full_Name: 'Jane Engineer',
        Login_Name: 'jane.eng',
        Jobcode: 'ENG002',
        JobDesc: 'Junior Engineer',
        FRCode: 200,
        Functional_ROLE: 'Engineer'
      };

      expect(adminRole.FRCode).toBe(999);
      expect(adminRole.Functional_ROLE).toBe('Administrator');
      expect(engineerRole.FRCode).toBe(200);
      expect(engineerRole.Functional_ROLE).toBe('Engineer');
    });

    it('should validate all required properties exist', () => {
      const roleItem: RoleItem = {
        User_ID: 100,
        User_Full_Name: 'Test User',
        Login_Name: 'test.user',
        Jobcode: 'TST001',
        JobDesc: 'Test Description',
        FRCode: 50,
        Functional_ROLE: 'Tester'
      };

      expect(roleItem).toHaveProperty('User_ID');
      expect(roleItem).toHaveProperty('User_Full_Name');
      expect(roleItem).toHaveProperty('Login_Name');
      expect(roleItem).toHaveProperty('Jobcode');
      expect(roleItem).toHaveProperty('JobDesc');
      expect(roleItem).toHaveProperty('FRCode');
      expect(roleItem).toHaveProperty('Functional_ROLE');
    });

    it('should handle edge cases with empty strings and zero values', () => {
      const emptyRoleItem: RoleItem = {
        User_ID: 0,
        User_Full_Name: '',
        Login_Name: '',
        Jobcode: '',
        JobDesc: '',
        FRCode: 0,
        Functional_ROLE: ''
      };

      expect(typeof emptyRoleItem.User_ID).toBe('number');
      expect(typeof emptyRoleItem.User_Full_Name).toBe('string');
      expect(typeof emptyRoleItem.Login_Name).toBe('string');
      expect(typeof emptyRoleItem.Jobcode).toBe('string');
      expect(typeof emptyRoleItem.JobDesc).toBe('string');
      expect(typeof emptyRoleItem.FRCode).toBe('number');
      expect(typeof emptyRoleItem.Functional_ROLE).toBe('string');
    });

    it('should handle special characters and long names', () => {
      const specialRoleItem: RoleItem = {
        User_ID: Number.MAX_SAFE_INTEGER,
        User_Full_Name: 'José María Gutiérrez-Solana y García de la Torre',
        Login_Name: '<EMAIL>',
        Jobcode: 'SPEC-JOB-001!@#',
        JobDesc: 'Very Long Job Description with Special Characters !@#$%^&*()_+-=[]{}|;:,.<>?',
        FRCode: Number.MAX_SAFE_INTEGER,
        Functional_ROLE: 'Senior Architect & Technical Lead'
      };

      expect(specialRoleItem.User_Full_Name.includes('María')).toBe(true);
      expect(specialRoleItem.Login_Name.includes('@')).toBe(true);
      expect(specialRoleItem.Jobcode.includes('!')).toBe(true);
      expect(specialRoleItem.JobDesc.length).toBeGreaterThan(50);
      expect(specialRoleItem.Functional_ROLE.includes('&')).toBe(true);
    });

    it('should handle various job codes and functional roles', () => {
      const jobRoles = [
        { Jobcode: 'ENG', Functional_ROLE: 'Engineer' },
        { Jobcode: 'MGR', Functional_ROLE: 'Manager' },
        { Jobcode: 'QA', Functional_ROLE: 'Quality Assurance' },
        { Jobcode: 'DEV', Functional_ROLE: 'Developer' },
        { Jobcode: 'BA', Functional_ROLE: 'Business Analyst' }
      ];

      jobRoles.forEach((role, index) => {
        const roleItem: RoleItem = {
          User_ID: index + 1,
          User_Full_Name: `User ${index + 1}`,
          Login_Name: `user${index + 1}`,
          Jobcode: role.Jobcode,
          JobDesc: `${role.Functional_ROLE} Job Description`,
          FRCode: (index + 1) * 100,
          Functional_ROLE: role.Functional_ROLE
        };

        expect(roleItem.Jobcode).toBe(role.Jobcode);
        expect(roleItem.Functional_ROLE).toBe(role.Functional_ROLE);
        expect(roleItem.JobDesc).toContain(role.Functional_ROLE);
      });
    });
  });

  describe('RolesResponse Interface', () => {
    it('should create a valid RolesResponse with single role', () => {
      const rolesResponse: RolesResponse = {
        RolesList: [
          {
            User_ID: 1,
            User_Full_Name: 'John Doe',
            Login_Name: 'john.doe',
            Jobcode: 'ENG001',
            JobDesc: 'Senior Engineer',
            FRCode: 100,
            Functional_ROLE: 'Engineer'
          }
        ]
      };

      expect(Array.isArray(rolesResponse.RolesList)).toBe(true);
      expect(rolesResponse.RolesList.length).toBe(1);
      expect(rolesResponse.RolesList[0].User_ID).toBe(1);
      expect(rolesResponse.RolesList[0].User_Full_Name).toBe('John Doe');
    });

    it('should create a valid RolesResponse with multiple roles', () => {
      const rolesResponse: RolesResponse = {
        RolesList: [
          {
            User_ID: 1,
            User_Full_Name: 'Admin User',
            Login_Name: 'admin',
            Jobcode: 'ADM001',
            JobDesc: 'Administrator',
            FRCode: 999,
            Functional_ROLE: 'Admin'
          },
          {
            User_ID: 2,
            User_Full_Name: 'Manager User',
            Login_Name: 'manager',
            Jobcode: 'MGR001',
            JobDesc: 'Project Manager',
            FRCode: 500,
            Functional_ROLE: 'Manager'
          },
          {
            User_ID: 3,
            User_Full_Name: 'Developer User',
            Login_Name: 'developer',
            Jobcode: 'DEV001',
            JobDesc: 'Software Developer',
            FRCode: 300,
            Functional_ROLE: 'Developer'
          }
        ]
      };

      expect(rolesResponse.RolesList.length).toBe(3);
      expect(rolesResponse.RolesList[0].Functional_ROLE).toBe('Admin');
      expect(rolesResponse.RolesList[1].Functional_ROLE).toBe('Manager');
      expect(rolesResponse.RolesList[2].Functional_ROLE).toBe('Developer');
    });

    it('should handle empty roles list', () => {
      const emptyRolesResponse: RolesResponse = {
        RolesList: []
      };

      expect(Array.isArray(emptyRolesResponse.RolesList)).toBe(true);
      expect(emptyRolesResponse.RolesList.length).toBe(0);
    });

    it('should validate required properties exist', () => {
      const rolesResponse: RolesResponse = {
        RolesList: [
          {
            User_ID: 1,
            User_Full_Name: 'Test',
            Login_Name: 'test',
            Jobcode: 'TEST',
            JobDesc: 'Test Role',
            FRCode: 1,
            Functional_ROLE: 'Tester'
          }
        ]
      };

      expect(rolesResponse).toHaveProperty('RolesList');
      expect(Array.isArray(rolesResponse.RolesList)).toBe(true);
    });

    it('should handle large number of roles', () => {
      const largeRolesList: RoleItem[] = Array.from({ length: 100 }, (_, index) => ({
        User_ID: index + 1,
        User_Full_Name: `User ${index + 1}`,
        Login_Name: `user${index + 1}`,
        Jobcode: `JOB${String(index + 1).padStart(3, '0')}`,
        JobDesc: `Job Description ${index + 1}`,
        FRCode: (index + 1) * 10,
        Functional_ROLE: `Role ${index + 1}`
      }));

      const largeRolesResponse: RolesResponse = {
        RolesList: largeRolesList
      };

      expect(largeRolesResponse.RolesList.length).toBe(100);
      expect(largeRolesResponse.RolesList[0].User_ID).toBe(1);
      expect(largeRolesResponse.RolesList[99].User_ID).toBe(100);
    });
  });

  describe('RolesRequest Interface', () => {
    it('should create a valid RolesRequest object', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      const rolesRequest: RolesRequest = {
        User_ID: '12345',
        RoleType: 'Manager',
        cb: mockCallback
      };

      expect(rolesRequest.User_ID).toBe('12345');
      expect(rolesRequest.RoleType).toBe('Manager');
      expect(typeof rolesRequest.cb).toBe('function');
    });

    it('should handle different role types', () => {
      const roleTypes = ['Admin', 'Manager', 'Engineer', 'QA', 'Developer', 'Analyst'];
      
      roleTypes.forEach((roleType, index) => {
        const mockCallback: StatusCallback = jest.fn();
        const rolesRequest: RolesRequest = {
          User_ID: String(index + 1),
          RoleType: roleType,
          cb: mockCallback
        };

        expect(rolesRequest.RoleType).toBe(roleType);
        expect(rolesRequest.User_ID).toBe(String(index + 1));
      });
    });

    it('should validate all required properties exist', () => {
      const mockCallback: StatusCallback = jest.fn();
      const rolesRequest: RolesRequest = {
        User_ID: '100',
        RoleType: 'Test',
        cb: mockCallback
      };

      expect(rolesRequest).toHaveProperty('User_ID');
      expect(rolesRequest).toHaveProperty('RoleType');
      expect(rolesRequest).toHaveProperty('cb');
    });

    it('should handle callback function correctly', () => {
      const mockCallback: StatusCallback = jest.fn();
      const rolesRequest: RolesRequest = {
        User_ID: '123',
        RoleType: 'Engineer',
        cb: mockCallback
      };

      // Test callback functionality
      rolesRequest.cb({ success: true, data: { test: 'data' }, statusCode: 200 });
      
      expect(mockCallback).toHaveBeenCalledWith({
        success: true,
        data: { test: 'data' },
        statusCode: 200
      });
    });

    it('should handle edge cases for User_ID', () => {
      const userIdCases = ['', '0', '999999999', 'USER_123', 'special@user#id'];
      
      userIdCases.forEach(userId => {
        const mockCallback: StatusCallback = jest.fn();
        const rolesRequest: RolesRequest = {
          User_ID: userId,
          RoleType: 'TestRole',
          cb: mockCallback
        };

        expect(rolesRequest.User_ID).toBe(userId);
        expect(typeof rolesRequest.User_ID).toBe('string');
      });
    });

    it('should handle edge cases for RoleType', () => {
      const roleTypeCases = ['', 'A', 'VeryLongRoleTypeNameWithSpecialCharacters!@#$%^&*()', '123Role', 'Role With Spaces'];
      
      roleTypeCases.forEach(roleType => {
        const mockCallback: StatusCallback = jest.fn();
        const rolesRequest: RolesRequest = {
          User_ID: '123',
          RoleType: roleType,
          cb: mockCallback
        };

        expect(rolesRequest.RoleType).toBe(roleType);
        expect(typeof rolesRequest.RoleType).toBe('string');
      });
    });
  });

  describe('Interface Integration and Workflow', () => {
    it('should work together in a complete roles workflow', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      // Create a roles request
      const rolesRequest: RolesRequest = {
        User_ID: '12345',
        RoleType: 'Engineer',
        cb: mockCallback
      };

      // Simulate a successful response
      const successResponse: RolesResponse = {
        RolesList: [
          {
            User_ID: 12345,
            User_Full_Name: 'John Engineer',
            Login_Name: 'john.engineer',
            Jobcode: 'ENG001',
            JobDesc: 'Senior Software Engineer',
            FRCode: 200,
            Functional_ROLE: 'Engineer'
          },
          {
            User_ID: 12345,
            User_Full_Name: 'John Engineer',
            Login_Name: 'john.engineer',
            Jobcode: 'ENG002',
            JobDesc: 'Team Lead',
            FRCode: 250,
            Functional_ROLE: 'Team Lead'
          }
        ]
      };

      // Execute callback with success response
      rolesRequest.cb({ 
        success: true, 
        data: successResponse, 
        statusCode: 200 
      });

      expect(mockCallback).toHaveBeenCalledWith({
        success: true,
        data: successResponse,
        statusCode: 200
      });

      expect(successResponse.RolesList.length).toBe(2);
      expect(successResponse.RolesList[0].User_ID).toBe(12345);
    });

    it('should handle error scenarios in workflow', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      const rolesRequest: RolesRequest = {
        User_ID: 'invalid_user',
        RoleType: 'InvalidRole',
        cb: mockCallback
      };

      // Simulate error response
      rolesRequest.cb({ 
        success: false, 
        data: null, 
        statusCode: 404 
      });

      expect(mockCallback).toHaveBeenCalledWith({
        success: false,
        data: null,
        statusCode: 404
      });
    });

    it('should maintain type safety across all interfaces', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      // Test data consistency across interfaces
      const userId = 12345;
      const userIdString = '12345';
      
      const roleItem: RoleItem = {
        User_ID: userId,
        User_Full_Name: 'Test User',
        Login_Name: 'test.user',
        Jobcode: 'TEST001',
        JobDesc: 'Test Description',
        FRCode: 100,
        Functional_ROLE: 'Tester'
      };

      const rolesResponse: RolesResponse = {
        RolesList: [roleItem]
      };

      const rolesRequest: RolesRequest = {
        User_ID: userIdString,
        RoleType: 'Tester',
        cb: mockCallback
      };

      expect(roleItem.User_ID).toBe(userId);
      expect(rolesRequest.User_ID).toBe(userIdString);
      expect(rolesResponse.RolesList[0].User_ID).toBe(userId);
      expect(Number(rolesRequest.User_ID)).toBe(roleItem.User_ID);
    });
  });
}); 