import { LoginRequestData, LoginResponseItem, LoginResponseData } from '../../../model/Auth/LoginData';

describe('Auth - LoginData Models', () => {
  describe('LoginRequestData Interface', () => {
    it('should create a valid LoginRequestData object', () => {
      const loginRequest: LoginRequestData = {
        UserName: 'testuser',
        Password: 'password123',
        Token: 'auth-token-12345',
        CompanyCode: 1001,
        intAppCode: 2,
        isipcheck: 'Y',
        strDeviceUniqueCode: 'device-unique-123'
      };

      expect(loginRequest.UserName).toBe('testuser');
      expect(loginRequest.Password).toBe('password123');
      expect(loginRequest.Token).toBe('auth-token-12345');
      expect(loginRequest.CompanyCode).toBe(1001);
      expect(loginRequest.intAppCode).toBe(2);
      expect(loginRequest.isipcheck).toBe('Y');
      expect(loginRequest.strDeviceUniqueCode).toBe('device-unique-123');
    });

    it('should handle empty string values', () => {
      const loginRequest: LoginRequestData = {
        UserName: '',
        Password: '',
        Token: '',
        CompanyCode: 0,
        intAppCode: 0,
        isipcheck: '',
        strDeviceUniqueCode: ''
      };

      expect(typeof loginRequest.UserName).toBe('string');
      expect(typeof loginRequest.Password).toBe('string');
      expect(typeof loginRequest.Token).toBe('string');
      expect(typeof loginRequest.CompanyCode).toBe('number');
      expect(typeof loginRequest.intAppCode).toBe('number');
      expect(typeof loginRequest.isipcheck).toBe('string');
      expect(typeof loginRequest.strDeviceUniqueCode).toBe('string');
    });

    it('should validate required properties exist', () => {
      const loginRequest: LoginRequestData = {
        UserName: 'admin',
        Password: 'admin123',
        Token: 'jwt-token',
        CompanyCode: 100,
        intAppCode: 1,
        isipcheck: 'N',
        strDeviceUniqueCode: 'unique-device-id'
      };

      expect(loginRequest).toHaveProperty('UserName');
      expect(loginRequest).toHaveProperty('Password');
      expect(loginRequest).toHaveProperty('Token');
      expect(loginRequest).toHaveProperty('CompanyCode');
      expect(loginRequest).toHaveProperty('intAppCode');
      expect(loginRequest).toHaveProperty('isipcheck');
      expect(loginRequest).toHaveProperty('strDeviceUniqueCode');
    });
  });

  describe('LoginResponseItem Interface', () => {
    it('should create a valid LoginResponseItem object', () => {
      const responseItem: LoginResponseItem = {
        Status: 'Success',
        StatusCode: 200,
        Message: 'Login successful',
        UID: 12345,
        intAppCode: 2,
        strDeviceUniqueCode: 'device-123',
        CompanyCode: 1001,
        PSNumber: 'PS001',
        UserName: 'john.doe',
        Department: 'Engineering',
        EMailid: '<EMAIL>',
        MobileNo: '9876543210',
        ISDCode: 91,
        RegTag: 'REG001',
        EmpDetails: {
          empId: 'EMP001',
          designation: 'Senior Engineer',
          joiningDate: '2020-01-15'
        }
      };

      expect(responseItem.Status).toBe('Success');
      expect(responseItem.StatusCode).toBe(200);
      expect(responseItem.Message).toBe('Login successful');
      expect(responseItem.UID).toBe(12345);
      expect(responseItem.UserName).toBe('john.doe');
      expect(responseItem.Department).toBe('Engineering');
      expect(responseItem.EMailid).toBe('<EMAIL>');
      expect(responseItem.MobileNo).toBe('9876543210');
      expect(responseItem.ISDCode).toBe(91);
      expect(typeof responseItem.EmpDetails).toBe('object');
    });

    it('should handle failure response', () => {
      const failureResponse: LoginResponseItem = {
        Status: 'Failed',
        StatusCode: 401,
        Message: 'Invalid credentials',
        UID: 0,
        intAppCode: 2,
        strDeviceUniqueCode: 'device-123',
        CompanyCode: 1001,
        PSNumber: '',
        UserName: '',
        Department: '',
        EMailid: '',
        MobileNo: '',
        ISDCode: 0,
        RegTag: '',
        EmpDetails: null
      };

      expect(failureResponse.Status).toBe('Failed');
      expect(failureResponse.StatusCode).toBe(401);
      expect(failureResponse.Message).toBe('Invalid credentials');
      expect(failureResponse.UID).toBe(0);
      expect(failureResponse.EmpDetails).toBeNull();
    });

    it('should validate all required properties', () => {
      const responseItem: LoginResponseItem = {
        Status: 'Success',
        StatusCode: 200,
        Message: 'OK',
        UID: 1,
        intAppCode: 1,
        strDeviceUniqueCode: 'test',
        CompanyCode: 1,
        PSNumber: 'PS1',
        UserName: 'test',
        Department: 'IT',
        EMailid: '<EMAIL>',
        MobileNo: '1234567890',
        ISDCode: 1,
        RegTag: 'REG1',
        EmpDetails: {}
      };

      expect(responseItem).toHaveProperty('Status');
      expect(responseItem).toHaveProperty('StatusCode');
      expect(responseItem).toHaveProperty('Message');
      expect(responseItem).toHaveProperty('UID');
      expect(responseItem).toHaveProperty('intAppCode');
      expect(responseItem).toHaveProperty('strDeviceUniqueCode');
      expect(responseItem).toHaveProperty('CompanyCode');
      expect(responseItem).toHaveProperty('PSNumber');
      expect(responseItem).toHaveProperty('UserName');
      expect(responseItem).toHaveProperty('Department');
      expect(responseItem).toHaveProperty('EMailid');
      expect(responseItem).toHaveProperty('MobileNo');
      expect(responseItem).toHaveProperty('ISDCode');
      expect(responseItem).toHaveProperty('RegTag');
      expect(responseItem).toHaveProperty('EmpDetails');
    });
  });

  describe('LoginResponseData Array Type', () => {
    it('should create a valid LoginResponseData array', () => {
      const responseData: LoginResponseData = [
        {
          Status: 'Success',
          StatusCode: 200,
          Message: 'Login successful',
          UID: 12345,
          intAppCode: 2,
          strDeviceUniqueCode: 'device-123',
          CompanyCode: 1001,
          PSNumber: 'PS001',
          UserName: 'john.doe',
          Department: 'Engineering',
          EMailid: '<EMAIL>',
          MobileNo: '9876543210',
          ISDCode: 91,
          RegTag: 'REG001',
          EmpDetails: { empId: 'EMP001' }
        }
      ];

      expect(Array.isArray(responseData)).toBe(true);
      expect(responseData.length).toBe(1);
      expect(responseData[0].Status).toBe('Success');
      expect(responseData[0].UID).toBe(12345);
    });

    it('should handle empty array', () => {
      const emptyResponse: LoginResponseData = [];
      
      expect(Array.isArray(emptyResponse)).toBe(true);
      expect(emptyResponse.length).toBe(0);
    });

    it('should handle multiple response items', () => {
      const multipleResponse: LoginResponseData = [
        {
          Status: 'Success',
          StatusCode: 200,
          Message: 'Login successful',
          UID: 1,
          intAppCode: 1,
          strDeviceUniqueCode: 'device-1',
          CompanyCode: 1001,
          PSNumber: 'PS001',
          UserName: 'user1',
          Department: 'IT',
          EMailid: '<EMAIL>',
          MobileNo: '1111111111',
          ISDCode: 91,
          RegTag: 'REG001',
          EmpDetails: {}
        },
        {
          Status: 'Success',
          StatusCode: 200,
          Message: 'Login successful',
          UID: 2,
          intAppCode: 1,
          strDeviceUniqueCode: 'device-2',
          CompanyCode: 1001,
          PSNumber: 'PS002',
          UserName: 'user2',
          Department: 'HR',
          EMailid: '<EMAIL>',
          MobileNo: '2222222222',
          ISDCode: 91,
          RegTag: 'REG002',
          EmpDetails: {}
        }
      ];

      expect(multipleResponse.length).toBe(2);
      expect(multipleResponse[0].UID).toBe(1);
      expect(multipleResponse[1].UID).toBe(2);
      expect(multipleResponse[0].Department).toBe('IT');
      expect(multipleResponse[1].Department).toBe('HR');
    });
  });

  describe('Type Safety and Edge Cases', () => {
    it('should handle numeric edge cases', () => {
      const loginRequest: LoginRequestData = {
        UserName: 'test',
        Password: 'test',
        Token: 'test',
        CompanyCode: Number.MAX_SAFE_INTEGER,
        intAppCode: Number.MIN_SAFE_INTEGER,
        isipcheck: 'Y',
        strDeviceUniqueCode: 'test'
      };

      expect(Number.isInteger(loginRequest.CompanyCode)).toBe(true);
      expect(Number.isInteger(loginRequest.intAppCode)).toBe(true);
    });

    it('should handle string edge cases', () => {
      const loginRequest: LoginRequestData = {
        UserName: '<EMAIL>',
        Password: 'P@ssw0rd!@#$%^&*()',
        Token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9',
        CompanyCode: 1001,
        intAppCode: 2,
        isipcheck: 'Y',
        strDeviceUniqueCode: 'DEVICE-UNIQUE-ID-WITH-SPECIAL-CHARS-123!@#'
      };

      expect(loginRequest.UserName.includes('@')).toBe(true);
      expect(loginRequest.Password.length).toBeGreaterThan(8);
      expect(loginRequest.strDeviceUniqueCode.includes('-')).toBe(true);
    });

    it('should maintain data integrity across different scenarios', () => {
      const scenarios = [
        { CompanyCode: 0, intAppCode: 0 },
        { CompanyCode: 1, intAppCode: 1 },
        { CompanyCode: 9999, intAppCode: 9999 }
      ];

      scenarios.forEach((scenario, index) => {
        const request: LoginRequestData = {
          UserName: `user${index}`,
          Password: `pass${index}`,
          Token: `token${index}`,
          CompanyCode: scenario.CompanyCode,
          intAppCode: scenario.intAppCode,
          isipcheck: index % 2 === 0 ? 'Y' : 'N',
          strDeviceUniqueCode: `device${index}`
        };

        expect(request.CompanyCode).toBe(scenario.CompanyCode);
        expect(request.intAppCode).toBe(scenario.intAppCode);
        expect(['Y', 'N']).toContain(request.isipcheck);
      });
    });
  });
}); 