import {
  StatusCallback,
  WBSI<PERSON>,
  WBSJobRecord,
  PendingForApprovalBQITRecord,
  ActualList,
  Attachment,
  ProgressUpdateRequestBody,
  UploadImageResponse,
  DownloadImageRequest,
  DownloadImageResponse,
  ProgressUpdateResponseData,
  FilterSource,
  EntityConstants
} from '../../../model/DailyProgress/DailyProgressData';

describe('DailyProgress - DailyProgressData Models', () => {
  describe('StatusCallback Type', () => {
    it('should create and execute a status callback with success', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      mockCallback({ success: true, data: { test: 'data' }, statusCode: 200 });
      
      expect(mockCallback).toHaveBeenCalledWith({
        success: true,
        data: { test: 'data' },
        statusCode: 200
      });
    });

    it('should create and execute a status callback with failure', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      mockCallback({ success: false, statusCode: 500 });
      
      expect(mockCallback).toHaveBeenCalledWith({
        success: false,
        statusCode: 500
      });
    });

    it('should handle callback without optional parameters', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      mockCallback({ success: true });
      
      expect(mockCallback).toHaveBeenCalledWith({
        success: true
      });
    });
  });

  describe('WBSItem Interface', () => {
    it('should create a valid WBSItem with required properties', () => {
      const wbsItem: WBSItem = {
        id: 'WBS001',
        entity_Code: 'ENT001',
        job_Code: 'JOB001',
        entity_Type: 'Task',
        leaf_Node_Tag: 'Y',
        entity_Description: 'Sample WBS Item Description',
        parent_WBS_Code: 'PARENT001'
      };

      expect(wbsItem.id).toBe('WBS001');
      expect(wbsItem.entity_Code).toBe('ENT001');
      expect(wbsItem.job_Code).toBe('JOB001');
      expect(wbsItem.entity_Type).toBe('Task');
      expect(wbsItem.leaf_Node_Tag).toBe('Y');
      expect(wbsItem.entity_Description).toBe('Sample WBS Item Description');
      expect(wbsItem.parent_WBS_Code).toBe('PARENT001');
    });

    it('should create a valid WBSItem with all optional properties', () => {
      const wbsItem: WBSItem = {
        id: 'WBS002',
        entity_Code: 'ENT002',
        job_Code: 'JOB002',
        entity_Type: 'Activity',
        leaf_Node_Tag: 'N',
        entity_Description: 'Complex WBS Item with all properties',
        parent_WBS_Code: null,
        parent_entity_code: 'PARENT_ENT002',
        parent_Task: 'PARENT_TASK002',
        gis_Tag: 'GIS002',
        isChild: true,
        et_Code: 'ET002',
        fullPath: '/WBS002/SUB002',
        fullDescriptionForTooltip: 'Full description for tooltip display',
        taskDescription: 'Detailed task description',
        ActualQuantity: 150.5,
        Alignment: 'Center',
        Distance_From_Center: 25.8,
        From_Length: 100.0,
        IS_Completed: 'Y',
        Image_ID: 'IMG002',
        Image_URL: 'https://example.com/image002.jpg',
        Is_Hindrance: 'N',
        JOB: 'Construction Job',
        Latitude: 40.7128,
        Longitude: -74.0060,
        Manpower: 5,
        NodeId: 'NODE002',
        Progress_Length: 75.2,
        Quantity: 200,
        ReferanceNodeId: 'REF_NODE002',
        Remarks: 'Progress remarks here',
        TDate: '2024-01-15',
        Task: 'Concrete Pouring',
        TotalQuantity: 300,
        Total_Length: 150.0,
        UOM: 'cubic meters',
        UserID: 12345,
        UserName: 'John Engineer',
        Leaf_Node_Tag: 'Y',
        GIS_Tag: 'GIS_MAIN',
        Parent_Entity_Code: 'PARENT_ENT',
        Job_Description: 'Main construction job',
        hierarchyLevel: 'Level 3',
        isActive: 'Y',
        wpCode: 'WP001',
        wpDescription: 'Work Package Description',
        childWorkCode: 'CWC001',
        childWorkDescription: 'Child Work Description',
        deliverableCode: 1001,
        deliverableCodeDesc: 'Deliverable Description'
      };

      expect(wbsItem.ActualQuantity).toBe(150.5);
      expect(wbsItem.Latitude).toBe(40.7128);
      expect(wbsItem.Longitude).toBe(-74.0060);
      expect(wbsItem.isChild).toBe(true);
      expect(wbsItem.deliverableCode).toBe(1001);
      expect(wbsItem.parent_WBS_Code).toBeNull();
    });

    it('should handle null and undefined optional values', () => {
      const wbsItem: WBSItem = {
        id: 'WBS003',
        entity_Code: 'ENT003',
        job_Code: 'JOB003',
        entity_Type: 'SubTask',
        leaf_Node_Tag: 'N',
        entity_Description: 'WBS Item with nulls',
        parent_WBS_Code: null,
        ActualQuantity: null,
        Distance_From_Center: null,
        From_Length: null,
        IS_Completed: null,
        Image_ID: null,
        Image_URL: null,
        Is_Hindrance: null,
        NodeId: null,
        Progress_Length: null,
        ReferanceNodeId: null,
        Remarks: null,
        TotalQuantity: null,
        Total_Length: null,
        UOM: null
      };

      expect(wbsItem.ActualQuantity).toBeNull();
      expect(wbsItem.parent_WBS_Code).toBeNull();
      expect(wbsItem.IS_Completed).toBeNull();
    });
  });

  describe('WBSJobRecord Interface', () => {
    it('should create a valid WBSJobRecord', () => {
      const wbsJobRecord: WBSJobRecord = {
        WBS_Code: 'WBS001',
        Job_Code: 'JOB001',
        Leaf_Node_Tag: 'Y',
        WBS_Description: 'Foundation Work',
        Parent_WBS_Code: null,
        ET_Code: 'ET001',
        GIS_Tag: 'GIS001',
        Parent_Entity_Code: 'PARENT001'
      };

      expect(wbsJobRecord.WBS_Code).toBe('WBS001');
      expect(wbsJobRecord.Job_Code).toBe('JOB001');
      expect(wbsJobRecord.Leaf_Node_Tag).toBe('Y');
      expect(wbsJobRecord.WBS_Description).toBe('Foundation Work');
      expect(wbsJobRecord.Parent_WBS_Code).toBeNull();
      expect(wbsJobRecord.ET_Code).toBe('ET001');
      expect(wbsJobRecord.GIS_Tag).toBe('GIS001');
      expect(wbsJobRecord.Parent_Entity_Code).toBe('PARENT001');
    });

    it('should handle hierarchical WBS records', () => {
      const parentRecord: WBSJobRecord = {
        WBS_Code: 'WBS_PARENT',
        Job_Code: 'JOB_PARENT',
        Leaf_Node_Tag: 'N',
        WBS_Description: 'Parent WBS Node',
        Parent_WBS_Code: null,
        ET_Code: 'ET_PARENT',
        GIS_Tag: 'GIS_PARENT',
        Parent_Entity_Code: 'ROOT'
      };

      const childRecord: WBSJobRecord = {
        WBS_Code: 'WBS_CHILD',
        Job_Code: 'JOB_CHILD',
        Leaf_Node_Tag: 'Y',
        WBS_Description: 'Child WBS Node',
        Parent_WBS_Code: 'WBS_PARENT',
        ET_Code: 'ET_CHILD',
        GIS_Tag: 'GIS_CHILD',
        Parent_Entity_Code: 'WBS_PARENT'
      };

      expect(parentRecord.Leaf_Node_Tag).toBe('N');
      expect(childRecord.Parent_WBS_Code).toBe('WBS_PARENT');
      expect(childRecord.Leaf_Node_Tag).toBe('Y');
    });
  });

  describe('PendingForApprovalBQITRecord Interface', () => {
    it('should create a valid PendingForApprovalBQITRecord', () => {
      const pendingRecord: PendingForApprovalBQITRecord = {
        ActualQuantity: 125.5,
        Alignment: 'Left',
        Distance_From_Center: 15.2,
        From_Length: 80.0,
        IS_Completed: 'N',
        Image_ID: 'IMG123',
        Image_URL: 'https://example.com/pending-image.jpg',
        Is_Hindrance: 'Y',
        JOB: 'Excavation Work',
        Latitude: 25.7617,
        Longitude: -80.1918,
        Manpower: 8,
        NodeId: 'NODE123',
        Progress_Length: 60.5,
        Quantity: 200,
        ReferanceNodeId: 'REF123',
        Remarks: 'Pending approval for excavation work',
        TDate: '2024-01-20',
        Task: 'Site Excavation',
        TaskType: 'Manual',
        Task_Custom_Description: 'Custom excavation task',
        Task_Description: 'Excavate foundation area',
        TotalQuantity: 250,
        Total_Length: 120.0,
        UOM: 'cubic yards',
        UserID: 54321,
        UserName: 'Jane Supervisor',
        WBS: 'WBS_EXC_001',
        WBS_Custom_Description: 'Custom WBS description',
        WBS_Description: 'Excavation WBS',
        Leaf_Node_Tag: 'Y',
        GIS_Tag: 'GIS_EXC',
        Parent_Entity_Code: 'PARENT_EXC',
        Job_Description: 'Foundation excavation job'
      };

      expect(pendingRecord.ActualQuantity).toBe(125.5);
      expect(pendingRecord.Is_Hindrance).toBe('Y');
      expect(pendingRecord.Latitude).toBe(25.7617);
      expect(pendingRecord.Longitude).toBe(-80.1918);
      expect(pendingRecord.UserID).toBe(54321);
      expect(pendingRecord.TaskType).toBe('Manual');
    });

    it('should handle null values appropriately', () => {
      const pendingRecordWithNulls: PendingForApprovalBQITRecord = {
        ActualQuantity: null,
        Alignment: null,
        Distance_From_Center: null,
        From_Length: null,
        IS_Completed: null,
        Image_ID: null,
        Image_URL: null,
        Is_Hindrance: null,
        JOB: 'Test Job',
        Latitude: 0,
        Longitude: 0,
        Manpower: 1,
        NodeId: null,
        Progress_Length: null,
        Quantity: 1,
        ReferanceNodeId: null,
        Remarks: null,
        TDate: '2024-01-01',
        Task: 'Test Task',
        TaskType: 'Test',
        Task_Custom_Description: 'Test Custom',
        Task_Description: 'Test Description',
        TotalQuantity: null,
        Total_Length: null,
        UOM: null,
        UserID: 1,
        UserName: 'Test User',
        WBS: 'WBS_TEST',
        WBS_Custom_Description: 'Test WBS Custom',
        WBS_Description: 'Test WBS Description',
        Leaf_Node_Tag: 'Y',
        GIS_Tag: 'GIS_TEST',
        Parent_Entity_Code: 'PARENT_TEST',
        Job_Description: 'Test Job Description'
      };

      expect(pendingRecordWithNulls.ActualQuantity).toBeNull();
      expect(pendingRecordWithNulls.UOM).toBeNull();
      expect(pendingRecordWithNulls.TotalQuantity).toBeNull();
    });
  });

  describe('ActualList Interface', () => {
    it('should create a valid ActualList entry', () => {
      const actualEntry: ActualList = {
        WBS: 'WBS_ACT_001',
        TaskCode: 'TASK_001',
        ADate: '2024-01-15',
        Quantity: '150.5',
        Manpower: '5',
        Remarks: 'Work completed successfully',
        Tasktype: 'Construction',
        Is_Approved: 'Y',
        Latitude: 40.7128,
        Longitude: -74.0060
      };

      expect(actualEntry.WBS).toBe('WBS_ACT_001');
      expect(actualEntry.TaskCode).toBe('TASK_001');
      expect(actualEntry.ADate).toBe('2024-01-15');
      expect(actualEntry.Quantity).toBe('150.5');
      expect(actualEntry.Manpower).toBe('5');
      expect(actualEntry.Is_Approved).toBe('Y');
      expect(actualEntry.Latitude).toBe(40.7128);
      expect(actualEntry.Longitude).toBe(-74.0060);
    });

    it('should handle different approval statuses', () => {
      const approvedEntry: ActualList = {
        WBS: 'WBS_001',
        TaskCode: 'TASK_001',
        ADate: '2024-01-01',
        Quantity: '100',
        Manpower: '3',
        Remarks: 'Approved work',
        Tasktype: 'Foundation',
        Is_Approved: 'Y',
        Latitude: 0,
        Longitude: 0
      };

      const pendingEntry: ActualList = {
        WBS: 'WBS_002',
        TaskCode: 'TASK_002',
        ADate: '2024-01-02',
        Quantity: '200',
        Manpower: '5',
        Remarks: 'Pending approval',
        Tasktype: 'Structure',
        Is_Approved: 'N',
        Latitude: 10,
        Longitude: 10
      };

      expect(approvedEntry.Is_Approved).toBe('Y');
      expect(pendingEntry.Is_Approved).toBe('N');
    });
  });

  describe('Attachment Interface', () => {
    it('should create a valid Attachment', () => {
      const attachment: Attachment = {
        WBS: 'WBS_ATT_001',
        TaskCode: 'TASK_ATT_001',
        ADate: '2024-01-15',
        Tasktype: 'Documentation',
        SiteUrl: 'https://sharepoint.company.com/sites/project',
        Unique: 'UNIQUE_123456'
      };

      expect(attachment.WBS).toBe('WBS_ATT_001');
      expect(attachment.TaskCode).toBe('TASK_ATT_001');
      expect(attachment.ADate).toBe('2024-01-15');
      expect(attachment.Tasktype).toBe('Documentation');
      expect(attachment.SiteUrl).toBe('https://sharepoint.company.com/sites/project');
      expect(attachment.Unique).toBe('UNIQUE_123456');
    });

    it('should handle different attachment types', () => {
      const imageAttachment: Attachment = {
        WBS: 'WBS_IMG',
        TaskCode: 'TASK_IMG',
        ADate: '2024-01-10',
        Tasktype: 'Photo',
        SiteUrl: 'https://storage.company.com/images',
        Unique: 'IMG_UNIQUE_789'
      };

      const documentAttachment: Attachment = {
        WBS: 'WBS_DOC',
        TaskCode: 'TASK_DOC',
        ADate: '2024-01-11',
        Tasktype: 'Report',
        SiteUrl: 'https://documents.company.com/reports',
        Unique: 'DOC_UNIQUE_101'
      };

      expect(imageAttachment.Tasktype).toBe('Photo');
      expect(documentAttachment.Tasktype).toBe('Report');
    });
  });

  describe('ProgressUpdateRequestBody Interface', () => {
    it('should create a valid ProgressUpdateRequestBody', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      const actualList: ActualList[] = [
        {
          WBS: 'WBS_001',
          TaskCode: 'TASK_001',
          ADate: '2024-01-15',
          Quantity: '100',
          Manpower: '3',
          Remarks: 'Progress update',
          Tasktype: 'Construction',
          Is_Approved: 'N',
          Latitude: 40.7128,
          Longitude: -74.0060
        }
      ];

      const attachments: Attachment[] = [
        {
          WBS: 'WBS_001',
          TaskCode: 'TASK_001',
          ADate: '2024-01-15',
          Tasktype: 'Photo',
          SiteUrl: 'https://storage.company.com',
          Unique: 'UNIQUE_123'
        }
      ];

      const progressUpdate: ProgressUpdateRequestBody = {
        jobCode: 'JOB_PROG_001',
        UID: '12345',
        Type: 'Daily',
        Notification_Desc: 'Daily progress update submitted',
        Quantity: 150,
        uOM: 'cubic meters',
        manPower: 5,
        ActualList: actualList,
        Attachments: attachments,
        cb: mockCallback
      };

      expect(progressUpdate.jobCode).toBe('JOB_PROG_001');
      expect(progressUpdate.UID).toBe('12345');
      expect(progressUpdate.Type).toBe('Daily');
      expect(progressUpdate.Quantity).toBe(150);
      expect(progressUpdate.uOM).toBe('cubic meters');
      expect(progressUpdate.manPower).toBe(5);
      expect(progressUpdate.ActualList.length).toBe(1);
      expect(progressUpdate.Attachments.length).toBe(1);
      expect(typeof progressUpdate.cb).toBe('function');
    });

    it('should handle empty arrays', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      const progressUpdate: ProgressUpdateRequestBody = {
        jobCode: 'JOB_EMPTY',
        UID: '0',
        Type: 'Initial',
        Notification_Desc: 'Initial submission',
        Quantity: 0,
        uOM: '',
        manPower: 0,
        ActualList: [],
        Attachments: [],
        cb: mockCallback
      };

      expect(progressUpdate.ActualList.length).toBe(0);
      expect(progressUpdate.Attachments.length).toBe(0);
      expect(Array.isArray(progressUpdate.ActualList)).toBe(true);
      expect(Array.isArray(progressUpdate.Attachments)).toBe(true);
    });
  });

  describe('UploadImageResponse Interface', () => {
    it('should create a valid UploadImageResponse', () => {
      const uploadResponse: UploadImageResponse = {
        UniqueID: 'UPLOAD_123456',
        SiteUrl: 'https://sharepoint.company.com/sites/project/images',
        LibraryName: 'ProjectImages',
        FileSize: '2048000'
      };

      expect(uploadResponse.UniqueID).toBe('UPLOAD_123456');
      expect(uploadResponse.SiteUrl).toBe('https://sharepoint.company.com/sites/project/images');
      expect(uploadResponse.LibraryName).toBe('ProjectImages');
      expect(uploadResponse.FileSize).toBe('2048000');
    });

    it('should handle different file sizes and libraries', () => {
      const smallFile: UploadImageResponse = {
        UniqueID: 'SMALL_IMG_001',
        SiteUrl: 'https://storage.com/small',
        LibraryName: 'ThumbnailLibrary',
        FileSize: '50000'
      };

      const largeFile: UploadImageResponse = {
        UniqueID: 'LARGE_IMG_001',
        SiteUrl: 'https://storage.com/large',
        LibraryName: 'HighResLibrary',
        FileSize: '10485760'
      };

      expect(parseInt(smallFile.FileSize)).toBeLessThan(parseInt(largeFile.FileSize));
      expect(smallFile.LibraryName).toBe('ThumbnailLibrary');
      expect(largeFile.LibraryName).toBe('HighResLibrary');
    });
  });

  describe('DownloadImageRequest Interface', () => {
    it('should create a valid DownloadImageRequest', () => {
      const downloadRequest: DownloadImageRequest = {
        ModuleName: 'DailyProgress',
        Unique: 'DOWNLOAD_123456',
        SiteUrl: 'https://sharepoint.company.com/sites/project'
      };

      expect(downloadRequest.ModuleName).toBe('DailyProgress');
      expect(downloadRequest.Unique).toBe('DOWNLOAD_123456');
      expect(downloadRequest.SiteUrl).toBe('https://sharepoint.company.com/sites/project');
    });

    it('should handle different module names', () => {
      const modules = ['DailyProgress', 'WBS', 'Reports', 'Attachments'];
      
      modules.forEach((moduleName, index) => {
        const request: DownloadImageRequest = {
          ModuleName: moduleName,
          Unique: `UNIQUE_${index}`,
          SiteUrl: `https://site${index}.company.com`
        };

        expect(request.ModuleName).toBe(moduleName);
        expect(request.Unique).toBe(`UNIQUE_${index}`);
      });
    });
  });

  describe('DownloadImageResponse Interface', () => {
    it('should create a successful DownloadImageResponse', () => {
      const successResponse: DownloadImageResponse = {
        success: true,
        data: 'base64encodedimagedata==',
        message: 'Image downloaded successfully'
      };

      expect(successResponse.success).toBe(true);
      expect(successResponse.data).toBe('base64encodedimagedata==');
      expect(successResponse.message).toBe('Image downloaded successfully');
    });

    it('should create a failed DownloadImageResponse', () => {
      const failureResponse: DownloadImageResponse = {
        success: false,
        message: 'Image not found'
      };

      expect(failureResponse.success).toBe(false);
      expect(failureResponse.data).toBeUndefined();
      expect(failureResponse.message).toBe('Image not found');
    });
  });

  describe('ProgressUpdateResponseData Interface', () => {
    it('should create a successful ProgressUpdateResponseData', () => {
      const successResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Progress update saved successfully',
        data: { id: 'PROG_123', timestamp: '2024-01-15T10:30:00Z' }
      };

      expect(successResponse.success).toBe(true);
      expect(successResponse.message).toBe('Progress update saved successfully');
      expect(successResponse.data).toEqual({ id: 'PROG_123', timestamp: '2024-01-15T10:30:00Z' });
    });

    it('should create a failed ProgressUpdateResponseData', () => {
      const failureResponse: ProgressUpdateResponseData = {
        success: false,
        message: 'Validation failed: Missing required fields'
      };

      expect(failureResponse.success).toBe(false);
      expect(failureResponse.message).toBe('Validation failed: Missing required fields');
      expect(failureResponse.data).toBeUndefined();
    });
  });

  describe('FilterSource Type', () => {
    it('should validate FilterSource union type values', () => {
      const bookMarkSource: FilterSource = 'Book Mark';
      const recentSource: FilterSource = 'Recent';
      const pendingSource: FilterSource = 'Pending';

      expect(bookMarkSource).toBe('Book Mark');
      expect(recentSource).toBe('Recent');
      expect(pendingSource).toBe('Pending');
    });

    it('should use FilterSource in various contexts', () => {
      const filterOptions: FilterSource[] = ['Book Mark', 'Recent', 'Pending'];
      
      filterOptions.forEach(option => {
        expect(['Book Mark', 'Recent', 'Pending']).toContain(option);
      });
    });
  });

  describe('EntityConstants', () => {
    it('should validate all EntityConstants values', () => {
      expect(EntityConstants.WBSJob).toBe('WBSJob');
      expect(EntityConstants.WBSTask).toBe('WBSTask');
      expect(EntityConstants.WBSDetails).toBe('WBSDetails');
      expect(EntityConstants.GISNode).toBe('GISNode');
      expect(EntityConstants.Y).toBe('Y');
    });

    it('should verify EntityConstants are read-only', () => {
      expect(typeof EntityConstants).toBe('object');
      expect(Object.keys(EntityConstants).length).toBe(5);
    });

    it('should use EntityConstants in conditional logic', () => {
      const entityType = EntityConstants.WBSJob;
      
      switch (entityType) {
        case EntityConstants.WBSJob:
          expect(entityType).toBe('WBSJob');
          break;
        case EntityConstants.WBSTask:
          expect(entityType).toBe('WBSTask');
          break;
        default:
          fail('Unexpected entity type');
      }
    });
  });

  describe('Complex Integration Scenarios', () => {
    it('should handle complete progress update workflow', () => {
      const mockCallback: StatusCallback = jest.fn();
      
      // Create complex actual list with multiple entries
      const actualList: ActualList[] = [
        {
          WBS: 'WBS_FOUNDATION',
          TaskCode: 'EXCAVATION',
          ADate: '2024-01-15',
          Quantity: '150.5',
          Manpower: '8',
          Remarks: 'Foundation excavation completed',
          Tasktype: 'Excavation',
          Is_Approved: 'Y',
          Latitude: 40.7128,
          Longitude: -74.0060
        },
        {
          WBS: 'WBS_FOUNDATION',
          TaskCode: 'CONCRETE_POUR',
          ADate: '2024-01-16',
          Quantity: '75.2',
          Manpower: '6',
          Remarks: 'Concrete pouring in progress',
          Tasktype: 'Concrete Work',
          Is_Approved: 'N',
          Latitude: 40.7130,
          Longitude: -74.0062
        }
      ];

      // Create multiple attachments
      const attachments: Attachment[] = [
        {
          WBS: 'WBS_FOUNDATION',
          TaskCode: 'EXCAVATION',
          ADate: '2024-01-15',
          Tasktype: 'Photo',
          SiteUrl: 'https://storage.com/excavation-photos',
          Unique: 'EXCAVATION_IMG_001'
        },
        {
          WBS: 'WBS_FOUNDATION',
          TaskCode: 'CONCRETE_POUR',
          ADate: '2024-01-16',
          Tasktype: 'Report',
          SiteUrl: 'https://docs.com/concrete-reports',
          Unique: 'CONCRETE_RPT_001'
        }
      ];

      // Create comprehensive progress update request
      const progressRequest: ProgressUpdateRequestBody = {
        jobCode: 'FOUNDATION_PROJECT_2024',
        UID: 'USER_12345',
        Type: 'Weekly',
        Notification_Desc: 'Foundation work progress update for week 3',
        Quantity: 225.7,
        uOM: 'cubic meters',
        manPower: 14,
        ActualList: actualList,
        Attachments: attachments,
        cb: mockCallback
      };

      // Simulate successful response
      const successResponse: ProgressUpdateResponseData = {
        success: true,
        message: 'Progress update processed successfully',
        data: {
          updateId: 'PROG_UPDATE_789',
          processedActuals: 2,
          processedAttachments: 2,
          approvalRequired: true
        }
      };

      // Execute callback
      progressRequest.cb({ success: true, data: successResponse, statusCode: 200 });

      expect(mockCallback).toHaveBeenCalledWith({
        success: true,
        data: successResponse,
        statusCode: 200
      });

      expect(progressRequest.ActualList.length).toBe(2);
      expect(progressRequest.Attachments.length).toBe(2);
      expect(progressRequest.Quantity).toBe(225.7);
    });

    it('should handle data consistency across related interfaces', () => {
      // Create related WBSItem and ActualList entries
      const wbsItem: WBSItem = {
        id: 'WBS_CONSISTENT_001',
        entity_Code: 'ENT_CONSISTENT_001',
        job_Code: 'JOB_CONSISTENT_001',
        entity_Type: 'Task',
        leaf_Node_Tag: 'Y',
        entity_Description: 'Consistent data test',
        parent_WBS_Code: null,
        Latitude: 25.7617,
        Longitude: -80.1918,
        UserID: 98765,
        UserName: 'Consistency Tester'
      };

      const actualEntry: ActualList = {
        WBS: 'WBS_CONSISTENT_001',
        TaskCode: 'CONSISTENT_TASK',
        ADate: '2024-01-20',
        Quantity: '100',
        Manpower: '4',
        Remarks: 'Consistent data entry',
        Tasktype: 'Verification',
        Is_Approved: 'Y',
        Latitude: 25.7617,
        Longitude: -80.1918
      };

      expect(wbsItem.Latitude).toBe(actualEntry.Latitude);
      expect(wbsItem.Longitude).toBe(actualEntry.Longitude);
      expect(wbsItem.id).toBe(actualEntry.WBS);
    });
  });
}); 