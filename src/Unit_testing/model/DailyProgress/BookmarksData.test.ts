import {
  BookmarksRequestData,
  JobWorkListsData,
  BookmarksResponseData
} from '../../../model/DailyProgress/BookmarksData';

describe('DailyProgress - BookmarksData Models', () => {
  describe('JobWorkListsData Interface', () => {
    it('should create a valid JobWorkListsData object', () => {
      const jobWorkList: JobWorkListsData = {
        jobcode: 'JOB_001',
        wpcode: 'WP_001'
      };

      expect(jobWorkList.jobcode).toBe('JOB_001');
      expect(jobWorkList.wpcode).toBe('WP_001');
    });

    it('should handle different job and work package codes', () => {
      const testCases = [
        { jobcode: 'CONSTRUCTION_2024', wpcode: 'WP_FOUNDATION' },
        { jobcode: 'INFRA_PROJECT_01', wpcode: 'WP_EXCAVATION' },
        { jobcode: 'BUILDING_ALPHA', wpcode: 'WP_STRUCTURAL' }
      ];

      testCases.forEach((testCase, index) => {
        const jobWorkList: JobWorkListsData = {
          jobcode: testCase.jobcode,
          wpcode: testCase.wpcode
        };

        expect(jobWorkList.jobcode).toBe(testCase.jobcode);
        expect(jobWorkList.wpcode).toBe(testCase.wpcode);
      });
    });

    it('should validate required properties exist', () => {
      const jobWorkList: JobWorkListsData = {
        jobcode: 'TEST_JOB',
        wpcode: 'TEST_WP'
      };

      expect(jobWorkList).toHaveProperty('jobcode');
      expect(jobWorkList).toHaveProperty('wpcode');
    });

    it('should handle empty string values', () => {
      const emptyJobWorkList: JobWorkListsData = {
        jobcode: '',
        wpcode: ''
      };

      expect(typeof emptyJobWorkList.jobcode).toBe('string');
      expect(typeof emptyJobWorkList.wpcode).toBe('string');
      expect(emptyJobWorkList.jobcode).toBe('');
      expect(emptyJobWorkList.wpcode).toBe('');
    });

    it('should handle special characters and long codes', () => {
      const specialJobWorkList: JobWorkListsData = {
        jobcode: 'JOB_WITH_SPECIAL_CHARS_!@#$%^&*()_+-=',
        wpcode: 'WP_UNICODE_测试_ÉMOJIS_🏗️_ACCENTS_café'
      };

      expect(specialJobWorkList.jobcode).toContain('!@#$%^&*()_+-=');
      expect(specialJobWorkList.wpcode).toContain('测试');
      expect(specialJobWorkList.wpcode).toContain('🏗️');
      expect(specialJobWorkList.wpcode).toContain('café');
    });
  });

  describe('BookmarksRequestData Interface', () => {
    it('should create a valid BookmarksRequestData object', () => {
      const jobWorkLists: JobWorkListsData[] = [
        { jobcode: 'JOB_001', wpcode: 'WP_001' },
        { jobcode: 'JOB_002', wpcode: 'WP_002' }
      ];

      const bookmarksRequest: BookmarksRequestData = {
        Uid: 'USER_12345',
        Type: 'BookmarkLinked',
        JobWorkLists: jobWorkLists
      };

      expect(bookmarksRequest.Uid).toBe('USER_12345');
      expect(bookmarksRequest.Type).toBe('BookmarkLinked');
      expect(Array.isArray(bookmarksRequest.JobWorkLists)).toBe(true);
      expect(bookmarksRequest.JobWorkLists.length).toBe(2);
      expect(bookmarksRequest.JobWorkLists[0].jobcode).toBe('JOB_001');
      expect(bookmarksRequest.JobWorkLists[1].jobcode).toBe('JOB_002');
    });

    it('should handle empty JobWorkLists array', () => {
      const bookmarksRequest: BookmarksRequestData = {
        Uid: 'USER_EMPTY',
        Type: 'BookmarkUnlinked',
        JobWorkLists: []
      };

      expect(bookmarksRequest.JobWorkLists.length).toBe(0);
      expect(Array.isArray(bookmarksRequest.JobWorkLists)).toBe(true);
    });

    it('should handle different bookmark types', () => {
      const bookmarkTypes = ['BookmarkLinked', 'BookmarkUnlinked', 'BookmarkManagement', 'BookmarkSync'];
      
      bookmarkTypes.forEach((type, index) => {
        const bookmarksRequest: BookmarksRequestData = {
          Uid: `USER_${index + 1}`,
          Type: type,
          JobWorkLists: [
            { jobcode: `JOB_${index + 1}`, wpcode: `WP_${index + 1}` }
          ]
        };

        expect(bookmarksRequest.Type).toBe(type);
        expect(bookmarksRequest.Uid).toBe(`USER_${index + 1}`);
      });
    });

    it('should validate all required properties exist', () => {
      const bookmarksRequest: BookmarksRequestData = {
        Uid: 'TEST_USER',
        Type: 'TEST_TYPE',
        JobWorkLists: []
      };

      expect(bookmarksRequest).toHaveProperty('Uid');
      expect(bookmarksRequest).toHaveProperty('Type');
      expect(bookmarksRequest).toHaveProperty('JobWorkLists');
    });

    it('should handle large number of JobWorkLists', () => {
      const largeJobWorkLists: JobWorkListsData[] = Array.from({ length: 100 }, (_, index) => ({
        jobcode: `JOB_${String(index + 1).padStart(3, '0')}`,
        wpcode: `WP_${String(index + 1).padStart(3, '0')}`
      }));

      const bookmarksRequest: BookmarksRequestData = {
        Uid: 'USER_BULK',
        Type: 'BulkBookmarkOperation',
        JobWorkLists: largeJobWorkLists
      };

      expect(bookmarksRequest.JobWorkLists.length).toBe(100);
      expect(bookmarksRequest.JobWorkLists[0].jobcode).toBe('JOB_001');
      expect(bookmarksRequest.JobWorkLists[99].jobcode).toBe('JOB_100');
    });

    it('should handle different UID formats', () => {
      const uidFormats = [
        'USER_123',
        '12345',
        '<EMAIL>',
        'USER-GUID-123-456-789',
        'employee_id_with_underscores',
        'VERY_LONG_USER_IDENTIFIER_WITH_SPECIAL_CHARACTERS_!@#$%^&*()'
      ];

      uidFormats.forEach((uid, index) => {
        const bookmarksRequest: BookmarksRequestData = {
          Uid: uid,
          Type: `Type_${index}`,
          JobWorkLists: [
            { jobcode: `JOB_UID_${index}`, wpcode: `WP_UID_${index}` }
          ]
        };

        expect(bookmarksRequest.Uid).toBe(uid);
        expect(typeof bookmarksRequest.Uid).toBe('string');
      });
    });

    it('should handle complex JobWorkLists scenarios', () => {
      const complexJobWorkLists: JobWorkListsData[] = [
        { jobcode: 'FOUNDATION_WORK_2024', wpcode: 'WP_EXCAVATION_PHASE_1' },
        { jobcode: 'STRUCTURAL_STEEL_INSTALLATION', wpcode: 'WP_BEAM_PLACEMENT' },
        { jobcode: 'ELECTRICAL_ROUGH_IN', wpcode: 'WP_CONDUIT_INSTALLATION' },
        { jobcode: 'PLUMBING_ROUGH_IN', wpcode: 'WP_PIPE_INSTALLATION' },
        { jobcode: 'HVAC_DUCTWORK', wpcode: 'WP_MAIN_TRUNK_INSTALLATION' }
      ];

      const bookmarksRequest: BookmarksRequestData = {
        Uid: 'PROJECT_MANAGER_001',
        Type: 'ComplexBookmarkManagement',
        JobWorkLists: complexJobWorkLists
      };

      expect(bookmarksRequest.JobWorkLists.length).toBe(5);
      expect(bookmarksRequest.JobWorkLists[0].jobcode).toContain('FOUNDATION');
      expect(bookmarksRequest.JobWorkLists[2].wpcode).toContain('CONDUIT');
    });
  });

  describe('BookmarksResponseData Interface', () => {
    it('should create a valid BookmarksResponseData object with all properties', () => {
      const bookmarksResponse: BookmarksResponseData = {
        BookMarkUnlinkedOutput: {
          Message: 'Bookmarks unlinked successfully'
        },
        bookMarklinkedOutput: {
          Message: 'Bookmarks linked successfully'
        },
        MasterList: {
          BookMarkLinked: null,
          LatLongHierarchy: null,
          UserMannual: null,
          WBS_Description: null
        }
      };

      expect(bookmarksResponse.BookMarkUnlinkedOutput.Message).toBe('Bookmarks unlinked successfully');
      expect(bookmarksResponse.bookMarklinkedOutput.Message).toBe('Bookmarks linked successfully');
      expect(bookmarksResponse.MasterList.BookMarkLinked).toBeNull();
      expect(bookmarksResponse.MasterList.LatLongHierarchy).toBeNull();
      expect(bookmarksResponse.MasterList.UserMannual).toBeNull();
      expect(bookmarksResponse.MasterList.WBS_Description).toBeNull();
    });

    it('should validate all required properties exist', () => {
      const bookmarksResponse: BookmarksResponseData = {
        BookMarkUnlinkedOutput: { Message: 'Test unlinked' },
        bookMarklinkedOutput: { Message: 'Test linked' },
        MasterList: {
          BookMarkLinked: null,
          LatLongHierarchy: null,
          UserMannual: null,
          WBS_Description: null
        }
      };

      expect(bookmarksResponse).toHaveProperty('BookMarkUnlinkedOutput');
      expect(bookmarksResponse).toHaveProperty('bookMarklinkedOutput');
      expect(bookmarksResponse).toHaveProperty('MasterList');
      expect(bookmarksResponse.BookMarkUnlinkedOutput).toHaveProperty('Message');
      expect(bookmarksResponse.bookMarklinkedOutput).toHaveProperty('Message');
      expect(bookmarksResponse.MasterList).toHaveProperty('BookMarkLinked');
      expect(bookmarksResponse.MasterList).toHaveProperty('LatLongHierarchy');
      expect(bookmarksResponse.MasterList).toHaveProperty('UserMannual');
      expect(bookmarksResponse.MasterList).toHaveProperty('WBS_Description');
    });

    it('should handle different success and error messages', () => {
      const successResponse: BookmarksResponseData = {
        BookMarkUnlinkedOutput: {
          Message: 'Successfully removed 5 bookmarks from user workspace'
        },
        bookMarklinkedOutput: {
          Message: 'Successfully added 10 bookmarks to user workspace'
        },
        MasterList: {
          BookMarkLinked: null,
          LatLongHierarchy: null,
          UserMannual: null,
          WBS_Description: null
        }
      };

      const errorResponse: BookmarksResponseData = {
        BookMarkUnlinkedOutput: {
          Message: 'Error: Unable to unlink bookmarks - Access denied'
        },
        bookMarklinkedOutput: {
          Message: 'Error: Unable to link bookmarks - Invalid job codes provided'
        },
        MasterList: {
          BookMarkLinked: null,
          LatLongHierarchy: null,
          UserMannual: null,
          WBS_Description: null
        }
      };

      expect(successResponse.BookMarkUnlinkedOutput.Message).toContain('Successfully');
      expect(successResponse.bookMarklinkedOutput.Message).toContain('Successfully');
      expect(errorResponse.BookMarkUnlinkedOutput.Message).toContain('Error:');
      expect(errorResponse.bookMarklinkedOutput.Message).toContain('Error:');
    });

    it('should handle empty messages', () => {
      const emptyResponse: BookmarksResponseData = {
        BookMarkUnlinkedOutput: { Message: '' },
        bookMarklinkedOutput: { Message: '' },
        MasterList: {
          BookMarkLinked: null,
          LatLongHierarchy: null,
          UserMannual: null,
          WBS_Description: null
        }
      };

      expect(typeof emptyResponse.BookMarkUnlinkedOutput.Message).toBe('string');
      expect(typeof emptyResponse.bookMarklinkedOutput.Message).toBe('string');
      expect(emptyResponse.BookMarkUnlinkedOutput.Message).toBe('');
      expect(emptyResponse.bookMarklinkedOutput.Message).toBe('');
    });

    it('should handle long messages with special characters', () => {
      const complexResponse: BookmarksResponseData = {
        BookMarkUnlinkedOutput: {
          Message: 'Bookmark Operation Complete: Successfully processed 125 bookmark entries with special characters (åÄöÖüÜ), unicode content (测试内容), and emojis (🔖📌). Operation completed at 2024-01-15T10:30:45.123Z'
        },
        bookMarklinkedOutput: {
          Message: 'Link Operation Status: Established connections for job codes including "CONSTRUCTION-2024", "INFRA_PROJECT_α", and "建筑工程_01". Total processing time: 1.25 seconds. ✅ All operations successful!'
        },
        MasterList: {
          BookMarkLinked: null,
          LatLongHierarchy: null,
          UserMannual: null,
          WBS_Description: null
        }
      };

      expect(complexResponse.BookMarkUnlinkedOutput.Message.length).toBeGreaterThan(100);
      expect(complexResponse.BookMarkUnlinkedOutput.Message).toContain('测试内容');
      expect(complexResponse.BookMarkUnlinkedOutput.Message).toContain('🔖📌');
      expect(complexResponse.bookMarklinkedOutput.Message).toContain('✅');
    });

    it('should handle different MasterList scenarios', () => {
      const responseWithData: BookmarksResponseData = {
        BookMarkUnlinkedOutput: { Message: 'Unlinked successfully' },
        bookMarklinkedOutput: { Message: 'Linked successfully' },
        MasterList: {
          BookMarkLinked: { someData: 'linked data' },
          LatLongHierarchy: { coordinates: [40.7128, -74.0060] },
          UserMannual: { version: '2.1', lastUpdated: '2024-01-15' },
          WBS_Description: { count: 150, status: 'active' }
        }
      };

      expect(responseWithData.MasterList.BookMarkLinked).not.toBeNull();
      expect(responseWithData.MasterList.LatLongHierarchy).not.toBeNull();
      expect(responseWithData.MasterList.UserMannual).not.toBeNull();
      expect(responseWithData.MasterList.WBS_Description).not.toBeNull();
    });
  });

  describe('Interface Integration and Complex Scenarios', () => {
    it('should handle complete bookmark workflow', () => {
      // Create complex job work lists
      const jobWorkLists: JobWorkListsData[] = [
        { jobcode: 'FOUNDATION_EXCAVATION_2024', wpcode: 'WP_SITE_PREPARATION' },
        { jobcode: 'STRUCTURAL_FRAMEWORK', wpcode: 'WP_STEEL_ERECTION' },
        { jobcode: 'MECHANICAL_SYSTEMS', wpcode: 'WP_HVAC_INSTALLATION' },
        { jobcode: 'ELECTRICAL_SYSTEMS', wpcode: 'WP_POWER_DISTRIBUTION' },
        { jobcode: 'FINISHING_WORK', wpcode: 'WP_INTERIOR_COMPLETION' }
      ];

      // Create comprehensive request
      const bookmarksRequest: BookmarksRequestData = {
        Uid: 'PROJECT_COORDINATOR_789',
        Type: 'ComprehensiveBookmarkManagement',
        JobWorkLists: jobWorkLists
      };

      // Create detailed response
      const bookmarksResponse: BookmarksResponseData = {
        BookMarkUnlinkedOutput: {
          Message: `Successfully unlinked ${jobWorkLists.length} bookmark entries for user ${bookmarksRequest.Uid}`
        },
        bookMarklinkedOutput: {
          Message: `Successfully linked ${jobWorkLists.length} new bookmark entries for project coordination`
        },
        MasterList: {
          BookMarkLinked: {
            totalLinked: jobWorkLists.length,
            lastOperation: new Date().toISOString(),
            userAccess: 'full'
          },
          LatLongHierarchy: {
            coordinatesCount: 25,
            boundingBox: {
              north: 40.8,
              south: 40.7,
              east: -73.9,
              west: -74.1
            }
          },
          UserMannual: {
            version: '3.2.1',
            sections: ['Bookmark Management', 'Project Coordination', 'Workflow Optimization'],
            lastUpdated: '2024-01-15T09:00:00Z'
          },
          WBS_Description: {
            totalWBSItems: 150,
            activeItems: 125,
            pendingApproval: 25,
            categories: ['Foundation', 'Structure', 'MEP', 'Finishing']
          }
        }
      };

      expect(bookmarksRequest.JobWorkLists.length).toBe(5);
      expect(bookmarksResponse.BookMarkUnlinkedOutput.Message).toContain('5 bookmark entries');
      expect(bookmarksResponse.bookMarklinkedOutput.Message).toContain('5 new bookmark entries');
      expect(bookmarksResponse.MasterList.BookMarkLinked).not.toBeNull();
    });

    it('should maintain data consistency across interfaces', () => {
      const consistentJobCode = 'CONSISTENCY_TEST_JOB_001';
      const consistentWPCode = 'CONSISTENCY_TEST_WP_001';
      const consistentUID = 'CONSISTENCY_USER_123';

      const jobWorkList: JobWorkListsData = {
        jobcode: consistentJobCode,
        wpcode: consistentWPCode
      };

      const bookmarksRequest: BookmarksRequestData = {
        Uid: consistentUID,
        Type: 'ConsistencyTest',
        JobWorkLists: [jobWorkList]
      };

      const bookmarksResponse: BookmarksResponseData = {
        BookMarkUnlinkedOutput: {
          Message: `Processed job: ${consistentJobCode}, WP: ${consistentWPCode} for user: ${consistentUID}`
        },
        bookMarklinkedOutput: {
          Message: `Link established for job: ${consistentJobCode}`
        },
        MasterList: {
          BookMarkLinked: null,
          LatLongHierarchy: null,
          UserMannual: null,
          WBS_Description: null
        }
      };

      expect(bookmarksRequest.JobWorkLists[0].jobcode).toBe(consistentJobCode);
      expect(bookmarksRequest.JobWorkLists[0].wpcode).toBe(consistentWPCode);
      expect(bookmarksRequest.Uid).toBe(consistentUID);
      expect(bookmarksResponse.BookMarkUnlinkedOutput.Message).toContain(consistentJobCode);
      expect(bookmarksResponse.BookMarkUnlinkedOutput.Message).toContain(consistentWPCode);
      expect(bookmarksResponse.BookMarkUnlinkedOutput.Message).toContain(consistentUID);
    });

    it('should handle error scenarios appropriately', () => {
      const errorRequest: BookmarksRequestData = {
        Uid: '',
        Type: 'InvalidOperation',
        JobWorkLists: [
          { jobcode: '', wpcode: '' }
        ]
      };

      const errorResponse: BookmarksResponseData = {
        BookMarkUnlinkedOutput: {
          Message: 'Error: Invalid user ID provided. Cannot process bookmark unlink operation.'
        },
        bookMarklinkedOutput: {
          Message: 'Error: Empty job codes detected. Bookmark link operation failed.'
        },
        MasterList: {
          BookMarkLinked: null,
          LatLongHierarchy: null,
          UserMannual: null,
          WBS_Description: null
        }
      };

      expect(errorRequest.Uid).toBe('');
      expect(errorRequest.JobWorkLists[0].jobcode).toBe('');
      expect(errorResponse.BookMarkUnlinkedOutput.Message).toContain('Error:');
      expect(errorResponse.bookMarklinkedOutput.Message).toContain('Error:');
    });

    it('should work with arrays of bookmark data', () => {
      const bookmarkRequestsArray: BookmarksRequestData[] = [
        {
          Uid: 'USER_BATCH_1',
          Type: 'BatchOperation_1',
          JobWorkLists: [
            { jobcode: 'BATCH_JOB_1A', wpcode: 'BATCH_WP_1A' },
            { jobcode: 'BATCH_JOB_1B', wpcode: 'BATCH_WP_1B' }
          ]
        },
        {
          Uid: 'USER_BATCH_2',
          Type: 'BatchOperation_2',
          JobWorkLists: [
            { jobcode: 'BATCH_JOB_2A', wpcode: 'BATCH_WP_2A' },
            { jobcode: 'BATCH_JOB_2B', wpcode: 'BATCH_WP_2B' }
          ]
        }
      ];

      expect(Array.isArray(bookmarkRequestsArray)).toBe(true);
      expect(bookmarkRequestsArray.length).toBe(2);
      expect(bookmarkRequestsArray[0].JobWorkLists.length).toBe(2);
      expect(bookmarkRequestsArray[1].JobWorkLists.length).toBe(2);
      expect(bookmarkRequestsArray[0].Uid).toBe('USER_BATCH_1');
      expect(bookmarkRequestsArray[1].Uid).toBe('USER_BATCH_2');
    });
  });
}); 