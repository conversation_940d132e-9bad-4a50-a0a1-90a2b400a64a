import { MapData } from '../../../model/DailyProgress/MapData';

describe('DailyProgress - MapData Model', () => {
  describe('MapData Interface', () => {
    it('should create a valid MapData object with all properties', () => {
      const mapData: MapData = {
        id: 'MAP_001',
        jobCode: 'JOB_MAP_001',
        wbsCode: 'WBS_MAP_001',
        latitude: 40.7128,
        longitude: -74.0060,
        hierarchyLevel: 'Level 3',
        isActive: 'Y',
        jobDesc: 'Construction Site Mapping',
        il: 'IL001',
        ilDesc: 'Infrastructure Layer Description',
        wp: 'WP001',
        wpDesc: 'Work Package Description',
        swp: 'SWP001',
        cwpDesc: 'Child Work Package Description',
        deliverableCode: 1001,
        deliverableCodeDesc: 'Primary Deliverable',
        fullPath: '/Project/Phase1/Activity1/Task1'
      };

      expect(mapData.id).toBe('MAP_001');
      expect(mapData.jobCode).toBe('JOB_MAP_001');
      expect(mapData.wbsCode).toBe('WBS_MAP_001');
      expect(mapData.latitude).toBe(40.7128);
      expect(mapData.longitude).toBe(-74.0060);
      expect(mapData.hierarchyLevel).toBe('Level 3');
      expect(mapData.isActive).toBe('Y');
      expect(mapData.jobDesc).toBe('Construction Site Mapping');
      expect(mapData.il).toBe('IL001');
      expect(mapData.ilDesc).toBe('Infrastructure Layer Description');
      expect(mapData.wp).toBe('WP001');
      expect(mapData.wpDesc).toBe('Work Package Description');
      expect(mapData.swp).toBe('SWP001');
      expect(mapData.cwpDesc).toBe('Child Work Package Description');
      expect(mapData.deliverableCode).toBe(1001);
      expect(mapData.deliverableCodeDesc).toBe('Primary Deliverable');
      expect(mapData.fullPath).toBe('/Project/Phase1/Activity1/Task1');
    });

    it('should create MapData without optional fullPath', () => {
      const mapData: MapData = {
        id: 'MAP_002',
        jobCode: 'JOB_MAP_002',
        wbsCode: 'WBS_MAP_002',
        latitude: 25.7617,
        longitude: -80.1918,
        hierarchyLevel: 'Level 1',
        isActive: 'N',
        jobDesc: 'Inactive Job Description',
        il: 'IL002',
        ilDesc: 'Infrastructure Layer 2',
        wp: 'WP002',
        wpDesc: 'Work Package 2',
        swp: 'SWP002',
        cwpDesc: 'Child Work Package 2',
        deliverableCode: 2002,
        deliverableCodeDesc: 'Secondary Deliverable'
      };

      expect(mapData.fullPath).toBeUndefined();
      expect(mapData.isActive).toBe('N');
      expect(mapData.latitude).toBe(25.7617);
      expect(mapData.longitude).toBe(-80.1918);
    });

    it('should validate all required properties exist', () => {
      const mapData: MapData = {
        id: 'TEST_MAP',
        jobCode: 'TEST_JOB',
        wbsCode: 'TEST_WBS',
        latitude: 0,
        longitude: 0,
        hierarchyLevel: 'Root',
        isActive: 'Y',
        jobDesc: 'Test Job',
        il: 'IL_TEST',
        ilDesc: 'Test IL Description',
        wp: 'WP_TEST',
        wpDesc: 'Test WP Description',
        swp: 'SWP_TEST',
        cwpDesc: 'Test CWP Description',
        deliverableCode: 1,
        deliverableCodeDesc: 'Test Deliverable'
      };

      expect(mapData).toHaveProperty('id');
      expect(mapData).toHaveProperty('jobCode');
      expect(mapData).toHaveProperty('wbsCode');
      expect(mapData).toHaveProperty('latitude');
      expect(mapData).toHaveProperty('longitude');
      expect(mapData).toHaveProperty('hierarchyLevel');
      expect(mapData).toHaveProperty('isActive');
      expect(mapData).toHaveProperty('jobDesc');
      expect(mapData).toHaveProperty('il');
      expect(mapData).toHaveProperty('ilDesc');
      expect(mapData).toHaveProperty('wp');
      expect(mapData).toHaveProperty('wpDesc');
      expect(mapData).toHaveProperty('swp');
      expect(mapData).toHaveProperty('cwpDesc');
      expect(mapData).toHaveProperty('deliverableCode');
      expect(mapData).toHaveProperty('deliverableCodeDesc');
    });

    it('should handle edge cases for geographic coordinates', () => {
      const mapDataEdges = [
        {
          id: 'EDGE_1',
          latitude: 90,      // North Pole
          longitude: 180,    // International Date Line
          description: 'North Pole Edge Case'
        },
        {
          id: 'EDGE_2',
          latitude: -90,     // South Pole
          longitude: -180,   // International Date Line (West)
          description: 'South Pole Edge Case'
        },
        {
          id: 'EDGE_3',
          latitude: 0,       // Equator
          longitude: 0,      // Prime Meridian
          description: 'Null Island'
        }
      ];

      mapDataEdges.forEach((edge, index) => {
        const mapData: MapData = {
          id: edge.id,
          jobCode: `JOB_EDGE_${index + 1}`,
          wbsCode: `WBS_EDGE_${index + 1}`,
          latitude: edge.latitude,
          longitude: edge.longitude,
          hierarchyLevel: `Edge Level ${index + 1}`,
          isActive: 'Y',
          jobDesc: edge.description,
          il: `IL_EDGE_${index + 1}`,
          ilDesc: `Edge IL Description ${index + 1}`,
          wp: `WP_EDGE_${index + 1}`,
          wpDesc: `Edge WP Description ${index + 1}`,
          swp: `SWP_EDGE_${index + 1}`,
          cwpDesc: `Edge CWP Description ${index + 1}`,
          deliverableCode: index + 1,
          deliverableCodeDesc: `Edge Deliverable ${index + 1}`
        };

        expect(mapData.latitude).toBe(edge.latitude);
        expect(mapData.longitude).toBe(edge.longitude);
        expect(mapData.latitude).toBeGreaterThanOrEqual(-90);
        expect(mapData.latitude).toBeLessThanOrEqual(90);
        expect(mapData.longitude).toBeGreaterThanOrEqual(-180);
        expect(mapData.longitude).toBeLessThanOrEqual(180);
      });
    });

    it('should handle different hierarchy levels', () => {
      const hierarchyLevels = ['Root', 'Level 1', 'Level 2', 'Level 3', 'Level 4', 'Leaf'];
      
      hierarchyLevels.forEach((level, index) => {
        const mapData: MapData = {
          id: `MAP_HIERARCHY_${index + 1}`,
          jobCode: `JOB_${index + 1}`,
          wbsCode: `WBS_${index + 1}`,
          latitude: index * 10,
          longitude: index * 15,
          hierarchyLevel: level,
          isActive: index % 2 === 0 ? 'Y' : 'N',
          jobDesc: `Job Description for ${level}`,
          il: `IL_${index + 1}`,
          ilDesc: `Infrastructure for ${level}`,
          wp: `WP_${index + 1}`,
          wpDesc: `Work Package for ${level}`,
          swp: `SWP_${index + 1}`,
          cwpDesc: `Child Work Package for ${level}`,
          deliverableCode: (index + 1) * 100,
          deliverableCodeDesc: `Deliverable for ${level}`
        };

        expect(mapData.hierarchyLevel).toBe(level);
        expect(['Y', 'N']).toContain(mapData.isActive);
        expect(mapData.deliverableCode).toBe((index + 1) * 100);
      });
    });

    it('should handle active and inactive states', () => {
      const activeMapData: MapData = {
        id: 'ACTIVE_MAP',
        jobCode: 'ACTIVE_JOB',
        wbsCode: 'ACTIVE_WBS',
        latitude: 30.0,
        longitude: -95.0,
        hierarchyLevel: 'Active Level',
        isActive: 'Y',
        jobDesc: 'Active Job Description',
        il: 'IL_ACTIVE',
        ilDesc: 'Active Infrastructure Layer',
        wp: 'WP_ACTIVE',
        wpDesc: 'Active Work Package',
        swp: 'SWP_ACTIVE',
        cwpDesc: 'Active Child Work Package',
        deliverableCode: 5001,
        deliverableCodeDesc: 'Active Deliverable'
      };

      const inactiveMapData: MapData = {
        id: 'INACTIVE_MAP',
        jobCode: 'INACTIVE_JOB',
        wbsCode: 'INACTIVE_WBS',
        latitude: 35.0,
        longitude: -78.0,
        hierarchyLevel: 'Inactive Level',
        isActive: 'N',
        jobDesc: 'Inactive Job Description',
        il: 'IL_INACTIVE',
        ilDesc: 'Inactive Infrastructure Layer',
        wp: 'WP_INACTIVE',
        wpDesc: 'Inactive Work Package',
        swp: 'SWP_INACTIVE',
        cwpDesc: 'Inactive Child Work Package',
        deliverableCode: 5002,
        deliverableCodeDesc: 'Inactive Deliverable'
      };

      expect(activeMapData.isActive).toBe('Y');
      expect(inactiveMapData.isActive).toBe('N');
      expect(activeMapData.jobDesc).toContain('Active');
      expect(inactiveMapData.jobDesc).toContain('Inactive');
    });

    it('should handle long strings and special characters', () => {
      const mapDataWithSpecialChars: MapData = {
        id: 'SPECIAL_CHARS_!@#$%^&*()',
        jobCode: 'JOB_WITH_SPECIAL_CHARS_123!@#',
        wbsCode: 'WBS-WITH-DASHES_AND_UNDERSCORES',
        latitude: 37.7749,
        longitude: -122.4194,
        hierarchyLevel: 'Complex Level with Spaces & Special Characters',
        isActive: 'Y',
        jobDesc: 'Job Description with Special Characters: åÄöÖüÜ ñÑ çÇ 中文 🌟',
        il: 'IL_WITH_UNICODE_中文',
        ilDesc: 'Infrastructure Layer Description with émojis 🏗️🔧',
        wp: 'WP_UNICODE_测试',
        wpDesc: 'Work Package with Accented Characters: café naïve résumé',
        swp: 'SWP_MIXED_123abc!@#',
        cwpDesc: 'Child Work Package with Very Long Description That Exceeds Normal Length Expectations and Contains Multiple Sentences. This tests the system capability to handle extended text content.',
        deliverableCode: 999999,
        deliverableCodeDesc: 'Deliverable with Maximum Length Description'
      };

      expect(mapDataWithSpecialChars.id).toContain('!@#$%^&*()');
      expect(mapDataWithSpecialChars.jobDesc).toContain('🌟');
      expect(mapDataWithSpecialChars.ilDesc).toContain('🏗️🔧');
      expect(mapDataWithSpecialChars.wpDesc).toContain('café');
      expect(mapDataWithSpecialChars.cwpDesc.length).toBeGreaterThan(100);
    });

    it('should handle numeric edge cases for deliverableCode', () => {
      const numericEdgeCases = [
        { code: 0, desc: 'Zero deliverable code' },
        { code: 1, desc: 'Minimum positive deliverable code' },
        { code: Number.MAX_SAFE_INTEGER, desc: 'Maximum safe integer' },
        { code: 2147483647, desc: 'Maximum 32-bit signed integer' },
        { code: -1, desc: 'Negative deliverable code' }
      ];

      numericEdgeCases.forEach((testCase, index) => {
        const mapData: MapData = {
          id: `NUMERIC_EDGE_${index + 1}`,
          jobCode: `JOB_NUMERIC_${index + 1}`,
          wbsCode: `WBS_NUMERIC_${index + 1}`,
          latitude: 0,
          longitude: 0,
          hierarchyLevel: 'Numeric Test Level',
          isActive: 'Y',
          jobDesc: `Job for numeric test ${index + 1}`,
          il: `IL_NUMERIC_${index + 1}`,
          ilDesc: `IL Description ${index + 1}`,
          wp: `WP_NUMERIC_${index + 1}`,
          wpDesc: `WP Description ${index + 1}`,
          swp: `SWP_NUMERIC_${index + 1}`,
          cwpDesc: `CWP Description ${index + 1}`,
          deliverableCode: testCase.code,
          deliverableCodeDesc: testCase.desc
        };

        expect(mapData.deliverableCode).toBe(testCase.code);
        expect(typeof mapData.deliverableCode).toBe('number');
        expect(mapData.deliverableCodeDesc).toBe(testCase.desc);
      });
    });

    it('should handle empty strings appropriately', () => {
      const mapDataWithEmptyStrings: MapData = {
        id: '',
        jobCode: '',
        wbsCode: '',
        latitude: 0,
        longitude: 0,
        hierarchyLevel: '',
        isActive: '',
        jobDesc: '',
        il: '',
        ilDesc: '',
        wp: '',
        wpDesc: '',
        swp: '',
        cwpDesc: '',
        deliverableCode: 0,
        deliverableCodeDesc: ''
      };

      // Verify all string properties are of type string even when empty
      expect(typeof mapDataWithEmptyStrings.id).toBe('string');
      expect(typeof mapDataWithEmptyStrings.jobCode).toBe('string');
      expect(typeof mapDataWithEmptyStrings.wbsCode).toBe('string');
      expect(typeof mapDataWithEmptyStrings.hierarchyLevel).toBe('string');
      expect(typeof mapDataWithEmptyStrings.isActive).toBe('string');
      expect(typeof mapDataWithEmptyStrings.jobDesc).toBe('string');
      expect(typeof mapDataWithEmptyStrings.il).toBe('string');
      expect(typeof mapDataWithEmptyStrings.ilDesc).toBe('string');
      expect(typeof mapDataWithEmptyStrings.wp).toBe('string');
      expect(typeof mapDataWithEmptyStrings.wpDesc).toBe('string');
      expect(typeof mapDataWithEmptyStrings.swp).toBe('string');
      expect(typeof mapDataWithEmptyStrings.cwpDesc).toBe('string');
      expect(typeof mapDataWithEmptyStrings.deliverableCodeDesc).toBe('string');
      expect(typeof mapDataWithEmptyStrings.latitude).toBe('number');
      expect(typeof mapDataWithEmptyStrings.longitude).toBe('number');
      expect(typeof mapDataWithEmptyStrings.deliverableCode).toBe('number');
    });

    it('should handle path scenarios with fullPath property', () => {
      const pathScenarios = [
        '/',
        '/root',
        '/project/phase1',
        '/project/phase1/activity1/task1',
        '/very/deep/nested/hierarchy/structure/with/many/levels',
        '/path/with spaces/and-dashes/and_underscores',
        '/path/with/numbers/123/and/special/chars/!@#'
      ];

      pathScenarios.forEach((path, index) => {
        const mapData: MapData = {
          id: `PATH_TEST_${index + 1}`,
          jobCode: `JOB_PATH_${index + 1}`,
          wbsCode: `WBS_PATH_${index + 1}`,
          latitude: index * 5,
          longitude: index * -5,
          hierarchyLevel: `Path Level ${index + 1}`,
          isActive: 'Y',
          jobDesc: `Job with path ${index + 1}`,
          il: `IL_PATH_${index + 1}`,
          ilDesc: `IL for path test ${index + 1}`,
          wp: `WP_PATH_${index + 1}`,
          wpDesc: `WP for path test ${index + 1}`,
          swp: `SWP_PATH_${index + 1}`,
          cwpDesc: `CWP for path test ${index + 1}`,
          deliverableCode: index + 1000,
          deliverableCodeDesc: `Deliverable for path ${index + 1}`,
          fullPath: path
        };

        expect(mapData.fullPath).toBe(path);
        expect(mapData.fullPath).toContain('/');
        if (path.length > 1) {
          expect(mapData.fullPath!.startsWith('/')).toBe(true);
        }
      });
    });
  });

  describe('Type Safety and Data Integrity', () => {
    it('should maintain data integrity across different scenarios', () => {
      const scenarios = [
        { prefix: 'SCENARIO_1', active: 'Y', level: 'Level 1' },
        { prefix: 'SCENARIO_2', active: 'N', level: 'Level 2' },
        { prefix: 'SCENARIO_3', active: 'Y', level: 'Level 3' }
      ];

      scenarios.forEach((scenario, index) => {
        const mapData: MapData = {
          id: `${scenario.prefix}_MAP`,
          jobCode: `${scenario.prefix}_JOB`,
          wbsCode: `${scenario.prefix}_WBS`,
          latitude: (index + 1) * 10.5,
          longitude: (index + 1) * -15.7,
          hierarchyLevel: scenario.level,
          isActive: scenario.active,
          jobDesc: `${scenario.prefix} Job Description`,
          il: `${scenario.prefix}_IL`,
          ilDesc: `${scenario.prefix} Infrastructure Layer`,
          wp: `${scenario.prefix}_WP`,
          wpDesc: `${scenario.prefix} Work Package`,
          swp: `${scenario.prefix}_SWP`,
          cwpDesc: `${scenario.prefix} Child Work Package`,
          deliverableCode: (index + 1) * 1000,
          deliverableCodeDesc: `${scenario.prefix} Deliverable`
        };

        expect(mapData.isActive).toBe(scenario.active);
        expect(mapData.hierarchyLevel).toBe(scenario.level);
        expect(mapData.id).toContain(scenario.prefix);
        expect(mapData.deliverableCode).toBe((index + 1) * 1000);
      });
    });

    it('should work with array of MapData objects', () => {
      const mapDataArray: MapData[] = [
        {
          id: 'ARRAY_1',
          jobCode: 'JOB_ARRAY_1',
          wbsCode: 'WBS_ARRAY_1',
          latitude: 10.0,
          longitude: 20.0,
          hierarchyLevel: 'Array Level 1',
          isActive: 'Y',
          jobDesc: 'First Array Item',
          il: 'IL_ARRAY_1',
          ilDesc: 'IL Array 1',
          wp: 'WP_ARRAY_1',
          wpDesc: 'WP Array 1',
          swp: 'SWP_ARRAY_1',
          cwpDesc: 'CWP Array 1',
          deliverableCode: 1001,
          deliverableCodeDesc: 'Array Deliverable 1'
        },
        {
          id: 'ARRAY_2',
          jobCode: 'JOB_ARRAY_2',
          wbsCode: 'WBS_ARRAY_2',
          latitude: 30.0,
          longitude: 40.0,
          hierarchyLevel: 'Array Level 2',
          isActive: 'N',
          jobDesc: 'Second Array Item',
          il: 'IL_ARRAY_2',
          ilDesc: 'IL Array 2',
          wp: 'WP_ARRAY_2',
          wpDesc: 'WP Array 2',
          swp: 'SWP_ARRAY_2',
          cwpDesc: 'CWP Array 2',
          deliverableCode: 1002,
          deliverableCodeDesc: 'Array Deliverable 2'
        }
      ];

      expect(Array.isArray(mapDataArray)).toBe(true);
      expect(mapDataArray.length).toBe(2);
      expect(mapDataArray[0].isActive).toBe('Y');
      expect(mapDataArray[1].isActive).toBe('N');
      expect(mapDataArray[0].latitude).toBe(10.0);
      expect(mapDataArray[1].latitude).toBe(30.0);
    });
  });
}); 