import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';

// Mock the Fonts component that uses PixelRatio
jest.mock('../../../components/Fonts', () => ({
  AppFonts: {
    Regular: 'Manrope-Regular',
    Medium: 'Manrope-Medium',
    SemiBold: 'Manrope-SemiBold',
    Bold: 'Manrope-Bold',
  },
  DeviceType: {
    isTablet: false,
    isSmallPhone: false,
    isLargePhone: true,
    isLandscape: false,
    pixelDensity: 2,
    screenWidth: 375,
    screenHeight: 812,
  },
}));

import WBSBottomPopup from '../../../screens/WBS/components/WBSBottomPopup';

const defaultProps = {
  job: { id: 1, name: 'Test Job' },
  confirmLabel: 'Confirm',
  cancelLabel: 'Cancel',
  onCancel: jest.fn(),
  onConfirm: jest.fn(),
  visible: true,
  onClose: jest.fn(),
};

describe('<WBSBottomPopup />', () => {
  it('should render correctly and match snapshot', () => {
    const { toJSON } = render(<WBSBottomPopup {...defaultProps} />);
    expect(toJSON()).toMatchSnapshot();
  });

  it('should display popup content', () => {
    const { getByTestId } = render(<WBSBottomPopup {...defaultProps} />);
    // Example: expect(getByTestId('popup-content')).toBeTruthy();
  });

  it('should handle close button press', () => {
    const { getByTestId } = render(<WBSBottomPopup />);
    // fireEvent.press(getByTestId('close-button'));
    // expect(onCloseMock).toHaveBeenCalled();
  });

  // Add more tests as needed to reach 90%+ coverage
}); 