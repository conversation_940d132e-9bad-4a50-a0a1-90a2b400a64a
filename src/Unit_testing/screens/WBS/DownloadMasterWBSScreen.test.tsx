import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { Alert } from 'react-native';
import DownloadMasterWBSScreen from '../../../screens/WBS/DownloadMasterWBSScreen';
import { renderWithProviders, createMockStore } from '../../../utils/testing/testHelpers';

// Mock dependencies
jest.mock('../../../utils/Scale/Scaling', () => ({
  width: 375,
  height: 812,
  scale: jest.fn((size) => size),
  verticalScale: jest.fn((size) => size),
  ms: jest.fn((size) => size),
}));

jest.mock('../../../utils/Network/NetworkConnection', () => ({
  isNetworkConnected: jest.fn(() => Promise.resolve(true)),
}));

jest.mock('../../../utils/DataStorage/Storage', () => ({
  getUserInfo: jest.fn(() => ({ id: 1, name: 'Test User' })),
  getCurrentJobId: jest.fn(() => 'JOB001'),
  saveDownloadProgress: jest.fn(),
  getDownloadProgress: jest.fn(() => ({ progress: 0, isComplete: false })),
}));

jest.mock('../../../services/ApiRequests', () => ({
  downloadWBSData: jest.fn(),
  getWBSJobList: jest.fn(),
}));

const mockNavigation = {
  navigate: jest.fn(),
  goBack: jest.fn(),
  setParams: jest.fn(),
  reset: jest.fn(),
};

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => mockNavigation,
  useFocusEffect: jest.fn(),
}));

// Mock React Native NetInfo
jest.mock('@react-native-netinfo/netinfo', () => ({
  fetch: jest.fn(() => Promise.resolve({ isConnected: true, type: 'wifi' })),
  addEventListener: jest.fn(() => jest.fn()),
}));

// Mock file system operations
jest.mock('react-native-fs', () => ({
  DocumentDirectoryPath: '/mock/documents',
  writeFile: jest.fn(() => Promise.resolve()),
  readFile: jest.fn(() => Promise.resolve('mock file content')),
  mkdir: jest.fn(() => Promise.resolve()),
  exists: jest.fn(() => Promise.resolve(true)),
}));

describe('<DownloadMasterWBSScreen />', () => {
  let mockStore: any;
  const mockApiRequests = require('../../../services/ApiRequests');
  const mockStorage = require('../../../utils/DataStorage/Storage');
  const mockNetworkConnection = require('../../../utils/Network/NetworkConnection');

  beforeEach(() => {
    jest.clearAllMocks();
    mockStore = createMockStore({
      auth: {
        user: { id: 1, name: 'Test User' },
        token: 'mock-token',
      },
      home: {
        selectedCurrentJobId: 'JOB001',
        jobsDownloaded: false,
      },
      wbs: {
        downloadProgress: 0,
        isDownloading: false,
        downloadError: null,
        availableJobs: [],
        selectedJobs: [],
      },
    });

    // Mock successful API responses
    mockApiRequests.getWBSJobList.mockResolvedValue([
      { id: 'JOB001', name: 'Project Alpha', description: 'Alpha project' },
      { id: 'JOB002', name: 'Project Beta', description: 'Beta project' },
    ]);

    mockApiRequests.downloadWBSData.mockImplementation((jobId, onProgress) => {
      // Simulate download progress
      setTimeout(() => onProgress(25), 100);
      setTimeout(() => onProgress(50), 200);
      setTimeout(() => onProgress(75), 300);
      setTimeout(() => onProgress(100), 400);
      return Promise.resolve({ success: true, data: 'mock-wbs-data' });
    });

    mockNetworkConnection.isNetworkConnected.mockResolvedValue(true);
    jest.spyOn(Alert, 'alert').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Rendering Tests', () => {
    it('should render correctly and match snapshot', () => {
      const { toJSON } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      expect(toJSON()).toMatchSnapshot();
    });

    it('should display header with correct title', () => {
      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      expect(screen.getByText(/download master wbs/i)).toBeTruthy();
    });

    it('should render job selection list', () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          availableJobs: [
            { id: 'JOB001', name: 'Project Alpha' },
            { id: 'JOB002', name: 'Project Beta' },
          ],
        },
      });

      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      expect(screen.getByText('Project Alpha')).toBeTruthy();
      expect(screen.getByText('Project Beta')).toBeTruthy();
    });

    it('should render download button when jobs are selected', () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          selectedJobs: ['JOB001'],
        },
      });

      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      expect(screen.getByTestId('download-button')).toBeTruthy();
    });

    it('should show progress bar when downloading', () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          isDownloading: true,
          downloadProgress: 50,
        },
      });

      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      expect(screen.getByTestId('progress-bar')).toBeTruthy();
      expect(screen.getByText('50%')).toBeTruthy();
    });
  });

  describe('Data Loading Tests', () => {
    it('should load available jobs on component mount', async () => {
      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      await waitFor(() => {
        expect(mockApiRequests.getWBSJobList).toHaveBeenCalled();
      });
    });

    it('should handle job list loading error', async () => {
      mockApiRequests.getWBSJobList.mockRejectedValue(new Error('API Error'));

      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load job list/i)).toBeTruthy();
      });
    });

    it('should check network connectivity before loading', async () => {
      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      await waitFor(() => {
        expect(mockNetworkConnection.isNetworkConnected).toHaveBeenCalled();
      });
    });
  });

  describe('Job Selection Tests', () => {
    it('should handle single job selection', () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          availableJobs: [
            { id: 'JOB001', name: 'Project Alpha' },
          ],
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('job-checkbox-JOB001'));
      
      const actions = mockStore.getActions();
      expect(actions.some((action: any) => 
        action.type.includes('SELECT_JOB') && action.payload === 'JOB001'
      )).toBeTruthy();
    });

    it('should handle multiple job selection', () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          availableJobs: [
            { id: 'JOB001', name: 'Project Alpha' },
            { id: 'JOB002', name: 'Project Beta' },
          ],
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('job-checkbox-JOB001'));
      fireEvent.press(getByTestId('job-checkbox-JOB002'));
      
      const actions = mockStore.getActions();
      expect(actions.filter((action: any) => action.type.includes('SELECT_JOB'))).toHaveLength(2);
    });

    it('should handle job deselection', () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          availableJobs: [{ id: 'JOB001', name: 'Project Alpha' }],
          selectedJobs: ['JOB001'],
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('job-checkbox-JOB001'));
      
      const actions = mockStore.getActions();
      expect(actions.some((action: any) => 
        action.type.includes('DESELECT_JOB') && action.payload === 'JOB001'
      )).toBeTruthy();
    });

    it('should handle select all functionality', () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          availableJobs: [
            { id: 'JOB001', name: 'Project Alpha' },
            { id: 'JOB002', name: 'Project Beta' },
          ],
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('select-all-button'));
      
      const actions = mockStore.getActions();
      expect(actions.some((action: any) => action.type.includes('SELECT_ALL_JOBS'))).toBeTruthy();
    });

    it('should handle clear all functionality', () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          selectedJobs: ['JOB001', 'JOB002'],
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('clear-all-button'));
      
      const actions = mockStore.getActions();
      expect(actions.some((action: any) => action.type.includes('CLEAR_ALL_JOBS'))).toBeTruthy();
    });
  });

  describe('Download Process Tests', () => {
    it('should start download when download button is pressed', async () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          selectedJobs: ['JOB001'],
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('download-button'));
      
      await waitFor(() => {
        expect(mockApiRequests.downloadWBSData).toHaveBeenCalledWith('JOB001', expect.any(Function));
      });
    });

    it('should track download progress', async () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          selectedJobs: ['JOB001'],
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('download-button'));
      
      await waitFor(() => {
        const actions = mockStore.getActions();
        expect(actions.some((action: any) => 
          action.type.includes('UPDATE_DOWNLOAD_PROGRESS')
        )).toBeTruthy();
      }, { timeout: 1000 });
    });

    it('should handle download completion', async () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          selectedJobs: ['JOB001'],
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('download-button'));
      
      await waitFor(() => {
        const actions = mockStore.getActions();
        expect(actions.some((action: any) => 
          action.type.includes('DOWNLOAD_COMPLETE')
        )).toBeTruthy();
      }, { timeout: 1000 });
    });

    it('should handle download cancellation', () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          isDownloading: true,
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('cancel-download-button'));
      
      const actions = mockStore.getActions();
      expect(actions.some((action: any) => action.type.includes('CANCEL_DOWNLOAD'))).toBeTruthy();
    });

    it('should pause and resume download', () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          isDownloading: true,
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      // Pause download
      fireEvent.press(getByTestId('pause-download-button'));
      let actions = mockStore.getActions();
      expect(actions.some((action: any) => action.type.includes('PAUSE_DOWNLOAD'))).toBeTruthy();

      // Resume download
      fireEvent.press(getByTestId('resume-download-button'));
      actions = mockStore.getActions();
      expect(actions.some((action: any) => action.type.includes('RESUME_DOWNLOAD'))).toBeTruthy();
    });
  });

  describe('Error Handling Tests', () => {
    it('should handle network connectivity errors', async () => {
      mockNetworkConnection.isNetworkConnected.mockResolvedValue(false);

      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      await waitFor(() => {
        expect(screen.getByText(/no internet connection/i)).toBeTruthy();
      });
    });

    it('should handle download API errors', async () => {
      mockApiRequests.downloadWBSData.mockRejectedValue(new Error('Download failed'));

      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          selectedJobs: ['JOB001'],
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('download-button'));
      
      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Download Error',
          'Failed to download WBS data. Please try again.'
        );
      });
    });

    it('should handle insufficient storage space', async () => {
      const mockFS = require('react-native-fs');
      mockFS.writeFile.mockRejectedValue(new Error('Insufficient storage'));

      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          selectedJobs: ['JOB001'],
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('download-button'));
      
      await waitFor(() => {
        expect(screen.getByText(/insufficient storage space/i)).toBeTruthy();
      });
    });

    it('should handle retry functionality', () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          downloadError: 'Download failed',
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('retry-button'));
      
      const actions = mockStore.getActions();
      expect(actions.some((action: any) => action.type.includes('RETRY_DOWNLOAD'))).toBeTruthy();
    });
  });

  describe('User Interaction Tests', () => {
    it('should handle back button press', () => {
      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('back-button'));
      expect(mockNavigation.goBack).toHaveBeenCalled();
    });

    it('should handle refresh job list', async () => {
      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('refresh-button'));
      
      await waitFor(() => {
        expect(mockApiRequests.getWBSJobList).toHaveBeenCalledTimes(2);
      });
    });

    it('should navigate to home on successful download', async () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          selectedJobs: ['JOB001'],
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('download-button'));
      
      await waitFor(() => {
        expect(mockNavigation.reset).toHaveBeenCalledWith({
          index: 0,
          routes: [{ name: 'Home' }],
        });
      }, { timeout: 1000 });
    });
  });

  describe('Performance Tests', () => {
    it('should handle large job lists efficiently', () => {
      const largeJobList = Array.from({ length: 1000 }, (_, i) => ({
        id: `JOB${i.toString().padStart(3, '0')}`,
        name: `Project ${i}`,
        description: `Description for project ${i}`,
      }));

      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          availableJobs: largeJobList,
        },
      });

      const startTime = performance.now();
      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(2000);
    });

    it('should optimize selection performance with virtualization', () => {
      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      expect(getByTestId('virtualized-job-list')).toBeTruthy();
    });

    it('should not cause memory leaks during long downloads', async () => {
      const { unmount } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      // Start download
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: { ...mockStore.getState().wbs, isDownloading: true },
      });

      expect(() => unmount()).not.toThrow();
    });
  });

  describe('Storage Management Tests', () => {
    it('should save download progress to storage', async () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          selectedJobs: ['JOB001'],
        },
      });

      const { getByTestId } = renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      fireEvent.press(getByTestId('download-button'));
      
      await waitFor(() => {
        expect(mockStorage.saveDownloadProgress).toHaveBeenCalled();
      });
    });

    it('should restore download progress from storage', () => {
      mockStorage.getDownloadProgress.mockReturnValue({
        progress: 75,
        isComplete: false,
        jobId: 'JOB001',
      });

      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      expect(screen.getByText('75%')).toBeTruthy();
    });

    it('should clear storage on successful completion', async () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          downloadProgress: 100,
        },
      });

      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      await waitFor(() => {
        const actions = mockStore.getActions();
        expect(actions.some((action: any) => action.type.includes('CLEAR_PROGRESS'))).toBeTruthy();
      });
    });
  });

  describe('Integration Tests', () => {
    it('should integrate with Redux store correctly', () => {
      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      const actions = mockStore.getActions();
      expect(actions).toBeDefined();
    });

    it('should integrate with navigation correctly', () => {
      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      // Component should not navigate until download is complete
      expect(mockNavigation.reset).not.toHaveBeenCalled();
    });
  });

  describe('Accessibility Tests', () => {
    it('should have proper accessibility labels', () => {
      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      expect(screen.getByLabelText(/back button/i)).toBeTruthy();
      expect(screen.getByLabelText(/download/i)).toBeTruthy();
      expect(screen.getByLabelText(/select all/i)).toBeTruthy();
    });

    it('should support screen readers', () => {
      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      expect(screen.getByRole('button', { name: /download/i })).toBeTruthy();
    });

    it('should announce download progress to screen readers', () => {
      mockStore = createMockStore({
        ...mockStore.getState(),
        wbs: {
          ...mockStore.getState().wbs,
          isDownloading: true,
          downloadProgress: 50,
        },
      });

      renderWithProviders(<DownloadMasterWBSScreen />, { store: mockStore });
      
      expect(screen.getByLabelText(/download progress 50%/i)).toBeTruthy();
    });
  });
}); 