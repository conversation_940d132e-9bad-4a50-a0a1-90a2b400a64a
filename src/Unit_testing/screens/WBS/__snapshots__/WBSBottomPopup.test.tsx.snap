// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<WBSBottomPopup /> should render correctly and match snapshot 1`] = `
<Modal
  animationType="fade"
  hardwareAccelerated={false}
  onRequestClose={[MockFunction]}
  transparent={true}
  visible={true}
>
  <View
    style={
      {
        "flex": 1,
      }
    }
  >
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={true}
      onBlur={[Function]}
      onClick={[Function]}
      onFocus={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "backgroundColor": "#00000080",
          "bottom": 0,
          "left": 0,
          "position": "absolute",
          "right": 0,
          "top": 0,
        }
      }
    />
    <View
      style={
        {
          "flex": 1,
          "justifyContent": "flex-end",
        }
      }
    >
      <Modal
        animationType="slide"
        hardwareAccelerated={false}
        transparent={true}
        visible={true}
      >
        <TouchableWithoutFeedback>
          <View
            style={
              {
                "backgroundColor": "rgba(0, 0, 0, 0.3)",
                "flex": 1,
                "justifyContent": "flex-end",
              }
            }
          >
            <View
              style={
                [
                  {
                    "backgroundColor": "#FFFFFF",
                    "justifyContent": "center",
                  },
                  undefined,
                ]
              }
            >
              <View
                style={
                  {
                    "alignItems": "center",
                  }
                }
              >
                <View
                  style={
                    {
                      "backgroundColor": "#373D4B",
                      "borderBottomEndRadius": 8,
                      "borderBottomStartRadius": 8,
                      "height": 7,
                      "width": 130,
                    }
                  }
                />
              </View>
              <div
                data-testid="svg-mock"
                style={
                  {
                    "marginBottom": 10,
                    "marginTop": 30,
                  }
                }
              />
              <View
                style={
                  {
                    "alignItems": "center",
                    "marginVertical": 10,
                  }
                }
              >
                <Text
                  style={
                    {
                      "color": "#000000",
                      "fontFamily": "MNSemiBold",
                      "fontSize": 18,
                      "marginHorizontal": 10,
                      "textAlign": "center",
                    }
                  }
                >
                  undefined [object Object]
                </Text>
                <Text
                  style={
                    {
                      "color": "#000000",
                      "fontFamily": "MNSemiBold",
                      "fontSize": 18,
                      "marginHorizontal": 10,
                      "textAlign": "center",
                    }
                  }
                />
              </View>
              <View
                style={
                  [
                    {
                      "alignItems": "center",
                      "backgroundColor": "#FFFFFF",
                      "borderTopColor": "#E1E1ED",
                      "borderTopWidth": 1,
                      "elevation": 10,
                      "flexDirection": "row",
                      "paddingBottom": 10,
                      "paddingHorizontal": 15,
                      "paddingTop": 10,
                      "shadowColor": "#000",
                      "shadowOffset": {
                        "height": 2,
                        "width": 0,
                      },
                      "shadowOpacity": 0.1,
                      "shadowRadius": 4,
                    },
                    {
                      "paddingBottom": undefined,
                    },
                  ]
                }
              >
                <View
                  accessibilityState={
                    {
                      "busy": undefined,
                      "checked": undefined,
                      "disabled": undefined,
                      "expanded": undefined,
                      "selected": undefined,
                    }
                  }
                  accessibilityValue={
                    {
                      "max": undefined,
                      "min": undefined,
                      "now": undefined,
                      "text": undefined,
                    }
                  }
                  accessible={true}
                  collapsable={false}
                  focusable={true}
                  onBlur={[Function]}
                  onClick={[Function]}
                  onFocus={[Function]}
                  onResponderGrant={[Function]}
                  onResponderMove={[Function]}
                  onResponderRelease={[Function]}
                  onResponderTerminate={[Function]}
                  onResponderTerminationRequest={[Function]}
                  onStartShouldSetResponder={[Function]}
                  style={
                    [
                      {
                        "alignItems": "center",
                        "borderColor": "#0775CE",
                        "borderRadius": 8,
                        "borderWidth": 1,
                        "flex": 1,
                        "height": 44,
                        "justifyContent": "center",
                        "marginRight": 20,
                      },
                      undefined,
                    ]
                  }
                >
                  <Text
                    style={
                      [
                        {
                          "color": "#0775CE",
                          "fontFamily": "Manrope-Bold",
                          "fontSize": 16,
                        },
                      ]
                    }
                  >
                    Cancel
                  </Text>
                </View>
                <View
                  accessibilityState={
                    {
                      "busy": undefined,
                      "checked": undefined,
                      "disabled": undefined,
                      "expanded": undefined,
                      "selected": undefined,
                    }
                  }
                  accessibilityValue={
                    {
                      "max": undefined,
                      "min": undefined,
                      "now": undefined,
                      "text": undefined,
                    }
                  }
                  accessible={true}
                  collapsable={false}
                  focusable={true}
                  onBlur={[Function]}
                  onClick={[Function]}
                  onFocus={[Function]}
                  onResponderGrant={[Function]}
                  onResponderMove={[Function]}
                  onResponderRelease={[Function]}
                  onResponderTerminate={[Function]}
                  onResponderTerminationRequest={[Function]}
                  onStartShouldSetResponder={[Function]}
                  style={
                    [
                      {
                        "alignItems": "center",
                        "borderColor": "#0775CE",
                        "borderRadius": undefined,
                        "borderWidth": undefined,
                        "flex": 1,
                        "height": undefined,
                        "justifyContent": "center",
                        "width": undefined,
                      },
                      undefined,
                    ]
                  }
                >
                  <MockSvg
                    height="100%"
                    width="100%"
                  >
                    <MockDefs>
                      <MockLinearGradient
                        id="grad"
                        x1="0"
                        x2="0"
                        y1="0"
                        y2="1"
                      >
                        <MockStop
                          offset="0"
                          stopColor="#373D4B"
                          stopOpacity="1"
                        />
                        <MockStop
                          offset="1"
                          stopColor="#0775CE"
                          stopOpacity="1"
                        />
                      </MockLinearGradient>
                    </MockDefs>
                    <MockRect
                      fill="url(#grad)"
                      height="100%"
                      rx={8}
                      width="100%"
                      x="0"
                      y="0"
                    />
                  </MockSvg>
                  <Text
                    style={
                      {
                        "color": "#FFFFFF",
                        "fontFamily": "Manrope-Bold",
                        "fontSize": 16,
                      }
                    }
                  >
                    Confirm
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  </View>
</Modal>
`;
