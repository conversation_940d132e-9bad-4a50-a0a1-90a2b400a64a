// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<TableList /> should render correctly and match snapshot 1`] = `
<View>
  <FlatList
    ListEmptyComponent={[Function]}
    ListHeaderComponent={[Function]}
    contentContainerStyle={
      {
        "flexGrow": 1,
        "minHeight": "100%",
        "paddingBottom": 10,
      }
    }
    initialNumToRender={20}
    keyExtractor={[Function]}
    maxToRenderPerBatch={50}
    renderItem={[Function]}
    windowSize={10}
  />
</View>
`;
