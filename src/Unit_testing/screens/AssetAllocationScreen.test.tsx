import React from 'react';
import { render, fireEvent, screen, waitFor } from '@testing-library/react-native';
import { useTranslation } from 'react-i18next';
import AssetAllocationScreen from '../../../screens/AssetAllocationScreen';
import { renderWithProviders, setupTest, teardownTest } from '../../../utils/testing/testHelpers';

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(),
}));

// Mock the components used in AssetAllocationScreen
jest.mock('../../components/TopTabNavigator', () => ({
  TopTabNavigator: ({ tabs, activeTab, onTabChange }: any) => (
    <div data-testid="top-tab-navigator">
      {tabs.map((tab: any) => (
        <button
          key={tab.key}
          data-testid={`tab-${tab.key}`}
          onClick={() => onTabChange(tab.key)}
          className={activeTab === tab.key ? 'active' : ''}
        >
          {tab.label}
        </button>
      ))}
    </div>
  ),
}));

jest.mock('../../components/SearchComponent', () => ({
  SearchComponent: ({ onChange, value, customStyle }: any) => (
    <input
      data-testid="search-component"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      style={customStyle}
      placeholder="Search..."
    />
  ),
}));

jest.mock('../../components/CardView', () => ({
  CardView: ({ children, onPress, containerStyle }: any) => (
    <div data-testid="card-view" onClick={onPress} style={containerStyle}>
      {children}
    </div>
  ),
}));

jest.mock('../../screens/WBS/components/WBSBottomPopup', () => ({
  __esModule: true,
  default: ({ job, confirmLabel, cancelLabel, onConfirm, onCancel, onClose, isDeletePopup }: any) => (
    <div data-testid="wbs-bottom-popup">
      <span data-testid="popup-job">{job}</span>
      <button data-testid="popup-confirm" onClick={onConfirm}>
        {confirmLabel}
      </button>
      <button data-testid="popup-cancel" onClick={onCancel}>
        {cancelLabel}
      </button>
      <button data-testid="popup-close" onClick={onClose}>
        Close
      </button>
      <span data-testid="popup-is-delete">{isDeletePopup ? 'true' : 'false'}</span>
    </div>
  ),
}));

jest.mock('../../components/LoadingOverlay', () => ({
  LoadingOverlay: ({ visible, message }: any) => (
    visible ? (
      <div data-testid="loading-overlay">
        <span>{message}</span>
      </div>
    ) : null
  ),
}));

jest.mock('../../components/AppHeader', () => ({
  __esModule: true,
  default: ({ title }: any) => (
    <div data-testid="app-header">
      <span>{title}</span>
    </div>
  ),
}));

jest.mock('../../assets/SVG', () => ({
  SVG: {
    ArrowRight: () => <div data-testid="arrow-right">→</div>,
  },
}));

// Mock the utility functions
jest.mock('../../utils/ms', () => ({
  ms: jest.fn((value) => value * 2),
}));

jest.mock('../../styles/AppFonts', () => ({
  AppFonts: {
    SemiBold: 'SemiBold',
    Bold: 'Bold',
  },
}));

jest.mock('../../styles/Colors', () => ({
  Colors: {
    white: '#FFFFFF',
    black: '#000000',
  },
}));

describe('AssetAllocationScreen', () => {
  let mockT: jest.Mock;

  beforeEach(() => {
    setupTest();
    mockT = jest.fn((key) => {
      const translations: { [key: string]: string } = {
        'commonStrings.assetAllocation': 'Asset Allocation',
        'commonStrings.requestApproval': 'Request Approval',
        'commonStrings.assetStatus': 'Asset Status',
        'commonStrings.pleaseWait': 'Please Wait',
        'commonStrings.yes': 'Yes',
        'commonStrings.no': 'No',
        'commonStrings.setCurrentJob': 'Set Current Job',
        'commonStrings.delete': 'Delete',
      };
      return translations[key] || key;
    });
    (useTranslation as jest.Mock).mockReturnValue({ t: mockT });
  });

  afterEach(() => {
    teardownTest();
    jest.clearAllMocks();
  });

  // ============================================================================
  // RENDER TESTS
  // ============================================================================

  describe('Render Tests', () => {
    test('should render with basic structure', () => {
      renderWithProviders(<AssetAllocationScreen />);

      expect(screen.getByTestId('app-header')).toBeTruthy();
      expect(screen.getByTestId('top-tab-navigator')).toBeTruthy();
      expect(screen.getByTestId('search-component')).toBeTruthy();
    });

    test('should render with correct header title', () => {
      renderWithProviders(<AssetAllocationScreen />);

      expect(screen.getByText('Asset Allocation')).toBeTruthy();
      expect(mockT).toHaveBeenCalledWith('commonStrings.assetAllocation');
    });

    test('should render tabs with correct labels', () => {
      renderWithProviders(<AssetAllocationScreen />);

      expect(screen.getByTestId('tab-all')).toBeTruthy();
      expect(screen.getByTestId('tab-downloaded')).toBeTruthy();
      expect(screen.getByText('Request Approval')).toBeTruthy();
      expect(screen.getByText('Asset Status')).toBeTruthy();
    });

    test('should render search component with correct props', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const searchComponent = screen.getByTestId('search-component');
      expect(searchComponent).toBeTruthy();
      expect(searchComponent.props.value).toBe('');
    });

    test('should render "all" tab content by default', () => {
      renderWithProviders(<AssetAllocationScreen />);

      expect(screen.getByText('Request Approval - Asset Allocation')).toBeTruthy();
      expect(screen.queryByText('Asset Status - Current Allocations')).toBeFalsy();
    });

    test('should render "downloaded" tab content when tab is changed', () => {
      renderWithProviders(<AssetAllocationScreen />);

      // Change to downloaded tab
      fireEvent.press(screen.getByTestId('tab-downloaded'));

      expect(screen.getByText('Asset Status - Current Allocations')).toBeTruthy();
      expect(screen.getByText('Direct Allocation - Equipment')).toBeTruthy();
    });

    test('should render loading overlay when any loading state is true', () => {
      renderWithProviders(<AssetAllocationScreen />);

      // Initially no loading overlay should be visible
      expect(screen.queryByTestId('loading-overlay')).toBeFalsy();
    });

    test('should render WBS bottom popup when showActionPopup is true', () => {
      renderWithProviders(<AssetAllocationScreen />);

      // Initially popup should not be visible
      expect(screen.queryByTestId('wbs-bottom-popup')).toBeFalsy();
    });
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  describe('Interaction Tests', () => {
    test('should handle search input changes', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const searchInput = screen.getByTestId('search-component');
      fireEvent.change(searchInput, { target: { value: 'test search' } });

      expect(searchInput.props.value).toBe('test search');
    });

    test('should handle tab changes', () => {
      renderWithProviders(<AssetAllocationScreen />);

      // Initially on 'all' tab
      expect(screen.getByText('Request Approval - Asset Allocation')).toBeTruthy();

      // Change to 'downloaded' tab
      fireEvent.press(screen.getByTestId('tab-downloaded'));

      expect(screen.getByText('Asset Status - Current Allocations')).toBeTruthy();
      expect(screen.getByText('Direct Allocation - Equipment')).toBeTruthy();
    });

    test('should handle card press', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      renderWithProviders(<AssetAllocationScreen />);

      const card = screen.getByTestId('card-view');
      fireEvent.press(card);

      expect(consoleSpy).toHaveBeenCalledWith('Asset card pressed');
      
      consoleSpy.mockRestore();
    });

    test('should handle multiple tab switches', () => {
      renderWithProviders(<AssetAllocationScreen />);

      // Switch to downloaded tab
      fireEvent.press(screen.getByTestId('tab-downloaded'));
      expect(screen.getByText('Asset Status - Current Allocations')).toBeTruthy();

      // Switch back to all tab
      fireEvent.press(screen.getByTestId('tab-all'));
      expect(screen.getByText('Request Approval - Asset Allocation')).toBeTruthy();

      // Switch to downloaded tab again
      fireEvent.press(screen.getByTestId('tab-downloaded'));
      expect(screen.getByText('Asset Status - Current Allocations')).toBeTruthy();
    });

    test('should handle rapid search input changes', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const searchInput = screen.getByTestId('search-component');
      
      fireEvent.change(searchInput, { target: { value: 'a' } });
      fireEvent.change(searchInput, { target: { value: 'ab' } });
      fireEvent.change(searchInput, { target: { value: 'abc' } });
      fireEvent.change(searchInput, { target: { value: 'abcd' } });

      expect(searchInput.props.value).toBe('abcd');
    });
  });

  // ============================================================================
  // STATE CHANGE TESTS
  // ============================================================================

  describe('State Change Tests', () => {
    test('should update search state when input changes', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const searchInput = screen.getByTestId('search-component');
      
      fireEvent.change(searchInput, { target: { value: 'initial' } });
      expect(searchInput.props.value).toBe('initial');

      fireEvent.change(searchInput, { target: { value: 'updated' } });
      expect(searchInput.props.value).toBe('updated');
    });

    test('should update tab state when tab is changed', () => {
      renderWithProviders(<AssetAllocationScreen />);

      // Initially on 'all' tab
      expect(screen.getByText('Request Approval - Asset Allocation')).toBeTruthy();

      // Change to 'downloaded' tab
      fireEvent.press(screen.getByTestId('tab-downloaded'));
      expect(screen.getByText('Asset Status - Current Allocations')).toBeTruthy();
    });

    test('should maintain search state across tab changes', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const searchInput = screen.getByTestId('search-component');
      fireEvent.change(searchInput, { target: { value: 'test search' } });

      // Change tab
      fireEvent.press(screen.getByTestId('tab-downloaded'));

      // Search value should be maintained
      expect(searchInput.props.value).toBe('test search');
    });
  });

  // ============================================================================
  // FUNCTIONAL REQUIREMENTS TESTS
  // ============================================================================

  describe('Functional Requirements Tests', () => {
    test('should display correct content for "all" tab', () => {
      renderWithProviders(<AssetAllocationScreen />);

      expect(screen.getByText('Request Approval - Asset Allocation')).toBeTruthy();
      expect(screen.getByTestId('arrow-right')).toBeTruthy();
    });

    test('should display correct content for "downloaded" tab', () => {
      renderWithProviders(<AssetAllocationScreen />);

      fireEvent.press(screen.getByTestId('tab-downloaded'));

      expect(screen.getByText('Asset Status - Current Allocations')).toBeTruthy();
      expect(screen.getByText('Direct Allocation - Equipment')).toBeTruthy();
      expect(screen.getAllByTestId('arrow-right')).toHaveLength(2);
    });

    test('should handle search functionality', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const searchInput = screen.getByTestId('search-component');
      const searchTerm = 'asset allocation search';
      
      fireEvent.change(searchInput, { target: { value: searchTerm } });

      expect(searchInput.props.value).toBe(searchTerm);
    });

    test('should handle card navigation', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      renderWithProviders(<AssetAllocationScreen />);

      const cards = screen.getAllByTestId('card-view');
      
      // Press first card
      fireEvent.press(cards[0]);
      expect(consoleSpy).toHaveBeenCalledWith('Asset card pressed');

      // Press second card (if on downloaded tab)
      fireEvent.press(screen.getByTestId('tab-downloaded'));
      const downloadedCards = screen.getAllByTestId('card-view');
      fireEvent.press(downloadedCards[0]);
      expect(consoleSpy).toHaveBeenCalledWith('Asset card pressed');
      
      consoleSpy.mockRestore();
    });
  });

  // ============================================================================
  // EDGE CASES
  // ============================================================================

  describe('Edge Cases', () => {
    test('should handle empty search input', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const searchInput = screen.getByTestId('search-component');
      
      fireEvent.change(searchInput, { target: { value: '' } });
      expect(searchInput.props.value).toBe('');
    });

    test('should handle very long search input', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const searchInput = screen.getByTestId('search-component');
      const longSearchTerm = 'a'.repeat(1000);
      
      fireEvent.change(searchInput, { target: { value: longSearchTerm } });
      expect(searchInput.props.value).toBe(longSearchTerm);
    });

    test('should handle special characters in search', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const searchInput = screen.getByTestId('search-component');
      const specialSearch = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      
      fireEvent.change(searchInput, { target: { value: specialSearch } });
      expect(searchInput.props.value).toBe(specialSearch);
    });

    test('should handle rapid tab switching', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const allTab = screen.getByTestId('tab-all');
      const downloadedTab = screen.getByTestId('tab-downloaded');

      // Rapid tab switching
      fireEvent.press(downloadedTab);
      fireEvent.press(allTab);
      fireEvent.press(downloadedTab);
      fireEvent.press(allTab);
      fireEvent.press(downloadedTab);

      // Should end up on downloaded tab
      expect(screen.getByText('Asset Status - Current Allocations')).toBeTruthy();
    });

    test('should handle rapid card presses', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      renderWithProviders(<AssetAllocationScreen />);

      const card = screen.getByTestId('card-view');
      
      // Rapid card presses
      fireEvent.press(card);
      fireEvent.press(card);
      fireEvent.press(card);
      fireEvent.press(card);
      fireEvent.press(card);

      expect(consoleSpy).toHaveBeenCalledTimes(5);
      
      consoleSpy.mockRestore();
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling Tests', () => {
    test('should handle translation function errors gracefully', () => {
      const errorT = jest.fn().mockImplementation(() => {
        throw new Error('Translation error');
      });
      (useTranslation as jest.Mock).mockReturnValue({ t: errorT });

      // Should not crash the component
      expect(() => {
        renderWithProviders(<AssetAllocationScreen />);
      }).not.toThrow();
    });

    test('should handle search input errors gracefully', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const searchInput = screen.getByTestId('search-component');
      
      // Should handle invalid input gracefully
      expect(() => {
        fireEvent.change(searchInput, { target: { value: null } });
      }).not.toThrow();
    });

    test('should handle tab change errors gracefully', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const downloadedTab = screen.getByTestId('tab-downloaded');
      
      // Should handle tab changes gracefully even if there are errors
      expect(() => {
        fireEvent.press(downloadedTab);
      }).not.toThrow();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    test('should have accessible search input', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const searchInput = screen.getByTestId('search-component');
      expect(searchInput).toBeTruthy();
      expect(searchInput.props.placeholder).toBe('Search...');
    });

    test('should have accessible tabs', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const allTab = screen.getByTestId('tab-all');
      const downloadedTab = screen.getByTestId('tab-downloaded');

      expect(allTab).toBeTruthy();
      expect(downloadedTab).toBeTruthy();
    });

    test('should have accessible cards', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const cards = screen.getAllByTestId('card-view');
      expect(cards.length).toBeGreaterThan(0);
    });

    test('should have accessible header', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const header = screen.getByTestId('app-header');
      expect(header).toBeTruthy();
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    test('should render efficiently with minimal re-renders', () => {
      const { rerender } = renderWithProviders(<AssetAllocationScreen />);

      // Re-render with same props should not cause issues
      rerender(<AssetAllocationScreen />);
      rerender(<AssetAllocationScreen />);
      rerender(<AssetAllocationScreen />);

      expect(screen.getByTestId('app-header')).toBeTruthy();
    });

    test('should handle rapid state changes efficiently', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const searchInput = screen.getByTestId('search-component');
      const allTab = screen.getByTestId('tab-all');
      const downloadedTab = screen.getByTestId('tab-downloaded');

      // Rapid state changes
      fireEvent.change(searchInput, { target: { value: 'a' } });
      fireEvent.press(downloadedTab);
      fireEvent.change(searchInput, { target: { value: 'ab' } });
      fireEvent.press(allTab);
      fireEvent.change(searchInput, { target: { value: 'abc' } });

      expect(searchInput.props.value).toBe('abc');
    });

    test('should handle large search inputs efficiently', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const searchInput = screen.getByTestId('search-component');
      const largeSearchTerm = 'a'.repeat(10000);
      
      fireEvent.change(searchInput, { target: { value: largeSearchTerm } });
      expect(searchInput.props.value).toBe(largeSearchTerm);
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should work with Redux store', () => {
      renderWithProviders(<AssetAllocationScreen />);

      expect(screen.getByTestId('app-header')).toBeTruthy();
      expect(screen.getByTestId('top-tab-navigator')).toBeTruthy();
    });

    test('should work with navigation container', () => {
      renderWithProviders(<AssetAllocationScreen />);

      expect(screen.getByTestId('app-header')).toBeTruthy();
    });

    test('should integrate all components correctly', () => {
      renderWithProviders(<AssetAllocationScreen />);

      // All main components should be present
      expect(screen.getByTestId('app-header')).toBeTruthy();
      expect(screen.getByTestId('top-tab-navigator')).toBeTruthy();
      expect(screen.getByTestId('search-component')).toBeTruthy();
      expect(screen.getByTestId('card-view')).toBeTruthy();
    });

    test('should handle complete user workflow', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      renderWithProviders(<AssetAllocationScreen />);

      // 1. Search for something
      const searchInput = screen.getByTestId('search-component');
      fireEvent.change(searchInput, { target: { value: 'asset search' } });

      // 2. Switch to downloaded tab
      fireEvent.press(screen.getByTestId('tab-downloaded'));

      // 3. Press a card
      const card = screen.getByTestId('card-view');
      fireEvent.press(card);

      // 4. Switch back to all tab
      fireEvent.press(screen.getByTestId('tab-all'));

      // 5. Press another card
      fireEvent.press(card);

      // Verify all interactions worked
      expect(searchInput.props.value).toBe('asset search');
      expect(consoleSpy).toHaveBeenCalledWith('Asset card pressed');
      
      consoleSpy.mockRestore();
    });
  });

  // ============================================================================
  // CROSS-PLATFORM COMPATIBILITY TESTS
  // ============================================================================

  describe('Cross-Platform Compatibility Tests', () => {
    test('should render consistently across platforms', () => {
      renderWithProviders(<AssetAllocationScreen />);

      // Should render all components regardless of platform
      expect(screen.getByTestId('app-header')).toBeTruthy();
      expect(screen.getByTestId('top-tab-navigator')).toBeTruthy();
      expect(screen.getByTestId('search-component')).toBeTruthy();
    });

    test('should handle platform-specific interactions', () => {
      renderWithProviders(<AssetAllocationScreen />);

      const searchInput = screen.getByTestId('search-component');
      const card = screen.getByTestId('card-view');

      // Should handle press events consistently
      fireEvent.press(card);
      fireEvent.change(searchInput, { target: { value: 'test' } });

      expect(searchInput.props.value).toBe('test');
    });
  });

  // ============================================================================
  // SNAPSHOT TESTS
  // ============================================================================

  describe('Snapshot Tests', () => {
    test('should match snapshot for initial render', () => {
      const { toJSON } = renderWithProviders(<AssetAllocationScreen />);
      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot for "all" tab', () => {
      const { toJSON } = renderWithProviders(<AssetAllocationScreen />);
      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot for "downloaded" tab', () => {
      const { toJSON } = renderWithProviders(<AssetAllocationScreen />);
      
      // Switch to downloaded tab
      fireEvent.press(screen.getByTestId('tab-downloaded'));
      
      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot with search input', () => {
      const { toJSON } = renderWithProviders(<AssetAllocationScreen />);
      
      // Add search input
      const searchInput = screen.getByTestId('search-component');
      fireEvent.change(searchInput, { target: { value: 'test search' } });
      
      expect(toJSON()).toMatchSnapshot();
    });
  });
}); 