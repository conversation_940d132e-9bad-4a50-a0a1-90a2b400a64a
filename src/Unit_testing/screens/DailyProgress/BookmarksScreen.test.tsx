import React from 'react';
import { render, waitFor, screen, fireEvent } from '@testing-library/react-native';
import { renderWithProviders, setupTest, teardownTest, mockUserData, mockJobData } from '../../../utils/testing/testHelpers';
import BooksmarksView from '../../../screens/DailyProgress/BookmarksScreen';
import { useSelector } from 'react-redux';

// Mock dependencies
jest.mock('react-native/Libraries/Utilities/Dimensions', () => ({
  get: jest.fn(() => ({ width: 375, height: 812 })),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
}));

jest.mock('react-native/Libraries/Utilities/Platform', () => ({
  OS: 'ios',
  select: (obj: { ios: any }) => obj.ios,
  Version: 25,
}));

jest.mock('../../../database/index', () => ({
  database: {
    get: jest.fn(() => ({
      query: jest.fn(() => ({
        fetch: jest.fn(),
      })),
    })),
  },
}));

jest.mock('../../../redux/Root/RootReducer', () => ({
  RootState: {},
}));

jest.mock('../../../database/helper/DatabaseHelper', () => ({
  loadLatLongHierarchyData: jest.fn(),
}));

const mockFilterDataByType = jest.fn();
const mockGetFullPathDescriptionForBookmarks = jest.fn();

jest.mock('../../../screens/DailyProgress/Helper/DataFilter', () => ({
  filterDataByType: mockFilterDataByType,
  getFullPathDescriptionForBookmarks: mockGetFullPathDescriptionForBookmarks,
}));

jest.mock('../../../components/CustomAlert', () => ({
  customAlertWithOK: jest.fn(),
}));

// Mock SVG components
jest.mock('../../../assets/svg/direction_arrow.svg', () => 'DirectionArrow');
jest.mock('../../../assets/svg/arrow_down.svg', () => 'ArrowDown');
jest.mock('../../../assets/svg/add_circle_blue.svg', () => 'AddPath');
jest.mock('../../../assets/svg/map_denoter_blue.svg', () => 'MapDenoter');

// Mock other components
jest.mock('../../../components/AppHeader', () => {
  const React = require('react');
  return function MockAppHeader({ children, rightContent, ...props }: any) {
    return React.createElement('View', { testID: 'app-header' }, [
      children,
      rightContent
    ]);
  };
});

jest.mock('../../../components/SearchComponent', () => {
  const React = require('react');
  return function MockSearchComponent({ value, onChange, ...props }: any) {
    return React.createElement('TextInput', { 
      testID: 'search-component',
      value: value,
      onChangeText: onChange,
      ...props
    });
  };
});

jest.mock('../../../screens/DailyProgress/components/BottomPopupDeliverables', () => {
  const React = require('react');
  return {
    __esModule: true,
    default: function MockBottomPopupDeliverables({ visible, onClose, onSelectType, ...props }: any) {
      return visible ? React.createElement('View', { testID: 'filter-modal' }, 'Filter Modal') : null;
    }
  };
});

const mockNavigation = {
  navigate: jest.fn(),
  dispatch: jest.fn(),
};

// Mock i18next
jest.mock('i18next', () => ({
  t: (key: string) => key,
}));

// Mock navigation hooks  
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => mockNavigation,
  StackActions: {
    replace: jest.fn((routeName) => ({
      type: 'REPLACE',
      payload: { name: routeName },
    })),
  },
  useFocusEffect: jest.fn(),
  useRoute: () => ({ params: {} }),
  NavigationContainer: ({ children }: any) => children,
}));

// Mock database
jest.mock('../../../database', () => ({
  database: {
    get: jest.fn(() => ({
      query: jest.fn(() => ({
        fetch: jest.fn().mockResolvedValue([]),
      })),
    })),
  },
}));

// Mock database helper
jest.mock('../../../database/helper/DatabaseHelper', () => ({
  loadLatLongHierarchyData: jest.fn().mockResolvedValue([]),
}));

// Mock react-redux
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
}));

describe('BooksmarksView', () => {
  const mockUser = mockUserData();
  const mockJob = mockJobData();

  beforeEach(() => {
    setupTest();
    jest.clearAllMocks();
    
    // Setup default mock state for useSelector
    (useSelector as jest.MockedFunction<typeof useSelector>).mockImplementation((selector) => {
      const mockState = {
        home: {
          currentJobId: 'test-job-123',
          isJobsDownloaded: true,
          loading: false,
        },
      };
      return selector(mockState);
    });

    // Setup default mock implementations
    const mockBookmarkItems = [
      {
        id: '1',
        entity_Description: 'RCC - Upto plinth level',
        entity_Code: 'TEST001',
        job_Code: 'TEST_JOB_001',
      },
      {
        id: '2', 
        entity_Description: 'RCC - Upto half height',
        entity_Code: 'TEST002',
        job_Code: 'TEST_JOB_001',
      }
    ];

    mockFilterDataByType.mockReturnValue({
      filteredBookmark: mockBookmarkItems,
    });
    
    mockGetFullPathDescriptionForBookmarks.mockImplementation((item) => {
      return item?.entity_Description || 'Test Item';
    });

    // Setup database mock to return test data
    const { database } = require('../../../database/index');
    database.get.mockReturnValue({
      query: jest.fn(() => ({
        fetch: jest.fn().mockResolvedValue(mockBookmarkItems),
      })),
    });
  });

  afterEach(() => {
    teardownTest();
  });

  // ============================================================================
  // RENDER TESTS
  // ============================================================================

  describe('Render Tests', () => {
    test('should render correctly with initial state', () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      renderWithProviders(<BooksmarksView />, { initialState });

      expect(screen.getByTestId('app-header')).toBeTruthy();
    });

    test('should render with search component', () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      renderWithProviders(<BooksmarksView />, { initialState });

      expect(screen.getByTestId('search-component')).toBeTruthy();
    });

    test('should render without crashing when dependencies are missing', () => {
      const initialState = {
        home: {
          currentJobId: null,
        },
      };

      // Fix: Use undefined instead of null for currentJobId to match type
      const fixedInitialState = {
        home: {
          currentJobId: undefined,
        },
      };
      expect(() => renderWithProviders(<BooksmarksView />, { initialState: fixedInitialState })).not.toThrow();
    });

    test('should render with empty state when no bookmarks', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue([]),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      await waitFor(() => {
        // The component shows an empty state image when no items are available
        expect(screen.getByTestId('search-component')).toBeTruthy();
      });
    });

    test('should render with bookmark items list', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const mockBookmarkItems = [
        {
          id: '1',
          PRCGB_Job_Code: 'TEST_JOB_001',
          PRCGB_WBS_Code: 'IL7~WP100072~SWP102~1061',
          PRCGB_User_ID: 100078676,
          PRCGB_Hierarchy_Level: 'WP',
          PRCGB_IsActive: 'Y',
          JOB_DESC: 'Test Job',
          IL: 'IL7',
          IL_DESC: 'Other Civil Works',
          WP: 'WP100072',
          WP_DESC: 'Ancilliary Buildings',
          SWP: 'SWP102',
          CWP_DESC: 'Staff Quarters',
          DELIVERABLE_CODE: 1061,
          DELIVERABLE_CODE_DESC: 'RCC - Upto plinth level',
          taskType: 'BQ',
        },
        {
          id: '2',
          PRCGB_Job_Code: 'TEST_JOB_001',
          PRCGB_WBS_Code: 'IL7~WP100072~SWP102~1062',
          PRCGB_User_ID: 100078676,
          PRCGB_Hierarchy_Level: 'WP',
          PRCGB_IsActive: 'Y',
          JOB_DESC: 'Test Job',
          IL: 'IL7',
          IL_DESC: 'Other Civil Works',
          WP: 'WP100072',
          WP_DESC: 'Ancilliary Buildings',
          SWP: 'SWP102',
          CWP_DESC: 'Staff Quarters',
          DELIVERABLE_CODE: 1062,
          DELIVERABLE_CODE_DESC: 'RCC - Upto half height',
          taskType: 'IT',
        },
      ];

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue(mockBookmarkItems),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      await waitFor(() => {
        expect(screen.getByText('RCC - Upto plinth level')).toBeTruthy();
        expect(screen.getByText('RCC - Upto half height')).toBeTruthy();
      });
    });

    test('should render with loading state', () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      renderWithProviders(<BooksmarksView />, { initialState });

      // Component renders in loading state - just verify it doesn't crash
      expect(screen.getByTestId('app-header')).toBeTruthy();
    });
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  describe('Interaction Tests', () => {
    test('should handle bookmark item click with valid WBS details', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const mockBookmarkItems = [
        {
          id: '1',
          PRCGB_Job_Code: 'TEST_JOB_001',
          PRCGB_WBS_Code: 'IL7~WP100072~SWP102~1061',
          PRCGB_User_ID: 100078676,
          PRCGB_Hierarchy_Level: 'WP',
          PRCGB_IsActive: 'Y',
          JOB_DESC: 'Test Job',
          IL: 'IL7',
          IL_DESC: 'Other Civil Works',
          WP: 'WP100072',
          WP_DESC: 'Ancilliary Buildings',
          SWP: 'SWP102',
          CWP_DESC: 'Staff Quarters',
          DELIVERABLE_CODE: 1061,
          DELIVERABLE_CODE_DESC: 'RCC - Upto plinth level',
          taskType: 'BQ',
          job_Code: 'TEST_JOB_001',
          parent_WBS_Code: 'IL7~WP100072~SWP102',
          fullPath: 'Test Path',
        },
      ];

      const mockWBSDetails = [
        {
          _raw: {
            WBS: 'WP100072~SWP102',
            JobCode: 'TEST_JOB_001',
            WBS_Description: 'Test WBS Description',
          },
        },
      ];

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn()
            .mockResolvedValueOnce(mockBookmarkItems)
            .mockResolvedValueOnce(mockWBSDetails),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      await waitFor(() => {
        const item = screen.getByText('RCC - Upto plinth level');
        fireEvent.press(item);
      });

      await waitFor(() => {
        expect(mockNavigation.navigate).toHaveBeenCalledWith('DailyProgressDetailsView', {
          inputDetails: mockWBSDetails[0]._raw,
          wbsPath: 'Test Path',
          selectedItem: null,
          parentItems: null,
          fromWhere: 'DailyProgress',
        });
      });
    });

    test('should handle bookmark item click with no WBS details', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const mockBookmarkItems = [
        {
          id: '1',
          PRCGB_Job_Code: 'TEST_JOB_001',
          PRCGB_WBS_Code: 'IL7~WP100072~SWP102~1061',
          PRCGB_User_ID: 100078676,
          PRCGB_Hierarchy_Level: 'WP',
          PRCGB_IsActive: 'Y',
          JOB_DESC: 'Test Job',
          IL: 'IL7',
          IL_DESC: 'Other Civil Works',
          WP: 'WP100072',
          WP_DESC: 'Ancilliary Buildings',
          SWP: 'SWP102',
          CWP_DESC: 'Staff Quarters',
          DELIVERABLE_CODE: 1061,
          DELIVERABLE_CODE_DESC: 'RCC - Upto plinth level',
          taskType: 'BQ',
          job_Code: 'TEST_JOB_001',
          parent_WBS_Code: 'IL7~WP100072~SWP102',
          fullPath: 'Test Path',
        },
      ];

      const { database } = require('../../../database/index');
      const { customAlertWithOK } = require('../../../components/CustomAlert');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn()
            .mockResolvedValueOnce(mockBookmarkItems)
            .mockResolvedValueOnce([]),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      await waitFor(() => {
        const item = screen.getByText('RCC - Upto plinth level');
        fireEvent.press(item);
      });

      await waitFor(() => {
        expect(customAlertWithOK).toHaveBeenCalledWith('No details found', 'No input details found for the selected item.');
      });
    });

    test('should handle search input changes', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue([]),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      const searchInput = screen.getByTestId('search-component');
      fireEvent.changeText(searchInput, 'test search');

      await waitFor(() => {
        expect(searchInput.props.value).toBe('test search');
      });
    });

    test('should handle filter selection', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue([]),
        })),
      });

      mockFilterDataByType.mockReturnValue({
        filteredBookmark: [],
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      // Filter functionality is handled through the app header
      await waitFor(() => {
        expect(mockFilterDataByType).toHaveBeenCalled();
      });

      expect(mockFilterDataByType).toHaveBeenCalledWith('Billable', [], [], [], 'Book Mark');
    });

    test('should handle filter modal visibility', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue([]),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      // Filter functionality exists through the app header
      await waitFor(() => {
        expect(screen.getByTestId('app-header')).toBeTruthy();
      });
    });
  });

  // ============================================================================
  // STATE CHANGE TESTS
  // ============================================================================

  describe('State Change Tests', () => {
    test('should update search query state', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue([]),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      const searchInput = screen.getByTestId('search-component');
      fireEvent.changeText(searchInput, 'new search query');

      await waitFor(() => {
        expect(searchInput.props.value).toBe('new search query');
      });
    });

    test('should update selected filter state', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue([]),
        })),
      });

      mockFilterDataByType.mockReturnValue({
        filteredBookmark: [],
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      // Filter state is managed internally by the component
      await waitFor(() => {
        expect(mockFilterDataByType).toHaveBeenCalled();
      });
    });

    test('should update filtered items when search changes', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const mockBookmarkItems = [
        {
          id: '1',
          fullPath: 'Test Item 1',
          DELIVERABLE_CODE_DESC: 'Test Item 1',
        },
        {
          id: '2',
          fullPath: 'Test Item 2',
          DELIVERABLE_CODE_DESC: 'Test Item 2',
        },
      ];

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue(mockBookmarkItems),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      const searchInput = screen.getByTestId('search-component');
      fireEvent.changeText(searchInput, 'Item 1');

      await waitFor(() => {
        expect(screen.getByText('Test Item 1')).toBeTruthy();
        expect(screen.queryByText('Test Item 2')).toBeFalsy();
      });
    });

    test('should update modal visibility state', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue([]),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      // Modal state is managed internally by the component
      await waitFor(() => {
        expect(screen.getByTestId('app-header')).toBeTruthy();
      });
    });
  });

  // ============================================================================
  // DATABASE INTEGRATION TESTS
  // ============================================================================

  describe('Database Integration Tests', () => {
    test('should load bookmark items from database on mount', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue([]),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      await waitFor(() => {
        expect(database.get).toHaveBeenCalledWith('BookMarkList');
      });
    });

    test('should handle database query errors gracefully', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockRejectedValue(new Error('Database error')),
        })),
      });

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      renderWithProviders(<BooksmarksView />, { initialState });

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Error loading bookmark items:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });

    test('should query WBS details correctly', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const mockBookmarkItems = [
        {
          id: '1',
          PRCGB_Job_Code: 'TEST_JOB_001',
          PRCGB_WBS_Code: 'IL7~WP100072~SWP102~1061',
          job_Code: 'TEST_JOB_001',
          parent_WBS_Code: 'IL7~WP100072~SWP102',
          DELIVERABLE_CODE_DESC: 'Test Item',
        },
      ];

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn()
            .mockResolvedValueOnce(mockBookmarkItems)
            .mockResolvedValueOnce([]),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      await waitFor(() => {
        const item = screen.getByText('Test Item');
        fireEvent.press(item);
      });

      await waitFor(() => {
        expect(database.get).toHaveBeenCalledWith('WBSDetails');
      });
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling Tests', () => {
    test('should handle missing current job ID', () => {
      const initialState = {
        home: {
          currentJobId: null,
        },
      };

      // Fix: Use undefined instead of null for currentJobId to match type
      const fixedInitialState = {
        home: {
          currentJobId: undefined,
        },
      };
      
      // Just verify component doesn't crash when currentJobId is missing
      expect(() => {
        renderWithProviders(<BooksmarksView />, { initialState: fixedInitialState });
      }).not.toThrow();
    });

    test('should handle database connection errors', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockRejectedValue(new Error('Connection failed')),
        })),
      });

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      renderWithProviders(<BooksmarksView />, { initialState });

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Error loading bookmark items:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });

    test('should handle empty bookmark items gracefully', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue([]),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      await waitFor(() => {
        // The component renders properly even with empty bookmark items
        expect(screen.getByTestId('app-header')).toBeTruthy();
      });
    });

    test('should handle item click errors', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const mockBookmarkItems = [
        {
          id: '1',
          PRCGB_Job_Code: 'TEST_JOB_001',
          PRCGB_WBS_Code: 'IL7~WP100072~SWP102~1061',
          job_Code: 'TEST_JOB_001',
          parent_WBS_Code: 'IL7~WP100072~SWP102',
          DELIVERABLE_CODE_DESC: 'Test Item',
        },
      ];

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn()
            .mockResolvedValueOnce(mockBookmarkItems)
            .mockRejectedValueOnce(new Error('WBS details query failed')),
        })),
      });

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      renderWithProviders(<BooksmarksView />, { initialState });

      await waitFor(() => {
        const item = screen.getByText('Test Item');
        fireEvent.press(item);
      });

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Error fetching WBS details:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });
  });

  // ============================================================================
  // EDGE CASES
  // ============================================================================

  describe('Edge Cases', () => {
    test('should handle null values in bookmark items', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const mockBookmarkItems = [
        {
          id: '1',
          PRCGB_Job_Code: null,
          PRCGB_WBS_Code: null,
          PRCGB_User_ID: null,
          PRCGB_Hierarchy_Level: null,
          PRCGB_IsActive: null,
          JOB_DESC: null,
          IL: null,
          IL_DESC: null,
          WP: null,
          WP_DESC: null,
          SWP: null,
          CWP_DESC: null,
          DELIVERABLE_CODE: null,
          DELIVERABLE_CODE_DESC: null,
          taskType: null,
        },
      ];

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue(mockBookmarkItems),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      await waitFor(() => {
        expect(() => renderWithProviders(<BooksmarksView />, { initialState })).not.toThrow();
      });
    });

    test('should handle rapid search input', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue([]),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      const searchInput = screen.getByTestId('search-component');

      // Rapid text changes
      for (let i = 0; i < 10; i++) {
        fireEvent.changeText(searchInput, `search ${i}`);
      }

      await waitFor(() => {
        expect(searchInput.props.value).toBe('search 9');
      });
    });

    test('should handle component unmount during data loading', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000))),
        })),
      });

      const { unmount } = renderWithProviders(<BooksmarksView />, { initialState });

      // Unmount before data loads
      unmount();

      // Should not throw any errors
      expect(() => {}).not.toThrow();
    });

    test('should handle empty search query', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const mockBookmarkItems = [
        {
          id: '1',
          fullPath: 'Test Item 1',
          DELIVERABLE_CODE_DESC: 'Test Item 1',
        },
        {
          id: '2',
          fullPath: 'Test Item 2',
          DELIVERABLE_CODE_DESC: 'Test Item 2',
        },
      ];

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue(mockBookmarkItems),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      const searchInput = screen.getByTestId('search-component');
      fireEvent.changeText(searchInput, '   '); // Whitespace only

      await waitFor(() => {
        expect(screen.getByText('Test Item 1')).toBeTruthy();
        expect(screen.getByText('Test Item 2')).toBeTruthy();
      });
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    test('should have proper testIDs for accessibility', () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      renderWithProviders(<BooksmarksView />, { initialState });

      expect(screen.getByTestId('app-header')).toBeTruthy();
      expect(screen.getByTestId('search-component')).toBeTruthy();
    });

    test('should have proper accessibility labels', () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      renderWithProviders(<BooksmarksView />, { initialState });

      const searchInput = screen.getByTestId('search-component');
      // Search component exists and can be accessed
      expect(searchInput).toBeTruthy();
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    test('should handle large bookmark lists efficiently', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const largeBookmarkItems = Array.from({ length: 1000 }, (_, index) => ({
        id: index.toString(),
        PRCGB_Job_Code: 'TEST_JOB_001',
        PRCGB_WBS_Code: `IL7~WP100072~SWP102~${1061 + index}`,
        PRCGB_User_ID: 100078676,
        PRCGB_Hierarchy_Level: 'WP',
        PRCGB_IsActive: 'Y',
        JOB_DESC: 'Test Job',
        IL: 'IL7',
        IL_DESC: 'Other Civil Works',
        WP: 'WP100072',
        WP_DESC: 'Ancilliary Buildings',
        SWP: 'SWP102',
        CWP_DESC: 'Staff Quarters',
        DELIVERABLE_CODE: 1061 + index,
        DELIVERABLE_CODE_DESC: `Item ${index}`,
        taskType: 'BQ',
      }));

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue(largeBookmarkItems),
        })),
      });

      const startTime = Date.now();
      renderWithProviders(<BooksmarksView />, { initialState });
      const endTime = Date.now();

      // Should render within reasonable time
      expect(endTime - startTime).toBeLessThan(1000);
    });

    test('should handle rapid filter changes efficiently', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue([]),
        })),
      });

      mockFilterDataByType.mockReturnValue({
        filteredBookmark: [],
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      // The filter is in the app header, so we can test that the component renders
      await waitFor(() => {
        expect(screen.getByTestId('app-header')).toBeTruthy();
        expect(mockFilterDataByType).toHaveBeenCalled();
      });
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should integrate with Redux store correctly', () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { store } = renderWithProviders(<BooksmarksView />, { initialState });

      expect(store.getState().home.currentJobId).toBe('TEST_JOB_001');
    });

    test('should integrate with navigation correctly', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue([]),
        })),
      });

      renderWithProviders(<BooksmarksView />, { initialState });

      await waitFor(() => {
        expect(mockNavigation.navigate).not.toHaveBeenCalled();
      });
    });
  });

  // ============================================================================
  // SNAPSHOT TESTS
  // ============================================================================

  describe('Snapshot Tests', () => {
    test('should match snapshot with initial state', () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { toJSON } = renderWithProviders(<BooksmarksView />, { initialState });
      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot with bookmark items loaded', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const mockBookmarkItems = [
        {
          id: '1',
          PRCGB_Job_Code: 'TEST_JOB_001',
          PRCGB_WBS_Code: 'IL7~WP100072~SWP102~1061',
          PRCGB_User_ID: 100078676,
          PRCGB_Hierarchy_Level: 'WP',
          PRCGB_IsActive: 'Y',
          JOB_DESC: 'Test Job',
          IL: 'IL7',
          IL_DESC: 'Other Civil Works',
          WP: 'WP100072',
          WP_DESC: 'Ancilliary Buildings',
          SWP: 'SWP102',
          CWP_DESC: 'Staff Quarters',
          DELIVERABLE_CODE: 1061,
          DELIVERABLE_CODE_DESC: 'RCC - Upto plinth level',
          taskType: 'BQ',
        },
      ];

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue(mockBookmarkItems),
        })),
      });

      const { toJSON } = renderWithProviders(<BooksmarksView />, { initialState });

      await waitFor(() => {
        expect(toJSON()).toMatchSnapshot();
      });
    });

    test('should match snapshot with filter modal open', async () => {
      const initialState = {
        home: {
          currentJobId: 'TEST_JOB_001',
        },
      };

      const { database } = require('../../../database/index');
      database.get.mockReturnValue({
        query: jest.fn(() => ({
          fetch: jest.fn().mockResolvedValue([]),
        })),
      });

      const { toJSON } = renderWithProviders(<BooksmarksView />, { initialState });

      // Component renders with filter modal capability
      await waitFor(() => {
        expect(toJSON()).toMatchSnapshot();
      });
    });
  });
}); 