import React from 'react';
import { render } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { NavigationContainer } from '@react-navigation/native';
import { configureStore } from '@reduxjs/toolkit';
import LastUpdateScreen from '../../../screens/DailyProgress/LastUpdateScreen';

// Simple mock implementations
jest.mock('../../../utils/Scale/Scaling', () => ({
  width: 375,
  height: 812,
  scale: (size: number) => size,
  verticalScale: (size: number) => size,
  ms: (size: number) => size,
  moderateVerticalScale: (size: number) => size,
}));

jest.mock('../../../utils/DataStorage/Storage', () => ({
  getUserInfo: () => Promise.resolve({ id: 1, name: 'Test User' }),
  getCurrentJobId: () => Promise.resolve('JOB001'),
}));

jest.mock('../../../utils/Network/NetworkConnection', () => ({
  isNetworkConnected: () => Promise.resolve(true),
}));

jest.mock('../../../database', () => ({
  database: {
    collections: {
      get: () => ({
        query: () => ({
          fetch: () => Promise.resolve([]),
        }),
      }),
    },
  },
}));

jest.mock('@react-navigation/native', () => ({
  NavigationContainer: ({ children }: { children: React.ReactNode }) => children,
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
    reset: jest.fn(),
  }),
  useRoute: () => ({
    params: {},
    name: 'LastUpdateScreen',
    key: 'test-key',
  }),
}));

// Simple store configuration
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: (state = { user: null, token: null, loading: false, error: null }, action) => state,
      home: (state = { currentJobId: null, loading: false }, action) => state,
      dailyProgress: (state = { lastUpdateData: [], loading: false, error: null }, action) => state,
    },
    preloadedState: initialState,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
        immutableCheck: false,
      }),
  });
};

const renderWithStore = (component: React.ReactElement, initialState = {}) => {
  const store = createTestStore(initialState);
  return render(
    <Provider store={store}>
      <NavigationContainer>
        {component}
      </NavigationContainer>
    </Provider>
  );
};

describe('LastUpdateScreen', () => {
  describe('Basic Rendering', () => {
    it('should render without crashing', () => {
      const initialState = {
        auth: {
          user: { id: 1, name: 'Test User' },
          token: 'mock-token',
          loading: false,
          error: null,
        },
        home: {
          currentJobId: 'JOB001',
          loading: false,
        },
        dailyProgress: {
          lastUpdateData: [],
          loading: false,
          error: null,
        },
      };

      const { toJSON } = renderWithStore(<LastUpdateScreen />, initialState);
      expect(toJSON()).toBeTruthy();
    });

    it('should render with loading state', () => {
      const initialState = {
        auth: {
          user: { id: 1, name: 'Test User' },
          token: 'mock-token',
          loading: false,
          error: null,
        },
        home: {
          currentJobId: 'JOB001',
          loading: false,
        },
        dailyProgress: {
          lastUpdateData: [],
          loading: true,
          error: null,
        },
      };

      const { toJSON } = renderWithStore(<LastUpdateScreen />, initialState);
      expect(toJSON()).toBeTruthy();
    });

    it('should render with error state', () => {
      const initialState = {
        auth: {
          user: { id: 1, name: 'Test User' },
          token: 'mock-token',
          loading: false,
          error: null,
        },
        home: {
          currentJobId: 'JOB001',
          loading: false,
        },
        dailyProgress: {
          lastUpdateData: [],
          loading: false,
          error: 'Failed to load data',
        },
      };

      const { toJSON } = renderWithStore(<LastUpdateScreen />, initialState);
      expect(toJSON()).toBeTruthy();
    });

    it('should render with data', () => {
      const initialState = {
        auth: {
          user: { id: 1, name: 'Test User' },
          token: 'mock-token',
          loading: false,
          error: null,
        },
        home: {
          currentJobId: 'JOB001',
          loading: false,
        },
        dailyProgress: {
          lastUpdateData: [
            { id: '1', title: 'Update 1', date: '2024-01-15' },
            { id: '2', title: 'Update 2', date: '2024-01-14' },
          ],
          loading: false,
          error: null,
        },
      };

      const { toJSON } = renderWithStore(<LastUpdateScreen />, initialState);
      expect(toJSON()).toBeTruthy();
    });
  });

  describe('Store Integration', () => {
    it('should have access to auth state', () => {
      const initialState = {
        auth: {
          user: { id: 1, name: 'Test User' },
          token: 'mock-token',
          loading: false,
          error: null,
        },
      };

      const store = createTestStore(initialState);
      expect(store.getState().auth.user).toEqual({ id: 1, name: 'Test User' });
    });

    it('should have access to home state', () => {
      const initialState = {
        home: {
          currentJobId: 'JOB001',
          loading: false,
        },
      };

      const store = createTestStore(initialState);
      expect(store.getState().home.currentJobId).toBe('JOB001');
    });

    it('should have access to dailyProgress state', () => {
      const initialState = {
        dailyProgress: {
          lastUpdateData: [
            { id: '1', title: 'Update 1' },
          ],
          loading: false,
          error: null,
        },
      };

      const store = createTestStore(initialState);
      expect(store.getState().dailyProgress.lastUpdateData).toHaveLength(1);
    });
  });
}); 