import React from 'react';
import { render } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { NavigationContainer } from '@react-navigation/native';
import SplashScreen from '../../screens/Splash/SplashScreen';

// Create a consistent mock navigation object
const mockNavigation = {
  reset: jest.fn(),
  navigate: jest.fn(),
  goBack: jest.fn(),
};

// Mock React Navigation
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => mockNavigation,
  NavigationContainer: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock useDispatch
const mockDispatch = jest.fn();
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: () => mockDispatch,
}));

// Mock only the specific dependencies that aren't handled by global setup
jest.mock('../../utils/DataStorage/Storage', () => ({
  clearAllData: jest.fn(),
  getUserInfo: jest.fn(),
  getUserLastLoggedInDate: jest.fn(),
  getUserRolesInfo: jest.fn(),
}));

jest.mock('../../utils/Storage/Storage', () => ({
  getSelectedJobs: jest.fn(),
}));

jest.mock('../../redux/AuthRedux/Logout/LogoutAction', () => ({
  logoutRequest: jest.fn(() => ({ type: 'LOGOUT_REQUEST' })),
}));

jest.mock('../../redux/HomeRedux/HomeActions', () => ({
  setJobsDownloaded: jest.fn((payload) => ({ type: 'SET_JOBS_DOWNLOADED', payload })),
  setSelectedCurrentJobId: jest.fn((payload) => ({ type: 'SET_SELECTED_CURRENT_JOB_ID', payload })),
}));

jest.mock('../../utils/Colors/Colors', () => ({
  __esModule: true,
  default: {
    primary: '#007AFF',
    white: '#FFFFFF',
  },
}));

// Mock dayjs with proper ES module default export
jest.mock('dayjs', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    format: jest.fn(() => '2025-01-01'),
  })),
}));

// Create a simple mock store
const createMockStore = () => configureStore({
  reducer: {
    auth: (state = {}) => state,
    home: (state = {}) => state,
  },
  preloadedState: {
    auth: {},
    home: {},
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  const store = createMockStore();
  return render(
    <Provider store={store}>
      <NavigationContainer>
        {component}
      </NavigationContainer>
    </Provider>
  );
};

describe('<SplashScreen />', () => {
  // Import mocked modules
  const { 
    clearAllData, 
    getUserInfo, 
    getUserLastLoggedInDate, 
    getUserRolesInfo 
  } = require('../../utils/DataStorage/Storage');
  
  const { getSelectedJobs } = require('../../utils/Storage/Storage');
  const { logoutRequest } = require('../../redux/AuthRedux/Logout/LogoutAction');
  const { setJobsDownloaded, setSelectedCurrentJobId } = require('../../redux/HomeRedux/HomeActions');

  beforeEach(() => {
    jest.clearAllMocks();
    mockDispatch.mockClear();
    mockNavigation.reset.mockClear();
    mockNavigation.navigate.mockClear();
    mockNavigation.goBack.mockClear();
  });

  it('should render correctly and match snapshot', () => {
    getUserInfo.mockReturnValue({ UserName: 'Test User' });
    getUserRolesInfo.mockReturnValue({ RolesList: [{ Functional_ROLE: 'Site Engineer' }] });
    getUserLastLoggedInDate.mockReturnValue('2025-01-01');
    getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

    const { toJSON } = renderWithProviders(<SplashScreen />);
    expect(toJSON()).toMatchSnapshot();
  });

  it('should display activity indicator with correct testID', () => {
    getUserInfo.mockReturnValue({ UserName: 'Test User' });
    getUserRolesInfo.mockReturnValue({ RolesList: [{ Functional_ROLE: 'Site Engineer' }] });
    getUserLastLoggedInDate.mockReturnValue('2025-01-01');
    getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

    const { getByTestId } = renderWithProviders(<SplashScreen />);
    expect(getByTestId('activity-indicator')).toBeTruthy();
  });

  describe('Authentication Logic', () => {
    it('should navigate to Home when user is authenticated and logged in today', () => {
      getUserInfo.mockReturnValue({ UserName: 'Test User' });
      getUserRolesInfo.mockReturnValue({ RolesList: [{ Functional_ROLE: 'Site Engineer' }] });
      getUserLastLoggedInDate.mockReturnValue('2025-01-01');
      getSelectedJobs.mockReturnValue([{ id: 'job1', name: 'Test Job' }]);

      renderWithProviders(<SplashScreen />);

      expect(mockNavigation.reset).toHaveBeenCalledWith({
        index: 0,
        routes: [{ name: 'Home' }],
      });
      expect(setJobsDownloaded).toHaveBeenCalledWith(true);
      expect(setSelectedCurrentJobId).toHaveBeenCalledWith('job1');
    });

    it('should navigate to Login when user is not authenticated', () => {
      getUserInfo.mockReturnValue(null);
      getUserRolesInfo.mockReturnValue(null);
      getUserLastLoggedInDate.mockReturnValue(null);

      renderWithProviders(<SplashScreen />);

      expect(clearAllData).toHaveBeenCalled();
      expect(logoutRequest).toHaveBeenCalled();
      expect(mockNavigation.reset).toHaveBeenCalledWith({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    });

    it('should navigate to Login when user did not log in today', () => {
      getUserInfo.mockReturnValue({ UserName: 'Test User' });
      getUserRolesInfo.mockReturnValue({ RolesList: [{ Functional_ROLE: 'Site Engineer' }] });
      getUserLastLoggedInDate.mockReturnValue('2024-12-31'); // Different date

      renderWithProviders(<SplashScreen />);

      expect(clearAllData).toHaveBeenCalled();
      expect(logoutRequest).toHaveBeenCalled();
      expect(mockNavigation.reset).toHaveBeenCalledWith({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    });

    it('should handle case when user has no selected jobs', () => {
      getUserInfo.mockReturnValue({ UserName: 'Test User' });
      getUserRolesInfo.mockReturnValue({ RolesList: [{ Functional_ROLE: 'Site Engineer' }] });
      getUserLastLoggedInDate.mockReturnValue('2025-01-01');
      getSelectedJobs.mockReturnValue([]);

      renderWithProviders(<SplashScreen />);

      expect(mockNavigation.reset).toHaveBeenCalledWith({
        index: 0,
        routes: [{ name: 'Home' }],
      });
      expect(setJobsDownloaded).toHaveBeenCalledWith(false);
      expect(setSelectedCurrentJobId).toHaveBeenCalledWith(undefined);
    });

    it('should handle case when getSelectedJobs returns null', () => {
      getUserInfo.mockReturnValue({ UserName: 'Test User' });
      getUserRolesInfo.mockReturnValue({ RolesList: [{ Functional_ROLE: 'Site Engineer' }] });
      getUserLastLoggedInDate.mockReturnValue('2025-01-01');
      getSelectedJobs.mockReturnValue(null);

      renderWithProviders(<SplashScreen />);

      expect(setJobsDownloaded).toHaveBeenCalledWith(false);
      expect(setSelectedCurrentJobId).toHaveBeenCalledWith(undefined);
    });
  });

  describe('Edge Cases', () => {
    it('should handle missing user info', () => {
      getUserInfo.mockReturnValue(null);
      getUserRolesInfo.mockReturnValue({ RolesList: [{ Functional_ROLE: 'Site Engineer' }] });
      getUserLastLoggedInDate.mockReturnValue('2025-01-01');

      renderWithProviders(<SplashScreen />);

      expect(clearAllData).toHaveBeenCalled();
      expect(logoutRequest).toHaveBeenCalled();
      expect(mockNavigation.reset).toHaveBeenCalledWith({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    });

    it('should handle missing roles info', () => {
      getUserInfo.mockReturnValue({ UserName: 'Test User' });
      getUserRolesInfo.mockReturnValue(null);
      getUserLastLoggedInDate.mockReturnValue('2025-01-01');

      renderWithProviders(<SplashScreen />);

      expect(clearAllData).toHaveBeenCalled();
      expect(logoutRequest).toHaveBeenCalled();
      expect(mockNavigation.reset).toHaveBeenCalledWith({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    });

    it('should handle empty date format', () => {
      getUserInfo.mockReturnValue({ UserName: 'Test User' });
      getUserRolesInfo.mockReturnValue({ RolesList: [{ Functional_ROLE: 'Site Engineer' }] });
      getUserLastLoggedInDate.mockReturnValue('');

      renderWithProviders(<SplashScreen />);

      expect(clearAllData).toHaveBeenCalled();
      expect(logoutRequest).toHaveBeenCalled();
      expect(mockNavigation.reset).toHaveBeenCalledWith({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    });
  });

  describe('Redux Integration', () => {
    it('should properly dispatch actions when authenticated', () => {
      getUserInfo.mockReturnValue({ UserName: 'Test User' });
      getUserRolesInfo.mockReturnValue({ RolesList: [{ Functional_ROLE: 'Site Engineer' }] });
      getUserLastLoggedInDate.mockReturnValue('2025-01-01');
      getSelectedJobs.mockReturnValue([{ id: 'job1', name: 'Test Job' }]);

      renderWithProviders(<SplashScreen />);

      expect(setJobsDownloaded).toHaveBeenCalledWith(true);
      expect(setSelectedCurrentJobId).toHaveBeenCalledWith('job1');
    });

    it('should properly dispatch logout action when not authenticated', () => {
      getUserInfo.mockReturnValue(null);
      getUserRolesInfo.mockReturnValue(null);
      getUserLastLoggedInDate.mockReturnValue(null);

      renderWithProviders(<SplashScreen />);

      expect(logoutRequest).toHaveBeenCalled();
    });
  });
}); 