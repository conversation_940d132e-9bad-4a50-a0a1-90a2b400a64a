import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { renderWithProviders, setupTest, teardownTest } from '../../../utils/testing/testHelpers';
import HomeScreen from '../../../screens/Home/HomeScreen';

// Mock dependencies
jest.mock('../../../utils/DataStorage/Storage', () => ({
  clearAllData: jest.fn(),
  getUserInfo: jest.fn(),
  getUserRolesInfo: jest.fn(),
}));

jest.mock('../../../utils/Storage/Storage', () => ({
  getSelectedJobs: jest.fn(),
}));

jest.mock('../../../redux/AuthRedux/Logout/LogoutAction', () => ({
  logoutRequest: jest.fn(),
}));

jest.mock('../../../redux/HomeRedux/HomeActions', () => ({
  setJobsDownloaded: jest.fn(),
  setSelectedCurrentJobId: jest.fn(),
}));

jest.mock('../../../utils/Constants/UserFeature/userFeature', () => ({
  USER_FEATURES: {
    'Site Engineer': [
      { label: 'Download WBS', icon: 'DownloadIcon' },
      { label: 'Progress Update', icon: 'ProgressIcon' },
      { label: 'Hindrance', icon: 'HindranceIcon' },
      { label: 'Profile', icon: 'ProfileIcon' },
    ],
    'Approver': [
      { label: 'Download WBS', icon: 'DownloadIcon' },
      { label: 'Progress Update', icon: 'ProgressIcon' },
      { label: 'Profile', icon: 'ProfileIcon' },
    ],
    'Store Incharge': [
      { label: 'Sync Data', icon: 'SyncIcon' },
      { label: 'User Manual', icon: 'ManualIcon' },
      { label: 'Profile', icon: 'ProfileIcon' },
    ],
  },
}));

jest.mock('../../../utils/Strings/Strings', () => ({
  homeScreen: {
    downloadWBS: 'Download WBS',
    progressUpdate: 'Progress Update',
    syncData: 'Sync Data',
    hindrance: 'Hindrance',
    userManual: 'User Manual',
  },
}));

const mockNavigation = {
  navigate: jest.fn(),
  dispatch: jest.fn(),
};

jest.mock('@react-navigation/native', () => ({
  useNavigation: () => mockNavigation,
  CommonActions: {
    reset: jest.fn((config) => config),
  },
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock components
jest.mock('../../../components/LogOut', () => {
  return function MockLogout(props: any) {
    return props.visible ? <div data-testid="logout-modal" {...props} /> : null;
  };
});

jest.mock('../../../screens/Home/components/FeatureGrid', () => {
  return function MockFeatureGrid(props: any) {
    return <div data-testid={props.testID} onClick={props.onPress} {...props} />;
  };
});

jest.mock('../../../screens/Home/components/GreetingHeader', () => {
  return function MockGreetingHeader(props: any) {
    return <div data-testid="greeting-header" {...props} />;
  };
});

jest.mock('../../../screens/Home/components/InfoCard', () => {
  return function MockInfoCard(props: any) {
    return <div data-testid="info-card" {...props} />;
  };
});

jest.mock('../../../screens/Home/components/MenuCard', () => {
  return function MockMenuCard(props: any) {
    return <div data-testid={props.testID} onClick={props.onPress} {...props} />;
  };
});

jest.mock('../../../screens/Home/components/UserCard', () => {
  return function MockUserCard(props: any) {
    return <div data-testid="user-card" {...props} />;
  };
});

describe('HomeScreen', () => {
  beforeEach(() => {
    setupTest();
    jest.clearAllMocks();
  });

  afterEach(() => {
    teardownTest();
  });

  // ============================================================================
  // RENDER TESTS
  // ============================================================================

  describe('Render Tests', () => {
    test('should render correctly for Site Engineer role', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'John Doe' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      expect(screen.getByTestId('greeting-header')).toBeTruthy();
      expect(screen.getByTestId('info-card')).toBeTruthy();
      expect(screen.getByTestId('feature-Download WBS')).toBeTruthy();
      expect(screen.getByTestId('feature-Progress Update')).toBeTruthy();
      expect(screen.getByTestId('feature-Hindrance')).toBeTruthy();
      expect(screen.getByTestId('feature-Profile')).toBeTruthy();
    });

    test('should render correctly for Approver role', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Jane Smith' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Approver' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      expect(screen.getByTestId('greeting-header')).toBeTruthy();
      expect(screen.getByTestId('info-card')).toBeTruthy();
      expect(screen.getByTestId('feature-Download WBS')).toBeTruthy();
      expect(screen.getByTestId('feature-Progress Update')).toBeTruthy();
      expect(screen.getByTestId('feature-Profile')).toBeTruthy();
    });

    test('should render correctly for Store Incharge role', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Bob Wilson' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Store Incharge' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      expect(screen.getByTestId('user-card')).toBeTruthy();
      expect(screen.getByTestId('menu-Sync Data')).toBeTruthy();
      expect(screen.getByTestId('menu-User Manual')).toBeTruthy();
      expect(screen.getByTestId('menu-Profile')).toBeTruthy();
    });

    test('should render without user info', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue(null);
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      expect(screen.queryByTestId('greeting-header')).toBeFalsy();
      expect(screen.getByTestId('info-card')).toBeTruthy();
    });

    test('should render with no roles', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue(null);
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      expect(screen.getByTestId('user-card')).toBeTruthy();
    });

    test('should render with empty roles list', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({ RolesList: [] });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      expect(screen.getByTestId('user-card')).toBeTruthy();
    });

    test('should render activity indicator when loading', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      const { rerender } = renderWithProviders(<HomeScreen />);

      // Initially no loader
      expect(screen.queryByTestId('activity-indicator')).toBeFalsy();

      // Simulate loading state by re-rendering with different props
      // This would typically be done through state changes
      rerender(<HomeScreen />);
    });
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  describe('Interaction Tests', () => {
    test('should handle Download WBS feature press', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      fireEvent.press(screen.getByTestId('feature-Download WBS'));

      expect(mockNavigation.navigate).toHaveBeenCalledWith('DownloadWBS');
    });

    test('should handle Progress Update feature press for Site Engineer', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      fireEvent.press(screen.getByTestId('feature-Progress Update'));

      expect(mockNavigation.navigate).toHaveBeenCalledWith('DailyProgressView');
    });

    test('should handle Progress Update feature press for other roles', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Approver' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      fireEvent.press(screen.getByTestId('feature-Progress Update'));

      expect(mockNavigation.navigate).toHaveBeenCalledWith('DailyProgressApprove');
    });

    test('should handle Hindrance feature press with selected jobs', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      fireEvent.press(screen.getByTestId('feature-Hindrance'));

      expect(mockNavigation.navigate).toHaveBeenCalledWith('HindranceMapView');
    });

    test('should show alert for Hindrance without selected jobs', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');
      const Alert = require('react-native').Alert;

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([]);

      renderWithProviders(<HomeScreen />);

      fireEvent.press(screen.getByTestId('feature-Hindrance'));

      expect(Alert.alert).toHaveBeenCalledWith('Please download and set the current.');
    });

    test('should handle Profile feature press to show logout modal', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      fireEvent.press(screen.getByTestId('feature-Profile'));

      expect(screen.getByTestId('logout-modal')).toBeTruthy();
    });

    test('should handle Sync Data feature press for Store Incharge', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Store Incharge' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      fireEvent.press(screen.getByTestId('menu-Sync Data'));

      expect(mockNavigation.navigate).toHaveBeenCalledWith('SyncData');
    });

    test('should handle User Manual feature press', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Store Incharge' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      fireEvent.press(screen.getByTestId('menu-User Manual'));

      expect(mockNavigation.navigate).toHaveBeenCalledWith('UserMannualScreen');
    });

    test('should handle logout confirmation', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { clearAllData } = require('../../../utils/DataStorage/Storage');
      const { logoutRequest } = require('../../../redux/AuthRedux/Logout/LogoutAction');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      // Open logout modal
      fireEvent.press(screen.getByTestId('feature-Profile'));

      // Confirm logout
      const logoutModal = screen.getByTestId('logout-modal');
      fireEvent(logoutModal, 'onConfirm');

      await waitFor(() => {
        expect(clearAllData).toHaveBeenCalled();
        expect(logoutRequest).toHaveBeenCalled();
        expect(mockNavigation.dispatch).toHaveBeenCalled();
      });
    });

    test('should handle logout cancellation', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      // Open logout modal
      fireEvent.press(screen.getByTestId('feature-Profile'));

      // Cancel logout
      const logoutModal = screen.getByTestId('logout-modal');
      fireEvent(logoutModal, 'onCancel');

      expect(screen.queryByTestId('logout-modal')).toBeFalsy();
    });
  });

  // ============================================================================
  // STATE CHANGE TESTS
  // ============================================================================

  describe('State Change Tests', () => {
    test('should update jobs downloaded state on mount', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');
      const { setJobsDownloaded, setSelectedCurrentJobId } = require('../../../redux/HomeRedux/HomeActions');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1', name: 'Test Job' }]);

      renderWithProviders(<HomeScreen />);

      await waitFor(() => {
        expect(setJobsDownloaded).toHaveBeenCalledWith(true);
        expect(setSelectedCurrentJobId).toHaveBeenCalledWith('job1');
      });
    });

    test('should handle no selected jobs on mount', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');
      const { setJobsDownloaded, setSelectedCurrentJobId } = require('../../../redux/HomeRedux/HomeActions');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([]);

      renderWithProviders(<HomeScreen />);

      await waitFor(() => {
        expect(setJobsDownloaded).toHaveBeenCalledWith(false);
        expect(setSelectedCurrentJobId).toHaveBeenCalledWith(undefined);
      });
    });

    test('should handle null selected jobs on mount', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');
      const { setJobsDownloaded, setSelectedCurrentJobId } = require('../../../redux/HomeRedux/HomeActions');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue(null);

      renderWithProviders(<HomeScreen />);

      await waitFor(() => {
        expect(setJobsDownloaded).toHaveBeenCalledWith(false);
        expect(setSelectedCurrentJobId).toHaveBeenCalledWith(undefined);
      });
    });
  });

  // ============================================================================
  // EDGE CASES
  // ============================================================================

  describe('Edge Cases', () => {
    test('should handle unknown feature label', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      // This test ensures the switch statement handles unknown cases gracefully
      expect(() => renderWithProviders(<HomeScreen />)).not.toThrow();
    });

    test('should handle missing user role', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: null }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      expect(screen.getByTestId('user-card')).toBeTruthy();
    });

    test('should handle undefined user role', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: undefined }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      expect(screen.getByTestId('user-card')).toBeTruthy();
    });

    test('should handle empty user name', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: '' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      expect(screen.getByTestId('info-card')).toBeTruthy();
    });

    test('should handle null user name', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: null });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      expect(screen.getByTestId('info-card')).toBeTruthy();
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling Tests', () => {
    test('should handle storage function errors gracefully', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockImplementation(() => {
        throw new Error('Storage error');
      });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      expect(() => renderWithProviders(<HomeScreen />)).not.toThrow();
    });

    test('should handle navigation errors gracefully', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      mockNavigation.navigate.mockImplementation(() => {
        throw new Error('Navigation error');
      });

      expect(() => renderWithProviders(<HomeScreen />)).not.toThrow();
    });

    test('should handle dispatch errors gracefully', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');
      const { setJobsDownloaded } = require('../../../redux/HomeRedux/HomeActions');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      setJobsDownloaded.mockImplementation(() => {
        throw new Error('Dispatch error');
      });

      expect(() => renderWithProviders(<HomeScreen />)).not.toThrow();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    test('should have accessible feature buttons', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      expect(screen.getByTestId('feature-Download WBS')).toBeTruthy();
      expect(screen.getByTestId('feature-Progress Update')).toBeTruthy();
      expect(screen.getByTestId('feature-Hindrance')).toBeTruthy();
      expect(screen.getByTestId('feature-Profile')).toBeTruthy();
    });

    test('should have accessible menu buttons', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Store Incharge' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      expect(screen.getByTestId('menu-Sync Data')).toBeTruthy();
      expect(screen.getByTestId('menu-User Manual')).toBeTruthy();
      expect(screen.getByTestId('menu-Profile')).toBeTruthy();
    });

    test('should have accessible logout modal', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      fireEvent.press(screen.getByTestId('feature-Profile'));

      expect(screen.getByTestId('logout-modal')).toBeTruthy();
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    test('should render efficiently without unnecessary re-renders', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      const { rerender } = renderWithProviders(<HomeScreen />);

      // Re-render multiple times
      rerender(<HomeScreen />);
      rerender(<HomeScreen />);
      rerender(<HomeScreen />);

      expect(screen.getByTestId('greeting-header')).toBeTruthy();
    });

    test('should handle rapid feature presses efficiently', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      // Rapidly press multiple features
      fireEvent.press(screen.getByTestId('feature-Download WBS'));
      fireEvent.press(screen.getByTestId('feature-Progress Update'));
      fireEvent.press(screen.getByTestId('feature-Hindrance'));

      expect(mockNavigation.navigate).toHaveBeenCalledTimes(3);
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should work with Redux store integration', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      expect(screen.getByTestId('greeting-header')).toBeTruthy();
      expect(screen.getByTestId('info-card')).toBeTruthy();
    });

    test('should work with navigation container', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      fireEvent.press(screen.getByTestId('feature-Download WBS'));
      expect(mockNavigation.navigate).toHaveBeenCalledWith('DownloadWBS');
    });

    test('should integrate with all storage utilities', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      await waitFor(() => {
        expect(Storage.getUserInfo).toHaveBeenCalled();
        expect(Storage.getUserRolesInfo).toHaveBeenCalled();
        expect(getSelectedJobs).toHaveBeenCalled();
      });
    });

    test('should integrate with Redux actions', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');
      const { setJobsDownloaded, setSelectedCurrentJobId } = require('../../../redux/HomeRedux/HomeActions');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      await waitFor(() => {
        expect(setJobsDownloaded).toHaveBeenCalled();
        expect(setSelectedCurrentJobId).toHaveBeenCalled();
      });
    });

    test('should integrate with logout flow', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { clearAllData } = require('../../../utils/DataStorage/Storage');
      const { logoutRequest } = require('../../../redux/AuthRedux/Logout/LogoutAction');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      renderWithProviders(<HomeScreen />);

      fireEvent.press(screen.getByTestId('feature-Profile'));
      const logoutModal = screen.getByTestId('logout-modal');
      fireEvent(logoutModal, 'onConfirm');

      await waitFor(() => {
        expect(clearAllData).toHaveBeenCalled();
        expect(logoutRequest).toHaveBeenCalled();
        expect(mockNavigation.dispatch).toHaveBeenCalled();
      });
    });
  });

  // ============================================================================
  // SNAPSHOT TESTS
  // ============================================================================

  describe('Snapshot Tests', () => {
    test('should match snapshot for Site Engineer role', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      const { toJSON } = renderWithProviders(<HomeScreen />);
      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot for Store Incharge role', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Store Incharge' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      const { toJSON } = renderWithProviders(<HomeScreen />);
      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot with logout modal open', () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getSelectedJobs } = require('../../../utils/Storage/Storage');

      Storage.getUserInfo.mockReturnValue({ UserName: 'Test User' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });
      getSelectedJobs.mockReturnValue([{ id: 'job1' }]);

      const { toJSON } = renderWithProviders(<HomeScreen />);
      
      fireEvent.press(screen.getByTestId('feature-Profile'));
      
      expect(toJSON()).toMatchSnapshot();
    });
  });
}); 