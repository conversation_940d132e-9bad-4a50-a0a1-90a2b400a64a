import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { renderWithProviders, setupTest, teardownTest } from '../../../utils/testing/testHelpers';
import SyncDataScreen from '../../../screens/SyncData/SyncDataScreen';

// Mock dependencies
jest.mock('../../../utils/Strings/Strings', () => ({
  SyncData: {
    syncDataHeader: 'Sync Data',
  },
}));

jest.mock('../../../utils/Constants/SyncDataConstants', () => ({
  tabs: [
    { tabName: 'Progress', onPress: jest.fn() },
    { tabName: 'Hindrance', onPress: jest.fn() },
    { tabName: 'Day Plan', onPress: jest.fn() },
    { tabName: 'Assets', onPress: jest.fn() },
  ],
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

const mockNavigation = {
  navigate: jest.fn(),
  dispatch: jest.fn(),
};

jest.mock('@react-navigation/native', () => ({
  useNavigation: () => mockNavigation,
  CommonActions: {
    reset: jest.fn((config) => config),
  },
}));

// Mock components
jest.mock('../../../components/AppHeader', () => {
  return function MockAppHeader(props: any) {
    return <div data-testid="app-header" onClick={props.onBackPress} {...props} />;
  };
});

jest.mock('../../../components/ButtonComponent', () => {
  return function MockButtonComponent(props: any) {
    return <div data-testid="sync-button" onClick={props.onPress} {...props} />;
  };
});

jest.mock('../../../screens/SyncData/components/SyncProgressUpdate/ProgressCardComponent', () => {
  return function MockProgressCard(props: any) {
    return <div data-testid="progress-card" onClick={props.onPress} {...props} />;
  };
});

jest.mock('../../../screens/SyncData/components/SyncHindrance/HindaranceCardComponent', () => {
  return function MockHindranceCard(props: any) {
    return <div data-testid="hindrance-card" onClick={props.onPress} {...props} />;
  };
});

jest.mock('../../../screens/SyncData/components/SyncDayPlan/DayPlanCardComponent', () => {
  return function MockDayPlanCard(props: any) {
    return <div data-testid="day-plan-card" onClick={props.onPress} {...props} />;
  };
});

// Mock styles
jest.mock('../../../screens/SyncData/Styles', () => ({
  styles: {
    container: {},
    header: {},
    contentContainer: {},
    listContent: {},
    tabContainer: {},
  },
}));

describe('SyncDataScreen', () => {
  beforeEach(() => {
    setupTest();
    jest.clearAllMocks();
  });

  afterEach(() => {
    teardownTest();
  });

  // ============================================================================
  // RENDER TESTS
  // ============================================================================

  describe('Render Tests', () => {
    test('should render correctly with all components', () => {
      renderWithProviders(<SyncDataScreen />);

      expect(screen.getByTestId('app-header')).toBeTruthy();
      expect(screen.getByTestId('sync-button')).toBeTruthy();
      expect(screen.getByText('Progress')).toBeTruthy();
      expect(screen.getByText('Hindrance')).toBeTruthy();
      expect(screen.getByText('Day Plan')).toBeTruthy();
      expect(screen.getByText('Assets')).toBeTruthy();
    });

    test('should render with default active tab (Progress)', () => {
      renderWithProviders(<SyncDataScreen />);

      expect(screen.getByTestId('progress-card')).toBeTruthy();
      expect(screen.queryByTestId('hindrance-card')).toBeFalsy();
      expect(screen.queryByTestId('day-plan-card')).toBeFalsy();
    });

    test('should render with proper tab styling for active tab', () => {
      renderWithProviders(<SyncDataScreen />);

      const progressTab = screen.getByText('Progress');
      expect(progressTab).toBeTruthy();
    });

    test('should render with proper tab styling for inactive tabs', () => {
      renderWithProviders(<SyncDataScreen />);

      const hindranceTab = screen.getByText('Hindrance');
      const dayPlanTab = screen.getByText('Day Plan');
      const assetsTab = screen.getByText('Assets');

      expect(hindranceTab).toBeTruthy();
      expect(dayPlanTab).toBeTruthy();
      expect(assetsTab).toBeTruthy();
    });

    test('should render with sync button text', () => {
      renderWithProviders(<SyncDataScreen />);

      expect(screen.getByTestId('sync-button')).toBeTruthy();
    });

    test('should render with app header title', () => {
      renderWithProviders(<SyncDataScreen />);

      expect(screen.getByTestId('app-header')).toBeTruthy();
    });

    test('should render with proper container structure', () => {
      renderWithProviders(<SyncDataScreen />);

      expect(screen.getByTestId('app-header')).toBeTruthy();
      expect(screen.getByTestId('sync-button')).toBeTruthy();
    });
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  describe('Interaction Tests', () => {
    test('should handle Progress tab press', () => {
      renderWithProviders(<SyncDataScreen />);

      const progressTab = screen.getByText('Progress');
      fireEvent.press(progressTab);

      expect(screen.getByTestId('progress-card')).toBeTruthy();
      expect(screen.queryByTestId('hindrance-card')).toBeFalsy();
      expect(screen.queryByTestId('day-plan-card')).toBeFalsy();
    });

    test('should handle Hindrance tab press', () => {
      renderWithProviders(<SyncDataScreen />);

      const hindranceTab = screen.getByText('Hindrance');
      fireEvent.press(hindranceTab);

      expect(screen.getByTestId('hindrance-card')).toBeTruthy();
      expect(screen.queryByTestId('progress-card')).toBeFalsy();
      expect(screen.queryByTestId('day-plan-card')).toBeFalsy();
    });

    test('should handle Day Plan tab press', () => {
      renderWithProviders(<SyncDataScreen />);

      const dayPlanTab = screen.getByText('Day Plan');
      fireEvent.press(dayPlanTab);

      expect(screen.getByTestId('day-plan-card')).toBeTruthy();
      expect(screen.queryByTestId('progress-card')).toBeFalsy();
      expect(screen.queryByTestId('hindrance-card')).toBeFalsy();
    });

    test('should handle Assets tab press', () => {
      renderWithProviders(<SyncDataScreen />);

      const assetsTab = screen.getByText('Assets');
      fireEvent.press(assetsTab);

      expect(screen.getByTestId('day-plan-card')).toBeTruthy();
      expect(screen.queryByTestId('progress-card')).toBeFalsy();
      expect(screen.queryByTestId('hindrance-card')).toBeFalsy();
    });

    test('should handle Progress card press', () => {
      renderWithProviders(<SyncDataScreen />);

      const progressCard = screen.getByTestId('progress-card');
      fireEvent.press(progressCard);

      expect(mockNavigation.navigate).toHaveBeenCalledWith('SyncProgressUpdateScreen');
    });

    test('should handle Hindrance card press', () => {
      renderWithProviders(<SyncDataScreen />);

      const hindranceTab = screen.getByText('Hindrance');
      fireEvent.press(hindranceTab);

      const hindranceCard = screen.getByTestId('hindrance-card');
      fireEvent.press(hindranceCard);

      expect(mockNavigation.navigate).toHaveBeenCalledWith('SyncHindranceUpdateScreen');
    });

    test('should handle Day Plan card press', () => {
      renderWithProviders(<SyncDataScreen />);

      const dayPlanTab = screen.getByText('Day Plan');
      fireEvent.press(dayPlanTab);

      const dayPlanCard = screen.getByTestId('day-plan-card');
      fireEvent.press(dayPlanCard);

      expect(mockNavigation.navigate).toHaveBeenCalledWith('SyncDayPlanListScreen');
    });

    test('should handle Assets card press', () => {
      renderWithProviders(<SyncDataScreen />);

      const assetsTab = screen.getByText('Assets');
      fireEvent.press(assetsTab);

      const dayPlanCard = screen.getByTestId('day-plan-card');
      fireEvent.press(dayPlanCard);

      expect(mockNavigation.navigate).toHaveBeenCalledWith('RequestAssetsScreen');
    });

    test('should handle sync button press', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      renderWithProviders(<SyncDataScreen />);

      const syncButton = screen.getByTestId('sync-button');
      fireEvent.press(syncButton);

      expect(consoleSpy).toHaveBeenCalledWith('Syncing data...');
      consoleSpy.mockRestore();
    });

    test('should handle back button press', () => {
      renderWithProviders(<SyncDataScreen />);

      const appHeader = screen.getByTestId('app-header');
      fireEvent.press(appHeader);

      expect(mockNavigation.dispatch).toHaveBeenCalled();
    });

    test('should handle rapid tab switches', () => {
      renderWithProviders(<SyncDataScreen />);

      const progressTab = screen.getByText('Progress');
      const hindranceTab = screen.getByText('Hindrance');
      const dayPlanTab = screen.getByText('Day Plan');
      const assetsTab = screen.getByText('Assets');

      // Rapid tab switches
      fireEvent.press(progressTab);
      fireEvent.press(hindranceTab);
      fireEvent.press(dayPlanTab);
      fireEvent.press(assetsTab);
      fireEvent.press(progressTab);

      expect(screen.getByTestId('progress-card')).toBeTruthy();
    });

    test('should handle rapid card presses', () => {
      renderWithProviders(<SyncDataScreen />);

      const progressCard = screen.getByTestId('progress-card');

      // Rapid card presses
      fireEvent.press(progressCard);
      fireEvent.press(progressCard);
      fireEvent.press(progressCard);

      expect(mockNavigation.navigate).toHaveBeenCalledTimes(3);
    });
  });

  // ============================================================================
  // STATE CHANGE TESTS
  // ============================================================================

  describe('State Change Tests', () => {
    test('should update active tab state when tab is pressed', () => {
      renderWithProviders(<SyncDataScreen />);

      // Initially Progress tab should be active
      expect(screen.getByTestId('progress-card')).toBeTruthy();

      // Switch to Hindrance tab
      const hindranceTab = screen.getByText('Hindrance');
      fireEvent.press(hindranceTab);

      expect(screen.getByTestId('hindrance-card')).toBeTruthy();
      expect(screen.queryByTestId('progress-card')).toBeFalsy();
    });

    test('should maintain active tab state across re-renders', () => {
      const { rerender } = renderWithProviders(<SyncDataScreen />);

      // Switch to Day Plan tab
      const dayPlanTab = screen.getByText('Day Plan');
      fireEvent.press(dayPlanTab);

      expect(screen.getByTestId('day-plan-card')).toBeTruthy();

      // Re-render should maintain state
      rerender(<SyncDataScreen />);

      expect(screen.getByTestId('day-plan-card')).toBeTruthy();
    });

    test('should update tab styling when active tab changes', () => {
      renderWithProviders(<SyncDataScreen />);

      // Initially Progress tab should have active styling
      const progressTab = screen.getByText('Progress');
      const hindranceTab = screen.getByText('Hindrance');

      // Switch to Hindrance tab
      fireEvent.press(hindranceTab);

      // Both tabs should still be rendered
      expect(progressTab).toBeTruthy();
      expect(hindranceTab).toBeTruthy();
    });
  });

  // ============================================================================
  // EDGE CASES
  // ============================================================================

  describe('Edge Cases', () => {
    test('should handle invalid tab index', () => {
      renderWithProviders(<SyncDataScreen />);

      // This test ensures the switch statement handles invalid cases gracefully
      expect(() => renderWithProviders(<SyncDataScreen />)).not.toThrow();
    });

    test('should handle empty progress data', () => {
      renderWithProviders(<SyncDataScreen />);

      // Should render without crashing even with empty data
      expect(screen.getByTestId('progress-card')).toBeTruthy();
    });

    test('should handle missing tab data', () => {
      renderWithProviders(<SyncDataScreen />);

      // Should render without crashing even with missing tab data
      expect(screen.getByTestId('app-header')).toBeTruthy();
    });

    test('should handle rapid state changes', () => {
      renderWithProviders(<SyncDataScreen />);

      const progressTab = screen.getByText('Progress');
      const hindranceTab = screen.getByText('Hindrance');

      // Rapid state changes
      fireEvent.press(progressTab);
      fireEvent.press(hindranceTab);
      fireEvent.press(progressTab);
      fireEvent.press(hindranceTab);

      expect(screen.getByTestId('hindrance-card')).toBeTruthy();
    });

    test('should handle component unmounting during async operations', () => {
      const { unmount } = renderWithProviders(<SyncDataScreen />);

      const syncButton = screen.getByTestId('sync-button');
      fireEvent.press(syncButton);

      // Unmount during sync operation
      unmount();

      // Should not throw any errors
      expect(() => {}).not.toThrow();
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling Tests', () => {
    test('should handle navigation errors gracefully', () => {
      mockNavigation.navigate.mockImplementation(() => {
        throw new Error('Navigation error');
      });

      renderWithProviders(<SyncDataScreen />);

      const progressCard = screen.getByTestId('progress-card');
      fireEvent.press(progressCard);

      expect(() => {}).not.toThrow();
    });

    test('should handle dispatch errors gracefully', () => {
      mockNavigation.dispatch.mockImplementation(() => {
        throw new Error('Dispatch error');
      });

      renderWithProviders(<SyncDataScreen />);

      const appHeader = screen.getByTestId('app-header');
      fireEvent.press(appHeader);

      expect(() => {}).not.toThrow();
    });

    test('should handle console log errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {
        throw new Error('Console error');
      });

      renderWithProviders(<SyncDataScreen />);

      const syncButton = screen.getByTestId('sync-button');
      fireEvent.press(syncButton);

      expect(() => {}).not.toThrow();
      consoleSpy.mockRestore();
    });

    test('should handle flat list ref errors gracefully', () => {
      renderWithProviders(<SyncDataScreen />);

      const progressTab = screen.getByText('Progress');
      fireEvent.press(progressTab);

      // Should not throw errors when flat list ref is accessed
      expect(() => {}).not.toThrow();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    test('should have accessible tab buttons', () => {
      renderWithProviders(<SyncDataScreen />);

      expect(screen.getByText('Progress')).toBeTruthy();
      expect(screen.getByText('Hindrance')).toBeTruthy();
      expect(screen.getByText('Day Plan')).toBeTruthy();
      expect(screen.getByText('Assets')).toBeTruthy();
    });

    test('should have accessible card components', () => {
      renderWithProviders(<SyncDataScreen />);

      expect(screen.getByTestId('progress-card')).toBeTruthy();
    });

    test('should have accessible sync button', () => {
      renderWithProviders(<SyncDataScreen />);

      expect(screen.getByTestId('sync-button')).toBeTruthy();
    });

    test('should have accessible app header', () => {
      renderWithProviders(<SyncDataScreen />);

      expect(screen.getByTestId('app-header')).toBeTruthy();
    });

    test('should have proper testIDs for testing', () => {
      renderWithProviders(<SyncDataScreen />);

      expect(screen.getByTestId('app-header')).toBeTruthy();
      expect(screen.getByTestId('sync-button')).toBeTruthy();
      expect(screen.getByTestId('progress-card')).toBeTruthy();
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    test('should render efficiently without unnecessary re-renders', () => {
      const { rerender } = renderWithProviders(<SyncDataScreen />);

      // Re-render multiple times
      rerender(<SyncDataScreen />);
      rerender(<SyncDataScreen />);
      rerender(<SyncDataScreen />);

      expect(screen.getByTestId('app-header')).toBeTruthy();
      expect(screen.getByTestId('sync-button')).toBeTruthy();
    });

    test('should handle rapid tab switches efficiently', () => {
      renderWithProviders(<SyncDataScreen />);

      const progressTab = screen.getByText('Progress');
      const hindranceTab = screen.getByText('Hindrance');
      const dayPlanTab = screen.getByText('Day Plan');
      const assetsTab = screen.getByText('Assets');

      // Rapid tab switches
      fireEvent.press(progressTab);
      fireEvent.press(hindranceTab);
      fireEvent.press(dayPlanTab);
      fireEvent.press(assetsTab);
      fireEvent.press(progressTab);
      fireEvent.press(hindranceTab);

      expect(screen.getByTestId('hindrance-card')).toBeTruthy();
    });

    test('should handle rapid card interactions efficiently', () => {
      renderWithProviders(<SyncDataScreen />);

      const progressCard = screen.getByTestId('progress-card');

      // Rapid card interactions
      fireEvent.press(progressCard);
      fireEvent.press(progressCard);
      fireEvent.press(progressCard);
      fireEvent.press(progressCard);
      fireEvent.press(progressCard);

      expect(mockNavigation.navigate).toHaveBeenCalledTimes(5);
    });

    test('should handle large data sets efficiently', () => {
      renderWithProviders(<SyncDataScreen />);

      // Should render efficiently even with multiple tabs and components
      expect(screen.getByTestId('app-header')).toBeTruthy();
      expect(screen.getByTestId('sync-button')).toBeTruthy();
      expect(screen.getByTestId('progress-card')).toBeTruthy();
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should work with Redux store integration', () => {
      renderWithProviders(<SyncDataScreen />);

      expect(screen.getByTestId('app-header')).toBeTruthy();
      expect(screen.getByTestId('sync-button')).toBeTruthy();
    });

    test('should work with navigation container', () => {
      renderWithProviders(<SyncDataScreen />);

      expect(screen.getByTestId('app-header')).toBeTruthy();
    });

    test('should integrate with all child components', () => {
      renderWithProviders(<SyncDataScreen />);

      expect(screen.getByTestId('app-header')).toBeTruthy();
      expect(screen.getByTestId('sync-button')).toBeTruthy();
      expect(screen.getByTestId('progress-card')).toBeTruthy();
    });

    test('should integrate with translation system', () => {
      renderWithProviders(<SyncDataScreen />);

      expect(screen.getByTestId('sync-button')).toBeTruthy();
    });

    test('should integrate with navigation system', () => {
      renderWithProviders(<SyncDataScreen />);

      const progressCard = screen.getByTestId('progress-card');
      fireEvent.press(progressCard);

      expect(mockNavigation.navigate).toHaveBeenCalledWith('SyncProgressUpdateScreen');
    });

    test('should integrate with back navigation', () => {
      renderWithProviders(<SyncDataScreen />);

      const appHeader = screen.getByTestId('app-header');
      fireEvent.press(appHeader);

      expect(mockNavigation.dispatch).toHaveBeenCalled();
    });

    test('should integrate with tab navigation system', () => {
      renderWithProviders(<SyncDataScreen />);

      const hindranceTab = screen.getByText('Hindrance');
      fireEvent.press(hindranceTab);

      expect(screen.getByTestId('hindrance-card')).toBeTruthy();
    });
  });

  // ============================================================================
  // SNAPSHOT TESTS
  // ============================================================================

  describe('Snapshot Tests', () => {
    test('should match snapshot for initial render', () => {
      const { toJSON } = renderWithProviders(<SyncDataScreen />);
      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot with Progress tab active', () => {
      const { toJSON } = renderWithProviders(<SyncDataScreen />);
      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot with Hindrance tab active', () => {
      const { toJSON } = renderWithProviders(<SyncDataScreen />);

      const hindranceTab = screen.getByText('Hindrance');
      fireEvent.press(hindranceTab);

      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot with Day Plan tab active', () => {
      const { toJSON } = renderWithProviders(<SyncDataScreen />);

      const dayPlanTab = screen.getByText('Day Plan');
      fireEvent.press(dayPlanTab);

      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot with Assets tab active', () => {
      const { toJSON } = renderWithProviders(<SyncDataScreen />);

      const assetsTab = screen.getByText('Assets');
      fireEvent.press(assetsTab);

      expect(toJSON()).toMatchSnapshot();
    });
  });
}); 