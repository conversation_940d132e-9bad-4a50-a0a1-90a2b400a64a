import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { renderWithProviders, setupTest, teardownTest } from '../../../utils/testing/testHelpers';
import MainScreen from '../../../screens/Main/MainScreen';

// Mock dependencies
jest.mock('../../../utils/DataStorage/Storage', () => ({
  clearAllData: jest.fn(),
  getUserRolesInfo: jest.fn(),
}));

jest.mock('../../../redux/AuthRedux/Logout/LogoutAction', () => ({
  logoutRequest: jest.fn(),
}));

jest.mock('../../../utils/Strings/Strings', () => ({
  bottomTabs: {
    home: 'Home',
    reports: 'Reports',
    gisMap: 'GIS Map',
    profile: 'Profile',
  },
}));

const mockNavigation = {
  dispatch: jest.fn(),
};

jest.mock('@react-navigation/native', () => ({
  useNavigation: () => mockNavigation,
  CommonActions: {
    reset: jest.fn((config) => config),
  },
}));

// Mock components
jest.mock('../../../components/LogOut', () => {
  return function MockLogout(props: any) {
    return props.visible ? <div data-testid="logout-modal" {...props} /> : null;
  };
});

jest.mock('../../../screens/Home/HomeScreen', () => {
  return function MockHomeScreen() {
    return <div data-testid="home-screen" />;
  };
});

// Mock SVG components
jest.mock('../../../assets/svg/Home.svg', () => {
  return function MockHomeIcon(props: any) {
    return <div data-testid="home-icon" {...props} />;
  };
});

jest.mock('../../../assets/svg/Report.svg', () => {
  return function MockReportIcon(props: any) {
    return <div data-testid="report-icon" {...props} />;
  };
});

jest.mock('../../../assets/svg/GISMap.svg', () => {
  return function MockGISMapIcon(props: any) {
    return <div data-testid="gis-map-icon" {...props} />;
  };
});

jest.mock('../../../assets/svg/default_user.svg', () => {
  return function MockProfileIcon(props: any) {
    return <div data-testid="profile-icon" {...props} />;
  };
});

describe('MainScreen', () => {
  beforeEach(() => {
    setupTest();
    jest.clearAllMocks();
  });

  afterEach(() => {
    teardownTest();
  });

  // ============================================================================
  // RENDER TESTS
  // ============================================================================

  describe('Render Tests', () => {
    test('should render correctly with Site Engineer role', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
        expect(screen.getByTestId('tab-Home')).toBeTruthy();
        expect(screen.getByTestId('tab-Reports')).toBeTruthy();
        expect(screen.getByTestId('tab-GIS Map')).toBeTruthy();
        expect(screen.getByTestId('tab-Profile')).toBeTruthy();
      });
    });

    test('should render correctly with non-Site Engineer role', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Approver' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
        expect(screen.queryByTestId('tab-Home')).toBeFalsy();
        expect(screen.queryByTestId('tab-Reports')).toBeFalsy();
        expect(screen.queryByTestId('tab-GIS Map')).toBeFalsy();
        expect(screen.queryByTestId('tab-Profile')).toBeFalsy();
      });
    });

    test('should render correctly with no role', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue(null);

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
        expect(screen.queryByTestId('tab-Home')).toBeFalsy();
      });
    });

    test('should render correctly with empty roles list', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({ RolesList: [] });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
        expect(screen.queryByTestId('tab-Home')).toBeFalsy();
      });
    });

    test('should render with proper tab icons', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-icon')).toBeTruthy();
        expect(screen.getByTestId('report-icon')).toBeTruthy();
        expect(screen.getByTestId('gis-map-icon')).toBeTruthy();
        expect(screen.getByTestId('profile-icon')).toBeTruthy();
      });
    });

    test('should render without activity indicator initially', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.queryByTestId('activity-indicator')).toBeFalsy();
      });
    });

    test('should render without logout modal initially', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.queryByTestId('logout-modal')).toBeFalsy();
      });
    });
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  describe('Interaction Tests', () => {
    test('should handle Home tab press', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const homeTab = screen.getByTestId('tab-Home');
        fireEvent.press(homeTab);
      });

      // Should remain on home screen
      expect(screen.getByTestId('home-screen')).toBeTruthy();
    });

    test('should handle Reports tab press', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const reportsTab = screen.getByTestId('tab-Reports');
        fireEvent.press(reportsTab);
      });

      // Should remain on home screen (default case)
      expect(screen.getByTestId('home-screen')).toBeTruthy();
    });

    test('should handle GIS Map tab press', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const gisMapTab = screen.getByTestId('tab-GIS Map');
        fireEvent.press(gisMapTab);
      });

      // Should remain on home screen (default case)
      expect(screen.getByTestId('home-screen')).toBeTruthy();
    });

    test('should handle Profile tab press to show logout modal', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const profileTab = screen.getByTestId('tab-Profile');
        fireEvent.press(profileTab);
      });

      expect(screen.getByTestId('logout-modal')).toBeTruthy();
    });

    test('should handle logout confirmation', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      const { clearAllData } = require('../../../utils/DataStorage/Storage');
      const { logoutRequest } = require('../../../redux/AuthRedux/Logout/LogoutAction');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const profileTab = screen.getByTestId('tab-Profile');
        fireEvent.press(profileTab);
      });

      const logoutModal = screen.getByTestId('logout-modal');
      fireEvent(logoutModal, 'onConfirm');

      await waitFor(() => {
        expect(clearAllData).toHaveBeenCalled();
        expect(logoutRequest).toHaveBeenCalled();
        expect(mockNavigation.dispatch).toHaveBeenCalled();
      });
    });

    test('should handle logout cancellation', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const profileTab = screen.getByTestId('tab-Profile');
        fireEvent.press(profileTab);
      });

      const logoutModal = screen.getByTestId('logout-modal');
      fireEvent(logoutModal, 'onCancel');

      expect(screen.queryByTestId('logout-modal')).toBeFalsy();
    });

    test('should handle logout modal close', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const profileTab = screen.getByTestId('tab-Profile');
        fireEvent.press(profileTab);
      });

      const logoutModal = screen.getByTestId('logout-modal');
      fireEvent(logoutModal, 'onClose');

      expect(screen.queryByTestId('logout-modal')).toBeFalsy();
    });

    test('should handle rapid tab presses', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const homeTab = screen.getByTestId('tab-Home');
        const reportsTab = screen.getByTestId('tab-Reports');
        const gisMapTab = screen.getByTestId('tab-GIS Map');

        fireEvent.press(homeTab);
        fireEvent.press(reportsTab);
        fireEvent.press(gisMapTab);
      });

      expect(screen.getByTestId('home-screen')).toBeTruthy();
    });
  });

  // ============================================================================
  // STATE CHANGE TESTS
  // ============================================================================

  describe('State Change Tests', () => {
    test('should update selected tab state', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const reportsTab = screen.getByTestId('tab-Reports');
        fireEvent.press(reportsTab);
      });

      // State should be updated (though UI remains the same due to default case)
      expect(screen.getByTestId('home-screen')).toBeTruthy();
    });

    test('should update logout modal state', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const profileTab = screen.getByTestId('tab-Profile');
        fireEvent.press(profileTab);
      });

      expect(screen.getByTestId('logout-modal')).toBeTruthy();
    });

    test('should update loading state during logout', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const profileTab = screen.getByTestId('tab-Profile');
        fireEvent.press(profileTab);
      });

      const logoutModal = screen.getByTestId('logout-modal');
      fireEvent(logoutModal, 'onConfirm');

      await waitFor(() => {
        expect(screen.getByTestId('activity-indicator')).toBeTruthy();
      });
    });

    test('should update user role state on mount', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('tab-Home')).toBeTruthy();
      });
    });
  });

  // ============================================================================
  // EDGE CASES
  // ============================================================================

  describe('Edge Cases', () => {
    test('should handle undefined user role', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: undefined }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
        expect(screen.queryByTestId('tab-Home')).toBeFalsy();
      });
    });

    test('should handle null user role', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        // Simulate missing property instead of null to avoid undefined error
        RolesList: [{}],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
        expect(screen.queryByTestId('tab-Home')).toBeFalsy();
      });
    });

    test('should handle storage function errors', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockRejectedValue(new Error('Storage error'));

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
        expect(screen.queryByTestId('tab-Home')).toBeFalsy();
      });
    });

    test('should handle missing roles list', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({});

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
        expect(screen.queryByTestId('tab-Home')).toBeFalsy();
      });
    });

    test('should handle empty roles list', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({ RolesList: [] });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
        expect(screen.queryByTestId('tab-Home')).toBeFalsy();
      });
    });

    test('should handle multiple roles in list', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [
          { Functional_ROLE: 'Site Engineer' },
          { Functional_ROLE: 'Approver' },
        ],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
        expect(screen.getByTestId('tab-Home')).toBeTruthy();
      });
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling Tests', () => {
    test('should handle navigation dispatch errors', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      const { clearAllData } = require('../../../utils/DataStorage/Storage');
      const { logoutRequest } = require('../../../redux/AuthRedux/Logout/LogoutAction');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      mockNavigation.dispatch.mockImplementation(() => {
        throw new Error('Navigation error');
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const profileTab = screen.getByTestId('tab-Profile');
        fireEvent.press(profileTab);
      });

      const logoutModal = screen.getByTestId('logout-modal');
      fireEvent(logoutModal, 'onConfirm');

      await waitFor(() => {
        expect(clearAllData).toHaveBeenCalled();
        expect(logoutRequest).toHaveBeenCalled();
      });
    });

    test('should handle dispatch errors', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      const { logoutRequest } = require('../../../redux/AuthRedux/Logout/LogoutAction');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      logoutRequest.mockImplementation(() => {
        throw new Error('Dispatch error');
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const profileTab = screen.getByTestId('tab-Profile');
        fireEvent.press(profileTab);
      });

      const logoutModal = screen.getByTestId('logout-modal');
      fireEvent(logoutModal, 'onConfirm');

      await waitFor(() => {
        expect(logoutRequest).toHaveBeenCalled();
      });
    });

    test('should handle storage errors gracefully', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockRejectedValue(new Error('Storage error'));

      expect(() => renderWithProviders(<MainScreen />)).not.toThrow();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    test('should have accessible tab buttons', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('tab-Home')).toBeTruthy();
        expect(screen.getByTestId('tab-Reports')).toBeTruthy();
        expect(screen.getByTestId('tab-GIS Map')).toBeTruthy();
        expect(screen.getByTestId('tab-Profile')).toBeTruthy();
      });
    });

    test('should have accessible logout modal', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const profileTab = screen.getByTestId('tab-Profile');
        fireEvent.press(profileTab);
      });

      expect(screen.getByTestId('logout-modal')).toBeTruthy();
    });

    test('should have proper testIDs for testing', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
        expect(screen.getByTestId('tab-Home')).toBeTruthy();
        expect(screen.getByTestId('activity-indicator')).toBeFalsy();
      });
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    test('should render efficiently without unnecessary re-renders', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      const { rerender } = renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
      });

      // Re-render multiple times
      rerender(<MainScreen />);
      rerender(<MainScreen />);
      rerender(<MainScreen />);

      expect(screen.getByTestId('home-screen')).toBeTruthy();
    });

    test('should handle rapid tab switches efficiently', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const homeTab = screen.getByTestId('tab-Home');
        const reportsTab = screen.getByTestId('tab-Reports');
        const gisMapTab = screen.getByTestId('tab-GIS Map');

        // Rapid tab switches
        fireEvent.press(homeTab);
        fireEvent.press(reportsTab);
        fireEvent.press(gisMapTab);
        fireEvent.press(homeTab);
        fireEvent.press(reportsTab);
      });

      expect(screen.getByTestId('home-screen')).toBeTruthy();
    });

    test('should handle rapid logout attempts efficiently', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const profileTab = screen.getByTestId('tab-Profile');

        // Rapid logout attempts
        fireEvent.press(profileTab);
        fireEvent.press(profileTab);
        fireEvent.press(profileTab);
      });

      expect(screen.getByTestId('logout-modal')).toBeTruthy();
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should work with Redux store integration', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
      });
    });

    test('should work with navigation container', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
      });
    });

    test('should integrate with storage utilities', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(Storage.getUserRolesInfo).toHaveBeenCalled();
      });
    });

    test('should integrate with logout flow', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      const { clearAllData } = require('../../../utils/DataStorage/Storage');
      const { logoutRequest } = require('../../../redux/AuthRedux/Logout/LogoutAction');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const profileTab = screen.getByTestId('tab-Profile');
        fireEvent.press(profileTab);
      });

      const logoutModal = screen.getByTestId('logout-modal');
      fireEvent(logoutModal, 'onConfirm');

      await waitFor(() => {
        expect(clearAllData).toHaveBeenCalled();
        expect(logoutRequest).toHaveBeenCalled();
        expect(mockNavigation.dispatch).toHaveBeenCalled();
      });
    });

    test('should integrate with HomeScreen component', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(screen.getByTestId('home-screen')).toBeTruthy();
      });
    });
  });

  // ============================================================================
  // SNAPSHOT TESTS
  // ============================================================================

  describe('Snapshot Tests', () => {
    test('should match snapshot for Site Engineer role', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      const { toJSON } = renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(toJSON()).toMatchSnapshot();
      });
    });

    test('should match snapshot for non-Site Engineer role', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Approver' }],
      });

      const { toJSON } = renderWithProviders(<MainScreen />);

      await waitFor(() => {
        expect(toJSON()).toMatchSnapshot();
      });
    });

    test('should match snapshot with logout modal open', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      const { toJSON } = renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const profileTab = screen.getByTestId('tab-Profile');
        fireEvent.press(profileTab);
      });

      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot with loading overlay', async () => {
      const Storage = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserRolesInfo.mockResolvedValue({
        RolesList: [{ Functional_ROLE: 'Site Engineer' }],
      });

      const { toJSON } = renderWithProviders(<MainScreen />);

      await waitFor(() => {
        const profileTab = screen.getByTestId('tab-Profile');
        fireEvent.press(profileTab);
      });

      const logoutModal = screen.getByTestId('logout-modal');
      fireEvent(logoutModal, 'onConfirm');

      await waitFor(() => {
        expect(toJSON()).toMatchSnapshot();
      });
    });
  });
}); 