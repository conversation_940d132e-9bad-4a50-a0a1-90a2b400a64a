import React from 'react';
import GapButton from '../../../../screens/Hindrance/HindranceComponents/GapButton';

// Mock React Native components completely
jest.mock('react-native', () => {
  const mockComponent = (name: string) => ({ children, ...props }: any) => {
    const React = require('react');
    return React.createElement(name, props, children);
  };

  return {
    View: mockComponent('View'),
    Text: mockComponent('Text'),
    Pressable: mockComponent('Pressable'),
    StyleSheet: {
      create: (styles: any) => styles,
    },
  };
});

// Mock utils
jest.mock('../../../../utils/Colors/Colors', () => ({
  white: '#FFFFFF',
  secondary: '#FF6B35',
  textPrimary: '#000000',
}));

jest.mock('../../../../utils/Scale/Scaling', () => ({
  ms: (size: number) => size,
}));

jest.mock('../../../../components/Fonts', () => ({
  AppFonts: {
    SemiBold: 'Manrope-SemiBold',
  },
}));

describe('GapButton Component', () => {
  const defaultProps = {
    label: 'Test Button',
    icon: React.createElement('View', { testID: 'test-icon' }),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Test 1: Component renders successfully
  test('renders without crashing', () => {
    expect(() => GapButton(defaultProps)).not.toThrow();
  });

  // Test 2: Renders with correct structure
  test('renders with correct structure', () => {
    const component = GapButton(defaultProps);
    expect(component).toBeDefined();
    expect(typeof component.type).toBe('function');
    
    // Should have icon and text as children
    expect(component.props.children).toHaveLength(2);
    expect(component.props.children[0]).toBe(defaultProps.icon); // icon
    expect(typeof component.props.children[1].type).toBe('function'); // text
  });

  // Test 3: Renders label correctly
  test('renders label correctly', () => {
    const testLabel = 'My Test Label';
    const propsWithLabel = {
      ...defaultProps,
      label: testLabel,
    };
    
    const component = GapButton(propsWithLabel);
    const textComponent = component.props.children[1];
    expect(textComponent.props.children).toBe(testLabel);
  });

  // Test 4: Renders icon correctly
  test('renders icon correctly', () => {
    const testIcon = React.createElement('View', { testID: 'custom-icon' });
    const propsWithIcon = {
      ...defaultProps,
      icon: testIcon,
    };
    
    const component = GapButton(propsWithIcon);
    expect(component.props.children[0]).toBe(testIcon);
  });

  // Test 5: Handles press event when onPress is provided
  test('handles press event when onPress is provided', () => {
    const mockOnPress = jest.fn();
    const propsWithPress = {
      ...defaultProps,
      onPress: mockOnPress,
    };
    
    const component = GapButton(propsWithPress);
    
    // Simulate press
    component.props.onPress();
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  // Test 6: Handles missing onPress gracefully
  test('handles missing onPress gracefully', () => {
    const component = GapButton(defaultProps);
    
    // Should not crash when onPress is undefined
    if (component.props.onPress) {
      expect(() => component.props.onPress()).not.toThrow();
    } else {
      expect(component.props.onPress).toBeUndefined();
    }
  });

  // Test 7: Renders unselected state correctly
  test('renders unselected state correctly', () => {
    const component = GapButton(defaultProps);
    
    // Check if normal styles are applied (not selected styles)
    const pressableStyle = component.props.style;
    expect(pressableStyle).toBeDefined();
    
    const textComponent = component.props.children[1];
    const textStyle = textComponent.props.style;
    expect(textStyle).toBeDefined();
  });

  // Test 8: Renders selected state correctly
  test('renders selected state correctly', () => {
    const selectedProps = {
      ...defaultProps,
      isSelected: true,
    };
    
    const component = GapButton(selectedProps);
    
    // Check if selected styles are applied
    const pressableStyle = component.props.style;
    expect(pressableStyle).toBeDefined();
    
    const textComponent = component.props.children[1];
    const textStyle = textComponent.props.style;
    expect(textStyle).toBeDefined();
  });

  // Test 9: Multiple press events
  test('handles multiple press events', () => {
    const mockOnPress = jest.fn();
    const propsWithPress = {
      ...defaultProps,
      onPress: mockOnPress,
    };
    
    const component = GapButton(propsWithPress);
    
    // Simulate multiple presses
    component.props.onPress();
    component.props.onPress();
    component.props.onPress();
    
    expect(mockOnPress).toHaveBeenCalledTimes(3);
  });

  // Test 10: Component styling structure
  test('has correct styling structure', () => {
    const component = GapButton(defaultProps);
    
    // Check main Pressable style
    expect(component.props.style).toBeDefined();
    
    // Check text has correct style
    const textComponent = component.props.children[1];
    expect(textComponent.props.style).toBeDefined();
  });

  // Test 11: Selected vs unselected styling
  test('applies different styling for selected vs unselected states', () => {
    // Test unselected
    const unselectedComponent = GapButton({ ...defaultProps, isSelected: false });
    const unselectedStyle = unselectedComponent.props.style;
    const unselectedTextStyle = unselectedComponent.props.children[1].props.style;
    
    // Test selected
    const selectedComponent = GapButton({ ...defaultProps, isSelected: true });
    const selectedStyle = selectedComponent.props.style;
    const selectedTextStyle = selectedComponent.props.children[1].props.style;
    
    // Styles should be different between selected and unselected
    expect(unselectedStyle).toBeDefined();
    expect(selectedStyle).toBeDefined();
    expect(unselectedTextStyle).toBeDefined();
    expect(selectedTextStyle).toBeDefined();
  });

  // Test 12: Different label values
  test('handles different label values', () => {
    const labels = ['Short', 'Very Long Button Label', '', 'Label with numbers 123'];
    
    labels.forEach(label => {
      const component = GapButton({ ...defaultProps, label });
      const textComponent = component.props.children[1];
      expect(textComponent.props.children).toBe(label);
    });
  });

  // Test 13: Different icon types
  test('handles different icon types', () => {
    const icons = [
      React.createElement('View', { testID: 'view-icon' }),
      React.createElement('Text', { children: '🔍' }),
      null,
    ];
    
    icons.forEach(icon => {
      const component = GapButton({ ...defaultProps, icon });
      expect(component.props.children[0]).toBe(icon);
    });
  });

  // Test 14: Component with all props
  test('renders with all props correctly', () => {
    const allProps = {
      label: 'Complete Button',
      icon: React.createElement('View', { testID: 'all-props-icon' }),
      onPress: jest.fn(),
      isSelected: true,
    };
    
    const component = GapButton(allProps);
    expect(component).toBeDefined();
    
    // Check all elements are present
    expect(component.props.children[0]).toBe(allProps.icon);
    expect(component.props.children[1].props.children).toBe(allProps.label);
    expect(component.props.onPress).toBe(allProps.onPress);
  });

  // Test 15: Text component properties
  test('text component has correct properties', () => {
    const component = GapButton(defaultProps);
    const textComponent = component.props.children[1];
    
    expect(typeof textComponent.type).toBe('function');
    expect(textComponent.props.style).toBeDefined();
    expect(textComponent.props.children).toBe(defaultProps.label);
  });

  // Test 16: Component reusability
  test('demonstrates component reusability', () => {
    const button1Props = {
      label: 'Button 1',
      icon: React.createElement('View', { testID: 'icon1' }),
      onPress: jest.fn(),
      isSelected: false,
    };
    
    const button2Props = {
      label: 'Button 2',
      icon: React.createElement('View', { testID: 'icon2' }),
      onPress: jest.fn(),
      isSelected: true,
    };
    
    const button1 = GapButton(button1Props);
    const button2 = GapButton(button2Props);
    
    // Test first button
    button1.props.onPress();
    expect(button1Props.onPress).toHaveBeenCalledTimes(1);
    
    // Test second button
    button2.props.onPress();
    expect(button2Props.onPress).toHaveBeenCalledTimes(1);
    
    // Ensure they're independent
    expect(button1Props.onPress).toHaveBeenCalledTimes(1);
    expect(button2Props.onPress).toHaveBeenCalledTimes(1);
  });

  // Test 17: Component consistency across renders
  test('maintains component consistency across renders', () => {
    const component1 = GapButton(defaultProps);
    const component2 = GapButton(defaultProps);
    
    // Both should have the same structure
    expect(component1.type).toBe(component2.type);
    expect(component1.props.children.length).toBe(component2.props.children.length);
  });

  // Test 18: Props validation
  test('handles all prop combinations correctly', () => {
    const propCombinations = [
      { label: 'Test', icon: React.createElement('View') },
      { label: 'Test', icon: React.createElement('View'), onPress: jest.fn() },
      { label: 'Test', icon: React.createElement('View'), isSelected: true },
      { label: 'Test', icon: React.createElement('View'), onPress: jest.fn(), isSelected: false },
    ];
    
    propCombinations.forEach(props => {
      expect(() => GapButton(props)).not.toThrow();
      
      const component = GapButton(props);
      expect(component).toBeDefined();
    });
  });

  // Test 19: Component type checking
  test('ensures correct component types', () => {
    const component = GapButton(defaultProps);
    
    expect(typeof component).toBe('object');
    expect(typeof component.type).toBe('function');
    expect(Array.isArray(component.props.children)).toBe(true);
    expect(typeof component.props.children[1].type).toBe('function');
  });

  // Test 20: Edge cases and realistic scenarios
  test('handles edge cases and realistic scenarios', () => {
    // Test with empty label
    const emptyLabelProps = {
      ...defaultProps,
      label: '',
    };
    expect(() => GapButton(emptyLabelProps)).not.toThrow();
    
    // Test with complex icon
    const complexIcon = React.createElement('View', {
      children: [
        React.createElement('Text', { key: 1, children: 'Icon' }),
        React.createElement('View', { key: 2 }),
      ]
    });
    
    const complexProps = {
      label: 'Complex Button',
      icon: complexIcon,
      onPress: jest.fn(),
      isSelected: true,
    };
    
    expect(() => GapButton(complexProps)).not.toThrow();
    
    const component = GapButton(complexProps);
    expect(component.props.children[0]).toBe(complexIcon);
    
    // Test realistic button scenario
    const realisticProps = {
      label: 'Open Gap',
      icon: React.createElement('View', { testID: 'gap-icon' }),
      onPress: jest.fn(() => console.log('Gap button pressed')),
      isSelected: false,
    };
    
    const realisticComponent = GapButton(realisticProps);
    realisticComponent.props.onPress();
    expect(realisticProps.onPress).toHaveBeenCalled();
  });
}); 