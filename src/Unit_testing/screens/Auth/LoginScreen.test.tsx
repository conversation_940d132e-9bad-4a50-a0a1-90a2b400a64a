import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { renderWithProviders, setupTest, teardownTest } from '../../../utils/testing/testHelpers';
import LoginScreen from '../../../screens/Auth/LoginScreen';

// Mock Scaling utility to avoid Dimensions issues
jest.mock('../../../utils/Scale/Scaling', () => ({
  width: 375,
  height: 812,
  scale: jest.fn((size) => size),
  verticalScale: jest.fn((size) => size),
  ms: jest.fn((size) => size),
  moderateVerticalScale: jest.fn((size) => size),
}));

// Mock dependencies
jest.mock('../../../utils/DataStorage/Storage', () => ({
  getUserInfo: jest.fn(),
  getUserRolesInfo: jest.fn(),
  removeUserInfo: jest.fn(),
  saveUserInfo: jest.fn(),
  saveUserRolesInfo: jest.fn(),
  setUserLastLoggedInDate: jest.fn(),
}));

jest.mock('../../../redux/AuthRedux/Token/TokenActions', () => ({
  validateTokenRequest: jest.fn(),
}));

jest.mock('../../../redux/AuthRedux/Login/LoginActions', () => ({
  loginRequest: jest.fn(),
}));

jest.mock('../../../redux/WBSDownload/WBSActions', () => ({
  wbsRequest: jest.fn(),
  getPendingApproverWbsRequest: jest.fn(),
}));

jest.mock('../../../redux/RolesRedux/RolesActions', () => ({
  rolesRequest: jest.fn(),
}));

jest.mock('../../../utils/Network/NetworkConnection', () => ({
  isNetworkConnected: jest.fn(),
}));

jest.mock('../../../utils/Logger/PrintLog', () => ({
  debug: jest.fn(),
  error: jest.fn(),
}));

jest.mock('../../../utils/Crypto/Encryption', () => ({
  encryptString: jest.fn((text) => `encrypted_${text}`),
}));

jest.mock('react-native-device-info', () => ({
  getUniqueId: jest.fn(() => Promise.resolve('test-device-id')),
}));

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({ bottom: 0, top: 0, left: 0, right: 0 }),
}));

jest.mock('../../../utils/Constants/Validations', () => ({
  removeEmojis: jest.fn((text) => text),
}));

jest.mock('../../../utils/Strings/Strings', () => ({
  loginScreen: {
    title: 'Login',
    usernamePlaceholder: 'Username',
    passwordPlaceholder: 'Password',
    credsErrorMsg: 'Invalid credentials',
    credtsCommonErrorMsg: 'Please enter username and password',
    userIdErrorMsg: 'Please enter username',
    passwordErrorMsg: 'Please enter password',
    approver: 'approver',
  },
}));

jest.mock('../../../utils/Constants/CommonConstant', () => ({
  companyCode: 'TEST_COMPANY',
  appCode: 123,
}));

jest.mock('dayjs', () => {
  const originalDayjs = jest.requireActual('dayjs');
  return {
    ...originalDayjs,
    __esModule: true,
    default: jest.fn(() => ({
      format: jest.fn(() => '2025-01-15'),
    })),
  };
});

jest.mock('i18next', () => ({
  t: jest.fn((key) => key),
}));

const mockNavigation = {
  reset: jest.fn(),
};

jest.mock('@react-navigation/native', () => ({
  useNavigation: () => mockNavigation,
}));

// Mock components
jest.mock('../../../components/TextInputComponent', () => {
  return function MockTextInput(props: any) {
    return (
      <div
        data-testid={`text-input-${props.label}`}
        onClick={() => props.onChangeText && props.onChangeText('test')}
        {...props}
      />
    );
  };
});

jest.mock('../../../components/ButtonComponent', () => {
  return function MockButton(props: any) {
    return (
      <div
        data-testid={props.testID || 'button'}
        onClick={props.onPress}
        {...props}
      />
    );
  };
});

jest.mock('../../../components/LoadingOverlay', () => {
  return function MockLoadingOverlay(props: any) {
    return props.visible ? <div data-testid="loading-overlay" {...props} /> : null;
  };
});

jest.mock('../../../assets/svg/app_logo.svg', () => {
  return function MockAppLogo(props: any) {
    return <div data-testid="app-logo" {...props} />;
  };
});

describe('LoginScreen', () => {
  beforeEach(() => {
    setupTest();
    jest.clearAllMocks();
  });

  afterEach(() => {
    teardownTest();
  });

  // ============================================================================
  // RENDER TESTS
  // ============================================================================

  describe('Render Tests', () => {
    test('should render correctly with all components', () => {
      renderWithProviders(<LoginScreen />);

      expect(screen.getByTestId('login-screen')).toBeTruthy();
      expect(screen.getByTestId('app-logo')).toBeTruthy();
      expect(screen.getByText('Login')).toBeTruthy();
      expect(screen.getByTestId('text-input-Username')).toBeTruthy();
      expect(screen.getByTestId('text-input-Password')).toBeTruthy();
      expect(screen.getByTestId('LoginButton')).toBeTruthy();
    });

    test('should render with background image', () => {
      renderWithProviders(<LoginScreen />);

      const loginScreen = screen.getByTestId('login-screen');
      expect(loginScreen).toBeTruthy();
    });

    test('should render without error messages initially', () => {
      renderWithProviders(<LoginScreen />);

      expect(screen.queryByText('Invalid credentials')).toBeFalsy();
      expect(screen.queryByText('Please enter username and password')).toBeFalsy();
    });

    test('should render with proper styling', () => {
      renderWithProviders(<LoginScreen />);

      expect(screen.getByTestId('login-screen')).toBeTruthy();
      expect(screen.getByTestId('app-logo')).toBeTruthy();
    });

    test('should render with keyboard avoiding view', () => {
      renderWithProviders(<LoginScreen />);

      expect(screen.getByTestId('login-screen')).toBeTruthy();
    });

    test('should render with safe area view', () => {
      renderWithProviders(<LoginScreen />);

      expect(screen.getByTestId('login-screen')).toBeTruthy();
    });
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  describe('Interaction Tests', () => {
    test('should handle username input change', () => {
      const { removeEmojis } = require('../../../utils/Constants/Validations');
      
      renderWithProviders(<LoginScreen />);

      const usernameInput = screen.getByTestId('text-input-Username');
      fireEvent.press(usernameInput);

      expect(removeEmojis).toHaveBeenCalled();
    });

    test('should handle password input change', () => {
      const { removeEmojis } = require('../../../utils/Constants/Validations');
      
      renderWithProviders(<LoginScreen />);

      const passwordInput = screen.getByTestId('text-input-Password');
      fireEvent.press(passwordInput);

      expect(removeEmojis).toHaveBeenCalled();
    });

    test('should handle login button press with valid credentials', async () => {
      const { isNetworkConnected } = require('../../../utils/Network/NetworkConnection');
      const { loginRequest } = require('../../../redux/AuthRedux/Login/LoginActions');
      const { encryptString } = require('../../../utils/Crypto/Encryption');
      
      isNetworkConnected.mockResolvedValue(true);

      renderWithProviders(<LoginScreen />);

      // Simulate input changes
      const usernameInput = screen.getByTestId('text-input-Username');
      const passwordInput = screen.getByTestId('text-input-Password');
      
      fireEvent.press(usernameInput);
      fireEvent.press(passwordInput);

      // Press login button
      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(encryptString).toHaveBeenCalled();
        expect(loginRequest).toHaveBeenCalled();
      });
    });

    test('should handle login button press with empty credentials', async () => {
      renderWithProviders(<LoginScreen />);

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter username and password')).toBeTruthy();
      });
    });

    test('should handle login button press with empty username', async () => {
      renderWithProviders(<LoginScreen />);

      // Only set password
      const passwordInput = screen.getByTestId('text-input-Password');
      fireEvent.press(passwordInput);

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter username')).toBeTruthy();
      });
    });

    test('should handle login button press with empty password', async () => {
      renderWithProviders(<LoginScreen />);

      // Only set username
      const usernameInput = screen.getByTestId('text-input-Username');
      fireEvent.press(usernameInput);

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter password')).toBeTruthy();
      });
    });

    test('should handle network disconnection during login', async () => {
      const { isNetworkConnected } = require('../../../utils/Network/NetworkConnection');
      
      isNetworkConnected.mockResolvedValue(false);

      renderWithProviders(<LoginScreen />);

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(isNetworkConnected).toHaveBeenCalled();
      });
    });

    test('should dismiss keyboard on login button press', () => {
      const Keyboard = require('react-native').Keyboard;
      
      renderWithProviders(<LoginScreen />);

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      expect(Keyboard.dismiss).toHaveBeenCalled();
    });
  });

  // ============================================================================
  // AUTHENTICATION FLOW TESTS
  // ============================================================================

  describe('Authentication Flow Tests', () => {
    test('should validate token on mount when network is connected', async () => {
      const { isNetworkConnected } = require('../../../utils/Network/NetworkConnection');
      const { validateTokenRequest } = require('../../../redux/AuthRedux/Token/TokenActions');
      
      isNetworkConnected.mockResolvedValue(true);

      renderWithProviders(<LoginScreen />);

      await waitFor(() => {
        expect(isNetworkConnected).toHaveBeenCalled();
        expect(validateTokenRequest).toHaveBeenCalled();
      });
    });

    test('should not validate token when network is disconnected', async () => {
      const { isNetworkConnected } = require('../../../utils/Network/NetworkConnection');
      const { validateTokenRequest } = require('../../../redux/AuthRedux/Token/TokenActions');
      
      isNetworkConnected.mockResolvedValue(false);

      renderWithProviders(<LoginScreen />);

      await waitFor(() => {
        expect(isNetworkConnected).toHaveBeenCalled();
        expect(validateTokenRequest).not.toHaveBeenCalled();
      });
    });

    test('should handle successful login with single role', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { rolesRequest } = require('../../../redux/RolesRedux/RolesActions');
      const { wbsRequest } = require('../../../redux/WBSDownload/WBSActions');
      
      Storage.getUserInfo.mockReturnValue({ UID: '12345' });

      const { rerender } = renderWithProviders(<LoginScreen />);

      // Simulate successful login response
      rerender(<LoginScreen />);

      await waitFor(() => {
        expect(rolesRequest).toHaveBeenCalled();
      });
    });

    test('should handle successful login with multiple roles', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { rolesRequest } = require('../../../redux/RolesRedux/RolesActions');
      
      Storage.getUserInfo.mockReturnValue({ UID: '12345' });

      const { rerender } = renderWithProviders(<LoginScreen />);

      // Simulate successful login response
      rerender(<LoginScreen />);

      await waitFor(() => {
        expect(rolesRequest).toHaveBeenCalled();
      });
    });

    test('should handle login error', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { removeUserInfo } = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserInfo.mockReturnValue(null);

      const { rerender } = renderWithProviders(<LoginScreen />);

      // Simulate login error
      rerender(<LoginScreen />);

      await waitFor(() => {
        expect(removeUserInfo).toHaveBeenCalled();
      });
    });

    test('should handle approver role with pending WBS request', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { getPendingApproverWbsRequest } = require('../../../redux/WBSDownload/WBSActions');
      
      Storage.getUserInfo.mockReturnValue({ UID: '12345' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'approver', Jobcode: 'JOB001', JobDesc: 'Test Job' }],
      });

      const { rerender } = renderWithProviders(<LoginScreen />);

      // Simulate successful login response
      rerender(<LoginScreen />);

      await waitFor(() => {
        expect(getPendingApproverWbsRequest).toHaveBeenCalled();
      });
    });

    test('should handle non-approver role with WBS request', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { wbsRequest } = require('../../../redux/WBSDownload/WBSActions');
      
      Storage.getUserInfo.mockReturnValue({ UID: '12345' });
      Storage.getUserRolesInfo.mockReturnValue({
        RolesList: [{ Functional_ROLE: 'engineer', Jobcode: 'JOB001', JobDesc: 'Test Job' }],
      });

      const { rerender } = renderWithProviders(<LoginScreen />);

      // Simulate successful login response
      rerender(<LoginScreen />);

      await waitFor(() => {
        expect(wbsRequest).toHaveBeenCalled();
      });
    });
  });

  // ============================================================================
  // STATE CHANGE TESTS
  // ============================================================================

  describe('State Change Tests', () => {
    test('should update loading state during login', async () => {
      const { isNetworkConnected } = require('../../../utils/Network/NetworkConnection');
      
      isNetworkConnected.mockResolvedValue(true);

      renderWithProviders(<LoginScreen />);

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(screen.getByTestId('loading-overlay')).toBeTruthy();
      });
    });

    test('should clear error message on new login attempt', async () => {
      renderWithProviders(<LoginScreen />);

      // First attempt with empty credentials
      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter username and password')).toBeTruthy();
      });

      // Second attempt should clear previous error
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter username and password')).toBeTruthy();
      });
    });

    test('should update username state on input change', () => {
      renderWithProviders(<LoginScreen />);

      const usernameInput = screen.getByTestId('text-input-Username');
      fireEvent.press(usernameInput);

      // State should be updated through the handler
      expect(usernameInput).toBeTruthy();
    });

    test('should update password state on input change', () => {
      renderWithProviders(<LoginScreen />);

      const passwordInput = screen.getByTestId('text-input-Password');
      fireEvent.press(passwordInput);

      // State should be updated through the handler
      expect(passwordInput).toBeTruthy();
    });
  });

  // ============================================================================
  // EDGE CASES
  // ============================================================================

  describe('Edge Cases', () => {
    test('should handle empty user data after login', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { PrintLog } = require('../../../utils/Logger/PrintLog');
      
      Storage.getUserInfo.mockReturnValue(null);

      const { rerender } = renderWithProviders(<LoginScreen />);

      // Simulate login response
      rerender(<LoginScreen />);

      await waitFor(() => {
        expect(PrintLog.error).toHaveBeenCalledWith('Login screen -- User Details is Empty');
      });
    });

    test('should handle roles API error', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { PrintLog } = require('../../../utils/Logger/PrintLog');
      
      Storage.getUserInfo.mockReturnValue({ UID: '12345' });

      const { rerender } = renderWithProviders(<LoginScreen />);

      // Simulate login response
      rerender(<LoginScreen />);

      await waitFor(() => {
        expect(PrintLog.error).toHaveBeenCalled();
      });
    });

    test('should handle roles dispatch error', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      const { PrintLog } = require('../../../utils/Logger/PrintLog');
      
      Storage.getUserInfo.mockImplementation(() => {
        throw new Error('Storage error');
      });

      const { rerender } = renderWithProviders(<LoginScreen />);

      // Simulate login response
      rerender(<LoginScreen />);

      await waitFor(() => {
        expect(PrintLog.error).toHaveBeenCalledWith('Error dispatching findRolesRequest:', expect.any(Error));
      });
    });

    test('should handle device ID retrieval error', async () => {
      const DeviceInfo = require('react-native-device-info');
      const { isNetworkConnected } = require('../../../utils/Network/NetworkConnection');
      
      DeviceInfo.getUniqueId.mockRejectedValue(new Error('Device ID error'));
      isNetworkConnected.mockResolvedValue(true);

      renderWithProviders(<LoginScreen />);

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(DeviceInfo.getUniqueId).toHaveBeenCalled();
      });
    });

    test('should handle encryption errors', async () => {
      const { encryptString } = require('../../../utils/Crypto/Encryption');
      const { isNetworkConnected } = require('../../../utils/Network/NetworkConnection');
      
      encryptString.mockImplementation(() => {
        throw new Error('Encryption error');
      });
      isNetworkConnected.mockResolvedValue(true);

      renderWithProviders(<LoginScreen />);

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(encryptString).toHaveBeenCalled();
      });
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling Tests', () => {
    test('should handle network connection errors', async () => {
      const { isNetworkConnected } = require('../../../utils/Network/NetworkConnection');
      
      isNetworkConnected.mockRejectedValue(new Error('Network error'));

      renderWithProviders(<LoginScreen />);

      await waitFor(() => {
        expect(isNetworkConnected).toHaveBeenCalled();
      });
    });

    test('should handle token validation errors', async () => {
      const { validateTokenRequest } = require('../../../redux/AuthRedux/Token/TokenActions');
      
      validateTokenRequest.mockImplementation(() => {
        throw new Error('Token validation error');
      });

      expect(() => renderWithProviders(<LoginScreen />)).not.toThrow();
    });

    test('should handle login request errors', async () => {
      const { loginRequest } = require('../../../redux/AuthRedux/Login/LoginActions');
      const { isNetworkConnected } = require('../../../utils/Network/NetworkConnection');
      
      isNetworkConnected.mockResolvedValue(true);
      loginRequest.mockImplementation(() => {
        throw new Error('Login request error');
      });

      renderWithProviders(<LoginScreen />);

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(loginRequest).toHaveBeenCalled();
      });
    });

    test('should handle WBS request errors', async () => {
      const { wbsRequest } = require('../../../redux/WBSDownload/WBSActions');
      
      wbsRequest.mockImplementation(() => {
        throw new Error('WBS request error');
      });

      expect(() => renderWithProviders(<LoginScreen />)).not.toThrow();
    });

    test('should handle roles request errors', async () => {
      const { rolesRequest } = require('../../../redux/RolesRedux/RolesActions');
      
      rolesRequest.mockImplementation(() => {
        throw new Error('Roles request error');
      });

      expect(() => renderWithProviders(<LoginScreen />)).not.toThrow();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    test('should have accessible login form', () => {
      renderWithProviders(<LoginScreen />);

      expect(screen.getByTestId('login-screen')).toBeTruthy();
      expect(screen.getByTestId('text-input-Username')).toBeTruthy();
      expect(screen.getByTestId('text-input-Password')).toBeTruthy();
      expect(screen.getByTestId('LoginButton')).toBeTruthy();
    });

    test('should have proper testIDs for testing', () => {
      renderWithProviders(<LoginScreen />);

      expect(screen.getByTestId('login-screen')).toBeTruthy();
      expect(screen.getByTestId('LoginButton')).toBeTruthy();
    });

    test('should have accessible error messages', () => {
      renderWithProviders(<LoginScreen />);

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      expect(screen.getByText('Please enter username and password')).toBeTruthy();
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    test('should render efficiently without unnecessary re-renders', () => {
      const { rerender } = renderWithProviders(<LoginScreen />);

      // Re-render multiple times
      rerender(<LoginScreen />);
      rerender(<LoginScreen />);
      rerender(<LoginScreen />);

      expect(screen.getByTestId('login-screen')).toBeTruthy();
    });

    test('should handle rapid login attempts efficiently', async () => {
      const { isNetworkConnected } = require('../../../utils/Network/NetworkConnection');
      
      isNetworkConnected.mockResolvedValue(true);

      renderWithProviders(<LoginScreen />);

      const loginButton = screen.getByTestId('LoginButton');

      // Rapid login attempts
      fireEvent.press(loginButton);
      fireEvent.press(loginButton);
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(isNetworkConnected).toHaveBeenCalled();
      });
    });

    test('should handle rapid input changes efficiently', () => {
      renderWithProviders(<LoginScreen />);

      const usernameInput = screen.getByTestId('text-input-Username');
      const passwordInput = screen.getByTestId('text-input-Password');

      // Rapid input changes
      fireEvent.press(usernameInput);
      fireEvent.press(passwordInput);
      fireEvent.press(usernameInput);
      fireEvent.press(passwordInput);

      expect(usernameInput).toBeTruthy();
      expect(passwordInput).toBeTruthy();
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should work with Redux store integration', () => {
      renderWithProviders(<LoginScreen />);

      expect(screen.getByTestId('login-screen')).toBeTruthy();
    });

    test('should work with navigation container', () => {
      renderWithProviders(<LoginScreen />);

      expect(screen.getByTestId('login-screen')).toBeTruthy();
    });

    test('should integrate with all authentication services', async () => {
      const { isNetworkConnected } = require('../../../utils/Network/NetworkConnection');
      const { validateTokenRequest } = require('../../../redux/AuthRedux/Token/TokenActions');
      const { loginRequest } = require('../../../redux/AuthRedux/Login/LoginActions');
      
      isNetworkConnected.mockResolvedValue(true);

      renderWithProviders(<LoginScreen />);

      await waitFor(() => {
        expect(isNetworkConnected).toHaveBeenCalled();
        expect(validateTokenRequest).toHaveBeenCalled();
      });

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(loginRequest).toHaveBeenCalled();
      });
    });

    test('should integrate with storage utilities', async () => {
      const { Storage } = require('../../../utils/DataStorage/Storage');
      
      Storage.getUserInfo.mockReturnValue({ UID: '12345' });

      const { rerender } = renderWithProviders(<LoginScreen />);

      rerender(<LoginScreen />);

      await waitFor(() => {
        expect(Storage.getUserInfo).toHaveBeenCalled();
        expect(Storage.saveUserInfo).toHaveBeenCalled();
      });
    });

    test('should integrate with encryption utilities', async () => {
      const { encryptString } = require('../../../utils/Crypto/Encryption');
      const { isNetworkConnected } = require('../../../utils/Network/NetworkConnection');
      
      isNetworkConnected.mockResolvedValue(true);

      renderWithProviders(<LoginScreen />);

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(encryptString).toHaveBeenCalled();
      });
    });

    test('should integrate with device info utilities', async () => {
      const DeviceInfo = require('react-native-device-info');
      const { isNetworkConnected } = require('../../../utils/Network/NetworkConnection');
      
      isNetworkConnected.mockResolvedValue(true);

      renderWithProviders(<LoginScreen />);

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(DeviceInfo.getUniqueId).toHaveBeenCalled();
      });
    });
  });

  // ============================================================================
  // SNAPSHOT TESTS
  // ============================================================================

  describe('Snapshot Tests', () => {
    test('should match snapshot for initial render', () => {
      const { toJSON } = renderWithProviders(<LoginScreen />);
      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot with error message', () => {
      const { toJSON } = renderWithProviders(<LoginScreen />);

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot with loading overlay', async () => {
      const { isNetworkConnected } = require('../../../utils/Network/NetworkConnection');
      
      isNetworkConnected.mockResolvedValue(true);

      const { toJSON } = renderWithProviders(<LoginScreen />);

      const loginButton = screen.getByTestId('LoginButton');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(toJSON()).toMatchSnapshot();
      });
    });
  });
}); 