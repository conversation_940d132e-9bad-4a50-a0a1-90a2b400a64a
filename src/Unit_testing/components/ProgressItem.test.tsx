import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import ProgressItem from '../../components/ProgressItem';

// ✅ Mock the SVG icon
jest.mock('../../assets/svg/add_blue.svg', () => {
  return {
    __esModule: true,
    default: () => <svg data-testid="add-blue-icon" />,
  };
});

// ✅ Mock the Colors
jest.mock('../../utils/Colors/Colors', () => ({
  white: '#fff',
  searchBorderGrey: '#ccc',
  textPrimary: '#000',
}));

// ✅ Mock Scaling
jest.mock('../../utils/Scale/Scaling', () => ({
  ms: (value: number) => value,
}));

describe('ProgressItem Component', () => {
  it('renders the provided text', () => {
    const { getByText } = render(
      <ProgressItem text="Hello Progress" onClick={jest.fn()} />
    );
    expect(getByText('Hello Progress')).toBeTruthy();
  });

  it('calls onClick when pressed', () => {
    const onClickMock = jest.fn();
    const { getByText } = render(
      <ProgressItem text="Tap Me" onClick={onClickMock} />
    );

    fireEvent.press(getByText('Tap Me'));
    expect(onClickMock).toHaveBeenCalled();
  });

  it('renders the SVG icon', () => {
    const { getByTestId } = render(
      <ProgressItem text="With Icon" onClick={jest.fn()} />
    );
    expect(getByTestId('add-blue-icon')).toBeTruthy();
  });
});
