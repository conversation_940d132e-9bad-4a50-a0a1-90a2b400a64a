import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import AttachmentComponent from '../../../components/Attachment';
import * as Permissions from 'react-native-permissions';
import * as ImagePicker from 'react-native-image-picker';
import * as ApiRequests from '../../../services/ApiRequests';

// Mock dependencies
jest.mock('react-native-permissions');
jest.mock('react-native-image-picker');
jest.mock('../../services/ApiRequests');
jest.mock('react-native/Libraries/Alert/Alert', () => ({
  alert: jest.fn(),
}));

describe('AttachmentComponent', () => {
  const baseProps = {
    onUploadComplete: jest.fn(),
    uploadedImages: [],
    selectedTab: 'Pending For Approval',
    imageUrl: 'http://test.com/image.jpg',
    imageId: 'img-1',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with minimal props', () => {
    const { getByTestId } = render(<AttachmentComponent />);
    expect(getByTestId('attachment-root')).toBeTruthy();
  });

  it('renders with uploadedImages', () => {
    const { getByTestId } = render(
      <AttachmentComponent {...baseProps} uploadedImages={[{ UniqueID: '1', FileName: 'file.jpg' }]} />
    );
    expect(getByTestId('attachment-root')).toBeTruthy();
  });

  it('handles camera permission denied', async () => {
    Permissions.check.mockResolvedValueOnce('denied');
    Permissions.request.mockResolvedValueOnce('denied');
    const { getByTestId } = render(<AttachmentComponent {...baseProps} />);
    await waitFor(() => {
      fireEvent.press(getByTestId('camera-button'));
    });
    // Should not proceed to launchCamera
    expect(ImagePicker.launchCamera).not.toHaveBeenCalled();
  });

  it('handles camera permission granted and upload success', async () => {
    Permissions.check.mockResolvedValueOnce('granted');
    ImagePicker.launchCamera.mockImplementationOnce((opts, cb) =>
      cb({ assets: [{ uri: 'file://img.jpg', base64: 'abc123' }] })
    );
    ApiRequests.downloadImage.mockImplementationOnce((req, onSuccess) =>
      onSuccess({ data: 'base64data', success: true })
    );
    const { getByTestId } = render(<AttachmentComponent {...baseProps} />);
    await waitFor(() => {
      fireEvent.press(getByTestId('camera-button'));
    });
    // Should call upload logic
    expect(ImagePicker.launchCamera).toHaveBeenCalled();
  });

  it('handles gallery permission blocked', async () => {
    Permissions.check.mockResolvedValueOnce('blocked');
    const { getByTestId } = render(<AttachmentComponent {...baseProps} />);
    await waitFor(() => {
      fireEvent.press(getByTestId('gallery-button'));
    });
    // Should show alert
    // (mocked Alert.alert)
  });

  it('handles image download error', async () => {
    ApiRequests.downloadImage.mockImplementationOnce((req, onSuccess, onError) =>
      onError(new Error('fail'))
    );
    const { getByTestId } = render(<AttachmentComponent {...baseProps} />);
    // Simulate effect
    await waitFor(() => {
      // Should handle error and not crash
    });
  });

  it('handles remove uploaded image', () => {
    const { getByTestId } = render(<AttachmentComponent {...baseProps} />);
    // Simulate add and remove
    // fireEvent.press(getByTestId('remove-uploaded-image-0'));
    // Add assertions as per your UI
  });

  it('handles remove downloaded image', () => {
    const { getByTestId } = render(<AttachmentComponent {...baseProps} />);
    // Simulate add and remove
    // fireEvent.press(getByTestId('remove-downloaded-image-0'));
    // Add assertions as per your UI
  });

  it('handles upload error', async () => {
    Permissions.check.mockResolvedValueOnce('granted');
    ImagePicker.launchCamera.mockImplementationOnce((opts, cb) =>
      cb({ assets: [{ uri: 'file://img.jpg', base64: 'abc123' }] })
    );
    ApiRequests.downloadImage.mockImplementationOnce((req, onSuccess, onError) =>
      onError(new Error('fail'))
    );
    const { getByTestId } = render(<AttachmentComponent {...baseProps} />);
    await waitFor(() => {
      fireEvent.press(getByTestId('camera-button'));
    });
    // Should handle upload error
  });

  // Add more tests for:
  // - Edge cases: no images, duplicate images, rapid add/remove
  // - Invalid base64, invalid API response
  // - Modal open/close, UI state changes
  // - onUploadComplete callback firing
}); 