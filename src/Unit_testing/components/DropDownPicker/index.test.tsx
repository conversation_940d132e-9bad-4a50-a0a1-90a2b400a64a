import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import CustomDropdownPicker from '../../../components/DropDownPicker';

jest.mock('react-native-element-dropdown', () => ({
  Dropdown: ({
    data,
    value,
    onChange,
    onFocus,
    onBlur,
    search,
    ...props
  }: any) => (
    <div>
      <button data-testid="dropdown-focus" onClick={onFocus}>focus</button>
      <button data-testid="dropdown-blur" onClick={onBlur}>blur</button>
      {data.map((item: any, idx: number) => (
        <button key={item.value} data-testid={`dropdown-item-${idx}`} onClick={() => onChange(item)}>
          {item.label}
        </button>
      ))}
      <span data-testid="dropdown-value">{value}</span>
      {search && <input data-testid="dropdown-search" />}
    </div>
  ),
}));
jest.mock('../../../utils/Colors/Colors', () => ({
  pipeIdTextBlack: '#222',
  inputBorder: '#ccc',
  containerligetBlue: '#f0f0f0',
  textLightGray: '#aaa',
  black: '#000',
  progressAsh: '#eee',
}));
jest.mock('../../../utils/Scale/Scaling', () => ({ ms: (v: number) => v }));
jest.mock('../../../components/Fonts', () => ({ AppFonts: { Medium: 'Medium' } }));
jest.mock('react-i18next', () => ({ useTranslation: () => ({ t: (k: string) => k }) }));

describe('CustomDropdownPicker', () => {
  const data = [
    { label: 'Item 1', value: '1' },
    { label: 'Item 2', value: '2' },
  ];
  const baseProps = {
    data,
    onSelect: jest.fn(),
    title: 'Test Title',
    defaultValue: '1',
    value: undefined,
    search: false,
    placeHolder: 'Select...',
    mainContainerStyle: { backgroundColor: 'red' },
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders with required props', () => {
    const { getByText } = render(<CustomDropdownPicker {...baseProps} />);
    expect(getByText('Test Title')).toBeTruthy();
    expect(getByText('Item 1')).toBeTruthy();
    expect(getByText('Item 2')).toBeTruthy();
  });

  it('renders with no title', () => {
    const { getByText } = render(
      <CustomDropdownPicker {...baseProps} title={undefined} />
    );
    expect(getByText('')).toBeTruthy();
  });

  it('renders with custom placeholder', () => {
    const { getByText } = render(
      <CustomDropdownPicker {...baseProps} placeHolder="Pick one" />
    );
    expect(getByText('Pick one')).toBeTruthy();
  });

  it('renders with default placeholder if not provided', () => {
    const { getByText } = render(
      <CustomDropdownPicker {...baseProps} placeHolder={undefined} />
    );
    expect(getByText('commonStrings.select')).toBeTruthy();
  });

  it('renders with search enabled', () => {
    const { getByTestId } = render(
      <CustomDropdownPicker {...baseProps} search={true} />
    );
    expect(getByTestId('dropdown-search')).toBeTruthy();
  });

  it('handles onSelect when item is clicked', () => {
    const onSelect = jest.fn();
    const { getByTestId } = render(
      <CustomDropdownPicker {...baseProps} onSelect={onSelect} />
    );
    fireEvent.click(getByTestId('dropdown-item-0'));
    expect(onSelect).toHaveBeenCalledWith(data[0]);
  });

  it('handles focus and blur events', () => {
    const { getByTestId } = render(<CustomDropdownPicker {...baseProps} />);
    fireEvent.click(getByTestId('dropdown-focus'));
    fireEvent.click(getByTestId('dropdown-blur'));
    // No error means success
  });

  it('handles controlled value', () => {
    const { getByTestId } = render(
      <CustomDropdownPicker {...baseProps} value="2" />
    );
    expect(getByTestId('dropdown-value').textContent).toBe('2');
  });

  it('handles uncontrolled value (defaultValue)', () => {
    const { getByTestId } = render(
      <CustomDropdownPicker {...baseProps} value={undefined} defaultValue="1" />
    );
    expect(getByTestId('dropdown-value').textContent).toBe('1');
  });

  it('handles edge case: empty data', () => {
    const { container } = render(
      <CustomDropdownPicker {...baseProps} data={[]} />
    );
    expect(container).toBeTruthy();
  });

  it('handles edge case: onSelect not provided', () => {
    const { getByTestId } = render(
      <CustomDropdownPicker {...baseProps} onSelect={undefined as any} />
    );
    fireEvent.click(getByTestId('dropdown-item-0'));
    // No error should be thrown
  });

  it('handles edge case: value and defaultValue both undefined', () => {
    const { getByTestId } = render(
      <CustomDropdownPicker {...baseProps} value={undefined} defaultValue={undefined} />
    );
    expect(getByTestId('dropdown-value').textContent).toBe('');
  });
}); 