import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { Alert, Platform } from 'react-native';
import AttachmentComponent from '../../../components/Attachment/index';
import {
  createMockImageAsset,
  createMockUploadResponse,
  createMockUser,
  mockPermissionsGranted,
  mockPermissionsDenied,
  mockPermissionsBlocked,
  mockImagePickerSuccess,
  mockImagePickerCancel,
  mockImagePickerError,
  mockStorageGetUserInfo,
  mockAxiosSuccess,
  mockAxiosError,
  mockPlatformOS,
  setupTest,
  teardownTest,
  waitForAsyncOperation,
} from '../../../utils/testing/testHelpers';

// Mock the required modules
jest.mock('../../../utils/ImagePath', () => ({
  SVG: {
    Upload: () => 'MockUploadIcon',
    Cross: () => 'MockCrossIcon',
  },
}));

jest.mock('../../../utils/Scale/Scaling', () => ({
  ms: (value: number) => value,
}));

jest.mock('../../../components/Fonts', () => ({
  AppFonts: {
    Light: 'MockLightFont',
  },
}));

jest.mock('../../../utils/Strings/Strings', () => ({
  default: {
    DailyProgress: {
      uploadImage: 'Upload Image',
    },
  },
}));

jest.mock('../../../utils/Colors/Colors', () => ({
  default: {
    pipeIdTextBlack: '#000000',
    searchBorderGrey: '#CCCCCC',
    secondary: '#0066CC',
    containerligetBlue: '#E6F3FF',
    textPrimary: '#333333',
  },
}));

jest.mock('../../../components/BottomPopupImageUpload', () => {
  return function MockBottomPopupImageUpload(props: any) {
    return (
      <div testID="bottom-popup-image-upload">
        <button testID="camera-button" onPress={props.onCameraPress}>
          Camera
        </button>
        <button testID="gallery-button" onPress={props.onGalleryPress}>
          Gallery
        </button>
      </div>
    );
  };
});

jest.mock('../../../components/BottomPopup', () => {
  return function MockBottomPopup(props: any) {
    return props.visible ? (
      <div data-testid="bottom-popup">
        {props.children}
        <button data-testid="cancel-button" onClick={props.onCancelPress}>
          Cancel
        </button>
      </div>
    ) : null;
  };
});

jest.mock('../../../services/ApiRequests', () => ({
  downloadImage: jest.fn(),
}));

jest.mock('../../../utils/DataStorage/Storage', () => ({
  getUserInfo: jest.fn(),
  getUserRolesInfo: jest.fn(),
}));

describe('AttachmentComponent', () => {
  let mockOnUploadComplete: jest.Mock;
  let mockDownloadImage: jest.Mock;

  beforeEach(() => {
    setupTest();
    mockOnUploadComplete = jest.fn();
    mockDownloadImage = require('../../../services/ApiRequests').downloadImage;
    mockDownloadImage.mockClear();
  });

  afterEach(() => {
    teardownTest();
  });

  describe('Rendering', () => {
    test('should render attachment component with upload button', () => {
      mockStorageGetUserInfo(createMockUser());
      
      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      expect(screen.getByText('Attachments *')).toBeTruthy();
      expect(screen.getByText('Upload Image')).toBeTruthy();
    });

    test('should not render upload button when selectedTab is "Pending For Approval"', () => {
      mockStorageGetUserInfo(createMockUser());
      
      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Pending For Approval"
        />
      );

      expect(screen.getByText('Attachments *')).toBeTruthy();
      expect(screen.queryByText('Upload Image')).toBeFalsy();
    });

    test('should render with custom props', () => {
      mockStorageGetUserInfo(createMockUser());
      
      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
          imageUrl="https://test.com"
          imageId="test-123"
        />
      );

      expect(screen.getByText('Attachments *')).toBeTruthy();
    });
  });

  describe('User Role-based Functionality', () => {
    test('should show upload button for Site Engineer in non-pending tabs', () => {
      mockStorageGetUserInfo(createMockUser({ role: { Functional_ROLE: 'Site Engineer' } }));
      
      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      expect(screen.getByText('Upload Image')).toBeTruthy();
    });

    test('should not show upload button for Site Engineer in Pending For Approval', () => {
      mockStorageGetUserInfo(createMockUser({ role: { Functional_ROLE: 'Site Engineer' } }));
      
      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Pending For Approval"
        />
      );

      expect(screen.queryByText('Upload Image')).toBeFalsy();
    });

    test('should handle different user roles correctly', () => {
      mockStorageGetUserInfo(createMockUser({ role: { Functional_ROLE: 'Project Manager' } }));
      
      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      expect(screen.getByText('Upload Image')).toBeTruthy();
    });
  });

  describe('Image Upload Modal', () => {
    test('should open image upload modal when upload button is pressed', async () => {
      mockStorageGetUserInfo(createMockUser());
      
      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));

      await waitFor(() => {
        expect(screen.getByTestId('bottom-popup')).toBeTruthy();
      });
    });

    test('should close image upload modal when cancel is pressed', async () => {
      mockStorageGetUserInfo(createMockUser());
      
      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));
      
      await waitFor(() => {
        expect(screen.getByTestId('bottom-popup')).toBeTruthy();
      });

      fireEvent.press(screen.getByTestId('cancel-button'));

      await waitFor(() => {
        expect(screen.queryByTestId('bottom-popup')).toBeFalsy();
      });
    });
  });

  describe('Camera Permissions', () => {
    test('should request camera permission and launch camera when granted', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      mockImagePickerSuccess([createMockImageAsset()]);
      mockAxiosSuccess([createMockUploadResponse()]);

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));

      await waitFor(() => {
        expect(screen.getByTestId('camera-button')).toBeTruthy();
      });

      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        const { check } = require('react-native-permissions');
        expect(check).toHaveBeenCalled();
      });
    });

    test('should show alert when camera permission is blocked', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsBlocked();
      jest.spyOn(Alert, 'alert');

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));

      await waitFor(() => {
        expect(screen.getByTestId('camera-button')).toBeTruthy();
      });

      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Permission Required',
          'Please enable camera access in your device settings to use this feature.',
          expect.any(Array)
        );
      });
    });

    test('should handle camera permission denied', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsDenied();

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));

      await waitFor(() => {
        expect(screen.getByTestId('camera-button')).toBeTruthy();
      });

      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        const { check, request } = require('react-native-permissions');
        expect(check).toHaveBeenCalled();
        expect(request).toHaveBeenCalled();
      });
    });
  });

  describe('Gallery Permissions', () => {
    test('should request gallery permission and launch gallery when granted', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      mockImagePickerSuccess([createMockImageAsset()]);
      mockAxiosSuccess([createMockUploadResponse()]);

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));

      await waitFor(() => {
        expect(screen.getByTestId('gallery-button')).toBeTruthy();
      });

      fireEvent.press(screen.getByTestId('gallery-button'));

      await waitFor(() => {
        const { check } = require('react-native-permissions');
        expect(check).toHaveBeenCalled();
      });
    });

    test('should handle different Android versions for gallery permissions', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPlatformOS('android');
      Platform.Version = 33;
      mockPermissionsGranted();
      mockImagePickerSuccess([createMockImageAsset()]);
      mockAxiosSuccess([createMockUploadResponse()]);

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));

      await waitFor(() => {
        expect(screen.getByTestId('gallery-button')).toBeTruthy();
      });

      fireEvent.press(screen.getByTestId('gallery-button'));

      await waitFor(() => {
        const { check } = require('react-native-permissions');
        expect(check).toHaveBeenCalled();
      });
    });
  });

  describe('Image Selection and Upload', () => {
    test('should handle successful image selection from camera', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      const mockAsset = createMockImageAsset();
      mockImagePickerSuccess([mockAsset]);
      mockAxiosSuccess([createMockUploadResponse()]);

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));

      await waitFor(() => {
        expect(screen.getByTestId('camera-button')).toBeTruthy();
      });

      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        const { launchCamera } = require('react-native-image-picker');
        expect(launchCamera).toHaveBeenCalled();
      });
    });

    test('should handle successful image selection from gallery', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      const mockAssets = [createMockImageAsset(), createMockImageAsset()];
      mockImagePickerSuccess(mockAssets);
      mockAxiosSuccess([createMockUploadResponse()]);

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));

      await waitFor(() => {
        expect(screen.getByTestId('gallery-button')).toBeTruthy();
      });

      fireEvent.press(screen.getByTestId('gallery-button'));

      await waitFor(() => {
        const { launchImageLibrary } = require('react-native-image-picker');
        expect(launchImageLibrary).toHaveBeenCalled();
      });
    });

    test('should handle image picker cancellation', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      mockImagePickerCancel();
      jest.spyOn(Alert, 'alert');

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));

      await waitFor(() => {
        expect(screen.getByTestId('camera-button')).toBeTruthy();
      });

      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith('Error', 'Failed to take photo');
      });
    });

    test('should handle image picker error', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      mockImagePickerError('Camera not available');
      jest.spyOn(Alert, 'alert');

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));

      await waitFor(() => {
        expect(screen.getByTestId('camera-button')).toBeTruthy();
      });

      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith('Error', 'Failed to take photo');
      });
    });
  });

  describe('Image Upload to Server', () => {
    test('should upload image to server successfully', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      const mockAsset = createMockImageAsset();
      mockImagePickerSuccess([mockAsset]);
      const mockUploadResponse = createMockUploadResponse();
      mockAxiosSuccess([mockUploadResponse]);

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));
      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        const axios = require('axios');
        expect(axios.post).toHaveBeenCalledWith(
          '*********************************:443/Pragatiext/UploadImaage',
          {
            FileName: 'Attachment 1.jpg ',
            File: mockAsset.base64,
          },
          {
            headers: {
              'Ocp-Apim-Subscription-Key': 'ececb381826c4379843eef6128c6360e',
              'Content-Type': 'application/json',
              Accept: 'application/json',
            },
          }
        );
      });
    });

    test('should handle upload error', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      const mockAsset = createMockImageAsset();
      mockImagePickerSuccess([mockAsset]);
      mockAxiosError(new Error('Network error'));

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));
      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(screen.getByText('Upload Error')).toBeTruthy();
      });
    });

    test('should handle invalid upload response', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      const mockAsset = createMockImageAsset();
      mockImagePickerSuccess([mockAsset]);
      mockAxiosSuccess({}); // Invalid response

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));
      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(screen.getByText('Upload Error')).toBeTruthy();
      });
    });
  });

  describe('Image Download', () => {
    test('should download image when imageId and imageUrl are provided', async () => {
      mockStorageGetUserInfo(createMockUser({ role: { Functional_ROLE: 'Project Manager' } }));
      
      mockDownloadImage.mockImplementation((request, onSuccess, onError) => {
        onSuccess('base64ImageData');
      });

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
          imageId="test-123"
          imageUrl="https://test.com"
        />
      );

      await waitFor(() => {
        expect(mockDownloadImage).toHaveBeenCalledWith(
          {
            ModuleName: 'PMP',
            Unique: 'test-123',
            SiteUrl: 'https://test.com',
          },
          expect.any(Function),
          expect.any(Function)
        );
      });
    });

    test('should handle download error', async () => {
      mockStorageGetUserInfo(createMockUser({ role: { Functional_ROLE: 'Project Manager' } }));
      
      mockDownloadImage.mockImplementation((request, onSuccess, onError) => {
        onError(new Error('Download failed'));
      });

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
          imageId="test-123"
          imageUrl="https://test.com"
        />
      );

      await waitFor(() => {
        expect(mockDownloadImage).toHaveBeenCalled();
      });
    });

    test('should validate and convert base64 data', async () => {
      mockStorageGetUserInfo(createMockUser({ role: { Functional_ROLE: 'Project Manager' } }));
      
      const validBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';
      
      mockDownloadImage.mockImplementation((request, onSuccess, onError) => {
        onSuccess(validBase64);
      });

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
          imageId="test-123"
          imageUrl="https://test.com"
        />
      );

      await waitFor(() => {
        expect(mockDownloadImage).toHaveBeenCalled();
      });
    });
  });

  describe('Image Display and Removal', () => {
    test('should display uploaded images', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      const mockAsset = createMockImageAsset();
      mockImagePickerSuccess([mockAsset]);
      mockAxiosSuccess([createMockUploadResponse()]);

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));
      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(screen.getByDisplayValue(mockAsset.uri)).toBeTruthy();
      });
    });

    test('should remove uploaded image when cross button is pressed', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      const mockAsset = createMockImageAsset();
      mockImagePickerSuccess([mockAsset]);
      mockAxiosSuccess([createMockUploadResponse()]);

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));
      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(screen.getByDisplayValue(mockAsset.uri)).toBeTruthy();
      });

      // Find and press the remove button
      const removeButton = screen.getByTestId('MockCrossIcon');
      fireEvent.press(removeButton);

      await waitFor(() => {
        expect(screen.queryByDisplayValue(mockAsset.uri)).toBeFalsy();
      });
    });

    test('should show loading state for downloading images', async () => {
      mockStorageGetUserInfo(createMockUser({ role: { Functional_ROLE: 'Project Manager' } }));
      
      mockDownloadImage.mockImplementation((request, onSuccess, onError) => {
        // Simulate slow download
        setTimeout(() => onSuccess('base64ImageData'), 1000);
      });

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
          imageId="test-123"
          imageUrl="https://test.com"
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Loading...')).toBeTruthy();
      });
    });
  });

  describe('Callback Functions', () => {
    test('should call onUploadComplete when images are uploaded', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      const mockAsset = createMockImageAsset();
      mockImagePickerSuccess([mockAsset]);
      const mockUploadResponse = createMockUploadResponse();
      mockAxiosSuccess([mockUploadResponse]);

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));
      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(mockOnUploadComplete).toHaveBeenCalled();
      });
    });

    test('should call onUploadComplete with correct image data', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      const mockAsset = createMockImageAsset();
      mockImagePickerSuccess([mockAsset]);
      const mockUploadResponse = createMockUploadResponse();
      mockAxiosSuccess([mockUploadResponse]);

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));
      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(mockOnUploadComplete).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              uniqueId: expect.any(String),
              imageUri: mockAsset.uri,
              source: 'uploaded',
            }),
          ])
        );
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle network errors gracefully', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      const mockAsset = createMockImageAsset();
      mockImagePickerSuccess([mockAsset]);
      mockAxiosError(new Error('Network error'));

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));
      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(screen.getByText('Upload Error')).toBeTruthy();
        expect(screen.getByText(/Failed to upload/)).toBeTruthy();
      });
    });

    test('should handle permission errors', async () => {
      mockStorageGetUserInfo(createMockUser());
      
      const { check } = require('react-native-permissions');
      check.mockRejectedValue(new Error('Permission check failed'));

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));
      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(console.error).toHaveBeenCalledWith(
          'Error checking camera permission:',
          expect.any(Error)
        );
      });
    });
  });

  describe('Platform-specific Behavior', () => {
    test('should handle iOS platform correctly', async () => {
      mockPlatformOS('ios');
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));
      fireEvent.press(screen.getByTestId('gallery-button'));

      await waitFor(() => {
        const { check } = require('react-native-permissions');
        expect(check).toHaveBeenCalledWith('ios.permission.PHOTO_LIBRARY');
      });
    });

    test('should handle Android platform correctly', async () => {
      mockPlatformOS('android');
      Platform.Version = 28;
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));
      fireEvent.press(screen.getByTestId('gallery-button'));

      await waitFor(() => {
        const { check } = require('react-native-permissions');
        expect(check).toHaveBeenCalledWith('android.permission.READ_EXTERNAL_STORAGE');
      });
    });
  });

  describe('Edge Cases', () => {
    test('should handle empty user info', () => {
      mockStorageGetUserInfo(null);

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      expect(screen.getByText('Attachments *')).toBeTruthy();
    });

    test('should handle malformed user role data', () => {
      mockStorageGetUserInfo({ RolesList: [] });

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      expect(screen.getByText('Attachments *')).toBeTruthy();
    });

    test('should handle missing base64 data in image asset', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      const mockAsset = createMockImageAsset({ base64: undefined });
      mockImagePickerSuccess([mockAsset]);

      render(
        <AttachmentComponent 
          onUploadComplete={mockOnUploadComplete}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));
      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        const axios = require('axios');
        expect(axios.post).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            File: '', // Should default to empty string
          }),
          expect.any(Object)
        );
      });
    });
  });

  describe('Edge Cases - Missing Props and Invalid Data', () => {
    test('should handle missing onUploadComplete prop gracefully', () => {
      render(
        <AttachmentComponent 
          selectedTab="Daily Progress"
        />
      );
      // Try uploading, expect no crash
      fireEvent.press(screen.getByText('Upload Image'));
    });

    test('should handle unexpected selectedTab value', () => {
      render(
        <AttachmentComponent 
          onUploadComplete={jest.fn()}
          selectedTab="Unknown Tab"
        />
      );
      // Should still render without crashing
      expect(screen.getByText('Attachments *')).toBeTruthy();
    });

    test('should handle upload API returning invalid data', async () => {
      mockStorageGetUserInfo(createMockUser());
      mockPermissionsGranted();
      mockImagePickerSuccess([createMockImageAsset()]);
      mockAxiosSuccess({ notExpected: true }); // Invalid response

      render(
        <AttachmentComponent 
          onUploadComplete={jest.fn()}
          selectedTab="Daily Progress"
        />
      );

      fireEvent.press(screen.getByText('Upload Image'));
      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(screen.getByText('Upload Error')).toBeTruthy();
      });
    });
  });
}); 