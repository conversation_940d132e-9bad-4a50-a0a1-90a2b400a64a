import React from 'react';
import { render, fireEvent, screen, waitFor } from '@testing-library/react-native';
import ProgressData from '../../components/ProgressData';
import { renderWithProviders, setupTest, teardownTest } from '../../utils/testing/testHelpers';

// Mock react-native-vector-icons
jest.mock('react-native-vector-icons/MaterialIcons', () => 'MaterialIcons');

describe('ProgressData', () => {
  beforeEach(() => {
    setupTest();
  });

  afterEach(() => {
    teardownTest();
    jest.clearAllMocks();
  });

  const defaultProps = {
    text: 'Sample progress item',
    onClick: jest.fn(),
  };

  // ============================================================================
  // RENDER TESTS
  // ============================================================================

  describe('Render Tests', () => {
    test('should render correctly with basic props', () => {
      renderWithProviders(<ProgressData {...defaultProps} />);

      expect(screen.getByText('Sample progress item')).toBeTruthy();
    });

    test('should render with empty text', () => {
      renderWithProviders(
        <ProgressData {...defaultProps} text="" />
      );

      expect(screen.getByText('')).toBeTruthy();
    });

    test('should render with long text', () => {
      const longText = 'This is a very long progress item text that should be handled properly by the component with multiple lines and sections';
      renderWithProviders(
        <ProgressData {...defaultProps} text={longText} />
      );

      expect(screen.getByText(longText)).toBeTruthy();
    });

    test('should render with special characters in text', () => {
      const specialText = 'Progress with @#$%^&*() characters';
      renderWithProviders(
        <ProgressData {...defaultProps} text={specialText} />
      );

      expect(screen.getByText(specialText)).toBeTruthy();
    });

    test('should render with unicode characters in text', () => {
      const unicodeText = 'Progress with émojis 📊 and unicode 进度';
      renderWithProviders(
        <ProgressData {...defaultProps} text={unicodeText} />
      );

      expect(screen.getByText(unicodeText)).toBeTruthy();
    });

    test('should render with numbers in text', () => {
      renderWithProviders(
        <ProgressData {...defaultProps} text="Progress 123 items" />
      );

      expect(screen.getByText('Progress 123 items')).toBeTruthy();
    });

    test('should render with whitespace in text', () => {
      renderWithProviders(
        <ProgressData {...defaultProps} text="  Progress with spaces  " />
      );

      expect(screen.getByText('  Progress with spaces  ')).toBeTruthy();
    });

    test('should render with different text variations', () => {
      const textVariations = [
        'Task completed',
        'In progress',
        'Pending review',
        'Approved',
        'Rejected'
      ];
      
      textVariations.forEach(text => {
        const { unmount } = renderWithProviders(
          <ProgressData {...defaultProps} text={text} />
        );
        
        expect(screen.getByText(text)).toBeTruthy();
        unmount();
      });
    });

    test('should render with text containing newlines', () => {
      const textWithNewlines = 'Line 1\nLine 2\nLine 3';
      renderWithProviders(
        <ProgressData {...defaultProps} text={textWithNewlines} />
      );

      expect(screen.getByText(textWithNewlines)).toBeTruthy();
    });

    test('should render with text containing HTML-like content', () => {
      const htmlText = '<strong>Bold</strong> and <em>italic</em> text';
      renderWithProviders(
        <ProgressData {...defaultProps} text={htmlText} />
      );

      expect(screen.getByText(htmlText)).toBeTruthy();
    });

    test('should render with text containing mathematical symbols', () => {
      const mathText = 'Progress: 75% ± 5% (σ = 2.5)';
      renderWithProviders(
        <ProgressData {...defaultProps} text={mathText} />
      );

      expect(screen.getByText(mathText)).toBeTruthy();
    });
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  describe('Interaction Tests', () => {
    test('should call onClick when item is pressed', () => {
      const onClickMock = jest.fn();
      
      renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      fireEvent.press(screen.getByText('Sample progress item'));
      expect(onClickMock).toHaveBeenCalledTimes(1);
    });

    test('should call onClick multiple times when item is pressed repeatedly', () => {
      const onClickMock = jest.fn();
      
      renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      const item = screen.getByText('Sample progress item');
      fireEvent.press(item);
      fireEvent.press(item);
      fireEvent.press(item);

      expect(onClickMock).toHaveBeenCalledTimes(3);
    });

    test('should handle rapid item presses', () => {
      const onClickMock = jest.fn();
      
      renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      const item = screen.getByText('Sample progress item');
      
      // Simulate rapid presses
      for (let i = 0; i < 10; i++) {
        fireEvent.press(item);
      }

      expect(onClickMock).toHaveBeenCalledTimes(10);
    });

    test('should handle item press with different text lengths', () => {
      const onClickMock = jest.fn();
      
      const { rerender } = renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      fireEvent.press(screen.getByText('Sample progress item'));
      expect(onClickMock).toHaveBeenCalledTimes(1);

      rerender(
        <ProgressData 
          {...defaultProps} 
          text="Very long progress item text that should be handled properly"
          onClick={onClickMock}
        />
      );
      fireEvent.press(screen.getByText('Very long progress item text that should be handled properly'));
      expect(onClickMock).toHaveBeenCalledTimes(2);
    });

    test('should handle item press with different text content', () => {
      const onClickMock = jest.fn();
      
      const { rerender } = renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      fireEvent.press(screen.getByText('Sample progress item'));
      expect(onClickMock).toHaveBeenCalledTimes(1);

      rerender(
        <ProgressData 
          {...defaultProps} 
          text="Different progress item"
          onClick={onClickMock}
        />
      );
      fireEvent.press(screen.getByText('Different progress item'));
      expect(onClickMock).toHaveBeenCalledTimes(2);
    });
  });

  // ============================================================================
  // EDGE CASE TESTS
  // ============================================================================

  describe('Edge Case Tests', () => {
    test('should handle onClick function that throws error', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const onClickMock = jest.fn(() => {
        throw new Error('Test error');
      });
      
      renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      expect(() => {
        fireEvent.press(screen.getByText('Sample progress item'));
      }).toThrow('Test error');

      consoleSpy.mockRestore();
    });

    test('should handle onClick function that returns undefined', () => {
      const onClickMock = jest.fn(() => undefined);
      
      renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      fireEvent.press(screen.getByText('Sample progress item'));
      expect(onClickMock).toHaveBeenCalledTimes(1);
    });

    test('should handle onClick function that returns null', () => {
      const onClickMock = jest.fn(() => null);
      
      renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      fireEvent.press(screen.getByText('Sample progress item'));
      expect(onClickMock).toHaveBeenCalledTimes(1);
    });

    test('should handle onClick function that returns a value', () => {
      const onClickMock = jest.fn(() => 'return value');
      
      renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      fireEvent.press(screen.getByText('Sample progress item'));
      expect(onClickMock).toHaveBeenCalledTimes(1);
    });

    test('should handle async onClick function', async () => {
      const onClickMock = jest.fn(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'async result';
      });
      
      renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      fireEvent.press(screen.getByText('Sample progress item'));
      expect(onClickMock).toHaveBeenCalledTimes(1);
      
      // Wait for async operation to complete
      await new Promise(resolve => setTimeout(resolve, 20));
    });

    test('should handle onClick function with parameters', () => {
      const onClickMock = jest.fn((param) => param);
      
      renderWithProviders(
        <ProgressData 
          {...defaultProps} 
          onClick={() => onClickMock('test param')}
        />
      );

      fireEvent.press(screen.getByText('Sample progress item'));
      expect(onClickMock).toHaveBeenCalledWith('test param');
    });

    test('should handle very long text', () => {
      const veryLongText = 'A'.repeat(1000); // 1000 character text
      renderWithProviders(
        <ProgressData {...defaultProps} text={veryLongText} />
      );

      expect(screen.getByText(veryLongText)).toBeTruthy();
    });

    test('should handle text with only whitespace', () => {
      renderWithProviders(
        <ProgressData {...defaultProps} text="   " />
      );

      expect(screen.getByText('   ')).toBeTruthy();
    });

    test('should handle text with control characters', () => {
      const controlText = 'Progress with \x00\x01\x02\x03 control chars';
      renderWithProviders(
        <ProgressData {...defaultProps} text={controlText} />
      );

      expect(screen.getByText(controlText)).toBeTruthy();
    });

    test('should handle text with script-like content', () => {
      const scriptText = '<script>alert("test")</script>';
      renderWithProviders(
        <ProgressData {...defaultProps} text={scriptText} />
      );

      expect(screen.getByText(scriptText)).toBeTruthy();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    test('should be accessible with proper content', () => {
      renderWithProviders(<ProgressData {...defaultProps} />);

      expect(screen.getByText('Sample progress item')).toBeTruthy();
    });

    test('should handle accessibility focus', () => {
      renderWithProviders(<ProgressData {...defaultProps} />);

      const item = screen.getByText('Sample progress item');
      fireEvent(item, 'focus');
      expect(item).toBeTruthy();
    });

    test('should handle accessibility blur', () => {
      renderWithProviders(<ProgressData {...defaultProps} />);

      const item = screen.getByText('Sample progress item');
      fireEvent(item, 'blur');
      expect(item).toBeTruthy();
    });

    test('should be accessible with descriptive text', () => {
      renderWithProviders(
        <ProgressData {...defaultProps} text="Task completed successfully" />
      );

      expect(screen.getByText('Task completed successfully')).toBeTruthy();
    });

    test('should handle screen reader announcements', () => {
      renderWithProviders(<ProgressData {...defaultProps} />);

      expect(screen.getByText('Sample progress item')).toBeTruthy();
    });

    test('should be accessible with different languages', () => {
      const multilingualTexts = [
        'Task completed',
        'Tâche terminée',
        'Tarea completada',
        'タスク完了',
        '任务完成'
      ];
      
      multilingualTexts.forEach(text => {
        const { unmount } = renderWithProviders(
          <ProgressData {...defaultProps} text={text} />
        );
        
        expect(screen.getByText(text)).toBeTruthy();
        unmount();
      });
    });

    test('should be accessible with different content types', () => {
      renderWithProviders(<ProgressData {...defaultProps} />);

      expect(screen.getByText('Sample progress item')).toBeTruthy();
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    test('should render quickly with basic props', () => {
      const startTime = Date.now();
      
      renderWithProviders(<ProgressData {...defaultProps} />);
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(100); // Should render in less than 100ms
    });

    test('should handle rapid item presses efficiently', () => {
      const onClickMock = jest.fn();
      
      renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      const item = screen.getByText('Sample progress item');
      const startTime = Date.now();
      
      // Simulate rapid presses
      for (let i = 0; i < 20; i++) {
        fireEvent.press(item);
      }
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(500); // Should handle 20 presses in less than 500ms
      expect(onClickMock).toHaveBeenCalledTimes(20);
    });

    test('should handle text changes efficiently', () => {
      const { rerender } = renderWithProviders(<ProgressData {...defaultProps} />);

      const startTime = Date.now();
      
      rerender(<ProgressData {...defaultProps} text="Updated text" />);
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(50); // Should update in less than 50ms
      expect(screen.getByText('Updated text')).toBeTruthy();
    });

    test('should handle multiple re-renders efficiently', () => {
      const { rerender } = renderWithProviders(<ProgressData {...defaultProps} />);

      const startTime = Date.now();
      
      for (let i = 1; i <= 10; i++) {
        rerender(<ProgressData {...defaultProps} text={`Text ${i}`} />);
      }
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(200); // Should handle 10 re-renders in less than 200ms
      expect(screen.getByText('Text 10')).toBeTruthy();
    });

    test('should handle large text efficiently', () => {
      const largeText = 'A'.repeat(500); // 500 character text
      
      const startTime = Date.now();
      
      renderWithProviders(
        <ProgressData {...defaultProps} text={largeText} />
      );
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(150); // Should render large text in less than 150ms
      expect(screen.getByText(largeText)).toBeTruthy();
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should work with navigation context', () => {
      const onClickMock = jest.fn();
      
      renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      fireEvent.press(screen.getByText('Sample progress item'));
      expect(onClickMock).toHaveBeenCalledTimes(1);
    });

    test('should work with Redux context', () => {
      const onClickMock = jest.fn();
      
      renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      fireEvent.press(screen.getByText('Sample progress item'));
      expect(onClickMock).toHaveBeenCalledTimes(1);
    });

    test('should work with both navigation and Redux context', () => {
      const onClickMock = jest.fn();
      
      renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      fireEvent.press(screen.getByText('Sample progress item'));
      expect(onClickMock).toHaveBeenCalledTimes(1);
    });

    test('should work with other list components', () => {
      renderWithProviders(
        <>
          <ProgressData {...defaultProps} />
          <ProgressData {...defaultProps} text="Item 2" />
          <ProgressData {...defaultProps} text="Item 3" />
        </>
      );

      expect(screen.getByText('Sample progress item')).toBeTruthy();
      expect(screen.getByText('Item 2')).toBeTruthy();
      expect(screen.getByText('Item 3')).toBeTruthy();
    });

    test('should work with form components', () => {
      const onClickMock = jest.fn();
      
      renderWithProviders(
        <div>
          <input placeholder="Test input" />
          <ProgressData {...defaultProps} onClick={onClickMock} />
        </div>
      );

      fireEvent.press(screen.getByText('Sample progress item'));
      expect(onClickMock).toHaveBeenCalledTimes(1);
    });

    test('should work with card components', () => {
      renderWithProviders(
        <div>
          <div>Card content</div>
          <ProgressData {...defaultProps} />
        </div>
      );

      expect(screen.getByText('Sample progress item')).toBeTruthy();
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling Tests', () => {
    test('should handle missing onClick prop gracefully', () => {
      // This test ensures the component doesn't crash when onClick is undefined
      expect(() => {
        renderWithProviders(
          <ProgressData {...defaultProps} onClick={undefined} />
        );
      }).not.toThrow();
    });

    test('should handle null onClick prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <ProgressData {...defaultProps} onClick={null} />
        );
      }).not.toThrow();
    });

    test('should handle missing text prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <ProgressData {...defaultProps} text={undefined} />
        );
      }).not.toThrow();
    });

    test('should handle null text prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <ProgressData {...defaultProps} text={null} />
        );
      }).not.toThrow();
    });

    test('should handle empty text prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <ProgressData {...defaultProps} text="" />
        );
      }).not.toThrow();
    });
  });

  // ============================================================================
  // STYLING TESTS
  // ============================================================================

  describe('Styling Tests', () => {
    test('should apply default styling correctly', () => {
      renderWithProviders(<ProgressData {...defaultProps} />);

      expect(screen.getByText('Sample progress item')).toBeTruthy();
    });

    test('should handle item with different text lengths', () => {
      const shortText = "A";
      const longText = "This is a very long progress item text that should be handled properly";
      
      const { rerender } = renderWithProviders(
        <ProgressData {...defaultProps} text={shortText} />
      );

      expect(screen.getByText(shortText)).toBeTruthy();

      rerender(<ProgressData {...defaultProps} text={longText} />);
      expect(screen.getByText(longText)).toBeTruthy();
    });

    test('should maintain consistent styling across re-renders', () => {
      const { rerender } = renderWithProviders(<ProgressData {...defaultProps} />);

      const item1 = screen.getByText('Sample progress item');
      
      rerender(<ProgressData {...defaultProps} text="Updated item" />);
      const item2 = screen.getByText('Updated item');
      
      expect(item1).toBeTruthy();
      expect(item2).toBeTruthy();
    });

    test('should handle item with different text styles', () => {
      const textStyles = [
        'Task completed',
        'IN PROGRESS',
        'pending',
        'APPROVED',
        'rejected'
      ];
      
      textStyles.forEach(text => {
        const { unmount } = renderWithProviders(
          <ProgressData {...defaultProps} text={text} />
        );
        
        expect(screen.getByText(text)).toBeTruthy();
        unmount();
      });
    });
  });

  // ============================================================================
  // USABILITY TESTS
  // ============================================================================

  describe('Usability Tests', () => {
    test('should provide clear item information', () => {
      renderWithProviders(<ProgressData {...defaultProps} />);

      expect(screen.getByText('Sample progress item')).toBeTruthy();
    });

    test('should be easily tappable', () => {
      const onClickMock = jest.fn();
      
      renderWithProviders(
        <ProgressData {...defaultProps} onClick={onClickMock} />
      );

      fireEvent.press(screen.getByText('Sample progress item'));
      expect(onClickMock).toHaveBeenCalledTimes(1);
    });

    test('should provide clear visual feedback', () => {
      renderWithProviders(<ProgressData {...defaultProps} />);

      expect(screen.getByText('Sample progress item')).toBeTruthy();
    });

    test('should have appropriate touch target size', () => {
      renderWithProviders(<ProgressData {...defaultProps} />);

      const item = screen.getByText('Sample progress item');
      expect(item).toBeTruthy();
    });

    test('should be discoverable in the UI', () => {
      renderWithProviders(<ProgressData {...defaultProps} />);

      expect(screen.getByText('Sample progress item')).toBeTruthy();
    });

    test('should provide meaningful item descriptions', () => {
      const meaningfulTexts = [
        'Task completed successfully',
        'Review in progress',
        'Awaiting approval',
        'Changes requested',
        'Ready for deployment'
      ];
      
      meaningfulTexts.forEach(text => {
        const { unmount } = renderWithProviders(
          <ProgressData {...defaultProps} text={text} />
        );
        
        expect(screen.getByText(text)).toBeTruthy();
        unmount();
      });
    });
  });
}); 