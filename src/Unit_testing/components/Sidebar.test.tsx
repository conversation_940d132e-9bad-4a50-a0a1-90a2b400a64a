import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import Sidebar from '../../../components/Sidebar';
jest.mock('../../../assets/svg/arrow_down_blue.svg', () => () => <svg data-testid="arrow-down-blue-icon" />);
jest.mock('../../../utils/Colors/Colors', () => ({ white: '#fff', blue: '#00f', textPrimary: '#333', lightBlueSidebar: '#eef', pipeIdTextBlack: '#111' }));
jest.mock('../../../utils/Scale/Scaling', () => ({ ms: (v) => v }));

const mockItems = [
  { entity_Code: '1', entity_Description: 'Step 1' },
  { entity_Code: '2', entity_Description: 'Step 2' },
  { entity_Code: '3', entity_Description: 'Step 3' },
];

describe('Sidebar', () => {
  it('renders all selected items', () => {
    render(<Sidebar selectedItems={mockItems} onItemPress={jest.fn()} />);
    expect(screen.getByText('Step 1')).toBeTruthy();
    expect(screen.getByText('Step 2')).toBeTruthy();
    expect(screen.getByText('Step 3')).toBeTruthy();
    expect(screen.getAllByTestId('arrow-down-blue-icon').length).toBe(2);
  });

  it('calls onItemPress when an item is pressed', () => {
    const onItemPress = jest.fn();
    render(<Sidebar selectedItems={mockItems} onItemPress={onItemPress} />);
    fireEvent.press(screen.getByText('Step 2'));
    expect(onItemPress).toHaveBeenCalledWith(mockItems[1]);
  });

  it('renders with only one item (no arrows)', () => {
    render(<Sidebar selectedItems={[mockItems[0]]} onItemPress={jest.fn()} />);
    expect(screen.getByText('Step 1')).toBeTruthy();
    expect(screen.queryByTestId('arrow-down-blue-icon')).toBeNull();
  });

  it('handles empty selectedItems', () => {
    render(<Sidebar selectedItems={[]} onItemPress={jest.fn()} />);
    // Should render nothing but not crash
  });

  it('matches snapshot', () => {
    const { toJSON } = render(<Sidebar selectedItems={mockItems} onItemPress={jest.fn()} />);
    expect(toJSON()).toMatchSnapshot();
  });
}); 