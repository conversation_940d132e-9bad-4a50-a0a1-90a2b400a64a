import React from 'react';
import { render, fireEvent, screen, waitFor } from '@testing-library/react-native';
import TopTabNavigator from '../../../components/TopTabNavigator';
import { renderWithProviders, setupTest, teardownTest } from '../../../utils/testing/testHelpers';

// Mock react-native/Libraries/Utilities/Dimensions
jest.mock('react-native/Libraries/Utilities/Dimensions', () => ({
  get: jest.fn(() => ({ width: 375, height: 812 })),
}));

// Mock the Colors utility
jest.mock('../../../utils/Colors/Colors', () => ({
  tabBgColoor: '#f5f5f5',
  white: '#FFFFFF',
  secondary: '#3182ce',
  textInputBlack: '#333333',
}));

// Mock the Scale utility
jest.mock('../../../utils/Scale/Scaling', () => ({
  ms: jest.fn((value) => value * 2), // Simple mock that doubles the value
}));

// Mock the Fonts utility
jest.mock('../../../components/Fonts', () => ({
  AppFonts: {
    SemiBold: 'SemiBold',
  },
}));

describe('TopTabNavigator', () => {
  const mockTabs = [
    { key: 'tab1', label: 'Tab 1' },
    { key: 'tab2', label: 'Tab 2' },
    { key: 'tab3', label: 'Tab 3' },
  ];

  const mockOnTabChange = jest.fn();

  beforeEach(() => {
    setupTest();
    mockOnTabChange.mockClear();
  });

  afterEach(() => {
    teardownTest();
    jest.clearAllMocks();
  });

  // ============================================================================
  // RENDER TESTS
  // ============================================================================

  describe('Render Tests', () => {
    test('should render with basic tabs', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
      expect(screen.getByText('Tab 2')).toBeTruthy();
      expect(screen.getByText('Tab 3')).toBeTruthy();
    });

    test('should render with single tab', () => {
      const singleTab = [{ key: 'single', label: 'Single Tab' }];
      
      renderWithProviders(
        <TopTabNavigator
          tabs={singleTab}
          activeTab="single"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Single Tab')).toBeTruthy();
    });

    test('should render with empty tabs array', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={[]}
          activeTab=""
          onTabChange={mockOnTabChange}
        />
      );

      // Should render container but no tabs
      expect(screen.queryByText('Tab 1')).toBeFalsy();
    });

    test('should render with custom container style', () => {
      const customStyle = { backgroundColor: 'red', height: 100 };
      
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
          containerStyle={customStyle}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
    });

    test('should render with long tab labels', () => {
      const longTabs = [
        { key: 'long1', label: 'This is a very long tab label that might wrap' },
        { key: 'long2', label: 'Another extremely long tab label for testing' },
      ];
      
      renderWithProviders(
        <TopTabNavigator
          tabs={longTabs}
          activeTab="long1"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('This is a very long tab label that might wrap')).toBeTruthy();
      expect(screen.getByText('Another extremely long tab label for testing')).toBeTruthy();
    });

    test('should render with special characters in labels', () => {
      const specialTabs = [
        { key: 'special1', label: 'Tab with émojis 🚀' },
        { key: 'special2', label: 'Tab with symbols !@#$%^&*()' },
      ];
      
      renderWithProviders(
        <TopTabNavigator
          tabs={specialTabs}
          activeTab="special1"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Tab with émojis 🚀')).toBeTruthy();
      expect(screen.getByText('Tab with symbols !@#$%^&*()')).toBeTruthy();
    });

    test('should render with numeric tab keys', () => {
      const numericTabs = [
        { key: '1', label: 'First Tab' },
        { key: '2', label: 'Second Tab' },
      ];
      
      renderWithProviders(
        <TopTabNavigator
          tabs={numericTabs}
          activeTab="1"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('First Tab')).toBeTruthy();
      expect(screen.getByText('Second Tab')).toBeTruthy();
    });

    test('should render with empty tab labels', () => {
      const emptyLabelTabs = [
        { key: 'empty1', label: '' },
        { key: 'empty2', label: '   ' },
      ];
      
      renderWithProviders(
        <TopTabNavigator
          tabs={emptyLabelTabs}
          activeTab="empty1"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('')).toBeTruthy();
    });
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  describe('Interaction Tests', () => {
    test('should call onTabChange when tab is pressed', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      fireEvent.press(screen.getByText('Tab 2'));
      expect(mockOnTabChange).toHaveBeenCalledWith('tab2');
    });

    test('should call onTabChange when active tab is pressed', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      fireEvent.press(screen.getByText('Tab 1'));
      expect(mockOnTabChange).toHaveBeenCalledWith('tab1');
    });

    test('should handle multiple rapid tab presses', async () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      const tab2 = screen.getByText('Tab 2');
      const tab3 = screen.getByText('Tab 3');

      fireEvent.press(tab2);
      fireEvent.press(tab3);
      fireEvent.press(tab2);
      fireEvent.press(tab3);

      await waitFor(() => {
        expect(mockOnTabChange).toHaveBeenCalledTimes(4);
      });

      expect(mockOnTabChange).toHaveBeenCalledWith('tab2');
      expect(mockOnTabChange).toHaveBeenCalledWith('tab3');
    });

    test('should handle tab press without onTabChange handler', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={undefined as any}
        />
      );

      // Should not throw error when pressed without handler
      expect(() => {
        fireEvent.press(screen.getByText('Tab 2'));
      }).not.toThrow();
    });

    test('should handle tab press with null onTabChange handler', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={null as any}
        />
      );

      // Should not throw error when pressed with null handler
      expect(() => {
        fireEvent.press(screen.getByText('Tab 2'));
      }).not.toThrow();
    });
  });

  // ============================================================================
  // STATE CHANGE TESTS
  // ============================================================================

  describe('State Change Tests', () => {
    test('should update active tab when prop changes', () => {
      const { rerender } = renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      // Initially tab1 should be active
      expect(screen.getByText('Tab 1')).toBeTruthy();

      // Change active tab to tab2
      rerender(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab2"
          onTabChange={mockOnTabChange}
        />
      );

      // Tab2 should now be active
      expect(screen.getByText('Tab 2')).toBeTruthy();
    });

    test('should handle tabs array changes', () => {
      const { rerender } = renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
      expect(screen.getByText('Tab 2')).toBeTruthy();
      expect(screen.getByText('Tab 3')).toBeTruthy();

      const newTabs = [
        { key: 'new1', label: 'New Tab 1' },
        { key: 'new2', label: 'New Tab 2' },
      ];

      rerender(
        <TopTabNavigator
          tabs={newTabs}
          activeTab="new1"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('New Tab 1')).toBeTruthy();
      expect(screen.getByText('New Tab 2')).toBeTruthy();
      expect(screen.queryByText('Tab 1')).toBeFalsy();
    });

    test('should handle container style changes', () => {
      const { rerender } = renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
          containerStyle={{ backgroundColor: 'red' }}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();

      rerender(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
          containerStyle={{ backgroundColor: 'blue' }}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
    });
  });

  // ============================================================================
  // PROP VALIDATION TESTS
  // ============================================================================

  describe('Prop Validation Tests', () => {
    test('should handle undefined tabs prop', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={undefined as any}
          activeTab=""
          onTabChange={mockOnTabChange}
        />
      );

      // Should not crash with undefined tabs
      expect(screen.queryByText('Tab 1')).toBeFalsy();
    });

    test('should handle null tabs prop', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={null as any}
          activeTab=""
          onTabChange={mockOnTabChange}
        />
      );

      // Should not crash with null tabs
      expect(screen.queryByText('Tab 1')).toBeFalsy();
    });

    test('should handle undefined activeTab prop', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab={undefined as any}
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
      expect(screen.getByText('Tab 2')).toBeTruthy();
      expect(screen.getByText('Tab 3')).toBeTruthy();
    });

    test('should handle null activeTab prop', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab={null as any}
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
      expect(screen.getByText('Tab 2')).toBeTruthy();
      expect(screen.getByText('Tab 3')).toBeTruthy();
    });

    test('should handle undefined onTabChange prop', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={undefined as any}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
    });

    test('should handle null onTabChange prop', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={null as any}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
    });

    test('should handle undefined containerStyle prop', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
          containerStyle={undefined}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
    });

    test('should handle null containerStyle prop', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
          containerStyle={null as any}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
    });

    test('should handle tabs with missing key property', () => {
      const invalidTabs = [
        { label: 'Tab without key' },
        { key: 'valid', label: 'Valid Tab' },
      ] as any;
      
      renderWithProviders(
        <TopTabNavigator
          tabs={invalidTabs}
          activeTab="valid"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Valid Tab')).toBeTruthy();
    });

    test('should handle tabs with missing label property', () => {
      const invalidTabs = [
        { key: 'noLabel' },
        { key: 'valid', label: 'Valid Tab' },
      ] as any;
      
      renderWithProviders(
        <TopTabNavigator
          tabs={invalidTabs}
          activeTab="valid"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Valid Tab')).toBeTruthy();
    });
  });

  // ============================================================================
  // ERROR BOUNDARY TESTS
  // ============================================================================

  describe('Error Boundary Tests', () => {
    test('should handle onTabChange throwing error', () => {
      const errorOnTabChange = jest.fn().mockImplementation(() => {
        throw new Error('Test error');
      });
      
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={errorOnTabChange}
        />
      );

      // Should not crash the component
      expect(() => {
        fireEvent.press(screen.getByText('Tab 2'));
      }).not.toThrow();
    });

    test('should handle malformed tabs data', () => {
      const malformedTabs = [
        { key: 'valid', label: 'Valid' },
        null,
        undefined,
        { key: 'another', label: 'Another' },
      ] as any;
      
      renderWithProviders(
        <TopTabNavigator
          tabs={malformedTabs}
          activeTab="valid"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Valid')).toBeTruthy();
      expect(screen.getByText('Another')).toBeTruthy();
    });

    test('should handle non-string activeTab', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab={123 as any}
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
      expect(screen.getByText('Tab 2')).toBeTruthy();
      expect(screen.getByText('Tab 3')).toBeTruthy();
    });
  });

  // ============================================================================
  // EDGE CASES
  // ============================================================================

  describe('Edge Cases', () => {
    test('should handle very large number of tabs', () => {
      const manyTabs = Array.from({ length: 50 }, (_, i) => ({
        key: `tab${i}`,
        label: `Tab ${i}`,
      }));
      
      renderWithProviders(
        <TopTabNavigator
          tabs={manyTabs}
          activeTab="tab0"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Tab 0')).toBeTruthy();
      expect(screen.getByText('Tab 49')).toBeTruthy();
    });

    test('should handle very long tab keys', () => {
      const longKeyTabs = [
        { key: 'a'.repeat(1000), label: 'Long Key Tab' },
        { key: 'normal', label: 'Normal Tab' },
      ];
      
      renderWithProviders(
        <TopTabNavigator
          tabs={longKeyTabs}
          activeTab="normal"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Long Key Tab')).toBeTruthy();
      expect(screen.getByText('Normal Tab')).toBeTruthy();
    });

    test('should handle tabs with duplicate keys', () => {
      const duplicateTabs = [
        { key: 'duplicate', label: 'First Duplicate' },
        { key: 'duplicate', label: 'Second Duplicate' },
        { key: 'unique', label: 'Unique Tab' },
      ];
      
      renderWithProviders(
        <TopTabNavigator
          tabs={duplicateTabs}
          activeTab="unique"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('First Duplicate')).toBeTruthy();
      expect(screen.getByText('Second Duplicate')).toBeTruthy();
      expect(screen.getByText('Unique Tab')).toBeTruthy();
    });

    test('should handle activeTab that does not exist in tabs', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="nonexistent"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
      expect(screen.getByText('Tab 2')).toBeTruthy();
      expect(screen.getByText('Tab 3')).toBeTruthy();
    });

    test('should handle complex container styles', () => {
      const complexStyle = {
        backgroundColor: 'red',
        borderWidth: 2,
        borderRadius: 10,
        padding: 20,
        margin: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      };
      
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
          containerStyle={complexStyle}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    test('should have accessible tab buttons', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      const tabButtons = screen.getAllByRole('button');
      expect(tabButtons.length).toBe(3);
    });

    test('should have proper text accessibility', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      const tabTexts = screen.getAllByText(/Tab \d/);
      tabTexts.forEach(text => {
        expect(text.props.accessible).toBe(true);
      });
    });

    test('should handle screen reader navigation', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      const tabButtons = screen.getAllByRole('button');
      expect(tabButtons[0].props.accessibilityRole).toBe('button');
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    test('should render efficiently with minimal re-renders', () => {
      const { rerender } = renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      // Re-render with same props should not cause issues
      rerender(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );
      rerender(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
    });

    test('should handle rapid prop changes efficiently', () => {
      const { rerender } = renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      // Rapid prop changes
      rerender(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab2"
          onTabChange={mockOnTabChange}
        />
      );
      rerender(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab3"
          onTabChange={mockOnTabChange}
        />
      );
      rerender(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
    });

    test('should handle large tab arrays efficiently', () => {
      const largeTabs = Array.from({ length: 100 }, (_, i) => ({
        key: `tab${i}`,
        label: `Tab ${i}`,
      }));
      
      renderWithProviders(
        <TopTabNavigator
          tabs={largeTabs}
          activeTab="tab0"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Tab 0')).toBeTruthy();
      expect(screen.getByText('Tab 99')).toBeTruthy();
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should work with Redux store', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
      expect(screen.getByText('Tab 2')).toBeTruthy();
      expect(screen.getByText('Tab 3')).toBeTruthy();
    });

    test('should work with navigation container', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
    });

    test('should integrate with parent components', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
          containerStyle={{ backgroundColor: 'red' }}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
      expect(screen.getByText('Tab 2')).toBeTruthy();
      expect(screen.getByText('Tab 3')).toBeTruthy();
    });

    test('should handle complete user workflow', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      // User workflow: switch between tabs
      fireEvent.press(screen.getByText('Tab 2'));
      expect(mockOnTabChange).toHaveBeenCalledWith('tab2');

      fireEvent.press(screen.getByText('Tab 3'));
      expect(mockOnTabChange).toHaveBeenCalledWith('tab3');

      fireEvent.press(screen.getByText('Tab 1'));
      expect(mockOnTabChange).toHaveBeenCalledWith('tab1');
    });
  });

  // ============================================================================
  // CROSS-PLATFORM COMPATIBILITY TESTS
  // ============================================================================

  describe('Cross-Platform Compatibility Tests', () => {
    test('should render consistently across platforms', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      // Should render all tabs regardless of platform
      expect(screen.getByText('Tab 1')).toBeTruthy();
      expect(screen.getByText('Tab 2')).toBeTruthy();
      expect(screen.getByText('Tab 3')).toBeTruthy();
    });

    test('should handle platform-specific interactions', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );

      // Should handle press events consistently
      fireEvent.press(screen.getByText('Tab 2'));
      expect(mockOnTabChange).toHaveBeenCalledWith('tab2');
    });

    test('should handle platform-specific styles', () => {
      renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
          containerStyle={{ elevation: 5, shadowOpacity: 0.25 }}
        />
      );

      expect(screen.getByText('Tab 1')).toBeTruthy();
    });
  });

  // ============================================================================
  // SNAPSHOT TESTS
  // ============================================================================

  describe('Snapshot Tests', () => {
    test('should match snapshot for basic tabs', () => {
      const { toJSON } = renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
        />
      );
      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot for active tab', () => {
      const { toJSON } = renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab2"
          onTabChange={mockOnTabChange}
        />
      );
      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot with custom styles', () => {
      const { toJSON } = renderWithProviders(
        <TopTabNavigator
          tabs={mockTabs}
          activeTab="tab1"
          onTabChange={mockOnTabChange}
          containerStyle={{ backgroundColor: 'red' }}
        />
      );
      expect(toJSON()).toMatchSnapshot();
    });

    test('should match snapshot with single tab', () => {
      const { toJSON } = renderWithProviders(
        <TopTabNavigator
          tabs={[{ key: 'single', label: 'Single Tab' }]}
          activeTab="single"
          onTabChange={mockOnTabChange}
        />
      );
      expect(toJSON()).toMatchSnapshot();
    });
  });
}); 