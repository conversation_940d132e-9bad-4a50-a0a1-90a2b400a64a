import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import ButtonComponent from '../../../components/ButtonComponent';
import { renderWithProviders } from '../../../utils/testing/testHelpers';

// Mock dependencies
jest.mock('../../utils/Colors/Colors', () => ({
  darkBlue: '#1a365d',
  brandBlue: '#3182ce',
  white: '#FFFFFF',
  blue: '#3182ce',
  borderColor: '#e2e8f0',
  grey: '#ccc',
}));
jest.mock('../../utils/Scale/Scaling', () => ({ ms: (v: number) => v }));
jest.mock('../../components/Fonts', () => ({ AppFonts: { Bold: 'Bold' } }));
jest.mock('react-native-svg', () => ({
  Svg: ({ children }: any) => <>{children}</>,
  Rect: () => <></>,
  Defs: ({ children }: any) => <>{children}</>,
  LinearGradient: ({ children }: any) => <>{children}</>,
  Stop: () => <></>,
}));

describe('ButtonComponent', () => {
  // 1. Normal usage
  it('renders with required props and responds to press', () => {
    const onPress = jest.fn();
    renderWithProviders(<ButtonComponent onPress={onPress} title="Test" />);
    fireEvent.press(screen.getByTestId('button'));
    expect(onPress).toHaveBeenCalled();
  });

  // 2. Disabled state
  it('does not call onPress when disabled', () => {
    const onPress = jest.fn();
    renderWithProviders(<ButtonComponent onPress={onPress} title="Disabled" disabled />);
    fireEvent.press(screen.getByTestId('button'));
    expect(onPress).not.toHaveBeenCalled();
  });

  // 3. Secondary button
  it('renders and responds to secondary button press', () => {
    const onSecondaryPress = jest.fn();
    renderWithProviders(
      <ButtonComponent
        onPress={() => {}}
        title="Primary"
        showSecondaryButton
        secondaryButtonTitle="Secondary"
        onSecondaryPress={onSecondaryPress}
      />
    );
    fireEvent.press(screen.getByText('Secondary'));
    expect(onSecondaryPress).toHaveBeenCalled();
  });

  // 4. Edge: missing handlers
  it('does not crash if onSecondaryPress is missing', () => {
    renderWithProviders(
      <ButtonComponent
        onPress={() => {}}
        title="Primary"
        showSecondaryButton
        secondaryButtonTitle="Secondary"
      />
    );
    fireEvent.press(screen.getByText('Secondary'));
    // No error expected
  });

  // 5. Edge: empty/undefined titles
  it('renders with empty and undefined secondary button title', () => {
    renderWithProviders(
      <ButtonComponent
        onPress={() => {}}
        title="Primary"
        showSecondaryButton
        secondaryButtonTitle={undefined}
      />
    );
    expect(screen.getByText('Primary')).toBeTruthy();
    expect(screen.getByText('undefined')).toBeTruthy();
  });

  // 6. Custom styles
  it('applies custom mainContainerStyle and customWidth', () => {
    renderWithProviders(
      <ButtonComponent
        onPress={() => {}}
        title="Styled"
        mainContainerStyle={{ backgroundColor: 'red' }}
        customWidth={{ width: 123 }}
      />
    );
    expect(screen.getByText('Styled')).toBeTruthy();
  });

  // 7. Rapid presses
  it('handles rapid presses without error', () => {
    const onPress = jest.fn();
    renderWithProviders(<ButtonComponent onPress={onPress} title="Rapid" />);
    for (let i = 0; i < 5; i++) {
      fireEvent.press(screen.getByTestId('button'));
    }
    expect(onPress).toHaveBeenCalledTimes(5);
  });
}); 