import React from 'react';
import { render, screen } from '@testing-library/react-native';
import ProgressDays from '../../../components/ProgressDays';

// Mock Colors and Scaling
jest.mock('../../../utils/Colors/Colors', () => ({
  textInputBlack: '#222',
  textPrimary: '#333',
  white: '#fff',
}));
jest.mock('../../../utils/Scale/Scaling', () => ({ ms: (v: number) => v }));
jest.mock('../../../utils/Strings/Strings', () => ({
  DailyProgressApprover: {
    progressQty: 'Progress Qty',
    manDays: 'Man Days',
    date: 'Date',
  },
}));

describe('ProgressDays', () => {
  it('renders with all props', () => {
    render(
      <ProgressDays progressDays="5" manDays="10" date="2024-06-01" />
    );
    expect(screen.getByText('Progress Qty')).toBeTruthy();
    expect(screen.getByText('5')).toBeTruthy();
    expect(screen.getByText('Man Days')).toBeTruthy();
    expect(screen.getByText('10')).toBeTruthy();
    expect(screen.getByText('Date')).toBeTruthy();
    expect(screen.getByText('2024-06-01')).toBeTruthy();
  });

  it('renders with empty values', () => {
    render(
      <ProgressDays progressDays="" manDays="" date="" />
    );
    expect(screen.getByText('')).toBeTruthy();
  });

  it('renders with custom style', () => {
    const { toJSON } = render(
      <ProgressDays progressDays="1" manDays="2" date="2024-01-01" customStyle={{ backgroundColor: 'red' }} />
    );
    expect(toJSON()).toMatchSnapshot();
  });

  it('handles long string values', () => {
    render(
      <ProgressDays progressDays={"1234567890"} manDays={"9999999999"} date={"2024-12-31"} />
    );
    expect(screen.getByText('1234567890')).toBeTruthy();
    expect(screen.getByText('9999999999')).toBeTruthy();
    expect(screen.getByText('2024-12-31')).toBeTruthy();
  });

  it('is accessible', () => {
    const { getByText } = render(
      <ProgressDays progressDays="2" manDays="3" date="2024-05-01" />
    );
    expect(getByText('Progress Qty').props.accessibilityRole).toBeUndefined();
    expect(getByText('Man Days').props.accessibilityRole).toBeUndefined();
    expect(getByText('Date').props.accessibilityRole).toBeUndefined();
  });
}); 