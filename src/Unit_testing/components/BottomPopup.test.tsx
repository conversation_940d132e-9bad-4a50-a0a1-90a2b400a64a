import React from 'react';
import { render, fireEvent, screen, waitFor } from '@testing-library/react-native';
import BottomPopup from '../../../components/BottomPopup';
import { renderWithProviders, setupTest, teardownTest } from '../../../utils/testing/testHelpers';

// Mock the Colors utility
jest.mock('../../../utils/Colors/Colors', () => ({
  white: '#FFFFFF',
  darkBlue: '#1a365d',
}));

// Mock the Scale utility
jest.mock('../../../utils/Scale/Scaling', () => ({
  ms: jest.fn((value) => value * 2), // Simple mock that doubles the value
}));

describe('BottomPopup', () => {
  beforeEach(() => {
    setupTest();
  });

  afterEach(() => {
    teardownTest();
    jest.clearAllMocks();
  });

  // ============================================================================
  // RENDER TESTS
  // ============================================================================

  describe('Render Tests', () => {
    test('should render correctly when visible is true', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      expect(screen.getByText('Test Content')).toBeTruthy();
    });

    test('should not render when visible is false', () => {
      renderWithProviders(
        <BottomPopup
          visible={false}
          onCancelPress={() => {}}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      expect(screen.queryByText('Test Content')).toBeFalsy();
    });

    test('should render with custom minHeight', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          minHeight={200}
          onCancelPress={() => {}}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      expect(screen.getByText('Test Content')).toBeTruthy();
    });

    test('should render with string minHeight', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          minHeight="50%"
          onCancelPress={() => {}}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      expect(screen.getByText('Test Content')).toBeTruthy();
    });

    test('should render with custom innerContainerStyle', () => {
      const customStyle = { backgroundColor: 'red' };
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
          innerContainerStyle={customStyle}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      expect(screen.getByText('Test Content')).toBeTruthy();
    });

    test('should render with complex children', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          <div>
            <h1>Title</h1>
            <p>Description</p>
            <button>Action</button>
          </div>
        </BottomPopup>
      );

      expect(screen.getByText('Title')).toBeTruthy();
      expect(screen.getByText('Description')).toBeTruthy();
      expect(screen.getByText('Action')).toBeTruthy();
    });

    test('should render with empty children', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          {null}
        </BottomPopup>
      );

      // Should render without crashing
      expect(true).toBeTruthy();
    });

    test('should render with multiple children', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          <div>Child 1</div>
          <div>Child 2</div>
          <div>Child 3</div>
        </BottomPopup>
      );

      expect(screen.getByText('Child 1')).toBeTruthy();
      expect(screen.getByText('Child 2')).toBeTruthy();
      expect(screen.getByText('Child 3')).toBeTruthy();
    });

    test('should render with React components as children', () => {
      const TestComponent = () => <div>Test Component</div>;
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          <TestComponent />
        </BottomPopup>
      );

      expect(screen.getByText('Test Component')).toBeTruthy();
    });
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  describe('Interaction Tests', () => {
    test('should call onCancelPress when overlay is pressed', () => {
      const onCancelPressMock = jest.fn();
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={onCancelPressMock}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      // Simulate pressing the overlay
      fireEvent.press(screen.getByText('Test Content').parentElement);
      expect(onCancelPressMock).toHaveBeenCalledTimes(1);
    });

    test('should call onCancelPress when modal is closed via onRequestClose', () => {
      const onCancelPressMock = jest.fn();
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={onCancelPressMock}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      // Simulate modal close request
      fireEvent(screen.getByText('Test Content'), 'onRequestClose');
      expect(onCancelPressMock).toHaveBeenCalledTimes(1);
    });

    test('should handle multiple onCancelPress calls', () => {
      const onCancelPressMock = jest.fn();
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={onCancelPressMock}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      // Simulate multiple overlay presses
      const overlay = screen.getByText('Test Content').parentElement;
      fireEvent.press(overlay);
      fireEvent.press(overlay);
      fireEvent.press(overlay);

      expect(onCancelPressMock).toHaveBeenCalledTimes(3);
    });

    test('should handle onCancelPress with different visibility states', () => {
      const onCancelPressMock = jest.fn();
      
      const { rerender } = renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={onCancelPressMock}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      fireEvent.press(screen.getByText('Test Content').parentElement);
      expect(onCancelPressMock).toHaveBeenCalledTimes(1);

      // Change visibility and test again
      rerender(
        <BottomPopup
          visible={false}
          onCancelPress={onCancelPressMock}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      // Should not be able to press when not visible
      expect(screen.queryByText('Test Content')).toBeFalsy();
    });

    test('should handle child component interactions', () => {
      const onCancelPressMock = jest.fn();
      const childButtonPressMock = jest.fn();
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={onCancelPressMock}
        >
          <div>
            <button onClick={childButtonPressMock}>Child Button</button>
          </div>
        </BottomPopup>
      );

      fireEvent.press(screen.getByText('Child Button'));
      expect(childButtonPressMock).toHaveBeenCalledTimes(1);
      expect(onCancelPressMock).not.toHaveBeenCalled();
    });
  });

  // ============================================================================
  // EDGE CASE TESTS
  // ============================================================================

  describe('Edge Case Tests', () => {
    test('should handle onCancelPress function that throws error', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const onCancelPressMock = jest.fn(() => {
        throw new Error('Test error');
      });
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={onCancelPressMock}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      expect(() => {
        fireEvent.press(screen.getByText('Test Content').parentElement);
      }).toThrow('Test error');

      consoleSpy.mockRestore();
    });

    test('should handle onCancelPress function that returns undefined', () => {
      const onCancelPressMock = jest.fn(() => undefined);
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={onCancelPressMock}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      fireEvent.press(screen.getByText('Test Content').parentElement);
      expect(onCancelPressMock).toHaveBeenCalledTimes(1);
    });

    test('should handle onCancelPress function that returns null', () => {
      const onCancelPressMock = jest.fn(() => null);
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={onCancelPressMock}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      fireEvent.press(screen.getByText('Test Content').parentElement);
      expect(onCancelPressMock).toHaveBeenCalledTimes(1);
    });

    test('should handle async onCancelPress function', async () => {
      const onCancelPressMock = jest.fn(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'async result';
      });
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={onCancelPressMock}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      fireEvent.press(screen.getByText('Test Content').parentElement);
      expect(onCancelPressMock).toHaveBeenCalledTimes(1);
      
      // Wait for async operation to complete
      await new Promise(resolve => setTimeout(resolve, 20));
    });

    test('should handle complex minHeight values', () => {
      const complexMinHeight = {
        height: 300,
        minHeight: 200,
        maxHeight: 500,
      };
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          minHeight={complexMinHeight}
          onCancelPress={() => {}}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      expect(screen.getByText('Test Content')).toBeTruthy();
    });

    test('should handle very large minHeight values', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          minHeight={10000}
          onCancelPress={() => {}}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      expect(screen.getByText('Test Content')).toBeTruthy();
    });

    test('should handle negative minHeight values', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          minHeight={-100}
          onCancelPress={() => {}}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      expect(screen.getByText('Test Content')).toBeTruthy();
    });

    test('should handle zero minHeight value', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          minHeight={0}
          onCancelPress={() => {}}
        >
          <div>Test Content</div>
        </BottomPopup>
      );

      expect(screen.getByText('Test Content')).toBeTruthy();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    test('should be accessible when visible', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          <div>Accessible Content</div>
        </BottomPopup>
      );

      expect(screen.getByText('Accessible Content')).toBeTruthy();
    });

    test('should handle accessibility focus', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          <div>Focus Content</div>
        </BottomPopup>
      );

      const content = screen.getByText('Focus Content');
      fireEvent(content, 'focus');
      expect(content).toBeTruthy();
    });

    test('should handle accessibility blur', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          <div>Blur Content</div>
        </BottomPopup>
      );

      const content = screen.getByText('Blur Content');
      fireEvent(content, 'blur');
      expect(content).toBeTruthy();
    });

    test('should be accessible with descriptive content', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          <div>
            <h1>Modal Title</h1>
            <p>This is a descriptive modal content</p>
          </div>
        </BottomPopup>
      );

      expect(screen.getByText('Modal Title')).toBeTruthy();
      expect(screen.getByText('This is a descriptive modal content')).toBeTruthy();
    });

    test('should handle screen reader announcements', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          <div aria-label="Modal content">Screen reader content</div>
        </BottomPopup>
      );

      expect(screen.getByText('Screen reader content')).toBeTruthy();
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    test('should render quickly when visible', () => {
      const startTime = Date.now();
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          <div>Quick Content</div>
        </BottomPopup>
      );
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(100); // Should render in less than 100ms
    });

    test('should handle visibility changes efficiently', () => {
      const { rerender } = renderWithProviders(
        <BottomPopup
          visible={false}
          onCancelPress={() => {}}
        >
          <div>Content</div>
        </BottomPopup>
      );

      const startTime = Date.now();
      
      rerender(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          <div>Content</div>
        </BottomPopup>
      );
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(50); // Should update in less than 50ms
      expect(screen.getByText('Content')).toBeTruthy();
    });

    test('should handle rapid visibility toggles efficiently', () => {
      const { rerender } = renderWithProviders(
        <BottomPopup
          visible={false}
          onCancelPress={() => {}}
        >
          <div>Toggle Content</div>
        </BottomPopup>
      );

      const startTime = Date.now();
      
      // Toggle visibility multiple times
      for (let i = 0; i < 10; i++) {
        rerender(
          <BottomPopup
            visible={i % 2 === 0}
            onCancelPress={() => {}}
          >
            <div>Toggle Content</div>
          </BottomPopup>
        );
      }
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(500); // Should handle 10 toggles in less than 500ms
    });

    test('should handle large content efficiently', () => {
      const largeContent = Array.from({ length: 100 }, (_, i) => (
        <div key={i}>Content item {i}</div>
      ));
      
      const startTime = Date.now();
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          {largeContent}
        </BottomPopup>
      );
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(200); // Should render large content in less than 200ms
      expect(screen.getByText('Content item 0')).toBeTruthy();
      expect(screen.getByText('Content item 99')).toBeTruthy();
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should work with navigation context', () => {
      const onCancelPressMock = jest.fn();
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={onCancelPressMock}
        >
          <div>Navigation Content</div>
        </BottomPopup>
      );

      fireEvent.press(screen.getByText('Navigation Content').parentElement);
      expect(onCancelPressMock).toHaveBeenCalledTimes(1);
    });

    test('should work with Redux context', () => {
      const onCancelPressMock = jest.fn();
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={onCancelPressMock}
        >
          <div>Redux Content</div>
        </BottomPopup>
      );

      fireEvent.press(screen.getByText('Redux Content').parentElement);
      expect(onCancelPressMock).toHaveBeenCalledTimes(1);
    });

    test('should work with both navigation and Redux context', () => {
      const onCancelPressMock = jest.fn();
      
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={onCancelPressMock}
        >
          <div>Full Context Content</div>
        </BottomPopup>
      );

      fireEvent.press(screen.getByText('Full Context Content').parentElement);
      expect(onCancelPressMock).toHaveBeenCalledTimes(1);
    });

    test('should work with other modal components', () => {
      const onCancelPressMock = jest.fn();
      
      renderWithProviders(
        <>
          <BottomPopup
            visible={true}
            onCancelPress={onCancelPressMock}
          >
            <div>Modal 1</div>
          </BottomPopup>
          <BottomPopup
            visible={false}
            onCancelPress={() => {}}
          >
            <div>Modal 2</div>
          </BottomPopup>
        </>
      );

      expect(screen.getByText('Modal 1')).toBeTruthy();
      expect(screen.queryByText('Modal 2')).toBeFalsy();
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling Tests', () => {
    test('should handle missing onCancelPress prop gracefully', () => {
      // This test ensures the component doesn't crash when onCancelPress is undefined
      expect(() => {
        renderWithProviders(
          <BottomPopup
            visible={true}
            onCancelPress={undefined}
          >
            <div>Test Content</div>
          </BottomPopup>
        );
      }).not.toThrow();
    });

    test('should handle null onCancelPress prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <BottomPopup
            visible={true}
            onCancelPress={null}
          >
            <div>Test Content</div>
          </BottomPopup>
        );
      }).not.toThrow();
    });

    test('should handle missing children prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <BottomPopup
            visible={true}
            onCancelPress={() => {}}
          >
            {undefined}
          </BottomPopup>
        );
      }).not.toThrow();
    });

    test('should handle null children prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <BottomPopup
            visible={true}
            onCancelPress={() => {}}
          >
            {null}
          </BottomPopup>
        );
      }).not.toThrow();
    });

    test('should handle missing minHeight prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <BottomPopup
            visible={true}
            onCancelPress={() => {}}
          >
            <div>Test Content</div>
          </BottomPopup>
        );
      }).not.toThrow();
    });

    test('should handle missing innerContainerStyle prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <BottomPopup
            visible={true}
            onCancelPress={() => {}}
          >
            <div>Test Content</div>
          </BottomPopup>
        );
      }).not.toThrow();
    });
  });

  // ============================================================================
  // ANIMATION TESTS
  // ============================================================================

  describe('Animation Tests', () => {
    test('should handle slide animation type', () => {
      renderWithProviders(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          <div>Animated Content</div>
        </BottomPopup>
      );

      expect(screen.getByText('Animated Content')).toBeTruthy();
    });

    test('should handle animation state changes', () => {
      const { rerender } = renderWithProviders(
        <BottomPopup
          visible={false}
          onCancelPress={() => {}}
        >
          <div>Animation Content</div>
        </BottomPopup>
      );

      // Should not be visible initially
      expect(screen.queryByText('Animation Content')).toBeFalsy();

      // Make visible
      rerender(
        <BottomPopup
          visible={true}
          onCancelPress={() => {}}
        >
          <div>Animation Content</div>
        </BottomPopup>
      );

      // Should be visible after animation
      expect(screen.getByText('Animation Content')).toBeTruthy();
    });

    test('should handle rapid animation state changes', () => {
      const { rerender } = renderWithProviders(
        <BottomPopup
          visible={false}
          onCancelPress={() => {}}
        >
          <div>Rapid Animation Content</div>
        </BottomPopup>
      );

      const startTime = Date.now();
      
      // Rapidly toggle visibility
      for (let i = 0; i < 5; i++) {
        rerender(
          <BottomPopup
            visible={i % 2 === 0}
            onCancelPress={() => {}}
          >
            <div>Rapid Animation Content</div>
          </BottomPopup>
        );
      }
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(300); // Should handle rapid changes efficiently
    });
  });
}); 