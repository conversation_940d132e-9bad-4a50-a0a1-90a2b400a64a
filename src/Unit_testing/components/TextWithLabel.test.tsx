import React from 'react';
import { render, screen } from '@testing-library/react-native';
import TextWithLabel from '../../../components/TextWithLabel';

// Mock Colors if used
jest.mock('../../../utils/Colors/Colors', () => ({ black: '#000', white: '#fff' }));

// Minimal TextWithLabel component interface:
// export default function TextWithLabel({ label, value, style, labelStyle, valueStyle })

describe('TextWithLabel', () => {
  it('renders with label and value', () => {
    render(<TextWithLabel label="Label" value="Value" />);
    expect(screen.getByText('Label')).toBeTruthy();
    expect(screen.getByText('Value')).toBeTruthy();
  });

  it('renders with empty label and value', () => {
    render(<TextWithLabel label="" value="" />);
    expect(screen.getByText('')).toBeTruthy();
  });

  it('renders with long label and value', () => {
    const longLabel = 'L'.repeat(100);
    const longValue = 'V'.repeat(100);
    render(<TextWithLabel label={longLabel} value={longValue} />);
    expect(screen.getByText(longLabel)).toBeTruthy();
    expect(screen.getByText(longValue)).toBeTruthy();
  });

  it('renders with special characters', () => {
    render(<TextWithLabel label="!@#" value="$%^" />);
    expect(screen.getByText('!@#')).toBeTruthy();
    expect(screen.getByText('$%^')).toBeTruthy();
  });

  it('renders with custom style props', () => {
    const style = { backgroundColor: 'red' };
    render(<TextWithLabel label="Label" value="Value" style={style} labelStyle={style} valueStyle={style} />);
    expect(screen.getByText('Label')).toBeTruthy();
    expect(screen.getByText('Value')).toBeTruthy();
  });

  it('is accessible', () => {
    const { getByText } = render(<TextWithLabel label="Label" value="Value" />);
    expect(getByText('Label').props.accessibilityRole).toBeUndefined();
    expect(getByText('Value').props.accessibilityRole).toBeUndefined();
  });

  it('matches snapshot', () => {
    const { toJSON } = render(<TextWithLabel label="Label" value="Value" />);
    expect(toJSON()).toMatchSnapshot();
  });
}); 