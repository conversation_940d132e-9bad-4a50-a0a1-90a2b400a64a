import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import SearchComponent from '../../../components/SearchComponent';
jest.mock('../../../assets/svg/search.svg', () => () => <svg data-testid="search-icon" />);
jest.mock('../../../utils/Colors/Colors', () => ({ white: '#fff', searchTextBlack: '#333', black: '#000', grey: '#ccc' }));
jest.mock('../../../utils/Scale/Scaling', () => ({ ms: (v) => v }));
jest.mock('../../../utils/Strings/Strings', () => ({ DailyProgress: { search: 'Search...' } }));

describe('SearchComponent', () => {
  it('renders with value and placeholder', () => {
    render(<SearchComponent value="abc" onChange={jest.fn()} placeholder="Find..." />);
    expect(screen.getByPlaceholderText('Find...')).toBeTruthy();
    expect(screen.getByDisplayValue('abc')).toBeTruthy();
    expect(screen.getByTestId('search-icon')).toBeTruthy();
  });

  it('calls onChange when typing', () => {
    const onChange = jest.fn();
    render(<SearchComponent value="" onChange={onChange} />);
    fireEvent.changeText(screen.getByPlaceholderText('Search...'), 'new');
    expect(onChange).toHaveBeenCalledWith('new');
  });

  it('handles empty value', () => {
    render(<SearchComponent value="" onChange={jest.fn()} />);
    expect(screen.getByPlaceholderText('Search...')).toBeTruthy();
  });

  it('handles long input', () => {
    const longValue = 'A'.repeat(100);
    render(<SearchComponent value={longValue} onChange={jest.fn()} />);
    expect(screen.getByDisplayValue(longValue)).toBeTruthy();
  });

  it('does not crash if onChange is missing', () => {
    render(<SearchComponent value="test" onChange={undefined as any} />);
    fireEvent.changeText(screen.getByDisplayValue('test'), 'x');
    // No error expected
  });

  it('matches snapshot', () => {
    const { toJSON } = render(<SearchComponent value="Snapshot" onChange={jest.fn()} placeholder="Snap" />);
    expect(toJSON()).toMatchSnapshot();
  });

  it('is accessible with accessibilityRole', () => {
    const { getByRole } = render(<SearchComponent value="A11y" onChange={jest.fn()} />);
    expect(getByRole('search')).toBeTruthy();
  });

  it('renders with special characters in value', () => {
    render(<SearchComponent value={"!@#$%^&*()_+"} onChange={jest.fn()} />);
    expect(screen.getByDisplayValue('!@#$%^&*()_+')).toBeTruthy();
  });

  it('handles null value gracefully', () => {
    render(<SearchComponent value={null as any} onChange={jest.fn()} />);
    expect(screen.getByDisplayValue('')).toBeTruthy();
  });

  it('handles rapid typing without error', () => {
    const onChange = jest.fn();
    render(<SearchComponent value="" onChange={onChange} />);
    const input = screen.getByPlaceholderText('Search...');
    fireEvent.changeText(input, 'a');
    fireEvent.changeText(input, 'b');
    fireEvent.changeText(input, 'c');
    expect(onChange).toHaveBeenCalledTimes(3);
  });
}); 