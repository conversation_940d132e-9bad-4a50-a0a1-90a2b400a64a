import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import BorderedButton from '../../../components/BorderedButton';
import { renderWithProviders, setupTest, teardownTest } from '../../../utils/testing/testHelpers';

// Mock the Colors utility
jest.mock('../../../utils/Colors/Colors', () => ({
  white: '#FFFFFF',
  blue: '#3182ce',
}));

// Mock the Scale utility
jest.mock('../../../utils/Scale/Scaling', () => ({
  ms: jest.fn((value) => value * 2), // Simple mock that doubles the value
}));

describe('BorderedButton', () => {
  beforeEach(() => {
    setupTest();
  });

  afterEach(() => {
    teardownTest();
    jest.clearAllMocks();
  });

  // ============================================================================
  // RENDER TESTS
  // ============================================================================

  describe('Render Tests', () => {
    test('should render correctly with basic props', () => {
      renderWithProviders(
        <BorderedButton title="Test Button" onPress={() => {}} />
      );

      expect(screen.getByText('Test Button')).toBeTruthy();
    });

    test('should render with custom testID', () => {
      renderWithProviders(
        <BorderedButton testID="custom-button" title="Custom Button" onPress={() => {}} />
      );

      expect(screen.getByTestId('custom-button')).toBeTruthy();
    });

    test('should render with empty title', () => {
      renderWithProviders(
        <BorderedButton title="" onPress={() => {}} />
      );

      expect(screen.getByText('')).toBeTruthy();
    });

    test('should render with long title text', () => {
      const longTitle = 'This is a very long button title that should be handled properly by the component';
      renderWithProviders(
        <BorderedButton title={longTitle} onPress={() => {}} />
      );

      expect(screen.getByText(longTitle)).toBeTruthy();
    });

    test('should render with special characters in title', () => {
      const specialTitle = 'Button with @#$%^&*() characters';
      renderWithProviders(
        <BorderedButton title={specialTitle} onPress={() => {}} />
      );

      expect(screen.getByText(specialTitle)).toBeTruthy();
    });

    test('should render with unicode characters in title', () => {
      const unicodeTitle = 'Button with émojis 🚀 and unicode 中文';
      renderWithProviders(
        <BorderedButton title={unicodeTitle} onPress={() => {}} />
      );

      expect(screen.getByText(unicodeTitle)).toBeTruthy();
    });

    test('should render with numbers in title', () => {
      renderWithProviders(
        <BorderedButton title="Button 123" onPress={() => {}} />
      );

      expect(screen.getByText('Button 123')).toBeTruthy();
    });

    test('should render with whitespace in title', () => {
      renderWithProviders(
        <BorderedButton title="  Button with spaces  " onPress={() => {}} />
      );

      expect(screen.getByText('  Button with spaces  ')).toBeTruthy();
    });
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  describe('Interaction Tests', () => {
    test('should call onPress when button is pressed', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <BorderedButton title="Press Me" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Press Me'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should call onPress multiple times when button is pressed repeatedly', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <BorderedButton title="Press Me" onPress={onPressMock} />
      );

      const button = screen.getByText('Press Me');
      fireEvent.press(button);
      fireEvent.press(button);
      fireEvent.press(button);

      expect(onPressMock).toHaveBeenCalledTimes(3);
    });

    test('should call onPress with custom testID', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <BorderedButton testID="test-button" title="Press Me" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByTestId('test-button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should handle rapid button presses', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <BorderedButton title="Rapid Press" onPress={onPressMock} />
      );

      const button = screen.getByText('Rapid Press');
      
      // Simulate rapid presses
      for (let i = 0; i < 10; i++) {
        fireEvent.press(button);
      }

      expect(onPressMock).toHaveBeenCalledTimes(10);
    });

    test('should handle button press with different title lengths', () => {
      const onPressMock = jest.fn();
      
      const { rerender } = renderWithProviders(
        <BorderedButton title="Short" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Short'));
      expect(onPressMock).toHaveBeenCalledTimes(1);

      rerender(<BorderedButton title="Very Long Button Title" onPress={onPressMock} />);
      fireEvent.press(screen.getByText('Very Long Button Title'));
      expect(onPressMock).toHaveBeenCalledTimes(2);
    });
  });

  // ============================================================================
  // EDGE CASE TESTS
  // ============================================================================

  describe('Edge Case Tests', () => {
    test('should handle onPress function that throws error', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const onPressMock = jest.fn(() => {
        throw new Error('Test error');
      });
      
      renderWithProviders(
        <BorderedButton title="Error Button" onPress={onPressMock} />
      );

      expect(() => {
        fireEvent.press(screen.getByText('Error Button'));
      }).toThrow('Test error');

      consoleSpy.mockRestore();
    });

    test('should handle onPress function that returns undefined', () => {
      const onPressMock = jest.fn(() => undefined);
      
      renderWithProviders(
        <BorderedButton title="Undefined Button" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Undefined Button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should handle onPress function that returns null', () => {
      const onPressMock = jest.fn(() => null);
      
      renderWithProviders(
        <BorderedButton title="Null Button" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Null Button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should handle onPress function that returns a value', () => {
      const onPressMock = jest.fn(() => 'return value');
      
      renderWithProviders(
        <BorderedButton title="Return Button" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Return Button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should handle async onPress function', async () => {
      const onPressMock = jest.fn(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'async result';
      });
      
      renderWithProviders(
        <BorderedButton title="Async Button" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Async Button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
      
      // Wait for async operation to complete
      await new Promise(resolve => setTimeout(resolve, 20));
    });

    test('should handle onPress function with parameters', () => {
      const onPressMock = jest.fn((param) => param);
      
      renderWithProviders(
        <BorderedButton title="Param Button" onPress={() => onPressMock('test param')} />
      );

      fireEvent.press(screen.getByText('Param Button'));
      expect(onPressMock).toHaveBeenCalledWith('test param');
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    test('should be accessible with proper role', () => {
      renderWithProviders(
        <BorderedButton title="Accessible Button" onPress={() => {}} />
      );

      const button = screen.getByText('Accessible Button');
      expect(button).toBeTruthy();
    });

    test('should be accessible with proper text content', () => {
      renderWithProviders(
        <BorderedButton title="Accessible Button" onPress={() => {}} />
      );

      expect(screen.getByText('Accessible Button')).toBeTruthy();
    });

    test('should be accessible with testID', () => {
      renderWithProviders(
        <BorderedButton testID="accessible-button" title="Test Button" onPress={() => {}} />
      );

      expect(screen.getByTestId('accessible-button')).toBeTruthy();
    });

    test('should handle accessibility focus', () => {
      renderWithProviders(
        <BorderedButton title="Focus Button" onPress={() => {}} />
      );

      const button = screen.getByText('Focus Button');
      fireEvent(button, 'focus');
      expect(button).toBeTruthy();
    });

    test('should handle accessibility blur', () => {
      renderWithProviders(
        <BorderedButton title="Blur Button" onPress={() => {}} />
      );

      const button = screen.getByText('Blur Button');
      fireEvent(button, 'blur');
      expect(button).toBeTruthy();
    });

    test('should be accessible with descriptive text', () => {
      renderWithProviders(
        <BorderedButton title="Submit Form" onPress={() => {}} />
      );

      expect(screen.getByText('Submit Form')).toBeTruthy();
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    test('should render quickly with minimal props', () => {
      const startTime = Date.now();
      
      renderWithProviders(
        <BorderedButton title="Quick Button" onPress={() => {}} />
      );
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(100); // Should render in less than 100ms
    });

    test('should handle rapid button presses efficiently', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <BorderedButton title="Rapid Button" onPress={onPressMock} />
      );

      const button = screen.getByText('Rapid Button');
      const startTime = Date.now();
      
      // Simulate rapid presses
      for (let i = 0; i < 20; i++) {
        fireEvent.press(button);
      }
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(500); // Should handle 20 presses in less than 500ms
      expect(onPressMock).toHaveBeenCalledTimes(20);
    });

    test('should handle title changes efficiently', () => {
      const { rerender } = renderWithProviders(
        <BorderedButton title="Original Title" onPress={() => {}} />
      );

      const startTime = Date.now();
      
      rerender(<BorderedButton title="New Title" onPress={() => {}} />);
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(50); // Should update in less than 50ms
      expect(screen.getByText('New Title')).toBeTruthy();
    });

    test('should handle multiple re-renders efficiently', () => {
      const { rerender } = renderWithProviders(
        <BorderedButton title="Button 1" onPress={() => {}} />
      );

      const startTime = Date.now();
      
      for (let i = 2; i <= 10; i++) {
        rerender(<BorderedButton title={`Button ${i}`} onPress={() => {}} />);
      }
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(200); // Should handle 9 re-renders in less than 200ms
      expect(screen.getByText('Button 10')).toBeTruthy();
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should work with navigation context', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <BorderedButton title="Navigation Button" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Navigation Button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should work with Redux context', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <BorderedButton title="Redux Button" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Redux Button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should work with both navigation and Redux context', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <BorderedButton title="Full Context Button" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Full Context Button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should work with other components in the same view', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <>
          <BorderedButton title="Button 1" onPress={onPressMock} />
          <BorderedButton title="Button 2" onPress={onPressMock} />
        </>
      );

      fireEvent.press(screen.getByText('Button 1'));
      fireEvent.press(screen.getByText('Button 2'));
      expect(onPressMock).toHaveBeenCalledTimes(2);
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling Tests', () => {
    test('should handle missing onPress prop gracefully', () => {
      // This test ensures the component doesn't crash when onPress is undefined
      expect(() => {
        renderWithProviders(
          <BorderedButton title="No Press Button" onPress={undefined} />
        );
      }).not.toThrow();
    });

    test('should handle null onPress prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <BorderedButton title="Null Press Button" onPress={null} />
        );
      }).not.toThrow();
    });

    test('should handle missing title prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <BorderedButton title={undefined} onPress={() => {}} />
        );
      }).not.toThrow();
    });

    test('should handle null title prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <BorderedButton title={null} onPress={() => {}} />
        );
      }).not.toThrow();
    });

    test('should handle missing testID prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <BorderedButton title="No TestID Button" onPress={() => {}} />
        );
      }).not.toThrow();
    });

    test('should handle empty testID prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <BorderedButton testID="" title="Empty TestID Button" onPress={() => {}} />
        );
      }).not.toThrow();
    });
  });

  // ============================================================================
  // STYLING TESTS
  // ============================================================================

  describe('Styling Tests', () => {
    test('should apply default styling correctly', () => {
      renderWithProviders(
        <BorderedButton title="Styled Button" onPress={() => {}} />
      );

      const button = screen.getByText('Styled Button');
      expect(button).toBeTruthy();
    });

    test('should handle button with different title lengths', () => {
      const shortTitle = "A";
      const longTitle = "This is a very long button title that should be handled properly";
      
      const { rerender } = renderWithProviders(
        <BorderedButton title={shortTitle} onPress={() => {}} />
      );

      expect(screen.getByText(shortTitle)).toBeTruthy();

      rerender(<BorderedButton title={longTitle} onPress={() => {}} />);
      expect(screen.getByText(longTitle)).toBeTruthy();
    });

    test('should maintain consistent styling across re-renders', () => {
      const { rerender } = renderWithProviders(
        <BorderedButton title="Consistent Button" onPress={() => {}} />
      );

      const button1 = screen.getByText('Consistent Button');
      
      rerender(<BorderedButton title="Still Consistent" onPress={() => {}} />);
      const button2 = screen.getByText('Still Consistent');
      
      expect(button1).toBeTruthy();
      expect(button2).toBeTruthy();
    });
  });
}); 