import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { customAlertWithOK } from '../../../src/components/CustomAlert';
import { Alert, Platform } from 'react-native';

jest.spyOn(Alert, 'alert');

describe('CustomAlert', () => {
    it('calls Alert.alert with the correct parameters on Android', () => {
        Platform.OS = 'android';
        const title = 'Test Title';
        const message = 'Test Message';
        const buttons = [{ text: 'OK', onPress: jest.fn() }];
        const options = { cancelable: true };

        customAlertWithOK(title, message, buttons, true);

        expect(Alert.alert).toHaveBeenCalledWith(title, message, buttons, options);
    });

    it('calls Alert.alert with the correct parameters on iOS', () => {
        Platform.OS = 'ios';
        const title = 'Test Title';
        const message = 'Test Message';
        const buttons = [{ text: 'OK', onPress: jest.fn() }];

        customAlertWithOK(title, message, buttons);

        expect(Alert.alert).toHaveBeenCalledWith(title, message, buttons);
    });
}); 