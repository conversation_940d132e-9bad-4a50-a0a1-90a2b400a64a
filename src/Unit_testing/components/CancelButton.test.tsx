import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import CancelButton from '../../../components/CancelButton';
import { renderWithProviders, setupTest, teardownTest } from '../../../utils/testing/testHelpers';

// Mock the Colors utility
jest.mock('../../../utils/Colors/Colors', () => ({
  white: '#FFFFFF',
  blue: '#3182ce',
}));

// Mock the Scale utility
jest.mock('../../../utils/Scale/Scaling', () => ({
  ms: jest.fn((value) => value * 2), // Simple mock that doubles the value
}));

describe('CancelButton', () => {
  beforeEach(() => {
    setupTest();
  });

  afterEach(() => {
    teardownTest();
    jest.clearAllMocks();
  });

  // ============================================================================
  // RENDER TESTS
  // ============================================================================

  describe('Render Tests', () => {
    test('should render correctly with basic props', () => {
      renderWithProviders(
        <CancelButton title="Cancel" onPress={() => {}} />
      );

      expect(screen.getByText('Cancel')).toBeTruthy();
    });

    test('should render with empty title', () => {
      renderWithProviders(
        <CancelButton title="" onPress={() => {}} />
      );

      expect(screen.getByText('')).toBeTruthy();
    });

    test('should render with long title text', () => {
      const longTitle = 'This is a very long cancel button title that should be handled properly';
      renderWithProviders(
        <CancelButton title={longTitle} onPress={() => {}} />
      );

      expect(screen.getByText(longTitle)).toBeTruthy();
    });

    test('should render with special characters in title', () => {
      const specialTitle = 'Cancel with @#$%^&*() characters';
      renderWithProviders(
        <CancelButton title={specialTitle} onPress={() => {}} />
      );

      expect(screen.getByText(specialTitle)).toBeTruthy();
    });

    test('should render with unicode characters in title', () => {
      const unicodeTitle = 'Cancel with émojis 🚫 and unicode 取消';
      renderWithProviders(
        <CancelButton title={unicodeTitle} onPress={() => {}} />
      );

      expect(screen.getByText(unicodeTitle)).toBeTruthy();
    });

    test('should render with numbers in title', () => {
      renderWithProviders(
        <CancelButton title="Cancel 123" onPress={() => {}} />
      );

      expect(screen.getByText('Cancel 123')).toBeTruthy();
    });

    test('should render with whitespace in title', () => {
      renderWithProviders(
        <CancelButton title="  Cancel with spaces  " onPress={() => {}} />
      );

      expect(screen.getByText('  Cancel with spaces  ')).toBeTruthy();
    });

    test('should render with different title variations', () => {
      const titles = ['Cancel', 'No', 'Close', 'Dismiss', 'Back'];
      
      titles.forEach(title => {
        const { unmount } = renderWithProviders(
          <CancelButton title={title} onPress={() => {}} />
        );
        
        expect(screen.getByText(title)).toBeTruthy();
        unmount();
      });
    });
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  describe('Interaction Tests', () => {
    test('should call onPress when button is pressed', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <CancelButton title="Press Me" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Press Me'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should call onPress multiple times when button is pressed repeatedly', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <CancelButton title="Press Me" onPress={onPressMock} />
      );

      const button = screen.getByText('Press Me');
      fireEvent.press(button);
      fireEvent.press(button);
      fireEvent.press(button);

      expect(onPressMock).toHaveBeenCalledTimes(3);
    });

    test('should handle rapid button presses', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <CancelButton title="Rapid Press" onPress={onPressMock} />
      );

      const button = screen.getByText('Rapid Press');
      
      // Simulate rapid presses
      for (let i = 0; i < 10; i++) {
        fireEvent.press(button);
      }

      expect(onPressMock).toHaveBeenCalledTimes(10);
    });

    test('should handle button press with different title lengths', () => {
      const onPressMock = jest.fn();
      
      const { rerender } = renderWithProviders(
        <CancelButton title="Short" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Short'));
      expect(onPressMock).toHaveBeenCalledTimes(1);

      rerender(<CancelButton title="Very Long Cancel Button Title" onPress={onPressMock} />);
      fireEvent.press(screen.getByText('Very Long Cancel Button Title'));
      expect(onPressMock).toHaveBeenCalledTimes(2);
    });

    test('should handle button press with different title content', () => {
      const onPressMock = jest.fn();
      
      const { rerender } = renderWithProviders(
        <CancelButton title="Cancel" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Cancel'));
      expect(onPressMock).toHaveBeenCalledTimes(1);

      rerender(<CancelButton title="No" onPress={onPressMock} />);
      fireEvent.press(screen.getByText('No'));
      expect(onPressMock).toHaveBeenCalledTimes(2);
    });
  });

  // ============================================================================
  // EDGE CASE TESTS
  // ============================================================================

  describe('Edge Case Tests', () => {
    test('should handle onPress function that throws error', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const onPressMock = jest.fn(() => {
        throw new Error('Test error');
      });
      
      renderWithProviders(
        <CancelButton title="Error Button" onPress={onPressMock} />
      );

      expect(() => {
        fireEvent.press(screen.getByText('Error Button'));
      }).toThrow('Test error');

      consoleSpy.mockRestore();
    });

    test('should handle onPress function that returns undefined', () => {
      const onPressMock = jest.fn(() => undefined);
      
      renderWithProviders(
        <CancelButton title="Undefined Button" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Undefined Button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should handle onPress function that returns null', () => {
      const onPressMock = jest.fn(() => null);
      
      renderWithProviders(
        <CancelButton title="Null Button" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Null Button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should handle onPress function that returns a value', () => {
      const onPressMock = jest.fn(() => 'return value');
      
      renderWithProviders(
        <CancelButton title="Return Button" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Return Button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should handle async onPress function', async () => {
      const onPressMock = jest.fn(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'async result';
      });
      
      renderWithProviders(
        <CancelButton title="Async Button" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Async Button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
      
      // Wait for async operation to complete
      await new Promise(resolve => setTimeout(resolve, 20));
    });

    test('should handle onPress function with parameters', () => {
      const onPressMock = jest.fn((param) => param);
      
      renderWithProviders(
        <CancelButton title="Param Button" onPress={() => onPressMock('test param')} />
      );

      fireEvent.press(screen.getByText('Param Button'));
      expect(onPressMock).toHaveBeenCalledWith('test param');
    });

    test('should handle very long title text', () => {
      const veryLongTitle = 'A'.repeat(1000); // 1000 character title
      renderWithProviders(
        <CancelButton title={veryLongTitle} onPress={() => {}} />
      );

      expect(screen.getByText(veryLongTitle)).toBeTruthy();
    });

    test('should handle title with only whitespace', () => {
      renderWithProviders(
        <CancelButton title="   " onPress={() => {}} />
      );

      expect(screen.getByText('   ')).toBeTruthy();
    });

    test('should handle title with newlines', () => {
      const titleWithNewlines = 'Cancel\nButton';
      renderWithProviders(
        <CancelButton title={titleWithNewlines} onPress={() => {}} />
      );

      expect(screen.getByText(titleWithNewlines)).toBeTruthy();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    test('should be accessible with proper role', () => {
      renderWithProviders(
        <CancelButton title="Accessible Button" onPress={() => {}} />
      );

      const button = screen.getByText('Accessible Button');
      expect(button).toBeTruthy();
    });

    test('should be accessible with proper text content', () => {
      renderWithProviders(
        <CancelButton title="Accessible Button" onPress={() => {}} />
      );

      expect(screen.getByText('Accessible Button')).toBeTruthy();
    });

    test('should handle accessibility focus', () => {
      renderWithProviders(
        <CancelButton title="Focus Button" onPress={() => {}} />
      );

      const button = screen.getByText('Focus Button');
      fireEvent(button, 'focus');
      expect(button).toBeTruthy();
    });

    test('should handle accessibility blur', () => {
      renderWithProviders(
        <CancelButton title="Blur Button" onPress={() => {}} />
      );

      const button = screen.getByText('Blur Button');
      fireEvent(button, 'blur');
      expect(button).toBeTruthy();
    });

    test('should be accessible with descriptive text', () => {
      renderWithProviders(
        <CancelButton title="Cancel Operation" onPress={() => {}} />
      );

      expect(screen.getByText('Cancel Operation')).toBeTruthy();
    });

    test('should handle screen reader announcements', () => {
      renderWithProviders(
        <CancelButton title="Cancel" onPress={() => {}} />
      );

      expect(screen.getByText('Cancel')).toBeTruthy();
    });

    test('should be accessible with different languages', () => {
      const multilingualTitles = ['Cancel', 'Annuler', 'Cancelar', 'キャンセル', '取消'];
      
      multilingualTitles.forEach(title => {
        const { unmount } = renderWithProviders(
          <CancelButton title={title} onPress={() => {}} />
        );
        
        expect(screen.getByText(title)).toBeTruthy();
        unmount();
      });
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    test('should render quickly with minimal props', () => {
      const startTime = Date.now();
      
      renderWithProviders(
        <CancelButton title="Quick Button" onPress={() => {}} />
      );
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(100); // Should render in less than 100ms
    });

    test('should handle rapid button presses efficiently', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <CancelButton title="Rapid Button" onPress={onPressMock} />
      );

      const button = screen.getByText('Rapid Button');
      const startTime = Date.now();
      
      // Simulate rapid presses
      for (let i = 0; i < 20; i++) {
        fireEvent.press(button);
      }
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(500); // Should handle 20 presses in less than 500ms
      expect(onPressMock).toHaveBeenCalledTimes(20);
    });

    test('should handle title changes efficiently', () => {
      const { rerender } = renderWithProviders(
        <CancelButton title="Original Title" onPress={() => {}} />
      );

      const startTime = Date.now();
      
      rerender(<CancelButton title="New Title" onPress={() => {}} />);
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(50); // Should update in less than 50ms
      expect(screen.getByText('New Title')).toBeTruthy();
    });

    test('should handle multiple re-renders efficiently', () => {
      const { rerender } = renderWithProviders(
        <CancelButton title="Button 1" onPress={() => {}} />
      );

      const startTime = Date.now();
      
      for (let i = 2; i <= 10; i++) {
        rerender(<CancelButton title={`Button ${i}`} onPress={() => {}} />);
      }
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(200); // Should handle 9 re-renders in less than 200ms
      expect(screen.getByText('Button 10')).toBeTruthy();
    });

    test('should handle large title text efficiently', () => {
      const largeTitle = 'A'.repeat(500); // 500 character title
      
      const startTime = Date.now();
      
      renderWithProviders(
        <CancelButton title={largeTitle} onPress={() => {}} />
      );
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(150); // Should render large title in less than 150ms
      expect(screen.getByText(largeTitle)).toBeTruthy();
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should work with navigation context', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <CancelButton title="Navigation Button" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Navigation Button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should work with Redux context', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <CancelButton title="Redux Button" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Redux Button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should work with both navigation and Redux context', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <CancelButton title="Full Context Button" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Full Context Button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should work with other components in the same view', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <>
          <CancelButton title="Button 1" onPress={onPressMock} />
          <CancelButton title="Button 2" onPress={onPressMock} />
        </>
      );

      fireEvent.press(screen.getByText('Button 1'));
      fireEvent.press(screen.getByText('Button 2'));
      expect(onPressMock).toHaveBeenCalledTimes(2);
    });

    test('should work with form components', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <div>
          <input placeholder="Test input" />
          <CancelButton title="Cancel Form" onPress={onPressMock} />
        </div>
      );

      fireEvent.press(screen.getByText('Cancel Form'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling Tests', () => {
    test('should handle missing onPress prop gracefully', () => {
      // This test ensures the component doesn't crash when onPress is undefined
      expect(() => {
        renderWithProviders(
          <CancelButton title="No Press Button" onPress={undefined} />
        );
      }).not.toThrow();
    });

    test('should handle null onPress prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <CancelButton title="Null Press Button" onPress={null} />
        );
      }).not.toThrow();
    });

    test('should handle missing title prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <CancelButton title={undefined} onPress={() => {}} />
        );
      }).not.toThrow();
    });

    test('should handle null title prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <CancelButton title={null} onPress={() => {}} />
        );
      }).not.toThrow();
    });

    test('should handle empty title prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <CancelButton title="" onPress={() => {}} />
        );
      }).not.toThrow();
    });
  });

  // ============================================================================
  // STYLING TESTS
  // ============================================================================

  describe('Styling Tests', () => {
    test('should apply default styling correctly', () => {
      renderWithProviders(
        <CancelButton title="Styled Button" onPress={() => {}} />
      );

      const button = screen.getByText('Styled Button');
      expect(button).toBeTruthy();
    });

    test('should handle button with different title lengths', () => {
      const shortTitle = "X";
      const longTitle = "This is a very long cancel button title that should be handled properly";
      
      const { rerender } = renderWithProviders(
        <CancelButton title={shortTitle} onPress={() => {}} />
      );

      expect(screen.getByText(shortTitle)).toBeTruthy();

      rerender(<CancelButton title={longTitle} onPress={() => {}} />);
      expect(screen.getByText(longTitle)).toBeTruthy();
    });

    test('should maintain consistent styling across re-renders', () => {
      const { rerender } = renderWithProviders(
        <CancelButton title="Consistent Button" onPress={() => {}} />
      );

      const button1 = screen.getByText('Consistent Button');
      
      rerender(<CancelButton title="Still Consistent" onPress={() => {}} />);
      const button2 = screen.getByText('Still Consistent');
      
      expect(button1).toBeTruthy();
      expect(button2).toBeTruthy();
    });

    test('should handle button with different title styles', () => {
      const titles = ['Cancel', 'NO', 'close', 'DISMISS', 'Back'];
      
      titles.forEach(title => {
        const { unmount } = renderWithProviders(
          <CancelButton title={title} onPress={() => {}} />
        );
        
        expect(screen.getByText(title)).toBeTruthy();
        unmount();
      });
    });
  });

  // ============================================================================
  // USABILITY TESTS
  // ============================================================================

  describe('Usability Tests', () => {
    test('should be easily tappable', () => {
      renderWithProviders(
        <CancelButton title="Tappable Button" onPress={() => {}} />
      );

      const button = screen.getByText('Tappable Button');
      expect(button).toBeTruthy();
    });

    test('should provide clear visual feedback', () => {
      renderWithProviders(
        <CancelButton title="Visual Feedback" onPress={() => {}} />
      );

      const button = screen.getByText('Visual Feedback');
      expect(button).toBeTruthy();
    });

    test('should have appropriate touch target size', () => {
      renderWithProviders(
        <CancelButton title="Touch Target" onPress={() => {}} />
      );

      const button = screen.getByText('Touch Target');
      expect(button).toBeTruthy();
    });

    test('should be discoverable in the UI', () => {
      renderWithProviders(
        <CancelButton title="Discoverable" onPress={() => {}} />
      );

      expect(screen.getByText('Discoverable')).toBeTruthy();
    });
  });
}); 