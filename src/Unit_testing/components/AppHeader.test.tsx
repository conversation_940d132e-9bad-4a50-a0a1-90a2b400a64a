import React from 'react';
import { render, fireEvent, screen, waitFor } from '@testing-library/react-native';
import AppHeader from '../../../components/AppHeader';
import { renderWithProviders, setupTest, teardownTest } from '../../../utils/testing/testHelpers';

// Mock react-native-vector-icons
jest.mock('react-native-vector-icons/Ionicons', () => 'Icon');

// Mock the Colors utility
jest.mock('../../../utils/Colors/Colors', () => ({
  black: '#000000',
  white: '#FFFFFF',
  secondary: '#666666',
  textInputBlack: '#333333',
}));

// Mock the Scale utility
jest.mock('../../../utils/Scale/Scaling', () => ({
  ms: jest.fn((value) => value * 2),
}));

// Mock the Fonts utility
jest.mock('../../../components/Fonts', () => ({
  AppFonts: {
    SemiBold: 'SemiBold',
  },
}));

// Mock navigation
const mockNavigation = {
  goBack: jest.fn(),
  navigate: jest.fn(),
  setOptions: jest.fn(),
};

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => mockNavigation,
}));

describe('AppHeader', () => {
  beforeEach(() => {
    setupTest();
    jest.clearAllMocks();
  });

  afterEach(() => {
    teardownTest();
  });

  // ============================================================================
  // RENDER TESTS
  // ============================================================================

  describe('Render Tests', () => {
    test('should render correctly with basic props', () => {
      renderWithProviders(
        <AppHeader title="Test Header" />
      );

      expect(screen.getByText('Test Header')).toBeTruthy();
      expect(screen.getByTestId('back-button')).toBeTruthy();
    });

    test('should render with simple title without slashes', () => {
      renderWithProviders(
        <AppHeader title="Simple Title" />
      );

      expect(screen.getByText('Simple Title')).toBeTruthy();
    });

    test('should render with title containing single slash', () => {
      renderWithProviders(
        <AppHeader title="Home/Dashboard" />
      );

      expect(screen.getByText('Home')).toBeTruthy();
      expect(screen.getByText('/')).toBeTruthy();
      expect(screen.getByText('Dashboard')).toBeTruthy();
    });

    test('should render with title containing multiple slashes', () => {
      renderWithProviders(
        <AppHeader title="Home/Dashboard/Settings" />
      );

      expect(screen.getByText('Home')).toBeTruthy();
      expect(screen.getAllByText('/')).toHaveLength(2);
      expect(screen.getByText('Dashboard')).toBeTruthy();
      expect(screen.getByText('Settings')).toBeTruthy();
    });

    test('should render with title starting with slash', () => {
      renderWithProviders(
        <AppHeader title="/Dashboard" />
      );

      expect(screen.getByText('/')).toBeTruthy();
      expect(screen.getByText('Dashboard')).toBeTruthy();
    });

    test('should render with title ending with slash', () => {
      renderWithProviders(
        <AppHeader title="Dashboard/" />
      );

      expect(screen.getByText('Dashboard')).toBeTruthy();
      expect(screen.getByText('/')).toBeTruthy();
    });

    test('should render with title containing only slashes', () => {
      renderWithProviders(
        <AppHeader title="///" />
      );

      expect(screen.getAllByText('/')).toHaveLength(3);
    });

    test('should render with empty title', () => {
      renderWithProviders(
        <AppHeader title="" />
      );

      expect(screen.getByText('')).toBeTruthy();
    });

    test('should render with whitespace-only title', () => {
      renderWithProviders(
        <AppHeader title="   " />
      );

      expect(screen.getByText('   ')).toBeTruthy();
    });

    test('should render with very long title', () => {
      const longTitle = 'This is a very long title that should be handled properly by the component without breaking the layout or causing any rendering issues';
      renderWithProviders(
        <AppHeader title={longTitle} />
      );

      expect(screen.getByText(longTitle)).toBeTruthy();
    });

    test('should render with special characters in title', () => {
      const specialTitle = 'Test@#$%^&*()_+-=[]{}|;:,.<>?';
      renderWithProviders(
        <AppHeader title={specialTitle} />
      );

      expect(screen.getByText(specialTitle)).toBeTruthy();
    });

    test('should render with unicode characters in title', () => {
      const unicodeTitle = 'Testéñ中文हिन्दीالعربية';
      renderWithProviders(
        <AppHeader title={unicodeTitle} />
      );

      expect(screen.getByText(unicodeTitle)).toBeTruthy();
    });

    test('should render with right content when provided', () => {
      const rightContent = <div testID="custom-right-content">Custom Content</div>;
      renderWithProviders(
        <AppHeader title="Test Header" rightContent={rightContent} />
      );

      expect(screen.getByText('Test Header')).toBeTruthy();
      expect(screen.getByTestId('custom-right-content')).toBeTruthy();
      expect(screen.getByTestId('right-content-button')).toBeTruthy();
    });

    test('should not render right content when not provided', () => {
      renderWithProviders(
        <AppHeader title="Test Header" />
      );

      expect(screen.getByText('Test Header')).toBeTruthy();
      expect(screen.queryByTestId('right-content-button')).toBeFalsy();
    });

    test('should render with null right content', () => {
      renderWithProviders(
        <AppHeader title="Test Header" rightContent={null} />
      );

      expect(screen.getByText('Test Header')).toBeTruthy();
      expect(screen.queryByTestId('right-content-button')).toBeFalsy();
    });

    test('should render with undefined right content', () => {
      renderWithProviders(
        <AppHeader title="Test Header" rightContent={undefined} />
      );

      expect(screen.getByText('Test Header')).toBeTruthy();
      expect(screen.queryByTestId('right-content-button')).toBeFalsy();
    });

    test('should render with complex right content', () => {
      const complexRightContent = (
        <div testID="complex-right">
          <div testID="icon">Icon</div>
          <div testID="text">Text</div>
        </div>
      );
      renderWithProviders(
        <AppHeader title="Test Header" rightContent={complexRightContent} />
      );

      expect(screen.getByText('Test Header')).toBeTruthy();
      expect(screen.getByTestId('complex-right')).toBeTruthy();
      expect(screen.getByTestId('icon')).toBeTruthy();
      expect(screen.getByTestId('text')).toBeTruthy();
    });
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  describe('Interaction Tests', () => {
    test('should call navigation.goBack when back button is pressed without onBackPress', () => {
      renderWithProviders(
        <AppHeader title="Test Header" />
      );

      fireEvent.press(screen.getByTestId('back-button'));
      expect(mockNavigation.goBack).toHaveBeenCalledTimes(1);
    });

    test('should call custom onBackPress when provided', () => {
      const onBackPressMock = jest.fn();
      renderWithProviders(
        <AppHeader title="Test Header" onBackPress={onBackPressMock} />
      );

      fireEvent.press(screen.getByTestId('back-button'));
      expect(onBackPressMock).toHaveBeenCalledTimes(1);
      expect(mockNavigation.goBack).not.toHaveBeenCalled();
    });

    test('should call onBookmarkPress when right content is pressed', () => {
      const onBookmarkPressMock = jest.fn();
      const rightContent = <div testID="custom-right-content">Custom Content</div>;
      
      renderWithProviders(
        <AppHeader 
          title="Test Header" 
          rightContent={rightContent}
          onBookmarkPress={onBookmarkPressMock}
        />
      );

      fireEvent.press(screen.getByTestId('right-content-button'));
      expect(onBookmarkPressMock).toHaveBeenCalledTimes(1);
    });

    test('should not throw error when right content is pressed without onBookmarkPress', () => {
      const rightContent = <div testID="custom-right-content">Custom Content</div>;
      
      renderWithProviders(
        <AppHeader 
          title="Test Header" 
          rightContent={rightContent}
        />
      );

      expect(() => {
        fireEvent.press(screen.getByTestId('right-content-button'));
      }).not.toThrow();
    });

    test('should handle multiple rapid back button presses', () => {
      const onBackPressMock = jest.fn();
      renderWithProviders(
        <AppHeader title="Test Header" onBackPress={onBackPressMock} />
      );

      const backButton = screen.getByTestId('back-button');
      
      // Rapidly press the back button multiple times
      fireEvent.press(backButton);
      fireEvent.press(backButton);
      fireEvent.press(backButton);
      fireEvent.press(backButton);
      fireEvent.press(backButton);

      expect(onBackPressMock).toHaveBeenCalledTimes(5);
    });

    test('should handle multiple rapid right content presses', () => {
      const onBookmarkPressMock = jest.fn();
      const rightContent = <div testID="custom-right-content">Custom Content</div>;
      
      renderWithProviders(
        <AppHeader 
          title="Test Header" 
          rightContent={rightContent}
          onBookmarkPress={onBookmarkPressMock}
        />
      );

      const rightButton = screen.getByTestId('right-content-button');
      
      // Rapidly press the right button multiple times
      fireEvent.press(rightButton);
      fireEvent.press(rightButton);
      fireEvent.press(rightButton);
      fireEvent.press(rightButton);
      fireEvent.press(rightButton);

      expect(onBookmarkPressMock).toHaveBeenCalledTimes(5);
    });

    test('should handle back button press with async onBackPress', async () => {
      const onBackPressMock = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 100))
      );
      
      renderWithProviders(
        <AppHeader title="Test Header" onBackPress={onBackPressMock} />
      );

      fireEvent.press(screen.getByTestId('back-button'));
      
      await waitFor(() => {
        expect(onBackPressMock).toHaveBeenCalledTimes(1);
      });
    });

    test('should handle right content press with async onBookmarkPress', async () => {
      const onBookmarkPressMock = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 100))
      );
      const rightContent = <div testID="custom-right-content">Custom Content</div>;
      
      renderWithProviders(
        <AppHeader 
          title="Test Header" 
          rightContent={rightContent}
          onBookmarkPress={onBookmarkPressMock}
        />
      );

      fireEvent.press(screen.getByTestId('right-content-button'));
      
      await waitFor(() => {
        expect(onBookmarkPressMock).toHaveBeenCalledTimes(1);
      });
    });
  });

  // ============================================================================
  // EDGE CASE TESTS
  // ============================================================================

  describe('Edge Case Tests', () => {
    test('should handle onBackPress that throws an error', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const onBackPressMock = jest.fn().mockImplementation(() => {
        throw new Error('Test error');
      });
      
      renderWithProviders(
        <AppHeader title="Test Header" onBackPress={onBackPressMock} />
      );

      expect(() => {
        fireEvent.press(screen.getByTestId('back-button'));
      }).toThrow('Test error');

      consoleSpy.mockRestore();
    });

    test('should handle onBookmarkPress that throws an error', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const onBookmarkPressMock = jest.fn().mockImplementation(() => {
        throw new Error('Test error');
      });
      const rightContent = <div testID="custom-right-content">Custom Content</div>;
      
      renderWithProviders(
        <AppHeader 
          title="Test Header" 
          rightContent={rightContent}
          onBookmarkPress={onBookmarkPressMock}
        />
      );

      expect(() => {
        fireEvent.press(screen.getByTestId('right-content-button'));
      }).toThrow('Test error');

      consoleSpy.mockRestore();
    });

    test('should handle navigation.goBack that throws an error', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockNavigation.goBack.mockImplementation(() => {
        throw new Error('Navigation error');
      });
      
      renderWithProviders(
        <AppHeader title="Test Header" />
      );

      expect(() => {
        fireEvent.press(screen.getByTestId('back-button'));
      }).toThrow('Navigation error');

      consoleSpy.mockRestore();
    });

    test('should handle undefined onBackPress', () => {
      renderWithProviders(
        <AppHeader title="Test Header" onBackPress={undefined} />
      );

      fireEvent.press(screen.getByTestId('back-button'));
      expect(mockNavigation.goBack).toHaveBeenCalledTimes(1);
    });

    test('should handle undefined onBookmarkPress', () => {
      const rightContent = <div testID="custom-right-content">Custom Content</div>;
      
      renderWithProviders(
        <AppHeader 
          title="Test Header" 
          rightContent={rightContent}
          onBookmarkPress={undefined}
        />
      );

      expect(() => {
        fireEvent.press(screen.getByTestId('right-content-button'));
      }).not.toThrow();
    });

    test('should handle title with only slashes and spaces', () => {
      renderWithProviders(
        <AppHeader title=" / / " />
      );

      expect(screen.getAllByText('/')).toHaveLength(2);
    });

    test('should handle title with mixed content and slashes', () => {
      renderWithProviders(
        <AppHeader title="Home / Dashboard / Settings / Profile" />
      );

      expect(screen.getByText('Home ')).toBeTruthy();
      expect(screen.getByText(' Dashboard ')).toBeTruthy();
      expect(screen.getByText(' Settings ')).toBeTruthy();
      expect(screen.getByText(' Profile')).toBeTruthy();
      expect(screen.getAllByText('/')).toHaveLength(3);
    });

    test('should handle title with newlines and special characters', () => {
      const titleWithNewlines = 'Home\nDashboard\nSettings';
      renderWithProviders(
        <AppHeader title={titleWithNewlines} />
      );

      expect(screen.getByText(titleWithNewlines)).toBeTruthy();
    });

    test('should handle title with HTML-like content', () => {
      const htmlTitle = '<script>alert("test")</script>Dashboard';
      renderWithProviders(
        <AppHeader title={htmlTitle} />
      );

      expect(screen.getByText(htmlTitle)).toBeTruthy();
    });

    test('should handle title with emoji characters', () => {
      const emojiTitle = '🏠 Home 🏢 Dashboard ⚙️ Settings';
      renderWithProviders(
        <AppHeader title={emojiTitle} />
      );

      expect(screen.getByText(emojiTitle)).toBeTruthy();
    });

    test('should handle title with zero-width characters', () => {
      const zeroWidthTitle = 'Home\u200B/\u200BDashboard';
      renderWithProviders(
        <AppHeader title={zeroWidthTitle} />
      );

      expect(screen.getByText(zeroWidthTitle)).toBeTruthy();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    test('should have proper testID for back button', () => {
      renderWithProviders(
        <AppHeader title="Test Header" />
      );

      expect(screen.getByTestId('back-button')).toBeTruthy();
    });

    test('should have proper testID for right content button when provided', () => {
      const rightContent = <div testID="custom-right-content">Custom Content</div>;
      renderWithProviders(
        <AppHeader title="Test Header" rightContent={rightContent} />
      );

      expect(screen.getByTestId('right-content-button')).toBeTruthy();
    });

    test('should render back button with proper accessibility', () => {
      renderWithProviders(
        <AppHeader title="Test Header" />
      );

      const backButton = screen.getByTestId('back-button');
      expect(backButton).toBeTruthy();
    });

    test('should render right content button with proper accessibility when provided', () => {
      const rightContent = <div testID="custom-right-content">Custom Content</div>;
      renderWithProviders(
        <AppHeader title="Test Header" rightContent={rightContent} />
      );

      const rightButton = screen.getByTestId('right-content-button');
      expect(rightButton).toBeTruthy();
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should work correctly with navigation context', () => {
      renderWithProviders(
        <AppHeader title="Test Header" />
      );

      fireEvent.press(screen.getByTestId('back-button'));
      expect(mockNavigation.goBack).toHaveBeenCalledTimes(1);
    });

    test('should work correctly with Redux store context', () => {
      const initialState = {
        auth: {
          user: { name: 'Test User' },
          loading: false,
          error: null,
          token: 'test-token',
        },
      };

      renderWithProviders(
        <AppHeader title="Test Header" />,
        { initialState }
      );

      expect(screen.getByText('Test Header')).toBeTruthy();
      fireEvent.press(screen.getByTestId('back-button'));
      expect(mockNavigation.goBack).toHaveBeenCalledTimes(1);
    });

    test('should work correctly with both navigation and Redux contexts', () => {
      const initialState = {
        auth: {
          user: { name: 'Test User' },
          loading: false,
          error: null,
          token: 'test-token',
        },
        home: {
          currentJobId: 'TEST_JOB_001',
          isJobsDownloaded: true,
          loading: false,
        },
      };

      const onBookmarkPressMock = jest.fn();
      const rightContent = <div testID="custom-right-content">Custom Content</div>;

      renderWithProviders(
        <AppHeader 
          title="Test Header" 
          rightContent={rightContent}
          onBookmarkPress={onBookmarkPressMock}
        />,
        { initialState }
      );

      expect(screen.getByText('Test Header')).toBeTruthy();
      expect(screen.getByTestId('custom-right-content')).toBeTruthy();
      
      fireEvent.press(screen.getByTestId('back-button'));
      expect(mockNavigation.goBack).toHaveBeenCalledTimes(1);
      
      fireEvent.press(screen.getByTestId('right-content-button'));
      expect(onBookmarkPressMock).toHaveBeenCalledTimes(1);
    });
  });
}); 