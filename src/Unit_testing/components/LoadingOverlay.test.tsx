import React from 'react';
import { render, fireEvent, screen, waitFor } from '@testing-library/react-native';
import LoadingOverlay from '../../../components/LoadingOverlay';
import { renderWithProviders, setupTest, teardownTest } from '../../../utils/testing/testHelpers';

// Mock the Colors utility
jest.mock('../../../utils/Colors/Colors', () => ({
  white: '#FFFFFF',
  black: '#000000',
  primary: '#3182ce',
  textPrimary: '#2d3748',
}));

// Mock the Scale utility
jest.mock('../../../utils/Scale/Scaling', () => ({
  ms: jest.fn((value) => value * 2), // Simple mock that doubles the value
}));

describe('LoadingOverlay', () => {
  beforeEach(() => {
    setupTest();
  });

  afterEach(() => {
    teardownTest();
    jest.clearAllMocks();
  });

  // ============================================================================
  // RENDER TESTS
  // ============================================================================

  describe('Render Tests', () => {
    test('should render correctly when visible is true', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} />
      );

      // Should render the loading overlay
      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
    });

    test('should not render when visible is false', () => {
      renderWithProviders(
        <LoadingOverlay visible={false} />
      );

      // Should not render anything when not visible
      expect(screen.queryByTestId('activity-indicator')).toBeFalsy();
    });

    test('should render with custom message', () => {
      const message = 'Loading data...';
      renderWithProviders(
        <LoadingOverlay visible={true} message={message} />
      );

      expect(screen.getByText(message)).toBeTruthy();
    });

    test('should render without message when message is not provided', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} />
      );

      // Should only render the activity indicator
      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
      expect(screen.queryByText('Loading')).toBeFalsy();
    });

    test('should render with empty message', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="" />
      );

      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
      expect(screen.queryByText('')).toBeFalsy(); // Empty message should not render
    });

    test('should render with long message text', () => {
      const longMessage = 'This is a very long loading message that should be handled properly by the component';
      renderWithProviders(
        <LoadingOverlay visible={true} message={longMessage} />
      );

      expect(screen.getByText(longMessage)).toBeTruthy();
    });

    test('should render with special characters in message', () => {
      const specialMessage = 'Loading with @#$%^&*() characters';
      renderWithProviders(
        <LoadingOverlay visible={true} message={specialMessage} />
      );

      expect(screen.getByText(specialMessage)).toBeTruthy();
    });

    test('should render with unicode characters in message', () => {
      const unicodeMessage = 'Loading with émojis 🔄 and unicode 加载中';
      renderWithProviders(
        <LoadingOverlay visible={true} message={unicodeMessage} />
      );

      expect(screen.getByText(unicodeMessage)).toBeTruthy();
    });

    test('should render with numbers in message', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="Loading 123 items" />
      );

      expect(screen.getByText('Loading 123 items')).toBeTruthy();
    });

    test('should render with whitespace in message', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="  Loading with spaces  " />
      );

      expect(screen.getByText('  Loading with spaces  ')).toBeTruthy();
    });

    test('should render with different message variations', () => {
      const messages = [
        'Loading...',
        'Please wait',
        'Processing data',
        'Synchronizing',
        'Updating...'
      ];
      
      messages.forEach(message => {
        const { unmount } = renderWithProviders(
          <LoadingOverlay visible={true} message={message} />
        );
        
        expect(screen.getByText(message)).toBeTruthy();
        unmount();
      });
    });
  });

  // ============================================================================
  // VISIBILITY TESTS
  // ============================================================================

  describe('Visibility Tests', () => {
    test('should show when visible is true', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} />
      );

      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
    });

    test('should hide when visible is false', () => {
      renderWithProviders(
        <LoadingOverlay visible={false} />
      );

      expect(screen.queryByTestId('activity-indicator')).toBeFalsy();
    });

    test('should toggle visibility when visible prop changes', () => {
      const { rerender } = renderWithProviders(
        <LoadingOverlay visible={false} />
      );

      // Should not be visible initially
      expect(screen.queryByTestId('activity-indicator')).toBeFalsy();

      // Make visible
      rerender(<LoadingOverlay visible={true} />);
      expect(screen.getByTestId('activity-indicator')).toBeTruthy();

      // Make invisible again
      rerender(<LoadingOverlay visible={false} />);
      expect(screen.queryByTestId('activity-indicator')).toBeFalsy();
    });

    test('should handle rapid visibility toggles', () => {
      const { rerender } = renderWithProviders(
        <LoadingOverlay visible={false} />
      );

      // Rapidly toggle visibility
      for (let i = 0; i < 5; i++) {
        rerender(<LoadingOverlay visible={i % 2 === 0} />);
      }

      // Should be visible after last toggle (i = 4, so visible = true)
      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
    });

    test('should handle visibility with message changes', () => {
      const { rerender } = renderWithProviders(
        <LoadingOverlay visible={false} message="Initial message" />
      );

      // Should not be visible initially
      expect(screen.queryByText('Initial message')).toBeFalsy();

      // Make visible with message
      rerender(<LoadingOverlay visible={true} message="New message" />);
      expect(screen.getByText('New message')).toBeTruthy();
    });
  });

  // ============================================================================
  // MESSAGE TESTS
  // ============================================================================

  describe('Message Tests', () => {
    test('should display message when provided', () => {
      const message = 'Custom loading message';
      renderWithProviders(
        <LoadingOverlay visible={true} message={message} />
      );

      expect(screen.getByText(message)).toBeTruthy();
    });

    test('should not display message when not provided', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} />
      );

      expect(screen.queryByText('Loading')).toBeFalsy();
    });

    test('should handle message changes', () => {
      const { rerender } = renderWithProviders(
        <LoadingOverlay visible={true} message="Initial message" />
      );

      expect(screen.getByText('Initial message')).toBeTruthy();

      rerender(<LoadingOverlay visible={true} message="Updated message" />);
      expect(screen.getByText('Updated message')).toBeTruthy();
    });

    test('should handle empty message', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="" />
      );

      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
      expect(screen.queryByText('')).toBeFalsy();
    });

    test('should handle null message', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message={null} />
      );

      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
      expect(screen.queryByText('null')).toBeFalsy();
    });

    test('should handle undefined message', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message={undefined} />
      );

      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
      expect(screen.queryByText('undefined')).toBeFalsy();
    });

    test('should handle very long message text', () => {
      const veryLongMessage = 'A'.repeat(1000); // 1000 character message
      renderWithProviders(
        <LoadingOverlay visible={true} message={veryLongMessage} />
      );

      expect(screen.getByText(veryLongMessage)).toBeTruthy();
    });

    test('should handle message with newlines', () => {
      const messageWithNewlines = 'Loading\nPlease wait';
      renderWithProviders(
        <LoadingOverlay visible={true} message={messageWithNewlines} />
      );

      expect(screen.getByText(messageWithNewlines)).toBeTruthy();
    });

    test('should handle message with HTML-like content', () => {
      const htmlMessage = '<strong>Loading</strong> data...';
      renderWithProviders(
        <LoadingOverlay visible={true} message={htmlMessage} />
      );

      expect(screen.getByText(htmlMessage)).toBeTruthy();
    });
  });

  // ============================================================================
  // EDGE CASE TESTS
  // ============================================================================

  describe('Edge Case Tests', () => {
    test('should handle both visible and message props as undefined', () => {
      renderWithProviders(
        <LoadingOverlay visible={undefined} message={undefined} />
      );

      // Should not render when visible is undefined
      expect(screen.queryByTestId('activity-indicator')).toBeFalsy();
    });

    test('should handle visible as null', () => {
      renderWithProviders(
        <LoadingOverlay visible={null} />
      );

      // Should not render when visible is null
      expect(screen.queryByTestId('activity-indicator')).toBeFalsy();
    });

    test('should handle visible as string', () => {
      renderWithProviders(
        <LoadingOverlay visible="true" />
      );

      // Should not render when visible is string
      expect(screen.queryByTestId('activity-indicator')).toBeFalsy();
    });

    test('should handle visible as number', () => {
      renderWithProviders(
        <LoadingOverlay visible={1} />
      );

      // Should not render when visible is number
      expect(screen.queryByTestId('activity-indicator')).toBeFalsy();
    });

    test('should handle message as number', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message={123} />
      );

      expect(screen.getByText('123')).toBeTruthy();
    });

    test('should handle message as boolean', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message={true} />
      );

      expect(screen.getByText('true')).toBeTruthy();
    });

    test('should handle message as object', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message={{ text: 'Loading' }} />
      );

      expect(screen.getByText('[object Object]')).toBeTruthy();
    });

    test('should handle message as array', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message={['Loading', 'Please wait']} />
      );

      expect(screen.getByText('Loading,Please wait')).toBeTruthy();
    });

    test('should handle very large message text', () => {
      const largeMessage = 'A'.repeat(10000); // 10,000 character message
      renderWithProviders(
        <LoadingOverlay visible={true} message={largeMessage} />
      );

      expect(screen.getByText(largeMessage)).toBeTruthy();
    });

    test('should handle message with only whitespace', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="   " />
      );

      expect(screen.getByText('   ')).toBeTruthy();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    test('should be accessible when visible', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="Loading content" />
      );

      expect(screen.getByText('Loading content')).toBeTruthy();
      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
    });

    test('should handle accessibility focus', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="Focus test" />
      );

      const message = screen.getByText('Focus test');
      fireEvent(message, 'focus');
      expect(message).toBeTruthy();
    });

    test('should handle accessibility blur', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="Blur test" />
      );

      const message = screen.getByText('Blur test');
      fireEvent(message, 'blur');
      expect(message).toBeTruthy();
    });

    test('should be accessible with descriptive message', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="Please wait while we process your request" />
      );

      expect(screen.getByText('Please wait while we process your request')).toBeTruthy();
    });

    test('should handle screen reader announcements', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="Loading data" />
      );

      expect(screen.getByText('Loading data')).toBeTruthy();
    });

    test('should be accessible with different languages', () => {
      const multilingualMessages = [
        'Loading...',
        'Chargement...',
        'Cargando...',
        '読み込み中...',
        '加载中...'
      ];
      
      multilingualMessages.forEach(message => {
        const { unmount } = renderWithProviders(
          <LoadingOverlay visible={true} message={message} />
        );
        
        expect(screen.getByText(message)).toBeTruthy();
        unmount();
      });
    });

    test('should be accessible without message', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} />
      );

      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    test('should render quickly when visible', () => {
      const startTime = Date.now();
      
      renderWithProviders(
        <LoadingOverlay visible={true} message="Quick loading" />
      );
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(100); // Should render in less than 100ms
    });

    test('should handle visibility changes efficiently', () => {
      const { rerender } = renderWithProviders(
        <LoadingOverlay visible={false} />
      );

      const startTime = Date.now();
      
      rerender(<LoadingOverlay visible={true} message="Efficient loading" />);
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(50); // Should update in less than 50ms
      expect(screen.getByText('Efficient loading')).toBeTruthy();
    });

    test('should handle rapid visibility toggles efficiently', () => {
      const { rerender } = renderWithProviders(
        <LoadingOverlay visible={false} />
      );

      const startTime = Date.now();
      
      // Toggle visibility multiple times
      for (let i = 0; i < 10; i++) {
        rerender(<LoadingOverlay visible={i % 2 === 0} message={`Toggle ${i}`} />);
      }
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(500); // Should handle 10 toggles in less than 500ms
    });

    test('should handle large message text efficiently', () => {
      const largeMessage = 'A'.repeat(500); // 500 character message
      
      const startTime = Date.now();
      
      renderWithProviders(
        <LoadingOverlay visible={true} message={largeMessage} />
      );
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(150); // Should render large message in less than 150ms
      expect(screen.getByText(largeMessage)).toBeTruthy();
    });

    test('should handle message changes efficiently', () => {
      const { rerender } = renderWithProviders(
        <LoadingOverlay visible={true} message="Original message" />
      );

      const startTime = Date.now();
      
      rerender(<LoadingOverlay visible={true} message="Updated message" />);
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(50); // Should update message in less than 50ms
      expect(screen.getByText('Updated message')).toBeTruthy();
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should work with navigation context', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="Navigation loading" />
      );

      expect(screen.getByText('Navigation loading')).toBeTruthy();
    });

    test('should work with Redux context', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="Redux loading" />
      );

      expect(screen.getByText('Redux loading')).toBeTruthy();
    });

    test('should work with both navigation and Redux context', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="Full context loading" />
      );

      expect(screen.getByText('Full context loading')).toBeTruthy();
    });

    test('should work with other overlay components', () => {
      renderWithProviders(
        <>
          <LoadingOverlay visible={true} message="Loading 1" />
          <LoadingOverlay visible={false} message="Loading 2" />
        </>
      );

      expect(screen.getByText('Loading 1')).toBeTruthy();
      expect(screen.queryByText('Loading 2')).toBeFalsy();
    });

    test('should work with form components', () => {
      renderWithProviders(
        <div>
          <input placeholder="Test input" />
          <LoadingOverlay visible={true} message="Form loading" />
        </div>
      );

      expect(screen.getByText('Form loading')).toBeTruthy();
    });

    test('should work with modal components', () => {
      renderWithProviders(
        <div>
          <div>Modal content</div>
          <LoadingOverlay visible={true} message="Modal loading" />
        </div>
      );

      expect(screen.getByText('Modal loading')).toBeTruthy();
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling Tests', () => {
    test('should handle missing visible prop gracefully', () => {
      // This test ensures the component doesn't crash when visible is undefined
      expect(() => {
        renderWithProviders(
          <LoadingOverlay visible={undefined} />
        );
      }).not.toThrow();
    });

    test('should handle missing message prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <LoadingOverlay visible={true} message={undefined} />
        );
      }).not.toThrow();
    });

    test('should handle null visible prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <LoadingOverlay visible={null} />
        );
      }).not.toThrow();
    });

    test('should handle null message prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <LoadingOverlay visible={true} message={null} />
        );
      }).not.toThrow();
    });

    test('should handle empty message prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <LoadingOverlay visible={true} message="" />
        );
      }).not.toThrow();
    });
  });

  // ============================================================================
  // STYLING TESTS
  // ============================================================================

  describe('Styling Tests', () => {
    test('should apply default styling correctly', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="Styled loading" />
      );

      expect(screen.getByText('Styled loading')).toBeTruthy();
      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
    });

    test('should handle overlay with different message lengths', () => {
      const shortMessage = "Loading";
      const longMessage = "This is a very long loading message that should be handled properly";
      
      const { rerender } = renderWithProviders(
        <LoadingOverlay visible={true} message={shortMessage} />
      );

      expect(screen.getByText(shortMessage)).toBeTruthy();

      rerender(<LoadingOverlay visible={true} message={longMessage} />);
      expect(screen.getByText(longMessage)).toBeTruthy();
    });

    test('should maintain consistent styling across re-renders', () => {
      const { rerender } = renderWithProviders(
        <LoadingOverlay visible={true} message="Consistent loading" />
      );

      const message1 = screen.getByText('Consistent loading');
      
      rerender(<LoadingOverlay visible={true} message="Still consistent" />);
      const message2 = screen.getByText('Still consistent');
      
      expect(message1).toBeTruthy();
      expect(message2).toBeTruthy();
    });

    test('should handle overlay with different message styles', () => {
      const messages = [
        'Loading...',
        'PLEASE WAIT',
        'loading...',
        'Processing...',
        'SYNCING'
      ];
      
      messages.forEach(message => {
        const { unmount } = renderWithProviders(
          <LoadingOverlay visible={true} message={message} />
        );
        
        expect(screen.getByText(message)).toBeTruthy();
        unmount();
      });
    });
  });

  // ============================================================================
  // USABILITY TESTS
  // ============================================================================

  describe('Usability Tests', () => {
    test('should provide clear loading indication', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="Clear loading indication" />
      );

      expect(screen.getByText('Clear loading indication')).toBeTruthy();
      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
    });

    test('should be non-interactive when visible', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="Non-interactive" />
      );

      // Should not respond to touch events
      const overlay = screen.getByTestId('activity-indicator').parentElement;
      fireEvent.press(overlay);
      
      // Should still be visible
      expect(screen.getByText('Non-interactive')).toBeTruthy();
    });

    test('should provide appropriate visual feedback', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="Visual feedback" />
      );

      expect(screen.getByText('Visual feedback')).toBeTruthy();
      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
    });

    test('should be discoverable in the UI', () => {
      renderWithProviders(
        <LoadingOverlay visible={true} message="Discoverable loading" />
      );

      expect(screen.getByText('Discoverable loading')).toBeTruthy();
    });

    test('should provide meaningful loading messages', () => {
      const meaningfulMessages = [
        'Loading your data...',
        'Please wait while we process your request',
        'Synchronizing with server...',
        'Updating your profile...',
        'Preparing your dashboard...'
      ];
      
      meaningfulMessages.forEach(message => {
        const { unmount } = renderWithProviders(
          <LoadingOverlay visible={true} message={message} />
        );
        
        expect(screen.getByText(message)).toBeTruthy();
        unmount();
      });
    });
  });
}); 