import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import SearchBar from '../../../components/search';
jest.mock('../../../assets/svg/search.svg', () => () => <svg data-testid="search-icon" />);
jest.mock('../../../utils/Colors/Colors', () => ({ white: '#fff', text: '#333', black: '#000', grey: '#ccc' }));
jest.mock('../../../utils/Scale/Scaling', () => ({ ms: (v: any) => v }));
jest.mock('i18next', () => ({ t: (k: any) => k }));

describe('SearchBar', () => {
  it('renders with value and placeholder', () => {
    render(<SearchBar value="abc" onChange={jest.fn()} placeholder="Search here" />);
    expect(screen.getByPlaceholderText('Search here')).toBeTruthy();
    expect(screen.getByDisplayValue('abc')).toBeTruthy();
    expect(screen.getByTestId('search-icon')).toBeTruthy();
  });

  it('calls onChange when typing', () => {
    const onChange = jest.fn();
    render(<SearchBar value="" onChange={onChange} />);
    fireEvent.changeText(screen.getByPlaceholderText('commonStrings.search'), 'new');
    expect(onChange).toHaveBeenCalledWith('new');
  });

  it('handles empty value', () => {
    render(<SearchBar value="" onChange={jest.fn()} />);
    expect(screen.getByPlaceholderText('commonStrings.search')).toBeTruthy();
  });

  it('handles long input', () => {
    const longValue = 'A'.repeat(100);
    render(<SearchBar value={longValue} onChange={jest.fn()} />);
    expect(screen.getByDisplayValue(longValue)).toBeTruthy();
  });

  it('does not crash if onChange is missing', () => {
    render(<SearchBar value="test" onChange={undefined as any} />);
    fireEvent.changeText(screen.getByDisplayValue('test'), 'x');
    // No error expected
  });

  it('matches snapshot', () => {
    const { toJSON } = render(<SearchBar value="Snapshot" onChange={jest.fn()} placeholder="Snap" />);
    expect(toJSON()).toMatchSnapshot();
  });

  it('is accessible with accessibilityRole', () => {
    const { getByRole } = render(<SearchBar value="A11y" onChange={jest.fn()} />);
    expect(getByRole('search')).toBeTruthy();
  });

  it('renders with special characters in value', () => {
    render(<SearchBar value={"!@#$%^&*()_+"} onChange={jest.fn()} />);
    expect(screen.getByDisplayValue('!@#$%^&*()_+')).toBeTruthy();
  });

  it('handles null value gracefully', () => {
    render(<SearchBar value={null as any} onChange={jest.fn()} />);
    expect(screen.getByDisplayValue('')).toBeTruthy();
  });

  it('handles rapid typing without error', () => {
    const onChange = jest.fn();
    render(<SearchBar value="" onChange={onChange} />);
    const input = screen.getByPlaceholderText('commonStrings.search');
    fireEvent.changeText(input, 'a');
    fireEvent.changeText(input, 'b');
    fireEvent.changeText(input, 'c');
    expect(onChange).toHaveBeenCalledTimes(3);
  });
}); 