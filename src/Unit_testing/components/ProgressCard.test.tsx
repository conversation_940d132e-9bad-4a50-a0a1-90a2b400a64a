import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import ProgressCard from '../../../components/ProgressCard';
jest.mock('../../../assets/svg/approver_profile.svg', () => () => <svg data-testid="profile-icon" />);
jest.mock('../../../assets/svg/box_arrow.svg', () => () => <svg data-testid="box-arrow-icon" />);
jest.mock('../../../utils/Colors/Colors', () => ({ white: '#fff', blue: '#00f', textPrimary: '#333', textInputBlack: '#000', onlineGreen: '#0f0' }));
jest.mock('../../../utils/Scale/Scaling', () => ({ ms: (v) => v }));
jest.mock('../../../components/ProgressDays', () => () => <div data-testid="progress-days" />);
jest.mock('../../../components/ActionButton', () => ({ type, onPress }) => <button onClick={onPress} data-testid={`action-btn-${type}`}>{type}</button>);

describe('ProgressCard', () => {
  const baseProps = {
    name: 'John Doe',
    details: 'Detail/Info',
    progressQty: '10',
    manDays: '5',
    date: '2024-06-01',
    onApprove: jest.fn(),
    onReject: jest.fn(),
    onArrow: jest.fn(),
  };

  it('renders all main elements', () => {
    render(<ProgressCard {...baseProps} />);
    expect(screen.getByText('John Doe')).toBeTruthy();
    expect(screen.getByText('Detail')).toBeTruthy();
    expect(screen.getByText('Info')).toBeTruthy();
    expect(screen.getByTestId('profile-icon')).toBeTruthy();
    expect(screen.getByTestId('box-arrow-icon')).toBeTruthy();
    expect(screen.getByTestId('progress-days')).toBeTruthy();
  });

  it('calls onApprove when approve button is pressed', () => {
    render(<ProgressCard {...baseProps} />);
    fireEvent.click(screen.getByTestId('action-btn-approve'));
    expect(baseProps.onApprove).toHaveBeenCalled();
  });

  it('calls onReject when reject button is pressed', () => {
    render(<ProgressCard {...baseProps} />);
    fireEvent.click(screen.getByTestId('action-btn-reject'));
    expect(baseProps.onReject).toHaveBeenCalled();
  });

  it('calls onArrow when arrow is pressed', () => {
    render(<ProgressCard {...baseProps} />);
    fireEvent.click(screen.getByTestId('box-arrow-icon'));
    expect(baseProps.onArrow).toHaveBeenCalled();
  });

  it('matches snapshot', () => {
    const { toJSON } = render(<ProgressCard {...baseProps} />);
    expect(toJSON()).toMatchSnapshot();
  });

  it('handles details with only slashes', () => {
    render(<ProgressCard {...baseProps} details="/" />);
    expect(screen.getByText('/')).toBeTruthy();
  });

  it('does not crash with missing handlers', () => {
    render(<ProgressCard {...baseProps} onApprove={undefined as any} onReject={undefined as any} onArrow={undefined as any} />);
    // No error expected
  });
}); 