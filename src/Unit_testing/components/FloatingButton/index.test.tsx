import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import FloatingButton from '../../../components/FloatingButton';

jest.mock('../../../components/Icons', () => ({
  Icon: ({ name, ...props }: any) => <div data-testid={`icon-${name}`} {...props} />,
}));
jest.mock('../../../utils/Colors/Colors', () => ({
  white: '#fff',
  secondary: '#00f',
  circleGrey: '#ccc',
}));
jest.mock('../../../utils/Scale/Scaling', () => ({ ms: (v: number) => v }));
jest.mock('../../../components/Fonts', () => ({ AppFonts: { Medium: 'Medium' } }));
jest.mock('../../../utils/ImagePath', () => ({ SVG: { MapOutline: () => <div data-testid="map-outline" /> } }));

describe('FloatingButton', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders add icon by default', () => {
    const { getByTestId } = render(<FloatingButton onAddPress={jest.fn()} />);
    expect(getByTestId('icon-add-sharp')).toBeTruthy();
  });

  it('calls onAddPress when add icon is pressed', () => {
    const onAddPress = jest.fn();
    const { getByTestId } = render(<FloatingButton onAddPress={onAddPress} />);
    fireEvent.press(getByTestId('icon-add-sharp').parentNode);
    expect(onAddPress).toHaveBeenCalled();
  });

  it('renders image icon when showImageIcon is true', () => {
    const { getByTestId } = render(
      <FloatingButton onAddPress={jest.fn()} showImageIcon onMapPress={jest.fn()} />
    );
    expect(getByTestId('map-outline')).toBeTruthy();
  });

  it('calls onMapPress when image icon is pressed', () => {
    const onMapPress = jest.fn();
    const { getByTestId } = render(
      <FloatingButton onAddPress={jest.fn()} showImageIcon onMapPress={onMapPress} />
    );
    fireEvent.press(getByTestId('map-outline').parentNode);
    expect(onMapPress).toHaveBeenCalled();
  });

  it('does not render add icon if showAddIcon is false', () => {
    const { queryByTestId } = render(
      <FloatingButton onAddPress={jest.fn()} showAddIcon={false} />
    );
    expect(queryByTestId('icon-add-sharp')).toBeNull();
  });

  it('does not render image icon if showImageIcon is false', () => {
    const { queryByTestId } = render(
      <FloatingButton onAddPress={jest.fn()} showImageIcon={false} />
    );
    expect(queryByTestId('map-outline')).toBeNull();
  });

  it('handles edge case: onMapPress not provided', () => {
    const { getByTestId } = render(
      <FloatingButton onAddPress={jest.fn()} showImageIcon />
    );
    fireEvent.press(getByTestId('map-outline').parentNode);
    // No error should be thrown
  });

  it('handles edge case: onAddPress not provided', () => {
    const { getByTestId } = render(
      <FloatingButton showAddIcon />
    );
    fireEvent.press(getByTestId('icon-add-sharp').parentNode);
    // No error should be thrown
  });

  it('renders both icons when both showAddIcon and showImageIcon are true', () => {
    const { getByTestId } = render(
      <FloatingButton onAddPress={jest.fn()} showAddIcon showImageIcon onMapPress={jest.fn()} />
    );
    expect(getByTestId('icon-add-sharp')).toBeTruthy();
    expect(getByTestId('map-outline')).toBeTruthy();
  });
}); 