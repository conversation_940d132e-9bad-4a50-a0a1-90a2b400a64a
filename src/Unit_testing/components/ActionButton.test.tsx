import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import ActionButton from '../../../components/ActionButton';
import { renderWithProviders, setupTest, teardownTest } from '../../../utils/testing/testHelpers';

// Mock the Colors utility
jest.mock('../../../utils/Colors/Colors', () => ({
  white: '#FFFFFF',
  onlineGreen: '#4CAF50',
  lightRed: '#F44336',
  updateTextLightBlue: '#2196F3',
}));

// Mock the Scale utility
jest.mock('../../../utils/Scale/Scaling', () => ({
  ms: jest.fn((value) => value * 2), // Simple mock that doubles the value
}));

describe('ActionButton', () => {
  beforeEach(() => {
    setupTest();
  });

  afterEach(() => {
    teardownTest();
    jest.clearAllMocks();
  });

  // ============================================================================
  // RENDER TESTS
  // ============================================================================

  describe('Render Tests', () => {
    test('should render approve button correctly', () => {
      renderWithProviders(
        <ActionButton type="approve" onPress={() => {}} />
      );

      expect(screen.getByText('Approve')).toBeTruthy();
    });

    test('should render reject button correctly', () => {
      renderWithProviders(
        <ActionButton type="reject" onPress={() => {}} />
      );

      expect(screen.getByText('Reject')).toBeTruthy();
    });

    test('should render with custom style', () => {
      const customStyle = { backgroundColor: 'red' };
      renderWithProviders(
        <ActionButton type="approve" onPress={() => {}} customStyle={customStyle} />
      );

      expect(screen.getByText('Approve')).toBeTruthy();
    });

    test('should render with different button types', () => {
      const { rerender } = renderWithProviders(
        <ActionButton type="approve" onPress={() => {}} />
      );

      expect(screen.getByText('Approve')).toBeTruthy();

      rerender(<ActionButton type="reject" onPress={() => {}} />);
      expect(screen.getByText('Reject')).toBeTruthy();
    });

    test('should render with empty custom style', () => {
      renderWithProviders(
        <ActionButton type="approve" onPress={() => {}} customStyle={{}} />
      );

      expect(screen.getByText('Approve')).toBeTruthy();
    });

    test('should render with null custom style', () => {
      renderWithProviders(
        <ActionButton type="approve" onPress={() => {}} customStyle={null} />
      );

      expect(screen.getByText('Approve')).toBeTruthy();
    });
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  describe('Interaction Tests', () => {
    test('should call onPress when approve button is pressed', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <ActionButton type="approve" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Approve'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should call onPress when reject button is pressed', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <ActionButton type="reject" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Reject'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should call onPress multiple times when button is pressed repeatedly', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <ActionButton type="approve" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Approve'));
      fireEvent.press(screen.getByText('Approve'));
      fireEvent.press(screen.getByText('Approve'));

      expect(onPressMock).toHaveBeenCalledTimes(3);
    });

    test('should handle onPress with custom style', () => {
      const onPressMock = jest.fn();
      const customStyle = { backgroundColor: 'blue' };
      
      renderWithProviders(
        <ActionButton type="approve" onPress={onPressMock} customStyle={customStyle} />
      );

      fireEvent.press(screen.getByText('Approve'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });
  });

  // ============================================================================
  // EDGE CASE TESTS
  // ============================================================================

  describe('Edge Case Tests', () => {
    test('should handle onPress function that throws error', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const onPressMock = jest.fn(() => {
        throw new Error('Test error');
      });
      
      renderWithProviders(
        <ActionButton type="approve" onPress={onPressMock} />
      );

      expect(() => {
        fireEvent.press(screen.getByText('Approve'));
      }).toThrow('Test error');

      consoleSpy.mockRestore();
    });

    test('should handle onPress function that returns undefined', () => {
      const onPressMock = jest.fn(() => undefined);
      
      renderWithProviders(
        <ActionButton type="approve" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Approve'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should handle onPress function that returns null', () => {
      const onPressMock = jest.fn(() => null);
      
      renderWithProviders(
        <ActionButton type="approve" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Approve'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should handle complex custom style object', () => {
      const complexStyle = {
        backgroundColor: 'red',
        borderWidth: 2,
        borderRadius: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      };
      
      renderWithProviders(
        <ActionButton type="approve" onPress={() => {}} customStyle={complexStyle} />
      );

      expect(screen.getByText('Approve')).toBeTruthy();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    test('should be accessible with proper role', () => {
      renderWithProviders(
        <ActionButton type="approve" onPress={() => {}} />
      );

      const button = screen.getByText('Approve');
      expect(button).toBeTruthy();
    });

    test('should be accessible with proper text content', () => {
      renderWithProviders(
        <ActionButton type="approve" onPress={() => {}} />
      );

      expect(screen.getByText('Approve')).toBeTruthy();
    });

    test('should be accessible with reject text content', () => {
      renderWithProviders(
        <ActionButton type="reject" onPress={() => {}} />
      );

      expect(screen.getByText('Reject')).toBeTruthy();
    });

    test('should handle accessibility focus', () => {
      renderWithProviders(
        <ActionButton type="approve" onPress={() => {}} />
      );

      const button = screen.getByText('Approve');
      fireEvent(button, 'focus');
      expect(button).toBeTruthy();
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    test('should render quickly with minimal props', () => {
      const startTime = Date.now();
      
      renderWithProviders(
        <ActionButton type="approve" onPress={() => {}} />
      );
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(100); // Should render in less than 100ms
    });

    test('should handle rapid button presses', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <ActionButton type="approve" onPress={onPressMock} />
      );

      const button = screen.getByText('Approve');
      
      // Simulate rapid presses
      for (let i = 0; i < 10; i++) {
        fireEvent.press(button);
      }

      expect(onPressMock).toHaveBeenCalledTimes(10);
    });

    test('should handle style changes efficiently', () => {
      const { rerender } = renderWithProviders(
        <ActionButton type="approve" onPress={() => {}} />
      );

      const startTime = Date.now();
      
      rerender(
        <ActionButton 
          type="approve" 
          onPress={() => {}} 
          customStyle={{ backgroundColor: 'blue' }} 
        />
      );
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(50); // Should update in less than 50ms
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should work with navigation context', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <ActionButton type="approve" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Approve'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should work with Redux context', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <ActionButton type="reject" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Reject'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });

    test('should work with both navigation and Redux context', () => {
      const onPressMock = jest.fn();
      
      renderWithProviders(
        <ActionButton type="approve" onPress={onPressMock} />
      );

      fireEvent.press(screen.getByText('Approve'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling Tests', () => {
    test('should handle missing onPress prop gracefully', () => {
      // This test ensures the component doesn't crash when onPress is undefined
      expect(() => {
        renderWithProviders(
          <ActionButton type="approve" onPress={undefined} />
        );
      }).not.toThrow();
    });

    test('should handle null onPress prop gracefully', () => {
      expect(() => {
        renderWithProviders(
          <ActionButton type="approve" onPress={null} />
        );
      }).not.toThrow();
    });

    test('should handle invalid type prop gracefully', () => {
      // TypeScript should prevent this, but testing runtime behavior
      expect(() => {
        renderWithProviders(
          <ActionButton type="invalid" onPress={() => {}} />
        );
      }).not.toThrow();
    });
  });
}); 