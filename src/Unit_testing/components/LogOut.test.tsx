import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import LogOut from '../../../components/LogOut';
jest.mock('../../../assets/svg/Logout.svg', () => () => <svg data-testid="logout-icon" />);
jest.mock('../../../utils/Colors/Colors', () => ({ white: '#fff', black: '#000', grey: '#ccc' }));
jest.mock('../../../utils/Scale/Scaling', () => ({ ms: (v: any) => v }));
jest.mock('../../../utils/Strings/Strings', () => ({ commonStrings: { logoutConfirmationMessage: 'Are you sure?', yes: 'Yes', no: 'No' } }));
jest.mock('i18next', () => ({ t: (k: string) => k }));
jest.mock('../../../components/BottomPopup', () => ({ children, ...props }: { children: React.ReactNode }) => <>{children}</>);
jest.mock('../../../components/ButtonComponent', () => ({
  title,
  onPress,
  showSecondaryButton,
  secondaryButtonTitle,
  onSecondaryPress,
}: {
  title: string;
  onPress: () => void;
  showSecondaryButton?: boolean;
  secondaryButtonTitle?: string;
  onSecondaryPress?: () => void;
}) => (
  <>
    <button onClick={onPress} data-testid="confirm-btn">{title}</button>
    {showSecondaryButton && <button onClick={onSecondaryPress} data-testid="cancel-btn">{secondaryButtonTitle}</button>}
  </>
));


describe('LogOut', () => {
  const baseProps = {
    testIdConfirm: 'confirm',
    testIdCancel: 'cancel',
    onCancel: jest.fn(),
    onConfirm: jest.fn(),
    visible: true,
    onClose: jest.fn(),
  };

  it('renders confirmation message and icon', () => {
    render(<LogOut {...baseProps} />);
    expect(screen.getByText('Are you sure?')).toBeTruthy();
    expect(screen.getByTestId('logout-icon')).toBeTruthy();
  });

  it('calls onConfirm when confirm button is pressed', () => {
    render(<LogOut {...baseProps} />);
    fireEvent.press(screen.getByTestId('confirm-btn'));
    expect(baseProps.onConfirm).toHaveBeenCalled();
  });

  it('calls onCancel when cancel button is pressed', () => {
    render(<LogOut {...baseProps} />);
    fireEvent.press(screen.getByTestId('cancel-btn'));
    expect(baseProps.onCancel).toHaveBeenCalled();
  });

  it('calls onClose when closed', () => {
    render(<LogOut {...baseProps} />);
    baseProps.onClose();
    expect(baseProps.onClose).toHaveBeenCalled();
  });

  it('matches snapshot', () => {
    const { toJSON } = render(<LogOut {...baseProps} />);
    expect(toJSON()).toMatchSnapshot();
  });

  it('does not crash with missing handlers', () => {
    render(<LogOut {...baseProps} onCancel={undefined as any} onConfirm={undefined as any} onClose={undefined as any} />);
    // No error expected
  });
}); 