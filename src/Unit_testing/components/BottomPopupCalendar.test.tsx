import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import BottomPopupCalendar from '../../components/BottomPopupCalendar';

// Mock dependencies but keep component functionality intact
jest.mock('react-native-calendars', () => {
  const mockReact = require('react');
  
  const MockCalendar = ({ onDayPress, renderHeader, onPressArrowLeft, onPressArrowRight }: any) => {
    mockReact.useEffect(() => {
      // Simulate calendar header render
      if (renderHeader) {                 
        renderHeader();
      }
    }, [renderHeader]);
    
    return mockReact.createElement('div', { 'data-testid': 'calendar' }, [
      mockReact.createElement('button', { 
        'data-testid': 'calendar-day-15',
        key: 'day-15',
        onClick: () => onDayPress && onDayPress({ 
          year: 2025, 
          month: 0, // Calendar uses 0-11 format
          day: 15, 
          dateString: '2025-01-15' 
        })
      }, '15'),
      mockReact.createElement('button', { 
        'data-testid': 'calendar-day-20',
        key: 'day-20',
        onClick: () => onDayPress && onDayPress({ 
          year: 2025, 
          month: 0, 
          day: 20, 
          dateString: '2025-01-20' 
        })
      }, '20'),
      mockReact.createElement('button', { 
        'data-testid': 'prev-month',
        key: 'prev-month',
        onClick: () => onPressArrowLeft && onPressArrowLeft()
      }, 'Previous'),
      mockReact.createElement('button', { 
        'data-testid': 'next-month',
        key: 'next-month',
        onClick: () => onPressArrowRight && onPressArrowRight()
      }, 'Next')
    ]);
  };
  
  return {
    Calendar: MockCalendar,
    LocaleConfig: {
      locales: { 
        en: {
          monthNames: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
          monthNamesShort: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
          dayNames: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
          dayNamesShort: ['S', 'M', 'T', 'W', 'T', 'F', 'S'],
          today: 'Today'
        }
      },
      defaultLocale: 'en'
    }
  };
});

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

jest.mock('../../components/BottomPopup', () => ({ children, visible, onCancelPress }: any) => {
  const mockReact = require('react');
  return visible ? mockReact.createElement('div', { 'data-testid': 'bottom-popup' }, [
    children,
    mockReact.createElement('button', { 
      'data-testid': 'cancel-button', 
      key: 'cancel-btn',
      onClick: onCancelPress 
    }, 'Cancel')
  ]) : null;
});

jest.mock('../../components/ButtonComponent', () => ({ title, onPress }: any) => {
  const mockReact = require('react');
  // Handle both string titles and object references like Strings.commonStrings.apply
  const displayTitle = typeof title === 'string' ? title : 'Apply';
  const testId = displayTitle.toLowerCase().replace(/\s+/g, '-');
  return mockReact.createElement('button', { 
    'data-testid': `button-${testId}`,
    onClick: onPress 
  }, displayTitle);
});

jest.mock('../../assets/svg/calendar_down.svg', () => () => {
  const mockReact = require('react');
  return mockReact.createElement('div', { 'data-testid': 'arrow-down' });
});
jest.mock('../../assets/svg/calendar_left.svg', () => () => {
  const mockReact = require('react');
  return mockReact.createElement('div', { 'data-testid': 'arrow-left' });
});
jest.mock('../../assets/svg/calendar_Right.svg', () => () => {
  const mockReact = require('react');
  return mockReact.createElement('div', { 'data-testid': 'arrow-right' });
});

// Mock with correct relative paths
jest.mock('../../utils/Colors/Colors', () => {
  return {
    __esModule: true,
    default: {
      white: '#FFFFFF',
      forgotPinBlue: '#007AFF',
      textPrimary: '#000000',
      dailyProgressItemBg: '#F0F0F0',
      searchBorderGrey: '#CCCCCC',
      containerligetBlue: '#F5F5F5',
      pipeIdTextBlack: '#333333',
      grey: '#888888',
    }
  };
});

jest.mock('../../utils/Strings/Strings', () => {
  return {
    __esModule: true,
    default: {
      DailyProgress: { 
        selectDate: 'Select Date',
        fromDate: 'From Date',
        endDate: 'End Date'
      },
      SyncData: {
        todate: 'To Date'
      },
      commonStrings: {
        apply: 'Apply'
      }
    }
  };
});

jest.mock('../../utils/Scale/Scaling', () => ({ ms: (value: number) => value }));
jest.mock('../../components/Fonts', () => ({ AppFonts: { Medium: 'Medium', SemiBold: 'SemiBold' } }));

// Suppress console.log for cleaner test output
const originalConsoleLog = console.log;
beforeAll(() => {
  console.log = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
});

describe('BottomPopupCalendar', () => {
  const defaultProps = {
    visible: true,
    fromDateTitle: 'From Date',
    onClose: jest.fn(),
    selectedFromDate: '15/01/2025',
    onApply: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render when visible is true', () => {
      render(<BottomPopupCalendar {...defaultProps} />);
      expect(screen.getByTestId('bottom-popup')).toBeTruthy();
      expect(screen.getByText('Select Date')).toBeTruthy();
    });

    it('should not render when visible is false', () => {
      render(<BottomPopupCalendar {...defaultProps} visible={false} />);
      expect(screen.queryByTestId('bottom-popup')).toBeNull();
    });

    it('should render from date field when showFromDate is true', () => {
      render(<BottomPopupCalendar {...defaultProps} showFromDate={true} />);
      expect(screen.getByText('From Date')).toBeTruthy();
    });

    it('should render to date field when showToDate is true', () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          showToDate={true} 
          toDateTitle="To Date"
        />
      );
      expect(screen.getByText('To Date')).toBeTruthy();
    });

    it('should render apply button when showBottomButton is true', () => {
      render(<BottomPopupCalendar {...defaultProps} showBottomButton={true} />);
      expect(screen.getByTestId('button-commonstrings.apply')).toBeTruthy();
    });
  });

  describe('Date Initialization', () => {
    it('should initialize from date with DD/MM/YYYY format', () => {
      render(<BottomPopupCalendar {...defaultProps} selectedFromDate="15/01/2025" />);
      expect(screen.getByText('15/01/2025')).toBeTruthy();
    });

    it('should initialize from date with ISO format', () => {
      render(<BottomPopupCalendar {...defaultProps} selectedFromDate="2025-01-15" />);
      expect(screen.getByText('15/01/2025')).toBeTruthy();
    });

    it('should initialize to date with DD/MM/YYYY format', () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          showToDate={true}
          seelctedToDate="20/01/2025"
        />
      );
      expect(screen.getByText('20/01/2025')).toBeTruthy();
    });

    it('should initialize to date with ISO format', () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          showToDate={true}
          seelctedToDate="2025-01-20"
        />
      );
      expect(screen.getByText('20/01/2025')).toBeTruthy();
    });

    it('should use current date when no selectedFromDate provided', () => {
      const today = new Date();
      const expectedDate = `${String(today.getDate()).padStart(2, '0')}/${String(today.getMonth() + 1).padStart(2, '0')}/${today.getFullYear()}`;
      
      render(<BottomPopupCalendar {...defaultProps} selectedFromDate="" />);
      expect(screen.getByText(expectedDate)).toBeTruthy();
    });

    it('should set to date same as from date when showToDate is true but no seelctedToDate provided', () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          showToDate={true}
          selectedFromDate="15/01/2025"
        />
      );
      const dateElements = screen.getAllByText('15/01/2025');
      expect(dateElements).toHaveLength(2); // Both from and to dates should be the same
    });
  });

  describe('User Interactions', () => {
    it('should handle from date field press', async () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          showFromDate={true}
          selectedFromDate="15/01/2025"
        />
      );
      
      const fromDateField = screen.getByText('15/01/2025');
      fireEvent.press(fromDateField);
      
      // The selecting state should change, which affects calendar display
      await waitFor(() => {
        expect(screen.getByTestId('calendar')).toBeTruthy();
      });
    });

    it('should handle to date field press', async () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          showToDate={true}
          seelctedToDate="20/01/2025"
        />
      );
      
      const toDateElement = screen.getByText('20/01/2025');
      fireEvent.press(toDateElement);
      
      await waitFor(() => {
        expect(screen.getByTestId('calendar')).toBeTruthy();
      });
    });

    it('should handle calendar day selection for from date', async () => {
      render(<BottomPopupCalendar {...defaultProps} />);
      
      const dayButton = screen.getByTestId('calendar-day-15');
      fireEvent.press(dayButton);
      
      await waitFor(() => {
        expect(screen.getByText('15/01/2025')).toBeTruthy();
      });
    });

    it('should handle calendar day selection for to date', async () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          showToDate={true}
        />
      );
      
      // First click to date field to set selecting mode to 'to'
      const toDateField = screen.getAllByText(/\d{2}\/\d{2}\/\d{4}/)[1]; // Second date field (to date)
      fireEvent.press(toDateField);
      
      // Then select a day
      const dayButton = screen.getByTestId('calendar-day-20');
      fireEvent.press(dayButton);
      
      await waitFor(() => {
        expect(screen.getByText('20/01/2025')).toBeTruthy();
      });
    });

    it('should set to date when from date selected and showToDate is true but no toDate exists', async () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          showToDate={true}
          selectedFromDate=""
          seelctedToDate=""
        />
      );
      
      const dayButton = screen.getByTestId('calendar-day-15');
      fireEvent.press(dayButton);
      
      await waitFor(() => {
        const dateElements = screen.getAllByText('15/01/2025');
        expect(dateElements.length).toBeGreaterThanOrEqual(1);
      });
    });
  });

  describe('Month Navigation', () => {
    it('should handle previous month navigation', async () => {
      render(<BottomPopupCalendar {...defaultProps} />);
      
      const prevButton = screen.getByTestId('prev-month');
      fireEvent.press(prevButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('calendar')).toBeTruthy();
      });
    });

    it('should handle next month navigation', async () => {
      render(<BottomPopupCalendar {...defaultProps} />);
      
      const nextButton = screen.getByTestId('next-month');
      fireEvent.press(nextButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('calendar')).toBeTruthy();
      });
    });

    it('should handle year rollover when going to previous month from January', async () => {
      // Set up component with January date
      render(<BottomPopupCalendar {...defaultProps} selectedFromDate="15/01/2025" />);
      
      const prevButton = screen.getByTestId('prev-month');
      fireEvent.press(prevButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('calendar')).toBeTruthy();
      });
    });

    it('should handle year rollover when going to next month from December', async () => {
      // Set up component with December date
      render(<BottomPopupCalendar {...defaultProps} selectedFromDate="15/12/2025" />);
      
      const nextButton = screen.getByTestId('next-month');
      fireEvent.press(nextButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('calendar')).toBeTruthy();
      });
    });
  });

  describe('Month Picker', () => {
    it('should render calendar with month navigation elements', () => {
      render(<BottomPopupCalendar {...defaultProps} />);
      
      // Check that calendar and navigation elements are present
      expect(screen.getByTestId('calendar')).toBeTruthy();
      expect(screen.getByText('Previous')).toBeTruthy();
      expect(screen.getByText('Next')).toBeTruthy();
    });

    it('should handle month navigation', async () => {
      render(<BottomPopupCalendar {...defaultProps} />);
      
      // Test calendar month navigation
      const prevButton = screen.getByText('Previous');
      fireEvent.press(prevButton);
      
      const nextButton = screen.getByText('Next');
      fireEvent.press(nextButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('calendar')).toBeTruthy();
      });
    });

    it('should handle calendar day selection', async () => {
      render(<BottomPopupCalendar {...defaultProps} />);
      
      // Test calendar day selection
      const dayButton = screen.getByText('15');       
      fireEvent.press(dayButton);
                                  
      await waitFor(() => {
        expect(screen.getByText('15/01/2025')).toBeTruthy();
      });
    });

    it('should handle different calendar day selection', async () => {
      render(<BottomPopupCalendar {...defaultProps} />);
      
      // Test another calendar day selection
      const dayButton = screen.getByText('20');
      fireEvent.press(dayButton);
      
      await waitFor(() => {
        expect(screen.getByText('20/01/2025')).toBeTruthy();
      });
    });
  });

  describe('Apply Functionality', () => {
    it('should call onApply with formatted dates when apply button is pressed', async () => {
      const onApply = jest.fn();
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          onApply={onApply}
          showBottomButton={true}
          selectedFromDate="15/01/2025"
          showToDate={true}
          seelctedToDate="20/01/2025"
        />
      );
      
      const applyButton = screen.getByTestId('button-apply');
      fireEvent.press(applyButton);
      
      await waitFor(() => {
        expect(onApply).toHaveBeenCalledWith('15/01/2025', '20/01/2025');
      });
    });

    it('should call onApply with current date when no dates selected', async () => {
      const onApply = jest.fn();
      const today = new Date();
      const expectedDate = `${String(today.getDate()).padStart(2, '0')}/${String(today.getMonth() + 1).padStart(2, '0')}/${today.getFullYear()}`;
      
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          onApply={onApply}
          showBottomButton={true}
          selectedFromDate=""
        />
      );
      
      const applyButton = screen.getByTestId('button-apply');
      fireEvent.press(applyButton);
      
      await waitFor(() => {
        expect(onApply).toHaveBeenCalledWith(expectedDate, expectedDate);
      });
    });
  });

  describe('Close Functionality', () => {
    it('should call onClose when cancel button is pressed', async () => {
      const onClose = jest.fn();
      render(<BottomPopupCalendar {...defaultProps} onClose={onClose} />);
      
      const cancelButton = screen.getByTestId('cancel-button');
      fireEvent.press(cancelButton);
      
      expect(onClose).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('should handle invalid date gracefully', () => {
      render(<BottomPopupCalendar {...defaultProps} selectedFromDate="invalid-date" />);
      expect(screen.getByTestId('calendar')).toBeTruthy();
    });

    it('should handle undefined props gracefully', () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps}
          selectedFromDate={undefined as any}
          seelctedToDate={undefined as any}
        />
      );
      expect(screen.getByTestId('bottom-popup')).toBeTruthy();
    });

    it('should render gracefully with invalid date strings', () => {
      // Test with various invalid date formats
      render(<BottomPopupCalendar {...defaultProps} selectedFromDate="invalid-date-format" />);
      expect(screen.getByTestId('bottom-popup')).toBeTruthy();
    });

    it('should handle empty string dates', () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps}
          selectedFromDate=""
          seelctedToDate=""
          showToDate={true}
        />
      );
      expect(screen.getByTestId('calendar')).toBeTruthy();
    });

    it('should handle leap year dates', () => {
      render(<BottomPopupCalendar {...defaultProps} selectedFromDate="29/02/2024" />);
      expect(screen.getByText('29/02/2024')).toBeTruthy();
    });
  });

  describe('Props Variations', () => {
    it('should use default toDateTitle when not provided', () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          showToDate={true}
        />
      );
      expect(screen.getByText('SyncData.todate')).toBeTruthy();
    });

    it('should use provided toDateTitle', () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          showToDate={true}
          toDateTitle="Custom To Date"
        />
      );
      expect(screen.getByText('Custom To Date')).toBeTruthy();
    });

    it('should handle showFromDate false', () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          showFromDate={false}
        />
      );
      expect(screen.queryByText('From Date')).toBeNull();
    });

    it('should handle all boolean props as false', () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          showFromDate={false}
          showToDate={false}
          showBottomButton={false}
        />
      );
      expect(screen.queryByText('From Date')).toBeNull();
      expect(screen.queryByTestId('button-commonstrings.apply')).toBeNull();
    });
  });

  describe('State Management', () => {
    it('should maintain selecting state correctly', async () => {
      render(
        <BottomPopupCalendar 
          {...defaultProps} 
          showToDate={true}
        />
      );
      
      // Initially selecting 'from'
      const fromDateField = screen.getAllByText(/\d{2}\/\d{2}\/\d{4}/)[0];
      fireEvent.press(fromDateField);
      
      // Switch to 'to'
      const toDateField = screen.getAllByText(/\d{2}\/\d{2}\/\d{4}/)[1];
      fireEvent.press(toDateField);
      
      // Select a day while in 'to' mode
      const dayButton = screen.getByTestId('calendar-day-20');
      fireEvent.press(dayButton);
      
      await waitFor(() => {
        expect(screen.getByText('20/01/2025')).toBeTruthy();
      });
    });

    it('should handle calendar date updates correctly', async () => {
      render(<BottomPopupCalendar {...defaultProps} selectedFromDate="15/01/2025" />);
      
      // Navigate to next month
      const nextButton = screen.getByTestId('next-month');
      fireEvent.press(nextButton);
      
      // Navigate back
      const prevButton = screen.getByTestId('prev-month');
      fireEvent.press(prevButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('calendar')).toBeTruthy();
      });
    });
  });

  describe('Date Format Display', () => {
    it('should correctly format and display dates in DD/MM/YYYY format', () => {
      render(<BottomPopupCalendar {...defaultProps} selectedFromDate="05/03/2025" />);
      expect(screen.getByText('05/03/2025')).toBeTruthy();
    });

    it('should pad single digit days and months', () => {
      render(<BottomPopupCalendar {...defaultProps} selectedFromDate="1/3/2025" />);
      expect(screen.getByText('01/03/2025')).toBeTruthy();
    });
  });
}); 