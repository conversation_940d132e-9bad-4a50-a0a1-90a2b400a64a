import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import TextInputComponent from '../../../components/TextInputComponent';

jest.mock('../../../assets/svg/eye.svg', () => {
  return {
    __esModule: true,
    default: () => <svg data-testid="eye-icon" /> // Optional: replace <svg> with valid JSX tag if issues
  };
});

jest.mock('../../../assets/svg/eye_hide.svg', () => ({
  __esModule: true,
  default: () => <svg data-testid="pwd-hide-icon" />
}));

jest.mock('../../utils/Colors/Colors', () => ({
  placeholder: '#aaa',
  primary: '#333',
  pipeIdTextBlack: '#111',
  containerligetBlue: '#eee',
  inputBorder: '#ccc',
  blue: '#00f',
  black: '#000'
}));

jest.mock('../../utils/Scale/Scaling', () => ({
  ms: (v: any) => v
}));

jest.mock('./../../components/Fonts', () => ({
  AppFonts: { Medium: 'MNMedium', SemiBold: 'MNSemiBold' }
}));

describe('TextInputComponent', () => {
  it('renders with label and value', () => {
    render(
      <TextInputComponent
        label="Label"
        value="abc"
        onChangeText={jest.fn()}
        placeholder="Enter"
      />
    );
    expect(screen.getByText('Label')).toBeTruthy();
    expect(screen.getByDisplayValue('abc')).toBeTruthy();
  });

  it('calls onChangeText when typing', () => {
    const onChangeText = jest.fn();
    render(
      <TextInputComponent
        value=""
        onChangeText={onChangeText}
        placeholder="Type..."
      />
    );
    fireEvent.changeText(screen.getByPlaceholderText('Type...'), 'new');
    expect(onChangeText).toHaveBeenCalledWith('new');
  });

  it('toggles secure text entry', () => {
    render(
      <TextInputComponent
        value="secret"
        onChangeText={jest.fn()}
        placeholder="Pwd"
        secureTextEntry
        showToggleIcon
      />
    );

    const toggleIcon = screen.queryByTestId('pwd-hide-icon');
    if (toggleIcon) {
      fireEvent.press(toggleIcon);
    }

    // No assertion, just verifying no crash occurs
  });

  it('renders with secondary label and text', () => {
    render(
      <TextInputComponent
        value=""
        onChangeText={jest.fn()}
        placeholder="P"
        secondaryLabel="SecLabel"
        showSecondaryText
        secondaryText="SecText"
      />
    );
    expect(screen.getByText('SecLabel')).toBeTruthy();
    expect(screen.getByText('SecText')).toBeTruthy();
  });

  it('handles numeric and multiline input', () => {
    render(
      <TextInputComponent
        value="123"
        onChangeText={jest.fn()}
        placeholder="Num"
        isNumeric
        multiline
      />
    );
    expect(screen.getByDisplayValue('123')).toBeTruthy();
  });

  it('does not crash if onChangeText is missing', () => {
    render(
      <TextInputComponent
        value="test"
        onChangeText={undefined as any}
        placeholder="Test"
      />
    );
    const input = screen.getByDisplayValue('test');
    fireEvent.changeText(input, 'x');
    // No error expected
  });

  it('matches snapshot', () => {
    const { toJSON } = render(
      <TextInputComponent
        label="Snap"
        value="Snapshot"
        onChangeText={jest.fn()}
        placeholder="Snap"
      />
    );
    expect(toJSON()).toMatchSnapshot();
  });
});
