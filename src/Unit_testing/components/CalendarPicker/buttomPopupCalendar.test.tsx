import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import BottomCalendarModal from '../../../components/CalendarPicker/BottomPopupCalendar';

// Mock all SVGs used in the component
jest.mock('../../../../assets/svg/calendar_down.svg', () => () => <svg data-testid="calendar-down-icon" />);
jest.mock('../../../../assets/svg/calendar_left.svg', () => () => <svg data-testid="calendar-left-icon" />);
jest.mock('../../../../assets/svg/calendar_Right.svg', () => () => <svg data-testid="calendar-right-icon" />);

// Mock child components
jest.mock('../../../../components/BottomPopup', () => ({ children, ...props }: any) => <>{children}</>);
jest.mock('../../../../components/ButtonComponent', () => ({ title, onPress }: any) => (
  <button onClick={onPress}>{title}</button>
));

// Mock utility modules
jest.mock('../../../../utils/Scale/Scaling', () => ({ ms: (v: number) => v }));
jest.mock('../../../../components/Fonts', () => ({ AppFonts: { SemiBold: 'SemiBold', Medium: 'Medium' } }));
jest.mock('react-i18next', () => ({ useTranslation: () => ({ t: (k: string) => k }) }));
jest.mock('../../../../utils/helpers', () => ({ FormatMonthDate: (d: string) => d }));
jest.mock('moment', () => () => ({ format: () => '2024-03-15' }));

// Mock react-native-calendars
jest.mock('react-native-calendars', () => ({
  Calendar: ({ onDayPress, ...props }: any) => (
    <div data-testid="calendar" onClick={() => onDayPress && onDayPress({ year: 2024, month: 3, day: 15, dateString: '2024-03-15' })}>
      CalendarMock
    </div>
  ),
  LocaleConfig: {
    locales: { en: { monthNames: [], monthNamesShort: [], dayNames: [], dayNamesShort: [], today: '' } },
    defaultLocale: 'en'
  }
}));

describe('BottomCalendarModal', () => {
  const defaultProps = {
    visible: true,
    onClose: jest.fn(),
    selectedFromDate: '2024-03-15',
    fromDateTitle: 'From Date',
    onApply: jest.fn(),
  };

  it('renders without crashing', () => {
    render(<BottomCalendarModal {...defaultProps} />);
    expect(screen.getByText('selectDate')).toBeTruthy();
  });

  it('calls onApply when apply button is pressed', () => {
    render(<BottomCalendarModal {...defaultProps} showBottomButton />);
    fireEvent.press(screen.getByText('apply'));
    expect(defaultProps.onApply).toHaveBeenCalled();
  });

  it('calls onClose when closed', () => {
    render(<BottomCalendarModal {...defaultProps} />);
    // Simulate closing if you have a close button or similar
    // fireEvent.click(screen.getByTestId('close-button'));
    // expect(defaultProps.onClose).toHaveBeenCalled();
  });

  // Add more tests for date selection, month navigation, etc.
}); 