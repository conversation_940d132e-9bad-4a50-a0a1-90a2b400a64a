import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import BottomPopupImageUpload from '../../../components/BottomPopupImageUpload';
jest.mock('../../../assets/svg/camera.svg', () => () => <svg data-testid="camera-icon" />);
jest.mock('../../../assets/svg/gallery.svg', () => () => <svg data-testid="gallery-icon" />);
jest.mock('../../../utils/Colors/Colors', () => ({ white: '#fff', darkBlue: '#123', dailyProgressItemBg: '#eee', secondary: '#456' }));
jest.mock('../../../utils/Strings/Strings', () => ({ DailyProgress: { uploadImageFrom: 'Upload from', camera: 'Camera', gallery: 'Gallery' } }));
jest.mock('../../../utils/Scale/Scaling', () => ({ ms: (v: number) => v }));

describe('BottomPopupImageUpload', () => {
  it('renders both camera and gallery options', () => {
    render(<BottomPopupImageUpload onCameraPress={jest.fn()} onGalleryPress={jest.fn()} />);
    expect(screen.getByText('Upload from')).toBeTruthy();
    expect(screen.getByText('Camera')).toBeTruthy();
    expect(screen.getByText('Gallery')).toBeTruthy();
    expect(screen.getByTestId('camera-icon')).toBeTruthy();
    expect(screen.getByTestId('gallery-icon')).toBeTruthy();
  });

  it('calls onCameraPress when camera option is pressed', () => {
    const onCameraPress = jest.fn();
    render(<BottomPopupImageUpload onCameraPress={onCameraPress} onGalleryPress={jest.fn()} />);
    fireEvent.press(screen.getByText('Camera'));
    expect(onCameraPress).toHaveBeenCalled();
  });

  it('calls onGalleryPress when gallery option is pressed', () => {
    const onGalleryPress = jest.fn();
    render(<BottomPopupImageUpload onCameraPress={jest.fn()} onGalleryPress={onGalleryPress} />);
    fireEvent.press(screen.getByText('Gallery'));
    expect(onGalleryPress).toHaveBeenCalled();
  });

  it('handles errors in onCameraPress gracefully', () => {
    const errorFn = jest.fn(() => { throw new Error('Camera error'); });
    render(<BottomPopupImageUpload onCameraPress={errorFn} onGalleryPress={jest.fn()} />);
    fireEvent.press(screen.getByText('Camera'));
    expect(errorFn).toHaveBeenCalled();
  });

  it('handles errors in onGalleryPress gracefully', () => {
    const errorFn = jest.fn(() => { throw new Error('Gallery error'); });
    render(<BottomPopupImageUpload onCameraPress={jest.fn()} onGalleryPress={errorFn} />);
    fireEvent.press(screen.getByText('Gallery'));
    expect(errorFn).toHaveBeenCalled();
  });

  it('does not crash if handlers are undefined', () => {
    render(<BottomPopupImageUpload onCameraPress={undefined as any} onGalleryPress={undefined as any} />);
    fireEvent.press(screen.getByText('Camera'));
    fireEvent.press(screen.getByText('Gallery'));
    // No error expected
  });

  it('is accessible with accessibilityRole', () => {
    const { getAllByRole } = render(<BottomPopupImageUpload onCameraPress={jest.fn()} onGalleryPress={jest.fn()} />);
    expect(getAllByRole('button').length).toBe(2);
  });

  it('matches snapshot', () => {
    const { toJSON } = render(<BottomPopupImageUpload onCameraPress={jest.fn()} onGalleryPress={jest.fn()} />);
    expect(toJSON()).toMatchSnapshot();
  });

  it('handles rapid presses without error', () => {
    const onCameraPress = jest.fn();
    const onGalleryPress = jest.fn();
    render(<BottomPopupImageUpload onCameraPress={onCameraPress} onGalleryPress={onGalleryPress} />);
    const cameraBtn = screen.getByText('Camera');
    const galleryBtn = screen.getByText('Gallery');
    fireEvent.press(cameraBtn);
    fireEvent.press(cameraBtn);
    fireEvent.press(galleryBtn);
    fireEvent.press(galleryBtn);
    expect(onCameraPress).toHaveBeenCalledTimes(2);
    expect(onGalleryPress).toHaveBeenCalledTimes(2);
  });
}); 