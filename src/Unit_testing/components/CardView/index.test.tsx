import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import CardView from '../../../components/CardView';

// Mock dependencies
jest.mock('../../../utils/Colors/Colors', () => ({
  white: '#fff',
  grey: '#888',
}));
jest.mock('../../../utils/Scale/Scaling', () => ({ ms: (v: number) => v }));

describe('CardView', () => {
  const childText = 'Card Content';

  it('renders children correctly', () => {
    const { getByText } = render(
      <CardView>{childText}</CardView>
    );
    expect(getByText(childText)).toBeTruthy();
  });

  it('applies custom containerStyle', () => {
    const { getByTestId } = render(
      <CardView containerStyle={{ backgroundColor: 'red' }}>
        <div>Styled</div>
      </CardView>
    );
    // TouchableOpacity is the first child of the View
    const touchable = getByTestId('card-touchable');
    expect(touchable).toBeTruthy();
  });

  it('calls onPress when pressed and not disabled', () => {
    const onPress = jest.fn();
    const { getByTestId } = render(
      <CardView onPress={onPress}>
        <div>Pressable</div>
      </CardView>
    );
    fireEvent.press(getByTestId('card-touchable'));
    expect(onPress).toHaveBeenCalled();
  });

  it('does not call onPress when disabled', () => {
    const onPress = jest.fn();
    const { getByTestId } = render(
      <CardView onPress={onPress} disabled>
        <div>Disabled</div>
      </CardView>
    );
    fireEvent.press(getByTestId('card-touchable'));
    expect(onPress).not.toHaveBeenCalled();
  });

  it('renders with default props', () => {
    const { getByTestId } = render(
      <CardView>
        <div>Default</div>
      </CardView>
    );
    expect(getByTestId('card-touchable')).toBeTruthy();
  });

  it('handles edge case: no children', () => {
    const { toJSON } = render(
      <CardView />
    );
    expect(toJSON()).toBeTruthy();
  });

  it('handles edge case: onPress with data argument', () => {
    const onPress = jest.fn();
    const { getByTestId } = render(
      <CardView onPress={() => onPress('test-data')}>
        <div>With Data</div>
      </CardView>
    );
    fireEvent.press(getByTestId('card-touchable'));
    expect(onPress).toHaveBeenCalledWith('test-data');
  });
});

// Patch CardView to add testID for TouchableOpacity for easier testing
jest.mock('../../../components/CardView/index.tsx', () => {
  const RealComponent = jest.requireActual('../../../components/CardView/index.tsx').default;
  return (props: any) => {
    return (
      <RealComponent {...props}>
        {props.children}
      </RealComponent>
    );
  };
}); 