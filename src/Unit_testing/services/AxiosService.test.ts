import {
  AxiosRequestForAuth,
  AxiosRequestForDownload,
  AxiosRequest,
  AxiosRequestForNode,
  AxiosRequestForImageDownload,
} from '../../services/AxiosService';
import Config from '../../config/Config';
import BaseApiConstants from '../../utils/Constants/CommonConstant';
import PrintLog from '../../utils/Logger/PrintLog';

// Mock dependencies
jest.mock('axios', () => {
  const mockAxios = jest.fn();
  mockAxios.defaults = {
    baseURL: '',
    timeout: 0,
    headers: {},
  };
  return mockAxios;
});

jest.mock('../../config/Config', () => ({
  BASE_URL_FOR_AUTH: 'https://test-auth.com',
  BASE_URL_FOR_DOWNLOAD: 'https://test-download.com',
}));

jest.mock('../../utils/Constants/CommonConstant', () => ({
  nodeList: 'https://test-node.com',
  imageUploadDownloadUrl: 'https://test-images.com',
}));

jest.mock('../../utils/Logger/PrintLog', () => ({
  debug: jest.fn(),
  error: jest.fn(),
}));

const axios = require('axios');

describe('AxiosService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset axios defaults
    axios.defaults = {
      baseURL: '',
      timeout: 0,
      headers: {},
    };
  });

  describe('AxiosRequestForAuth', () => {
    it('should make successful auth request', async () => {
      // Arrange
      const mockResponse = {
        data: { success: true, token: 'test-token' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      };
      axios.mockResolvedValue(mockResponse);

      const apiParams = {
        url: '/login',
        method: 'POST',
        data: { username: 'test', password: 'test123' },
      };

      // Act
      const result = await AxiosRequestForAuth(apiParams);

      // Assert
      expect(axios).toHaveBeenCalledWith({
        timeout: 5000,
        ...apiParams,
      });
      expect(axios.defaults.baseURL).toBe(Config.BASE_URL_FOR_AUTH);
      expect(result).toEqual(mockResponse);
      expect(PrintLog.debug).toHaveBeenCalledWith(
        'AxiosRequestForAuth',
        ' -- baseUrl: ',
        Config.BASE_URL_FOR_AUTH,
        ' -- apiParams: ',
        apiParams
      );
    });

    it('should handle auth request error', async () => {
      // Arrange
      const mockError = {
        response: {
          status: 401,
          data: { error: 'Unauthorized' },
          statusText: 'Unauthorized',
          headers: {},
          config: {},
        },
        message: 'Request failed with status code 401',
      };
      axios.mockRejectedValue(mockError);

      const apiParams = {
        url: '/login',
        method: 'POST',
        data: { username: 'invalid', password: 'invalid' },
      };

      // Act
      const result = await AxiosRequestForAuth(apiParams);

      // Assert
      expect(result).toEqual(mockError.response);
      expect(PrintLog.error).toHaveBeenCalledWith(
        'AxiosRequestForAuth',
        { 'Catch Error: ': mockError }
      );
    });

    it('should handle auth request timeout', async () => {
      // Arrange
      const mockError = {
        response: undefined,
        message: 'timeout of 5000ms exceeded',
        code: 'ECONNABORTED',
      };
      axios.mockRejectedValue(mockError);

      const apiParams = {
        url: '/login',
        method: 'POST',
        data: { username: 'test', password: 'test123' },
      };

      // Act
      const result = await AxiosRequestForAuth(apiParams);

      // Assert
      expect(result).toBeUndefined();
      expect(PrintLog.error).toHaveBeenCalledWith(
        'AxiosRequestForAuth',
        { 'Catch Error: ': mockError }
      );
    });
  });

  describe('AxiosRequestForDownload', () => {
    it('should make successful download request', async () => {
      // Arrange
      const mockResponse = {
        data: { success: true, downloadUrl: 'https://test-download.com/file.pdf' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      };
      axios.mockResolvedValue(mockResponse);

      const apiParams = {
        url: '/download/file',
        method: 'GET',
        params: { fileId: 'test-file-id' },
      };

      // Act
      const result = await AxiosRequestForDownload(apiParams);

      // Assert
      expect(axios).toHaveBeenCalledWith({
        timeout: 15000,
        ...apiParams,
      });
      expect(axios.defaults.baseURL).toBe(Config.BASE_URL_FOR_DOWNLOAD);
      expect(result).toEqual(mockResponse);
      expect(PrintLog.debug).toHaveBeenCalledWith(
        'AxiosRequestForDownload',
        ' -- baseUrl: ',
        Config.BASE_URL_FOR_DOWNLOAD,
        ' -- apiParams: ',
        apiParams
      );
    });

    it('should handle download request error', async () => {
      // Arrange
      const mockError = {
        response: {
          status: 404,
          data: { error: 'File not found' },
          statusText: 'Not Found',
          headers: {},
          config: {},
        },
        message: 'Request failed with status code 404',
      };
      axios.mockRejectedValue(mockError);

      const apiParams = {
        url: '/download/invalid-file',
        method: 'GET',
      };

      // Act
      const result = await AxiosRequestForDownload(apiParams);

      // Assert
      expect(result).toEqual(mockError.response);
      expect(PrintLog.error).toHaveBeenCalledWith(
        'AxiosRequestForDownload',
        { 'Catch Error: ': mockError }
      );
    });

    it('should handle network error for download', async () => {
      // Arrange
      const mockError = {
        response: undefined,
        message: 'Network Error',
        code: 'ENOTFOUND',
      };
      axios.mockRejectedValue(mockError);

      const apiParams = {
        url: '/download/file',
        method: 'GET',
      };

      // Act
      const result = await AxiosRequestForDownload(apiParams);

      // Assert
      expect(result).toBeUndefined();
      expect(PrintLog.error).toHaveBeenCalledWith(
        'AxiosRequestForDownload',
        { 'Catch Error: ': mockError }
      );
    });

    it('should use longer timeout for download requests', async () => {
      // Arrange
      const mockResponse = {
        data: { success: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      };
      axios.mockResolvedValue(mockResponse);

      const apiParams = {
        url: '/download/large-file',
        method: 'GET',
      };

      // Act
      await AxiosRequestForDownload(apiParams);

      // Assert
      expect(axios).toHaveBeenCalledWith({
        timeout: 15000, // Should be 15 seconds, not 5 seconds
        ...apiParams,
      });
    });
  });

  describe('AxiosRequest', () => {
    it('should make successful request with custom baseURL', async () => {
      // Arrange
      const mockResponse = {
        data: { success: true, data: [] },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      };
      axios.mockResolvedValue(mockResponse);

      const baseUrl = 'https://custom-api.com';
      const apiParams = {
        url: '/custom-endpoint',
        method: 'GET',
      };

      // Act
      const result = await AxiosRequest(baseUrl, apiParams);

      // Assert
      expect(axios).toHaveBeenCalledWith({
        baseURL: baseUrl,
        timeout: 5000,
        ...apiParams,
      });
      expect(result).toEqual(mockResponse);
      expect(PrintLog.debug).toHaveBeenCalledWith(
        'AxiosRequest',
        ' -- baseUrl: ',
        baseUrl,
        ' -- apiParams: ',
        apiParams
      );
    });

    it('should handle custom request error', async () => {
      // Arrange
      const mockError = {
        response: {
          status: 500,
          data: { error: 'Internal Server Error' },
          statusText: 'Internal Server Error',
          headers: {},
          config: {},
        },
        message: 'Request failed with status code 500',
      };
      axios.mockRejectedValue(mockError);

      const baseUrl = 'https://custom-api.com';
      const apiParams = {
        url: '/failing-endpoint',
        method: 'POST',
        data: { invalid: 'data' },
      };

      // Act
      const result = await AxiosRequest(baseUrl, apiParams);

      // Assert
      expect(result).toEqual(mockError.response);
      expect(PrintLog.error).toHaveBeenCalledWith(
        'AxiosRequest',
        { 'Catch Error: ': mockError }
      );
    });

    it('should not affect global axios defaults', async () => {
      // Arrange
      const mockResponse = {
        data: { success: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      };
      axios.mockResolvedValue(mockResponse);

      const baseUrl = 'https://custom-api.com';
      const apiParams = {
        url: '/test',
        method: 'GET',
      };

      // Act
      await AxiosRequest(baseUrl, apiParams);

      // Assert
      expect(axios).toHaveBeenCalledWith({
        baseURL: baseUrl, // Should pass baseURL inline
        timeout: 5000,
        ...apiParams,
      });
      // Global defaults should not be affected
      expect(axios.defaults.baseURL).toBe('');
    });
  });

  describe('AxiosRequestForNode', () => {
    it('should make successful node request', async () => {
      // Arrange
      const mockResponse = {
        data: { success: true, nodeData: { id: 'node-123', status: 'active' } },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      };
      axios.mockResolvedValue(mockResponse);

      const apiParams = {
        url: '/node/status',
        method: 'GET',
        params: { nodeId: 'node-123' },
      };

      // Act
      const result = await AxiosRequestForNode(apiParams);

      // Assert
      expect(axios).toHaveBeenCalledWith({
        timeout: 15000,
        ...apiParams,
      });
      expect(axios.defaults.baseURL).toBe(BaseApiConstants.nodeList);
      expect(result).toEqual(mockResponse);
      expect(PrintLog.debug).toHaveBeenCalledWith(
        'AxiosRequestForNode',
        ' -- baseUrl: ',
        BaseApiConstants.nodeList,
        ' -- apiParams: ',
        apiParams
      );
    });

    it('should handle node request error', async () => {
      // Arrange
      const mockError = {
        response: {
          status: 503,
          data: { error: 'Service Unavailable' },
          statusText: 'Service Unavailable',
          headers: {},
          config: {},
        },
        message: 'Request failed with status code 503',
      };
      axios.mockRejectedValue(mockError);

      const apiParams = {
        url: '/node/failing',
        method: 'POST',
        data: { action: 'start' },
      };

      // Act
      const result = await AxiosRequestForNode(apiParams);

      // Assert
      expect(result).toEqual(mockError.response);
      expect(PrintLog.error).toHaveBeenCalledWith(
        'AxiosRequestForNode',
        { 'Catch Error: ': mockError }
      );
    });

    it('should use longer timeout for node requests', async () => {
      // Arrange
      const mockResponse = {
        data: { success: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      };
      axios.mockResolvedValue(mockResponse);

      const apiParams = {
        url: '/node/heavy-operation',
        method: 'POST',
        data: { operation: 'process' },
      };

      // Act
      await AxiosRequestForNode(apiParams);

      // Assert
      expect(axios).toHaveBeenCalledWith({
        timeout: 15000, // Should be 15 seconds
        ...apiParams,
      });
    });
  });

  describe('AxiosRequestForImageDownload', () => {
    it('should make successful image download request', async () => {
      // Arrange
      const mockResponse = {
        data: { success: true, imageData: 'base64-encoded-image-data' },
        status: 200,
        statusText: 'OK',
        headers: { 'content-type': 'image/jpeg' },
        config: {},
      };
      axios.mockResolvedValue(mockResponse);

      const apiParams = {
        url: '/image/download',
        method: 'POST',
        data: { imageId: 'img-123' },
      };

      // Act
      const result = await AxiosRequestForImageDownload(apiParams);

      // Assert
      expect(axios).toHaveBeenCalledWith({
        timeout: 15000,
        ...apiParams,
      });
      expect(axios.defaults.baseURL).toBe(BaseApiConstants.imageUploadDownloadUrl);
      expect(result).toEqual(mockResponse);
      expect(PrintLog.debug).toHaveBeenCalledWith(
        'AxiosRequestForDownload',
        ' -- baseUrl: ',
        Config.BASE_URL_FOR_DOWNLOAD,
        ' -- apiParams: ',
        apiParams
      );
    });

    it('should handle image download error', async () => {
      // Arrange
      const mockError = {
        response: {
          status: 404,
          data: { error: 'Image not found' },
          statusText: 'Not Found',
          headers: {},
          config: {},
        },
        message: 'Request failed with status code 404',
      };
      axios.mockRejectedValue(mockError);

      const apiParams = {
        url: '/image/download',
        method: 'POST',
        data: { imageId: 'invalid-img-id' },
      };

      // Act
      const result = await AxiosRequestForImageDownload(apiParams);

      // Assert
      expect(result).toEqual(mockError.response);
      expect(PrintLog.error).toHaveBeenCalledWith(
        'AxiosRequestForDownload',
        { 'Catch Error: ': mockError }
      );
    });

    it('should handle large image download timeout', async () => {
      // Arrange
      const mockError = {
        response: undefined,
        message: 'timeout of 15000ms exceeded',
        code: 'ECONNABORTED',
      };
      axios.mockRejectedValue(mockError);

      const apiParams = {
        url: '/image/large-download',
        method: 'POST',
        data: { imageId: 'large-img-id' },
      };

      // Act
      const result = await AxiosRequestForImageDownload(apiParams);

      // Assert
      expect(result).toBeUndefined();
      expect(PrintLog.error).toHaveBeenCalledWith(
        'AxiosRequestForDownload',
        { 'Catch Error: ': mockError }
      );
    });
  });

  describe('Error handling edge cases', () => {
    it('should handle errors without response object', async () => {
      // Arrange
      const mockError = {
        message: 'Network Error',
        code: 'ENOTFOUND',
      };
      axios.mockRejectedValue(mockError);

      const apiParams = {
        url: '/test',
        method: 'GET',
      };

      // Act
      const result = await AxiosRequestForAuth(apiParams);

      // Assert
      expect(result).toBeUndefined();
      expect(PrintLog.error).toHaveBeenCalledWith(
        'AxiosRequestForAuth',
        { 'Catch Error: ': mockError }
      );
    });

    it('should handle null/undefined errors gracefully', async () => {
      // Arrange
      const mockError = null;
      axios.mockRejectedValue(mockError);

      const apiParams = {
        url: '/test',
        method: 'GET',
      };

      // Act
      let result;
      try {
        result = await AxiosRequestForDownload(apiParams);
      } catch (error) {
        // The actual implementation tries to access error.response on null
        // which causes a TypeError, so we expect this to be caught
        expect(error).toBeInstanceOf(TypeError);
        expect(error.message).toContain('Cannot read properties of null');
      }

      // Assert
      expect(PrintLog.error).toHaveBeenCalledWith(
        'AxiosRequestForDownload',
        { 'Catch Error: ': mockError }
      );
    });
  });

  describe('Request configuration', () => {
    it('should merge custom config with default timeout', async () => {
      // Arrange
      const mockResponse = {
        data: { success: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      };
      axios.mockResolvedValue(mockResponse);

      const apiParams = {
        url: '/test',
        method: 'GET',
        headers: { 'Custom-Header': 'custom-value' },
        timeout: 10000, // Custom timeout should be overridden
      };

      // Act
      await AxiosRequestForAuth(apiParams);

      // Assert
      expect(axios).toHaveBeenCalledWith({
        timeout: 5000, // Should use service-specific timeout
        ...apiParams,
      });
    });

    it('should preserve custom headers and data', async () => {
      // Arrange
      const mockResponse = {
        data: { success: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      };
      axios.mockResolvedValue(mockResponse);

      const customHeaders = { 'Authorization': 'Bearer token123' };
      const customData = { key: 'value', nested: { data: 'test' } };
      const apiParams = {
        url: '/test',
        method: 'POST',
        headers: customHeaders,
        data: customData,
      };

      // Act
      await AxiosRequestForDownload(apiParams);

      // Assert
      expect(axios).toHaveBeenCalledWith({
        timeout: 15000,
        url: '/test',
        method: 'POST',
        headers: customHeaders,
        data: customData,
      });
    });
  });

  describe('BaseURL configuration', () => {
    it('should set correct baseURL for each service', async () => {
      // Arrange
      const mockResponse = {
        data: { success: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      };
      axios.mockResolvedValue(mockResponse);

      const apiParams = {
        url: '/test',
        method: 'GET',
      };

      // Act
      await AxiosRequestForAuth(apiParams);
      await AxiosRequestForDownload(apiParams);
      await AxiosRequestForNode(apiParams);
      await AxiosRequestForImageDownload(apiParams);

      // Assert - verify baseURL was set correctly for each service
      expect(axios.defaults.baseURL).toBe(BaseApiConstants.imageUploadDownloadUrl);
      // Note: The last service called sets the global baseURL
    });

    it('should not interfere with custom baseURL in AxiosRequest', async () => {
      // Arrange
      const mockResponse = {
        data: { success: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      };
      axios.mockResolvedValue(mockResponse);

      const customBaseUrl = 'https://custom-service.com';
      const apiParams = {
        url: '/test',
        method: 'GET',
      };

      // Act
      await AxiosRequest(customBaseUrl, apiParams);

      // Assert
      expect(axios).toHaveBeenCalledWith({
        baseURL: customBaseUrl,
        timeout: 5000,
        ...apiParams,
      });
    });
  });

  describe('Logging behavior', () => {
    it('should log debug information for successful requests', async () => {
      // Arrange
      const mockResponse = {
        data: { success: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      };
      axios.mockResolvedValue(mockResponse);

      const apiParams = {
        url: '/test',
        method: 'GET',
      };

      // Act
      await AxiosRequestForAuth(apiParams);

      // Assert
      expect(PrintLog.debug).toHaveBeenCalledWith(
        'AxiosRequestForAuth',
        ' -- baseUrl: ',
        Config.BASE_URL_FOR_AUTH,
        ' -- apiParams: ',
        apiParams
      );
    });

    it('should log error information for failed requests', async () => {
      // Arrange
      const mockError = {
        response: {
          status: 400,
          data: { error: 'Bad Request' },
        },
        message: 'Request failed',
      };
      axios.mockRejectedValue(mockError);

      const apiParams = {
        url: '/test',
        method: 'GET',
      };

      // Act
      await AxiosRequestForAuth(apiParams);

      // Assert
      expect(PrintLog.error).toHaveBeenCalledWith(
        'AxiosRequestForAuth',
        { 'Catch Error: ': mockError }
      );
    });
  });
});
// Export for use in other tests
export {
  AxiosRequestForAuth,
  AxiosRequestForDownload,
  AxiosRequest,
  AxiosRequestForNode,
  AxiosRequestForImageDownload,
}; 
