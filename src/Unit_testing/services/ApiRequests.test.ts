import * as ApiRequests from '../../services/ApiRequests';
import { AxiosRequestForAuth, AxiosRequestForDownload, AxiosRequestForNode, AxiosRequestForImageDownload, AxiosRequest } from '../../services/AxiosService';
import Config from '../../config/Config';
import Strings from '../../utils/Strings/Strings';
import PrintLog from '../../utils/Logger/PrintLog';
import axios from 'axios';

// Mock dependencies
jest.mock('../../services/AxiosService', () => ({
  AxiosRequestForAuth: jest.fn(),
  AxiosRequestForDownload: jest.fn(),
  AxiosRequestForNode: jest.fn(),
  AxiosRequestForImageDownload: jest.fn(),
  AxiosRequest: jest.fn(),
}));

jest.mock('axios', () => jest.fn());

jest.mock('../../config/Config', () => ({
  OCP_APIM_SUBSCRIPTION_KEY: 'test-subscription-key',
  OCP_APIM_SUBSCRIPTION_KEY_COMMON: 'test-common-key',
  BASE_URL_FOR_AUTH: 'https://test-auth.com',
  BASE_URL_FOR_DOWNLOAD: 'https://test-download.com',
  ENV: 'test',
}));

jest.mock('../../utils/Logger/PrintLog', () => ({
  debug: jest.fn(),
  error: jest.fn(),
}));

const mockAxiosRequestForAuth = AxiosRequestForAuth as jest.MockedFunction<typeof AxiosRequestForAuth>;
const mockAxiosRequestForDownload = AxiosRequestForDownload as jest.MockedFunction<typeof AxiosRequestForDownload>;
const mockAxiosRequestForNode = AxiosRequestForNode as jest.MockedFunction<typeof AxiosRequestForNode>;
const mockAxiosRequestForImageDownload = AxiosRequestForImageDownload as jest.MockedFunction<typeof AxiosRequestForImageDownload>;
const mockAxiosRequest = AxiosRequest as jest.MockedFunction<typeof AxiosRequest>;
const mockAxios = axios as jest.MockedFunction<typeof axios>;

describe('ApiRequests Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getData', () => {
    it('should successfully get data and call onSuccess', async () => {
      // Arrange
      const mockResponse = { data: { success: true, items: [] } };
      mockAxiosRequestForDownload.mockResolvedValue(mockResponse);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      
      // Act
      await ApiRequests.getData(
        'test-function',
        '/test-endpoint',
        onSuccess,
        onError
      );
      
      // Assert
      expect(mockAxiosRequestForDownload).toHaveBeenCalledWith({
        url: '/test-endpoint',
        method: 'GET',
      });
      expect(onSuccess).toHaveBeenCalledWith({ success: true, items: [] });
      expect(onError).not.toHaveBeenCalled();
    });

    it('should handle error when no data in response', async () => {
      // Arrange
      const mockResponse = { status: 200 };
      mockAxiosRequestForDownload.mockResolvedValue(mockResponse);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      
      // Act
      await ApiRequests.getData(
        'test-function',
        '/test-endpoint',
        onSuccess,
        onError
      );
      
      // Assert
      expect(onError).toHaveBeenCalledWith(mockResponse);
      expect(onSuccess).not.toHaveBeenCalled();
    });

    it('should handle network error', async () => {
      // Arrange
      const mockError = {
        response: { status: 500, data: { error: 'Server Error' } },
        message: 'Network Error'
      };
      mockAxiosRequestForDownload.mockRejectedValue(mockError);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      
      // Act
      await ApiRequests.getData(
        'test-function',
        '/test-endpoint',
        onSuccess,
        onError
      );
      
      // Assert
      expect(onError).toHaveBeenCalledWith(mockError.response);
      expect(onSuccess).not.toHaveBeenCalled();
    });

    it('should handle error without response', async () => {
      // Arrange
      const mockError = { message: 'Network Error' };
      mockAxiosRequestForDownload.mockRejectedValue(mockError);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      
      // Act
      await ApiRequests.getData(
        'test-function',
        '/test-endpoint',
        onSuccess,
        onError
      );
      
      // Assert
      expect(onError).toHaveBeenCalledWith(mockError);
      expect(onSuccess).not.toHaveBeenCalled();
    });
  });

  describe('getDataWithParams', () => {
    it('should successfully get data with params and call onSuccess', async () => {
      // Arrange
      const mockResponse = { data: { success: true, items: [] } };
      mockAxiosRequestForDownload.mockResolvedValue(mockResponse);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const queryParams = { page: 1, limit: 10 };
      
      // Act
      await ApiRequests.getDataWithParams(
        'test-function',
        '/test-endpoint',
        queryParams,
        onSuccess,
        onError
      );
      
      // Assert
      expect(mockAxiosRequestForDownload).toHaveBeenCalledWith({
        url: '/test-endpoint',
        method: 'GET',
        params: queryParams,
      });
      expect(onSuccess).toHaveBeenCalledWith({ success: true, items: [] });
      expect(onError).not.toHaveBeenCalled();
    });

    it('should handle error with params', async () => {
      // Arrange
      const mockError = {
        response: { status: 400, data: { error: 'Bad Request' } },
        message: 'Request failed'
      };
      mockAxiosRequestForDownload.mockRejectedValue(mockError);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const queryParams = { page: -1, limit: 0 };
      
      // Act
      await ApiRequests.getDataWithParams(
        'test-function',
        '/test-endpoint',
        queryParams,
        onSuccess,
        onError
      );
      
      // Assert
      expect(onError).toHaveBeenCalledWith(mockError.response);
      expect(onSuccess).not.toHaveBeenCalled();
    });
  });

  describe('postDataWithBodyForAuth', () => {
    it('should successfully post data and call onSuccess', async () => {
      // Arrange
      const mockResponse = { data: { success: true, token: 'test-token' } };
      mockAxiosRequestForAuth.mockResolvedValue(mockResponse);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { username: 'test', password: 'test123' };
      
      // Act
      await ApiRequests.postDataWithBodyForAuth(
        '/login',
        body,
        onSuccess,
        onError
      );
      
      // Assert
      expect(mockAxiosRequestForAuth).toHaveBeenCalledWith({
        url: '/login',
        method: 'POST',
        data: body,
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          'Ocp-Apim-Subscription-Key': expect.any(String),
        }),
      });
      expect(onSuccess).toHaveBeenCalledWith({ success: true, token: 'test-token' });
      expect(onError).not.toHaveBeenCalled();
    });

    it('should handle auth error', async () => {
      // Arrange
      const mockError = {
        response: { status: 401, data: { error: 'Unauthorized' } },
        message: 'Authentication failed'
      };
      mockAxiosRequestForAuth.mockRejectedValue(mockError);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { username: 'invalid', password: 'invalid' };
      
      // Act
      await ApiRequests.postDataWithBodyForAuth(
        '/login',
        body,
        onSuccess,
        onError
      );
      
      // Assert
      expect(onError).toHaveBeenCalledWith(mockError.response);
      expect(onSuccess).not.toHaveBeenCalled();
    });

    it('should handle empty response data', async () => {
      // Arrange
      const mockResponse = { status: 200 };
      mockAxiosRequestForAuth.mockResolvedValue(mockResponse);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { username: 'test', password: 'test123' };
      
      // Act
      await ApiRequests.postDataWithBodyForAuth(
        '/login',
        body,
        onSuccess,
        onError
      );
      
      // Assert
      expect(onError).toHaveBeenCalledWith(mockResponse);
      expect(onSuccess).not.toHaveBeenCalled();
    });
  });

  describe('postDataWithBodyForDownload', () => {
    it('should successfully post data for download and call onSuccess', async () => {
      // Arrange
      const mockResponse = { data: { success: true, downloadUrl: 'test-url' } };
      mockAxiosRequestForDownload.mockResolvedValue(mockResponse);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { fileId: 'test-file-id' };
      
      // Act
      await ApiRequests.postDataWithBodyForDownload(
        '/download',
        body,
        onSuccess,
        onError
      );
      
      // Assert
      expect(mockAxiosRequestForDownload).toHaveBeenCalledWith({
        url: '/download',
        method: 'POST',
        data: body,
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          'Ocp-Apim-Subscription-Key': expect.any(String),
        }),
      });
      expect(onSuccess).toHaveBeenCalledWith({ success: true, downloadUrl: 'test-url' });
      expect(onError).not.toHaveBeenCalled();
    });

    it('should handle download error', async () => {
      // Arrange
      const mockError = {
        response: { status: 404, data: { error: 'File not found' } },
        message: 'Download failed'
      };
      mockAxiosRequestForDownload.mockRejectedValue(mockError);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { fileId: 'invalid-file-id' };
      
      // Act
      await ApiRequests.postDataWithBodyForDownload(
        '/download',
        body,
        onSuccess,
        onError
      );
      
      // Assert
      expect(onError).toHaveBeenCalledWith(mockError.response);
      expect(onSuccess).not.toHaveBeenCalled();
    });
  });

  describe('postDataWithBodyToken', () => {
    it('should successfully post data with token and call onSuccess', async () => {
      // Arrange
      const mockResponse = { data: { success: true, data: [] } };
      mockAxiosRequest.mockResolvedValue(mockResponse);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { action: 'update' };
      const baseUrl = 'https://test-api.com';
      
      // Act
      await ApiRequests.postDataWithBodyToken(
        baseUrl,
        '/update',
        body,
        onSuccess,
        onError
      );
      
      // Assert
      expect(mockAxiosRequest).toHaveBeenCalledWith(baseUrl, {
        url: '/update',
        method: 'POST',
        data: body,
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          'Ocp-Apim-Subscription-Key': expect.any(String),
        }),
      });
      expect(onSuccess).toHaveBeenCalledWith({ success: true, data: [] });
      expect(onError).not.toHaveBeenCalled();
    });

    it('should handle token-based request error', async () => {
      // Arrange
      const mockError = {
        response: { status: 403, data: { error: 'Forbidden' } },
        message: 'Token invalid'
      };
      mockAxiosRequest.mockRejectedValue(mockError);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { action: 'delete' };
      const baseUrl = 'https://test-api.com';
      
      // Act
      await ApiRequests.postDataWithBodyToken(
        baseUrl,
        '/delete',
        body,
        onSuccess,
        onError
      );
      
      // Assert
      expect(onError).toHaveBeenCalledWith(mockError.response);
      expect(onSuccess).not.toHaveBeenCalled();
    });
  });

  describe('putDataWithBody', () => {
    it('should successfully put data and call onSuccess', async () => {
      // Arrange
      const mockResponse = { data: { success: true, updated: true } };
      mockAxiosRequestForDownload.mockResolvedValue(mockResponse);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { id: 1, name: 'Updated Name' };
      
      // Act
      await ApiRequests.putDataWithBody(
        '/update/1',
        body,
        onSuccess,
        onError
      );
      
      // Assert
      expect(mockAxiosRequestForDownload).toHaveBeenCalledWith({
        url: '/update/1',
        method: 'PUT',
        data: body,
      });
      expect(onSuccess).toHaveBeenCalledWith({ success: true, updated: true });
      expect(onError).not.toHaveBeenCalled();
    });

    it('should handle PUT request error', async () => {
      // Arrange
      const mockError = {
        response: { status: 422, data: { error: 'Validation failed' } },
        message: 'PUT request failed'
      };
      mockAxiosRequestForDownload.mockRejectedValue(mockError);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { id: 1, name: '' }; // Invalid data
      
      // Act
      await ApiRequests.putDataWithBody(
        '/update/1',
        body,
        onSuccess,
        onError
      );
      
      // Assert
      expect(onError).toHaveBeenCalledWith(mockError.response);
      expect(onSuccess).not.toHaveBeenCalled();
    });
  });

  describe('postDataAsParamForNode', () => {
    it('should successfully post data as params for node and call onSuccess', async () => {
      // Arrange
      const mockResponse = { data: { success: true, nodeData: [] } };
      mockAxios.mockResolvedValue(mockResponse);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const params = { nodeId: 'test-node', action: 'activate' };
      
      // Act
      await ApiRequests.postDataAsParamForNode(
        '/node/action',
        params,
        onSuccess,
        onError
      );
      
      // Assert
      expect(mockAxios).toHaveBeenCalledWith({
        url: expect.stringContaining('geospatial.lntecc.com'),
        method: 'POST',
        data: {},
      });
      expect(onSuccess).toHaveBeenCalledWith({ success: true, nodeData: [] });
      expect(onError).not.toHaveBeenCalled();
    });

    it('should handle node request error', async () => {
      // Arrange
      const mockError = {
        response: { status: 500, data: { error: 'Node service unavailable' } },
        message: 'Node request failed'
      };
      mockAxios.mockRejectedValue(mockError);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const params = { nodeId: 'invalid-node', action: 'activate' };
      
      // Act
      await ApiRequests.postDataAsParamForNode(
        '/node/action',
        params,
        onSuccess,
        onError
      );
      
      // Assert
      expect(onError).toHaveBeenCalledWith(mockError.response);
      expect(onSuccess).not.toHaveBeenCalled();
    });
  });

  describe('postDataWithHeaderForNode', () => {
    it('should successfully post data with headers for node and call onSuccess', async () => {
      // Arrange
      const mockResponse = { data: { success: true, result: 'processed' } };
      mockAxiosRequestForNode.mockResolvedValue(mockResponse);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { data: 'test-data' };
      const token = 'test-token';
      
      // Act
      await ApiRequests.postDataWithHeaderForNode(
        '/node/process',
        body,
        onSuccess,
        onError,
        token
      );
      
      // Assert
      expect(mockAxiosRequestForNode).toHaveBeenCalledWith({
        url: '/node/process',
        method: 'POST',
        data: body,
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          'Authorization': token,
        }),
      });
      expect(onSuccess).toHaveBeenCalledWith({ success: true, result: 'processed' });
      expect(onError).not.toHaveBeenCalled();
    });

    it('should handle node request with header error', async () => {
      // Arrange
      const mockError = {
        response: { status: 401, data: { error: 'Invalid token' } },
        message: 'Authentication failed'
      };
      mockAxiosRequestForNode.mockRejectedValue(mockError);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { data: 'test-data' };
      const token = 'invalid-token';
      
      // Act
      await ApiRequests.postDataWithHeaderForNode(
        '/node/process',
        body,
        onSuccess,
        onError,
        token
      );
      
      // Assert
      expect(onError).toHaveBeenCalledWith(mockError.response);
      expect(onSuccess).not.toHaveBeenCalled();
    });

    it('should work without token', async () => {
      // Arrange
      const mockResponse = { data: { success: true, result: 'processed' } };
      mockAxiosRequestForNode.mockResolvedValue(mockResponse);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { data: 'test-data' };
      
      // Act
      await ApiRequests.postDataWithHeaderForNode(
        '/node/process',
        body,
        onSuccess,
        onError
      );
      
      // Assert
      expect(mockAxiosRequestForNode).toHaveBeenCalledWith({
        url: '/node/process',
        method: 'POST',
        data: body,
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
        }),
      });
      expect(onSuccess).toHaveBeenCalledWith({ success: true, result: 'processed' });
      expect(onError).not.toHaveBeenCalled();
    });
  });

  describe('downloadImage', () => {
    it('should successfully download image and call onSuccess', async () => {
      // Arrange
      const mockResponse = { data: { success: true, imageData: 'base64-data' } };
      mockAxiosRequestForImageDownload.mockResolvedValue(mockResponse);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { imageId: 'test-image-id' };
      
      // Act
      await ApiRequests.downloadImage(
        body,
        onSuccess,
        onError
      );
      
      // Assert
      expect(mockAxiosRequestForImageDownload).toHaveBeenCalledWith({
        url: 'DownloadImaage',
        method: 'POST',
        data: body,
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          'Ocp-Apim-Subscription-Key': expect.any(String),
        }),
      });
      expect(onSuccess).toHaveBeenCalledWith({ success: true, imageData: 'base64-data' });
      expect(onError).not.toHaveBeenCalled();
    });

    it('should handle image download error', async () => {
      // Arrange
      const mockError = {
        response: { status: 404, data: { error: 'Image not found' } },
        message: 'Image download failed'
      };
      mockAxiosRequestForImageDownload.mockRejectedValue(mockError);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { imageId: 'invalid-image-id' };
      
      // Act
      await ApiRequests.downloadImage(
        body,
        onSuccess,
        onError
      );
      
      // Assert
      expect(onError).toHaveBeenCalledWith(mockError.response);
      expect(onSuccess).not.toHaveBeenCalled();
    });
  });

  describe('Header generation', () => {
    it('should generate headers correctly for different URL patterns', () => {
      // This is testing the internal getHeaders function indirectly
      const onSuccess = jest.fn();
      const onError = jest.fn();
      const body = { test: 'data' };
      
      ApiRequests.postDataWithBodyForAuth('/test', body, onSuccess, onError);
      
      // Check that the headers were generated correctly
      expect(mockAxiosRequestForAuth).toHaveBeenCalledWith({
        url: '/test',
        method: 'POST',
        data: body,
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          'Ocp-Apim-Subscription-Key': expect.any(String),
        }),
      });
    });
  });

  describe('Edge cases and error handling', () => {
    it('should handle null/undefined responses gracefully', async () => {
      // Arrange
      mockAxiosRequestForDownload.mockResolvedValue(null);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      
      // Act
      await ApiRequests.getData(
        'test-function',
        '/test-endpoint',
        onSuccess,
        onError
      );
      
      // Assert
      expect(onError).toHaveBeenCalledWith(null);
      expect(onSuccess).not.toHaveBeenCalled();
    });

    it('should handle empty string responses', async () => {
      // Arrange
      const mockResponse = { data: '' };
      mockAxiosRequestForDownload.mockResolvedValue(mockResponse);
      
      const onSuccess = jest.fn();
      const onError = jest.fn();
      
      // Act
      await ApiRequests.getData(
        'test-function',
        '/test-endpoint',
        onSuccess,
        onError
      );
      
      // Assert
      // Empty string is falsy, so it goes to error case
      expect(onError).toHaveBeenCalledWith(mockResponse);
      expect(onSuccess).not.toHaveBeenCalled();
    });

    it('should handle callback functions being undefined', async () => {
      // Arrange
      const mockResponse = { data: { success: true } };
      mockAxiosRequestForAuth.mockResolvedValue(mockResponse);
      
      // Act & Assert - should not throw error
      await expect(ApiRequests.postDataWithBodyForAuth(
        '/test',
        { test: 'data' },
        undefined as any,
        undefined as any
      )).resolves.not.toThrow();
    });
  });
});

// Export for use in other tests
export default ApiRequests; 