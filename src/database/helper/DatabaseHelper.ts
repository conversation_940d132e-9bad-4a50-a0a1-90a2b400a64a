import { WBSItem } from '../../model/DailyProgress/DailyProgressData';
import { database } from '../../database/index';
import { Q } from '@nozbe/watermelondb';
import WBSTask from '../model/WBSTask';
import WBSJob from '../model/WBSJob';

export const loadLatLongHierarchyData = async (jobCode: string, fromWhere?: string, bookmarksList?: WBSItem[]) => {
    try {

        const conditions = [Q.where('PRCLLH_Job_Code', jobCode)];

        if (fromWhere === 'Bookmarks') {
            const wbsCodes = bookmarksList?.map(item => item.parent_WBS_Code);
            // Add OR conditions for each wbsCode in list
            const wbsConditions = wbsCodes?.map(code =>
                Q.where('PRCLLH_WBS_Code', code)
            );
            conditions.push(...(wbsConditions ?? []));
        }

        const latLongData = await database
            .get('LatLongHierarchy')
            .query(...conditions)
            .fetch();

        // console.log('loadLatLongHierarchyData -- latLongData: ', latLongData), ' -- latLongData: ', latLongData.length;
        return latLongData.map(record => {
            const raw = record._raw as any;
            return {
                id: record.id,
                jobCode: raw.PRCLLH_Job_Code,
                wbsCode: raw.PRCLLH_WBS_Code,
                latitude: raw.PRCLLH_Latitude,
                longitude: raw.PRCLLH_Longitude,
                hierarchyLevel: raw.PRCLLH_Hierarchy_Level,
                isActive: raw.PRCLLH_IsActive,
                jobDesc: raw.JOB_DESC,
                il: raw.IL,
                ilDesc: raw.IL_DESC,
                wp: raw.WP,
                wpDesc: raw.WP_DESC,
                swp: raw.SWP,
                cwpDesc: raw.CWP_DESC,
                deliverableCode: raw.DELIVERABLE_CODE,
                deliverableCodeDesc: raw.DELIVERABLE_CODE_DESC
            };
        });

    } catch (error) {
        console.error('Error loading LatLongHierarchy data:', error);
        return [];
    }
};

export const getAllWBSItemsWithPathAllLevels = async (
    jobId: string,
    database: any,
    EntityConstants: any
): Promise<WBSItem[]> => {
    let results: WBSItem[] = [];

    // Recursive helper
    const traverse = async (item: WBSItem, path: WBSItem[]) => {
        const newPath = [...path, item];

        // If it's a leaf node or a task, try to fetch tasks
        if (
            (item.entity_Type === EntityConstants.WBSJob && item.leaf_Node_Tag === EntityConstants.Y) ||
            item.entity_Type === EntityConstants.WBSTask
        ) {
            // Try to fetch tasks under this node
            const tasks = await database
                .get('WBSTask')
                .query(
                    Q.where('Is_Active', 'Y'),
                    Q.where('Job_Code', item.job_Code),
                    Q.where('Parent_Task_Code', item.entity_Code),
                    Q.where('Parent_WBS', item.entity_Code)
                )
                .fetch(); //////WORKING

                if (tasks.length > 0) {
                    for (const task of tasks) {
                    // console.log('Inside TASK -- tasks Parent_WBS: ',task.parentTask);
                    const subTasks = await database
                    .get('WBSTask')
                    .query(
                        Q.where('Is_Active', 'Y'),
                        //   Q.where('Parent_Task', task.parentTask), // or task.IT if that is the correct code
                        //   Q.where('ET_Code', 'IT')
                        Q.where('Parent_Task', '3115')
                    )
                    .fetch();
                }
                }
      //   You can store with reference or merge as needed
//   allSubTasks.push({
//     parent: task,
//     children: subTasks
//   });


            // console.log('traverse -- tasks.length: ',tasks.length, '');
            if (tasks.length > 0) {
                // console.log('WBSTask length greayer than 0 -- lemngtg: ', tasks.length);
                // For each task, continue traversal
                for (const t of tasks) {
                    const wbsTask = t as WBSTask;
                    const taskItem: WBSItem = {
                        id: wbsTask.id,
                        entity_Code: wbsTask.taskCode,
                        job_Code: wbsTask.jobCode,
                        entity_Type: EntityConstants.WBSTask,
                        leaf_Node_Tag: 'Y',
                        entity_Description: wbsTask.taskDescription,
                        parent_WBS_Code: wbsTask.parentWbs,
                        et_Code: wbsTask.gisTag.trim(),
                        isChild: true,
                        // Add fullPath property
                        // fullPath: undefined, // will be set below
                        fullPath: wbsTask.etCode.trim(),
                    };
                    await traverse(taskItem, newPath);
                }
            } else {
                // console.log('WBSTask length else: ',);
                // No more children, this is a leaf
                // Build the full path string
                const fullPath = newPath.map(i => i.entity_Description).join(' / ');
                // Add the fullPath property to the last item
                const lastItem = { ...item, fullPath };
                results.push(lastItem);
            }
        } else if (item.entity_Type === EntityConstants.WBSJob && item.leaf_Node_Tag !== EntityConstants.Y) {
            // Fetch child WBSJobs
            const children = await database
                .get('WBSJob')
                .query(
                    Q.where('Job_Code', item.job_Code),
                    Q.where('Parent_WBS_Code', item.entity_Code),
                    Q.where('ET_Code', Q.notEq('PR')),
                    Q.where('Is_Active', 'Y')
                )
                .fetch();

            if (children.length > 0) {
                // For each child, continue traversal
                for (const c of children) {
                    const raw = c._raw as unknown as WBSJob;
                    const nextItem: WBSItem = {
                        id: c.id,
                        entity_Code: raw.WBS_Code,
                        job_Code: raw.Job_Code,
                        entity_Type: raw.entity_Type,
                        leaf_Node_Tag: raw.Leaf_Node_Tag,
                        entity_Description: raw.WBS_Description,
                        parent_WBS_Code: raw.Parent_WBS_Code,
                        et_Code: raw.ET_Code,
                        isChild: true,
                        fullPath: undefined, // will be set below
                    };
                    await traverse(nextItem, newPath);
                }
            } else {
                // No more children, this is a leaf
                const fullPath = newPath.map(i => i.entity_Description).join(' / ');
                const lastItem = { ...item, fullPath };
                results.push(lastItem);
            }
        }
        // else: GISNode or unknown, stop
    };

    // Start: fetch the first level (root) WBSJob(s)
    const rootItems = await database
        .get('WBSJob')
        .query(
            Q.where('Job_Code', jobId),
            Q.where('Parent_WBS_Code', jobId),
            Q.where('ET_Code', Q.notEq('PR')),
            Q.where('Is_Active', 'Y')
        )
        .fetch();

    if (rootItems.length === 0) return [];

    // For each root item, start traversal
    for (const root of rootItems) {
        const rootRaw = root._raw as unknown as WBSJob;
        const rootItem: WBSItem = {
            id: root.id,
            entity_Code: rootRaw.WBS_Code,
            job_Code: rootRaw.Job_Code,
            entity_Type: rootRaw.entity_Type,
            leaf_Node_Tag: rootRaw.Leaf_Node_Tag,
            entity_Description: rootRaw.WBS_Description,
            parent_WBS_Code: rootRaw.Parent_WBS_Code,
            et_Code: rootRaw.ET_Code,
            fullPath: undefined, // will be set in traverse
        };
        await traverse(rootItem, []);
    }

    return results;
};


export const buildWBSCodePath = async (item: WBSItem): Promise<string> => {
    try {
        // Get all parent WBS items recursively
        const getAllParentItems = async (jobCode: string, parentCode: string | null): Promise<any[]> => {
            if (!parentCode) return [];

            const parent = await database
                .get('WBSJob')
                .query(
                    Q.where('Job_Code', jobCode),
                    Q.where('WBS_Code', parentCode)
                )
                .fetch();

            if (parent.length === 0) return [];

            const raw = parent[0]._raw as unknown as WBSJob;
            const grandParents = await getAllParentItems(jobCode, raw.Parent_WBS_Code);
            return [...grandParents, raw];
        };

        // Get all parent items
        const parentItems = await getAllParentItems(item.job_Code, item.parent_WBS_Code);

        // Construct the full WBS path including all parent codes
        const wbsPath = [];

        // Add all parent codes to the path
        for (const parent of parentItems) {
            wbsPath.push(parent.WBS_Code);
        }

        // Add the current node's code
        wbsPath.push(item.entity_Code);

        return wbsPath.join('~');
    } catch (error) {
        console.error('Error building WBS code path:', error);
        return '';
    }
};