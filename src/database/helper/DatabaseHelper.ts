import { EntityConstants, PendingForApprovalBQITRecord, WBSItem, WBSJobRecord } from "../../model/DailyProgress/DailyProgressData";
import { database } from "../../database/index";
import { Q } from "@nozbe/watermelondb";
import { Alert } from "react-native";
import { t } from "i18next";

const BookmarkData = [
    {
        "PRCGB_Job_Code": "LE23M849",
        "PRCGB_WBS_Code": "IL7~WP100072~SWP102~1061",
        "PRCGB_User_ID": 100078676,
        "PRCGB_Hierarchy_Level": "WP",
        "PRCGB_IsActive": "Y",
        "JOB_DESC": "Chittorgarh Package-I",
        "IL": "IL7",
        "IL_DESC": "Other Civil Works",
        "WP": "WP100072",
        "WP_DESC": "Ancilliary Buildings",
        "SWP": "SWP102",
        "CWP_DESC": "J2 Staff Quarters - Intake Bhainsrodgarh",
        "DELIVERABLE_CODE": 1061,
        "DELIVERABLE_CODE_DESC": "RCC - Upto plinth level"
    },
    {
        "PRCGB_Job_Code": "LE23M849",
        "PRCGB_WBS_Code": "IL7~WP100072~SWP102~1061",
        "PRCGB_User_ID": 100078676,
        "PRCGB_Hierarchy_Level": "WP",
        "PRCGB_IsActive": "Y",
        "JOB_DESC": "Chittorgarh Package-I",
        "IL": "IL7",
        "IL_DESC": "Other Civil Works",
        "WP": "WP100072",
        "WP_DESC": "Ancilliary Buildings",
        "SWP": "SWP102",
        "CWP_DESC": "J2 Staff Quarters - Intake Bhainsrodgarh",
        "DELIVERABLE_CODE": 1061,
        "DELIVERABLE_CODE_DESC": "RCC - Upto plinth level"
    },
];

export const loadWBSItems = async (jobCode: string, type: string) => {


    try {
        const allItems = await database
            .get('WBSJob')
            .query(
                Q.where('Job_Code', jobCode),
                Q.where('Is_Active', 'Y')
            )
            .fetch();

        let rootItems: any[] = [];

        if (type === t('PendingForApprovalStrings.pendingForApproval')) {
            const allPendingItems = await database
                .get('PendingForApprovalBQIT')
                .query(
                    Q.where('JOB', jobCode),
                    // Q.where('Is_Active', 'Y') //this filter is required but api data missing this param
                )
                .fetch();

            // first to items is taken because data is huge coming from api but in relatime it will be very less for pending
            rootItems = allPendingItems.slice(0, 2);
        } else if (type === t('BookmarkStrings.bookmarks')) {
            const allBookmarkItems = await database
                .get('BookMarkList')
                .query(
                    Q.where('PRCGB_Job_Code', jobCode),
                    // Q.where('Is_Active', 'Y') //this filter is required but api data missing this param
                )
                .fetch();

            rootItems = allBookmarkItems;
        } else {
            rootItems = allItems.filter(item => {
                const raw = item._raw as unknown as WBSJobRecord;
                return !raw.Parent_WBS_Code || raw.Parent_WBS_Code === '';
            });
        }


        // if (rootItems.length === 0) return;

        const finalItems: WBSItem[] = [];


        if (type === t('PendingForApprovalStrings.pendingForApproval')) {
            // Process pending items
            for (const root of rootItems) {
                const raw = root._raw as PendingForApprovalBQITRecord;
                const pendingItem: WBSItem = {
                    id: root.id,
                    entity_Code: raw?.JOB,
                    job_Code: raw?.JOB,
                    entity_Type: EntityConstants.WBSJob,
                    leaf_Node_Tag: raw?.Leaf_Node_Tag || "N",
                    entity_Description: raw?.Job_Description || 'Chittorgarh Package-I',
                    parent_WBS_Code: raw?.JOB,
                    et_Code: raw?.TaskType,
                    fullPath: raw?.Job_Description || 'Chittorgarh Package-I',
                    parent_Task: undefined,
                    gis_Tag: raw?.GIS_Tag,
                    parent_entity_code: raw?.Parent_Entity_Code
                };
                await walkWBSBranch(pendingItem, allItems, [], finalItems);
            }
        } else if (type === t('BookmarkStrings.bookmarks')) {
            // Process bookmark items
            for (const root of BookmarkData) {
                const raw = root;
                // const raw = root._raw as any;
                const bookmarkItem: WBSItem = {
                    id: root.id,
                    entity_Code: raw.PRCGB_Job_Code,
                    job_Code: raw.PRCGB_Job_Code,
                    entity_Type: EntityConstants.WBSJob,
                    leaf_Node_Tag: raw.Leaf_Node_Tag || 'N',
                    entity_Description: raw.WBS_Description || 'Chittorgarh Package-I',
                    parent_WBS_Code: raw.Parent_WBS_Code,
                    et_Code: raw.ET_Code || 'BQ',
                    fullPath: raw.WBS_Description || 'Chittorgarh Package-I',
                    parent_Task: undefined,
                    gis_Tag: raw.GIS_Tag,
                    parent_entity_code: raw.Parent_Entity_Code
                };
                await walkWBSBranch(bookmarkItem, allItems, [], finalItems);
            }
        } else {
            // Process recent items (original logic)
            for (const root of rootItems) {
                const raw = root._raw as unknown as WBSJobRecord;

                const rootItem: WBSItem = {
                    id: root.id,
                    entity_Code: raw.WBS_Code,
                    job_Code: raw.Job_Code,
                    entity_Type: EntityConstants.WBSJob,
                    leaf_Node_Tag: raw.Leaf_Node_Tag,
                    entity_Description: raw.WBS_Description,
                    parent_WBS_Code: raw.Parent_WBS_Code,
                    et_Code: raw.ET_Code,
                    fullPath: raw.WBS_Description,
                    parent_Task: undefined,
                    gis_Tag: raw.GIS_Tag,
                    parent_entity_code: raw.Parent_Entity_Code
                };

                await walkWBSBranch(rootItem, allItems, [], finalItems);
            }
        }

        return finalItems
    } catch (error) {
        Alert.alert('DailyProgress -- Error loading WBS items');
    }
};

const walkWBSBranch = async (
    current: WBSItem,
    allItems: any[],
    path: WBSItem[],
    finalItems: WBSItem[]
) => {
    const newPath = [...path, current];

    if (current.leaf_Node_Tag === EntityConstants.Y) {
        // Try WBSTask
        const tasks = await database
            .get('WBSTask')
            .query(
                Q.where('Job_Code', current.job_Code),
                Q.where('Task_Code', current.entity_Code),
                Q.where('Parent_WBS', current.parent_WBS_Code || ''),
                Q.where('Parent_Task', current.parent_Task || ''),
                Q.where('ET_Code', 'IT'),
                Q.where('Is_Active', 'Y')
            ).fetch();

        if (tasks.length > 0) {
        } else if (current.gis_Tag === 'Y' && current.entity_Code.includes('~')) {
            const gisNodes = await database
                .get('GISNode')
                .query(
                    Q.where('Job_Code', current.job_Code),
                    Q.where('Parent_Entity_Code', current.parent_entity_code || ''),
                    Q.where('Parent_Task', current.parent_Task || '')
                ).fetch();

            if (gisNodes.length > 0) {
            }
        } else {
            await GetInputDetail(newPath);
        }

        // Final path
        const fullPath = newPath.map(i => i.entity_Description).join(' / ');
        finalItems.push({
            ...current,
            fullPath
        });

        return;
    }

    // Continue traversing children
    const children = allItems.filter(item => {
        const raw = item._raw as WBSJobRecord;
        return raw.Parent_WBS_Code === current.entity_Code && raw.ET_Code !== 'PR';
    });

    if (children.length === 0) {
        // Treat as final path if no children
        const fullPath = newPath.map(i => i.entity_Description).join(' / ');
        finalItems.push({
            ...current,
            fullPath
        });
        return;
    }

    for (const child of children) {
        const raw = child._raw as WBSJobRecord;

        const childItem: WBSItem = {
            id: child.id,
            entity_Code: raw.WBS_Code,
            job_Code: raw.Job_Code,
            entity_Type: EntityConstants.WBSJob,
            leaf_Node_Tag: raw.Leaf_Node_Tag,
            entity_Description: raw.WBS_Description,
            parent_WBS_Code: raw.Parent_WBS_Code,
            et_Code: raw.ET_Code,
            fullPath: '',
            parent_Task: undefined,
            gis_Tag: raw.GIS_Tag,
            parent_entity_code: raw.Parent_Entity_Code
        };

        await walkWBSBranch(childItem, allItems, newPath, finalItems);
    }
};

export const GetInputDetail = async (entityWBS: WBSItem[]) => {
    try {
        // Get the last node from the WBS hierarchy
        const lastNode = entityWBS[entityWBS.length - 1];

        // Get all parent WBS items recursively
        const getAllParentItems = async (jobCode: string, parentCode: string | null): Promise<WBSJobRecord[]> => {
            if (!parentCode) return [];

            const parent = await database
                .get('WBSJob')
                .query(
                    Q.where('Job_Code', jobCode),
                    Q.where('WBS_Code', parentCode)
                )
                .fetch();

            if (parent.length === 0) return [];

            const raw = parent[0]._raw as unknown as WBSJobRecord;
            const grandParents = await getAllParentItems(jobCode, raw.Parent_WBS_Code);
            return [...grandParents, raw];
        };

        // Get all parent items
        const parentItems = await getAllParentItems(entityWBS[0].job_Code, lastNode.parent_WBS_Code);

        // Construct the full WBS path including all parent codes
        const wbsPath = [];

        // Add all parent codes to the path
        for (const parent of parentItems) {
            wbsPath.push(parent.WBS_Code);
        }

        // Add the current node's code
        wbsPath.push(lastNode.entity_Code);

        // Join all codes with '~'
        const wbs = wbsPath.join('~');

        // First, let's check what records exist in WBSDetails for this job
        const allWBSDetails = await database
            .get('WBSDetails')
            .query(
                Q.where('JobCode', entityWBS[0].job_Code)
            )
            .fetch();

        // Get input fields from database with more flexible query
        const inputList = await database
            .get('WBSDetails')
            .query(
                Q.where('JobCode', entityWBS[0].job_Code),
                Q.or(
                    Q.where('WBS', wbs),
                    Q.where('WBS', Q.like(`%${lastNode.entity_Code}%`)),
                    Q.where('WBS', Q.like(`%${lastNode.parent_WBS_Code}%`))
                )
            )
            .fetch();

        if (inputList && inputList.length > 0) {
            return {
                selectedWBS: entityWBS,
                lastNodeCode: lastNode.entity_Code,
                inputFields: inputList.map(field => field._raw)
            };
        } else {
            return null;
        }
    } catch (error) {
        Alert.alert('Error', 'An error occurred while getting input details. -- GetInputDetail');
    }
};

export const loadLatLongHierarchyData = async (jobCode: string) => {
    try {
        const latLongData = await database
            .get('LatLongHierarchy')
            .query(
                Q.where('PRCLLH_Job_Code', jobCode)
            )
            .fetch();


        return latLongData.map(record => {
            const raw = record._raw as any;
            return {
                id: record.id,
                jobCode: raw.PRCLLH_Job_Code,
                wbsCode: raw.PRCLLH_WBS_Code,
                latitude: raw.PRCLLH_Latitude,
                longitude: raw.PRCLLH_Longitude,
                hierarchyLevel: raw.PRCLLH_Hierarchy_Level,
                isActive: raw.PRCLLH_IsActive,
                jobDesc: raw.JOB_DESC,
                il: raw.IL,
                ilDesc: raw.IL_DESC,
                wp: raw.WP,
                wpDesc: raw.WP_DESC,
                swp: raw.SWP,
                cwpDesc: raw.CWP_DESC,
                deliverableCode: raw.DELIVERABLE_CODE,
                deliverableCodeDesc: raw.DELIVERABLE_CODE_DESC
            };
        });
    } catch (error) {
        console.error('Error loading LatLongHierarchy data:', error);
        return [];
    }
};