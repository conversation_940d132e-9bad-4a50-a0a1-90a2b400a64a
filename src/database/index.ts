import { Database } from '@nozbe/watermelondb';
import SQLiteAdapter from '@nozbe/watermelondb/adapters/sqlite';
import schema from './schema';
import migrations from './migrations'
import WBSJob from './model/WBSJob';
import WBSTask from './model/WBSTask';
import WBSDetails from './model/WBSDetails';
import WBSGISDetails from './model/WBSGISDetails';
import BookmarkList from './model/BookmarkList';
import LatLongHierarchy from './model/LatLongHierarchy';
import PendingForApprovalBQIT from './model/PendingForApprovalBQIT';
import ProgressUpdateEngineer from './model/ProgressUpdateEngineer';
import ViewLastUpdateBQIT from './model/ViewLastUpdateBQIT';
import ViewLastUpdateGIS from './model/ViewLastUpdateGIS';
import ProgressDetailsConsolidated from './model/ProgressDetailsConsolidated';
import ProgressUpdateDetails from './model/ProgressUpdate/ProgressUpdate';
import ProgressParentItemDetails from './model/ProgressUpdate/ParentItem';

const adapter = new SQLiteAdapter({
    dbName: 'ePragati_DB',
    schema,
    migrations,
    jsi: true,
    onSetUpError: (error: any) => {
        console.error('WatermelonDB Setup Error:', error);
    },
});

export const database = new Database({
    adapter,
    modelClasses: [
        WBSJob,
        WBSTask,
        WBSDetails,
        WBSGISDetails,
        BookmarkList,
        LatLongHierarchy,
        PendingForApprovalBQIT,
        ProgressUpdateEngineer,
        ViewLastUpdateBQIT,
        ViewLastUpdateGIS,
        ProgressDetailsConsolidated,
        ProgressUpdateEngineer,
        ProgressUpdateDetails,
        ProgressParentItemDetails
    ],
});