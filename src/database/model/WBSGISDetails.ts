import { Model } from '@nozbe/watermelondb';
import { field } from '@nozbe/watermelondb/decorators';

export default class WBSGISDetails extends Model {
    static table = 'WBSGISDetails';

    @field('JobCode') jobCode!: string;
    @field('WBS') wbs!: string;
    @field('Task') task!: string;
    @field('Deliverable') deliverable!: string | null;
    @field('Remarks') remarks!: string | null;
    @field('Boq') boq!: string | null;
    @field('ActualDate') actualDate!: string;
    @field('NodeId') nodeId!: string;
    @field('NodeRefCode') nodeRefCode!: number;
    @field('FromLength') fromLength!: number;
    @field('ToLength') toLength!: number;
    @field('TotalLength') totalLength!: number;
    @field('Manpower') manpower!: number;
    @field('DistanceFCenter') distanceFCenter!: number;
    @field('Alignment') alignment!: string;
    @field('TaskType') taskType!: string;
    @field('Scope') scope!: number;
    @field('MonthwisePlanQty') monthwisePlanQty!: number;
    @field('Et_Code') etCode!: number;
    @field('DeliverableType_Et_Code') deliverableTypeEtCode!: string;
    @field('Is_Hindrance') isHindrance!: string;
    @field('DET_Assigned_Scope_Quantity') detAssignedScopeQuantity!: number;
    @field('DET_Year_Month') detYearMonth!: number | null;
    @field('TDD_Is_Engineer_Target_Mandatory') tddIsEngineerTargetMandatory!: string;
    @field('PlanedSDate') planedSDate!: string;
    @field('PlanedEDate') planedEDate!: string;
    @field('ActualSDate') actualSDate!: string;
    @field('ActualEDate') actualEDate!: string;

    get fullDesc(): string {
        return `${this.jobCode} - ${this.wbs}`;
    }
} 