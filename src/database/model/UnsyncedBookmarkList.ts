import { Model } from '@nozbe/watermelondb';
import { field } from '@nozbe/watermelondb/decorators';

export default class UnsyncedBookmarkList extends Model {
    static table = 'UnsyncedBookmarkList';

    @field('PRCGB_Job_Code') jobCode!: string;
    @field('PRCGB_WBS_Code') wbsCode!: string;
    @field('PRCGB_User_ID') userId!: number;
    @field('PRCGB_Hierarchy_Level') hierarchyLevel!: string;
    @field('PRCGB_IsActive') isActive!: string;
    @field('created_at') createdAt!: number;
    @field('updated_at') updatedAt!: number;
    @field('JOB_DESC') jobDescription!: string;
    @field('IL') ilCode!: string;
    @field('IL_DESC') ilDescription!: string;
    @field('WP') wpCode!: string;
    @field('WP_DESC') wpDescription!: string;
    @field('SWP') childWorkCode!: string;
    @field('CWP_DESC') childWorkDescription!: string;
    @field('DELIVERABLE_CODE') deliverableCode!: number;
    @field('DELIVERABLE_CODE_DESC') deliverableCodeDesc!: string;
    @field('ET_CODE') etCode!: string;
    @field('TYPE') type!: string;
    @field('WP_CODE_FOR_SYNC') wpCodeForSync!: string;
    // created_at is already defined above
} 