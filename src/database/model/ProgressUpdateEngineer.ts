import { Model } from '@nozbe/watermelondb';
import { field } from '@nozbe/watermelondb/decorators';

export default class ProgressUpdateEngineer extends Model {
    static table = 'ProgressUpdateEngineer';

    @field('TDD_Job_Code') tddJobCode!: string;
    @field('TDD_WBS_Code') tddWbsCode!: string;
    @field('TDD_Deliverable_Code') tddDeliverableCode!: number;
    @field('Delivscope') delivscope!: number;
    @field('CumProg') cumProg!: number;
    @field('CumManday') cumManday!: number;
    @field('FTMProgress') ftmProgress!: number;
    @field('FTMManday') ftmManday!: number;
    @field('CumTargetPlanQty') cumTargetPlanQty!: number;
    @field('FTMCumTargetPlanQty') ftmCumTargetPlanQty!: number;
    @field('TargetScope') targetScope!: number;

    get fullDesc(): string {
        return `${this.tddJobCode} - ${this.tddWbsCode}`;
    }
} 