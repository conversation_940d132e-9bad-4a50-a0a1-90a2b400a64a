import { Model } from '@nozbe/watermelondb';
import { field } from '@nozbe/watermelondb/decorators';

export default class LatLongHierarchy extends Model {
    static table = 'LatLongHierarchy';

    @field('PRCLLH_Job_Code') jobCode!: string;
    @field('PRCLLH_WBS_Code') wbsCode!: string;
    @field('PRCLLH_Latitude') latitude!: number;
    @field('PRCLLH_Longitude') longitude!: number;
    @field('PRCLLH_Hierarchy_Level') hierarchyLevel!: string;
    @field('PRCLLH_IsActive') isActive!: string;
    @field('JOB_DESC') jobDesc!: string;
    @field('IL') il!: string;
    @field('IL_DESC') ilDesc!: string;
    @field('WP') wp!: string;
    @field('WP_DESC') wpDesc!: string;
    @field('SWP') swp!: string;
    @field('CWP_DESC') cwpDesc!: string;
    @field('DELIVERABLE_CODE') deliverableCode!: number;
    @field('DELIVERABLE_CODE_DESC') deliverableCodeDesc!: string;
} 