// db/models/PragatiPipe.ts
import { Model } from '@nozbe/watermelondb';
import { field } from '@nozbe/watermelondb/decorators';

export default class EpragatiGisPipe extends Model {
  static table = 'EpragatiGisPipes';

  // @field('zone') ZONE!: string;
  // @field('dma') DMA!: number;
  // @field('sl_no') Sl_No!: number;
  // @field('pragati_wbs_dia') PRAGATI_WBS_Dia!: number;
  // @field('design_dia') Design_Dia!: number;
  // @field('material') Material!: string;
  // @field('Pipe_ID') Pipe_ID!: string;
  // @field('start_node_id') Start_Node_ID!: string;
  // @field('stop_node_id') Stop_Node_ID!: string;
  // @field('design_length') Design_Length!: number;
  // @field('deliverable_code') Deliverable_Code!: number;
  // @field('boq_code') Boq_Code!: string;


  @field('ZONE') ZONE!: string;
@field('DMA') DMA!: number;
@field('Sl_No') Sl_No!: number;
@field('PRAGATI_WBS_Dia') PRAGATI_WBS_Dia!: number;
@field('Design_Dia') Design_Dia!: number;
@field('Material') Material!: string;
@field('Pipe_ID') Pipe_ID!: string;
@field('Start_Node_ID') Start_Node_ID!: string;
@field('Stop_Node_ID') Stop_Node_ID!: string;
@field('Design_Length') Design_Length!: number;
@field('Deliverable_Code') Deliverable_Code!: number;
@field('Boq_Code') Boq_Code!: string;
}
