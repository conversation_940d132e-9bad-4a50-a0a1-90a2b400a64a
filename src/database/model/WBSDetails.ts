import { Model } from '@nozbe/watermelondb';
import { field } from '@nozbe/watermelondb/decorators';

export default class WBSDetails extends Model {
    static table = 'WBSDetails';

    @field('JobCode') jobCode!: string;
    @field('WBS') wbs!: string;
    @field('Task') task!: string;
    @field('PlanedQty') planedQty!: number;
    @field('PlanedSDate') planedSDate!: string | null;
    @field('PlanedEDate') planedEDate!: string | null;
    @field('PlanedLabour') planedLabour!: number;
    @field('ActualQty') actualQty!: number;
    @field('ActualSDate') actualSDate!: string | null;
    @field('ActualEDate') actualEDate!: string | null;
    @field('ActualLabour') actualLabour!: number;
    @field('LastUpdatedDate') lastUpdatedDate!: string | null;
    @field('LastUpdatedQty') lastUpdatedQty!: number;
    @field('LastUpdatedLabour') lastUpdatedLabour!: number;
    @field('Remarks') remarks!: string;
    @field('TaskType') taskType!: string;
    @field('Min_Productivity_Range') minProductivityRange!: number;
    @field('Max_Productivity_Range') maxProductivityRange!: number;
    @field('Scope') scope!: number;
    @field('MonthwisePlanQty') monthwisePlanQty!: number;
    @field('Et_Code') etCode!: number;
    @field('DeliverableType_Et_Code') deliverableTypeEtCode!: string;
    @field('DET_Assigned_Scope_Quantity') detAssignedScopeQuantity!: number;
    @field('DET_Year_Month') detYearMonth!: number;
    @field('TDD_Is_Engineer_Target_Mandatory') tddIsEngineerTargetMandatory!: string;
    @field('is_bookmarked') isBookmarked!: boolean;
    @field('isSyncRequiredForBookmark') isSyncRequiredForBookmark!: boolean;

    get fullDesc(): string {
        return `${this.jobCode} - ${this.wbs}`;
    }
} 