import { Model } from '@nozbe/watermelondb';
import { date, field, readonly } from '@nozbe/watermelondb/decorators';

export default class W<PERSON><PERSON>ob extends Model {
    static table = 'WBSJob';

    @field('WBS_TypeID') WBS_TypeID!: number
    @field('Job_Code') Job_Code!: string
    @field('WBS_Code') WBS_Code!: string
    @field('WBS_Description') WBS_Description!: string
    @field('Parent_WBS_Code') Parent_WBS_Code!: string
    @field('ET_Code') ET_Code!: string
    @field('Leaf_Node_Tag') Leaf_Node_Tag!: string
    @field('Sort_Order') Sort_Order!: number
    @field('Is_Active') Is_Active!: string
    @readonly @date('Created_Time') Created_Time!: Date
    @readonly @date('Modified_Time') Modified_Time!: Date
    @field('entity_Type') entity_Type!: string

    // Computed property for full description
    get fullDesc(): string {
        return `${this.Job_Code} - ${this.WBS_Description}`
    }
} 