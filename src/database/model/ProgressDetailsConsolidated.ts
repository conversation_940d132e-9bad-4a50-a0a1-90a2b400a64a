import { Model } from '@nozbe/watermelondb';
import { field } from '@nozbe/watermelondb/decorators';

export default class ProgressDetailsConsolidated extends Model {
    static table = 'ProgressUpdateConsolidated';

    @field('TDD_Job_Code') tddJobCode!: string;
    @field('TDD_WBS_Code') tddWbsCode!: string;
    @field('TDD_Deliverable_Code') tddDeliverableCode!: number;
    @field('Delivscope') delivscope!: number;
    @field('CumProg') cumProg!: number;
    @field('CumManday') cumManday!: number;
    @field('FTMProgress') ftmProgress!: number;
    @field('FTMManday') ftmManday!: number;
    @field('ScScopeE') scScopeE!: number;
    @field('ScProgress') scProgress!: number;
    @field('ScManday') scManday!: number;
    @field('CumPlanningQuantity') cumPlanningQuantity!: number;
    @field('FTMPlanningQuantity') ftmPlanningQuantity!: number;
} 