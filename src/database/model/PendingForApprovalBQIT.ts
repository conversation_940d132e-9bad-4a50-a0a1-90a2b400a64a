import { Model } from '@nozbe/watermelondb';
import { field } from '@nozbe/watermelondb/decorators';

export default class PendingForApprovalBQIT extends Model {
    static table = 'PendingForApprovalBQIT';

    @field('JOB') job!: string;
    @field('WBS') wbs!: string;
    @field('Task') task!: string;
    @field('TDate') tDate!: string;
    @field('UserID') userId!: number;
    @field('UserName') userName!: string;
    @field('Quantity') quantity!: number;
    @field('Manpower') manpower!: number;
    @field('Remarks') remarks!: string | null;
    @field('TaskType') taskType!: string;
    @field('Latitude') latitude!: number;
    @field('Longitude') longitude!: number;
    @field('TotalQuantity') totalQuantity!: number | null;
    @field('ActualQuantity') actualQuantity!: number | null;
    @field('WBS_Description') wbsDescription!: string;
    @field('WBS_Custom_Description') wbsCustomDescription!: string;
    @field('Task_Description') taskDescription!: string;
    @field('Task_Custom_Description') taskCustomDescription!: string;
    @field('Image_ID') imageId!: string | null;
    @field('Image_URL') imageUrl!: string | null;
    @field('UOM') uom!: string | null;
    @field('NodeId') nodeId!: string | null;
    @field('ReferanceNodeId') referanceNodeId!: string | null;
    @field('From_Length') fromLength!: number | null;
    @field('Progress_Length') progressLength!: number | null;
    @field('Total_Length') totalLength!: number | null;
    @field('Distance_From_Center') distanceFromCenter!: number | null;
    @field('Alignment') alignment!: string | null;
    @field('IS_Completed') isCompleted!: string | null;
    @field('Is_Hindrance') isHindrance!: string | null;
    // @field('Full_Description') fullDescription: string | null;
    // @field('Full_path') fullPath: string | null;
    
    get fullDesc(): string {
        return `${this.job} - ${this.wbs}`;
    }
} 