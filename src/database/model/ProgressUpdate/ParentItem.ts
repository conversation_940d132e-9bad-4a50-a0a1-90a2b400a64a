// models/ParentItem.ts
import { Model } from '@nozbe/watermelondb'
import { field, relation } from '@nozbe/watermelondb/decorators'

export default class ParentItem extends Model {
  static table = 'ParentItems'

  @field('wbs_code') wbsCode!: string
  @field('wbs_description') wbsDescription!: string
  @field('et_code') etCode!: string

  @relation('input_details', 'input_detail_id') inputDetail!: any
}
