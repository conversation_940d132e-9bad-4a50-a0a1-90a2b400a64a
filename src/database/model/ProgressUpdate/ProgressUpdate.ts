// models/InputDetail.ts
import { Model } from '@nozbe/watermelondb'
import { field, json, children } from '@nozbe/watermelondb/decorators'

export default class InputDetail extends Model {
  static table = 'InputDetails'

  @field('job_code') jobCode!: string
  @field('wbs') wbs!: string
  @field('task') task!: string
  @field('planned_qty') plannedQty!: number
  @field('planned_sdate') plannedSDate!: string | null
  @field('planned_edate') plannedEDate!: string | null
  @field('planned_labour') plannedLabour!: number
  @field('actual_qty') actualQty!: number
  @field('actual_sdate') actualSDate!: string | null
  @field('actual_edate') actualEDate!: string | null
  @field('actual_labour') actualLabour!: number
  @field('last_updated_date') lastUpdatedDate!: string | null
  @field('last_updated_qty') lastUpdatedQty!: number
  @field('last_updated_labour') lastUpdatedLabour!: number
  @field('remarks') remarks!: string
  @field('task_type') taskType!: string
  @field('min_productivity_range') minProdRange!: number
  @field('max_productivity_range') maxProdRange!: number
  @field('scope') scope!: number
  @field('monthwise_plan_qty') monthwisePlanQty!: number
  @field('et_code') etCode!: number
  @field('deliverable_type_et_code') deliverableTypeEtCode!: string
  @field('det_assigned_scope_quantity') detAssignedScopeQuantity!: number
  @field('det_year_month') detYearMonth!: number
  @field('tdd_is_engineer_target_mandatory') tddIsEngineerTargetMandatory!: string
  @field('full_desc') fullDesc!: string
  @field('wbs_path') wbsPath!: string

  @json('selected_item', sanitizeSelectedItem) selectedItem!: object
  @children('parent_items') parentItems!: any[]
}

function sanitizeSelectedItem(json: any) {
  return json
}
