import { Model } from '@nozbe/watermelondb';
import { field } from '@nozbe/watermelondb/decorators';

export default class ViewLastUpdateGIS extends Model {
    static table = 'ViewLastUpdateGIS';

    @field('JOB') job!: string;
    @field('TDP_WBS_CODE') tdpWbsCode!: string;
    @field('TDP_DELIVERABLE_CODE') tdpDeliverableCode!: number;
    @field('NodeID') nodeId!: string;
    @field('INSERTED_BY') insertedBy!: number;
    @field('Inserted_On') insertedOn!: string;
    @field('QTY') qty!: number;
    @field('MAN') man!: number;
    @field('MUSGD_User_ID') musgdUserId!: number;
    @field('Full_Name') fullName!: string;
    @field('Reference_ID') referenceId!: string;
    @field('MUOM_Short_Description') muomShortDescription!: string;
} 