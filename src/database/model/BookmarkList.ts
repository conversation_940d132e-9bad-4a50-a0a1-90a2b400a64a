import { Model } from '@nozbe/watermelondb';
import { field } from '@nozbe/watermelondb/decorators';

export default class BookMarkList extends Model {
    static table = 'BookMarkList';

    @field('PRCGB_Job_Code') jobCode!: string;
    @field('PRCGB_WBS_Code') wbsCode!: string;
    @field('PRCGB_User_ID') userId!: number;
    @field('PRCGB_Hierarchy_Level') hierarchyLevel!: string;
    @field('PRCGB_IsActive') isActive!: string;
} 