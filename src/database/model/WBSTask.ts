import { Model } from '@nozbe/watermelondb';
import { date, field, readonly } from '@nozbe/watermelondb/decorators';
import { tableSchema } from '@nozbe/watermelondb';

export default class WBSTask extends Model {
    static table = 'WBSTask';

    @field('Job_Code') jobCode!: string;
    @field('Task_Code') taskCode!: string;
    @field('Task_Description') taskDescription!: string;
    @field('Parent_Task_Code') parentTaskCode!: string;
    @field('ET_Code') etCode!: string;
    @field('UOM_Symbol') uomSymbol!: string;
    @field('Is_Active') isActive!: string;
    @field('Gis_Tag') gisTag!: string;
    @field('Parent_WBS') parentWbs!: string;
    @field('Parent_Task') parentTask!: string;
    @readonly @date('Created_Time') Created_Time!: Date;
    @readonly @date('Modified_Time') Modified_Time!: Date;
    @field('entity_Type') entity_Type!: string;
    // @field('Gis_Tag') Parent_WBS_Code!: string;
}
