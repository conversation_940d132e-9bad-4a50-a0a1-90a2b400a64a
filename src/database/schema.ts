import { appSchema, tableSchema } from '@nozbe/watermelondb'

export default appSchema({
    version: 1,
    tables: [
        // WSBJob Table
        tableSchema({
            name: 'WBSJob',
            columns: [
                { name: 'WBS_TypeID', type: 'number' },
                { name: 'Job_Code', type: 'string' },
                { name: 'WBS_Code', type: 'string' },
                { name: 'WBS_Description', type: 'string' },
                { name: 'Parent_WBS_Code', type: 'string' },
                { name: 'ET_Code', type: 'string' },
                { name: 'Leaf_Node_Tag', type: 'string' },
                { name: 'Sort_Order', type: 'number' },
                { name: 'Is_Active', type: 'string' },
                { name: 'Created_Time', type: 'number' },
                { name: 'Modified_Time', type: 'number' },
                { name: 'Full_Desc', type: 'string' },
                { name: 'entity_Type', type: 'string' }
            ],
        }),

        // WBSTask Table
        tableSchema({
            name: 'WBSTask',
            columns: [
                { name: 'Task_ID', type: 'number' },
                { name: 'Job_Code', type: 'string' },
                { name: 'Task_Code', type: 'string' },
                { name: 'Task_Description', type: 'string' },
                { name: 'Parent_Task_Code', type: 'string' },
                { name: 'ET_Code', type: 'string' },
                { name: 'UOM_Symbol', type: 'string' },
                { name: 'Is_Active', type: 'string' },
                { name: 'Gis_Tag', type: 'string' },
                { name: 'Parent_WBS', type: 'string' },
                { name: 'Parent_Task', type: 'string' },
                { name: 'Created_Time', type: 'string' },
                { name: 'Modified_Time', type: 'string' },
                { name: 'entity_Type', type: 'string' },
                { name: 'parent_WBSCode', type: 'string' }
            ],
        }),

        // NodeData Table
        tableSchema({
            name: 'NodeData',
            columns: [
                { name: 'Task_ID', type: 'number' },
                { name: 'Job_Code', type: 'string' },
                { name: 'Task_Code', type: 'string' },
                { name: 'Task_Description', type: 'string' },
                { name: 'Parent_Task_Code', type: 'string' },
                { name: 'Created_Time', type: 'string' },
                { name: 'Modified_Time', type: 'string' },
                { name: 'UOM_Symbol', type: 'string' },
                { name: 'Is_Active', type: 'string' },
                { name: 'Et_Code', type: 'string' },
                { name: 'Gis_Tag', type: 'string' },
                { name: 'Created_Time', type: 'number' },
                { name: 'Modified_Time', type: 'number' },
                { name: 'entity_Type', type: 'string' }
            ],
        }),

        // WBSDetails Table
        tableSchema({
            name: 'WBSDetails',
            columns: [
                { name: 'JobCode', type: 'string' },
                { name: 'WBS', type: 'string' },
                { name: 'Task', type: 'string' },
                { name: 'PlanedQty', type: 'number' },
                { name: 'PlanedSDate', type: 'string', isOptional: true },
                { name: 'PlanedEDate', type: 'string', isOptional: true },
                { name: 'PlanedLabour', type: 'number' },
                { name: 'ActualQty', type: 'number' },
                { name: 'ActualSDate', type: 'string', isOptional: true },
                { name: 'ActualEDate', type: 'string', isOptional: true },
                { name: 'ActualLabour', type: 'number' },
                { name: 'LastUpdatedDate', type: 'string', isOptional: true },
                { name: 'LastUpdatedQty', type: 'number' },
                { name: 'LastUpdatedLabour', type: 'number' },
                { name: 'Remarks', type: 'string' },
                { name: 'TaskType', type: 'string' },
                { name: 'Min_Productivity_Range', type: 'number' },
                { name: 'Max_Productivity_Range', type: 'number' },
                { name: 'Scope', type: 'number' },
                { name: 'MonthwisePlanQty', type: 'number' },
                { name: 'Et_Code', type: 'number' },
                { name: 'DeliverableType_Et_Code', type: 'string' },
                { name: 'DET_Assigned_Scope_Quantity', type: 'number' },
                { name: 'DET_Year_Month', type: 'number' },
                { name: 'TDD_Is_Engineer_Target_Mandatory', type: 'string' },
                { name: 'Full_Desc', type: 'string' }
            ]
        }),

        // GisDetail Table
        tableSchema({
            name: 'WBSGISDetails',
            columns: [
                { name: 'JobCode', type: 'string' },
                { name: 'WBS', type: 'string' },
                { name: 'Task', type: 'string' },
                { name: 'Deliverable', type: 'string', isOptional: true },
                { name: 'Remarks', type: 'string' },
                { name: 'Boq', type: 'string', isOptional: true },
                { name: 'ActualDate', type: 'string' },
                { name: 'NodeId', type: 'string' },
                { name: 'NodeRefCode', type: 'number' },
                { name: 'FromLength', type: 'number' },
                { name: 'ToLength', type: 'number' },
                { name: 'TotalLength', type: 'number' },
                { name: 'Manpower', type: 'number' },
                { name: 'DistanceFCenter', type: 'number' },
                { name: 'Alignment', type: 'string' },
                { name: 'TaskType', type: 'string' },
                { name: 'Scope', type: 'number' },
                { name: 'MonthwisePlanQty', type: 'number' },
                { name: 'Et_Code', type: 'number' },
                { name: 'DeliverableType_Et_Code', type: 'string' },
                { name: 'Is_Hindrance', type: 'string' },
                { name: 'DET_Assigned_Scope_Quantity', type: 'number' },
                { name: 'DET_Year_Month', type: 'number', isOptional: true },
                { name: 'TDD_Is_Engineer_Target_Mandatory', type: 'string' },
                { name: 'PlanedSDate', type: 'string' },
                { name: 'PlanedEDate', type: 'string' },
                { name: 'ActualSDate', type: 'string' },
                { name: 'ActualEDate', type: 'string' }
            ]
        }),

        // GISNodeDetail Table
        tableSchema({
            name: 'GISNodeDetail',
            columns: [
                { name: 'GISNodeDetailID', type: 'number' },
                { name: 'Job_Code', type: 'string' },
                { name: 'ZONE', type: 'string' },
                { name: 'DMA', type: 'number' },
                { name: 'Sl_No', type: 'number' },
                { name: 'PRAGATI_WBS_Dia', type: 'number' },
                { name: 'Design_Dia', type: 'number' },
                { name: 'Material', type: 'string' },
                { name: 'Pipe_ID', type: 'string' },
                { name: 'Start_Node_ID', type: 'string' },
                { name: 'Stop_Node_ID', type: 'string' },
                { name: 'Design_Length', type: 'number' },
                { name: 'Deliverable_Code', type: 'number' },
                { name: 'Boq_Code', type: 'string' },
                { name: 'Is_Hindrance', type: 'string' },
            ],
        }),

        // ViewLastUpdateBQIT Table
        tableSchema({
            name: 'ViewLastUpdateBQIT',
            columns: [
                { name: 'JOB', type: 'string' },
                { name: 'TDP_WBS_CODE', type: 'string' },
                { name: 'TDP_DELIVERABLE_CODE', type: 'number' },
                { name: 'INSERTED_BY', type: 'number' },
                { name: 'Inserted_On', type: 'string' },
                { name: 'QTY', type: 'number' },
                { name: 'MAN', type: 'number' },
                { name: 'MUSGD_User_ID', type: 'number' },
                { name: 'Full_Name', type: 'string' },
                { name: 'Reference_ID', type: 'string' },
                { name: 'MUOM_Short_Description', type: 'string' },
                { name: 'created_at', type: 'number' },
                { name: 'updated_at', type: 'number' }
            ]
        }),

        // ViewLastUpdateGIS Table
        tableSchema({
            name: 'ViewLastUpdateGIS',
            columns: [
                { name: 'JOB', type: 'string' },
                { name: 'TDP_WBS_CODE', type: 'string' },
                { name: 'TDP_DELIVERABLE_CODE', type: 'number' },
                { name: 'NodeID', type: 'string' },
                { name: 'INSERTED_BY', type: 'number' },
                { name: 'Inserted_On', type: 'string' },
                { name: 'QTY', type: 'number' },
                { name: 'MAN', type: 'number' },
                { name: 'MUSGD_User_ID', type: 'number' },
                { name: 'Full_Name', type: 'string' },
                { name: 'Reference_ID', type: 'string' },
                { name: 'MUOM_Short_Description', type: 'string' },
                { name: 'created_at', type: 'number' },
                { name: 'updated_at', type: 'number' }
            ]
        }),

        // Progress Details - Engineer Table
        tableSchema({
            name: 'ProgressUpdateEngineer',
            columns: [
                { name: 'TDD_Job_Code', type: 'string' },
                { name: 'TDD_WBS_Code', type: 'string' },
                { name: 'TDD_Deliverable_Code', type: 'number' },
                { name: 'Delivscope', type: 'number' },
                { name: 'CumProg', type: 'number' },
                { name: 'CumManday', type: 'number' },
                { name: 'FTMProgress', type: 'number' },
                { name: 'FTMManday', type: 'number' },
                { name: 'CumTargetPlanQty', type: 'number' },
                { name: 'FTMCumTargetPlanQty', type: 'number' },
                { name: 'TargetScope', type: 'number' }
            ]
        }),

        // Progress Update Consolidated Table
        tableSchema({
            name: 'ProgressUpdateConsolidated',
            columns: [
                { name: 'TDD_Job_Code', type: 'string' },
                { name: 'TDD_WBS_Code', type: 'string' },
                { name: 'TDD_Deliverable_Code', type: 'number' },
                { name: 'Delivscope', type: 'number' },
                { name: 'CumProg', type: 'number' },
                { name: 'CumManday', type: 'number' },
                { name: 'FTMProgress', type: 'number' },
                { name: 'FTMManday', type: 'number' },
                { name: 'ScScopeE', type: 'number' },
                { name: 'ScProgress', type: 'number' },
                { name: 'ScManday', type: 'number' },
                { name: 'CumPlanningQuantity', type: 'number' },
                { name: 'FTMPlanningQuantity', type: 'number' }
            ]
        }),

        // BookMark List Table 
        tableSchema({
            name: 'BookMarkList',
            columns: [
                { name: 'PRCGB_Job_Code', type: 'string' },
                { name: 'PRCGB_WBS_Code', type: 'string' },
                { name: 'PRCGB_User_ID', type: 'number' },
                { name: 'PRCGB_Hierarchy_Level', type: 'string' },
                { name: 'PRCGB_IsActive', type: 'string' },
                { name: 'created_at', type: 'number' },
                { name: 'updated_at', type: 'number' }
            ]
        }),

        // Latitude Longitude Hierarchy Table
        tableSchema({
            name: 'LatLongHierarchy',
            columns: [
                { name: 'PRCLLH_Job_Code', type: 'string' },
                { name: 'PRCLLH_WBS_Code', type: 'string' },
                { name: 'PRCLLH_Latitude', type: 'number' },
                { name: 'PRCLLH_Longitude', type: 'number' },
                { name: 'PRCLLH_Hierarchy_Level', type: 'string' },
                { name: 'PRCLLH_IsActive', type: 'string' },
                { name: 'JOB_DESC', type: 'string' },
                { name: 'IL', type: 'string' },
                { name: 'IL_DESC', type: 'string' },
                { name: 'WP', type: 'string' },
                { name: 'WP_DESC', type: 'string' },
                { name: 'SWP', type: 'string' },
                { name: 'CWP_DESC', type: 'string' },
                { name: 'DELIVERABLE_CODE', type: 'number' },
                { name: 'DELIVERABLE_CODE_DESC', type: 'string' },
                { name: 'created_at', type: 'number' },
                { name: 'updated_at', type: 'number' }
            ]
        }),

        // Pending for approval - BQ/IT Table
        tableSchema({
            name: 'PendingForApprovalBQIT',
            columns: [
                { name: 'JOB', type: 'string' },
                { name: 'WBS', type: 'string' },
                { name: 'Task', type: 'string' },
                { name: 'TDate', type: 'string' },
                { name: 'UserID', type: 'number' },
                { name: 'UserName', type: 'string' },
                { name: 'Quantity', type: 'number' },
                { name: 'Manpower', type: 'number' },
                { name: 'Remarks', type: 'string', isOptional: true },
                { name: 'TaskType', type: 'string' },
                { name: 'Latitude', type: 'number' },
                { name: 'Longitude', type: 'number' },
                { name: 'TotalQuantity', type: 'number', isOptional: true },
                { name: 'ActualQuantity', type: 'number', isOptional: true },
                { name: 'WBS_Description', type: 'string' },
                { name: 'WBS_Custom_Description', type: 'string' },
                { name: 'Task_Description', type: 'string' },
                { name: 'Task_Custom_Description', type: 'string' },
                { name: 'Image_ID', type: 'string', isOptional: true },
                { name: 'Image_URL', type: 'string', isOptional: true },
                { name: 'UOM', type: 'string', isOptional: true },
                { name: 'NodeId', type: 'string', isOptional: true },
                { name: 'ReferanceNodeId', type: 'string', isOptional: true },
                { name: 'From_Length', type: 'number', isOptional: true },
                { name: 'Progress_Length', type: 'number', isOptional: true },
                { name: 'Total_Length', type: 'number', isOptional: true },
                { name: 'Distance_From_Center', type: 'number', isOptional: true },
                { name: 'Alignment', type: 'string', isOptional: true },
                { name: 'IS_Completed', type: 'string', isOptional: true },
                { name: 'Is_Hindrance', type: 'string', isOptional: true }
            ]
        }),

        // Pending for approval - GIS Table
        tableSchema({
            name: 'PendingForApprovalGIS',
            columns: [

            ]
        }),

        tableSchema({
            name: 'InputDetails',
            columns: [
              { name: 'Job_Code', type: 'string' },
              { name: 'Wbs', type: 'string' },
              { name: 'Task', type: 'string' },
              { name: 'Planned_Qty', type: 'number' },
              { name: 'Planned_SDate', type: 'string', isOptional: true },
              { name: 'Planned_EDate', type: 'string', isOptional: true },
              { name: 'Planned_Labour', type: 'number' },
              { name: 'Actual_Qty', type: 'number' },
              { name: 'Actual_SDate', type: 'string', isOptional: true },
              { name: 'Actual_EDate', type: 'string', isOptional: true },
              { name: 'Actual_Labour', type: 'number' },
              { name: 'Last_Updated_Date', type: 'string', isOptional: true },
              { name: 'Last_Updated_Qty', type: 'number' },
              { name: 'Last_Updated_Labour', type: 'number' },
              { name: 'Remarks', type: 'string' },
              { name: 'Task_Type', type: 'string' },
              { name: 'Min_Productivity_Range', type: 'number' },
              { name: 'Max_Productivity_Range', type: 'number' },
              { name: 'Scope', type: 'number' },
              { name: 'Monthwise_Plan_Qty', type: 'number' },
              { name: 'Et_Code', type: 'number' },
              { name: 'Deliverable_Type_Et_Code', type: 'string' },
              { name: 'Det_Assigned_Scope_Quantity', type: 'number' },
              { name: 'Det_Year_Month', type: 'number' },
              { name: 'Tdd_Is_Engineer_Target_Mandatory', type: 'string' },
              { name: 'Full_Desc', type: 'string' },
              { name: 'Wbs_Path', type: 'string' },
              { name: 'Selected_Item', type: 'string' },
              { name: 'Man_Days', type: 'string' },
              { name: 'Date', type: 'string' },
            ],
          }),
          tableSchema({
            name: 'ParentItems',
            columns: [
              { name: 'Wbs_Code', type: 'string' },
              { name: 'Wbs_Description', type: 'string' },
              { name: 'Et_Code', type: 'string' },
              { name: 'Input_Detail_Id', type: 'string' },
            ],
          }),
          
    ]
})