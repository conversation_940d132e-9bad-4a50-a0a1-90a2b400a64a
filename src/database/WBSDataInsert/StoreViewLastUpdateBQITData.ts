import { database } from '../index';
import { Q } from '@nozbe/watermelondb';
import ViewLastUpdateBQIT from '../model/ViewLastUpdateBQIT';

interface ViewLastUpdateBQITItem {
    JOB: string;
    TDP_WBS_CODE: string;
    TDP_DELIVERABLE_CODE: number;
    INSERTED_BY: number;
    Inserted_On: string;
    QTY: number;
    MAN: number;
    MUSGD_User_ID: number;
    Full_Name: string;
    Reference_ID: string;
    MUOM_Short_Description: string;
}
const CHUNK_SIZE = 500;
export const StoreViewLastUpdateBQIT = async (bqitData: ViewLastUpdateBQITItem[]) => {
    const bqitCollection = database.collections.get<ViewLastUpdateBQIT>('ViewLastUpdateBQIT');
    const jobCodes = [...new Set(bqitData.map(item => item.JOB))];

    await database.write(async () => {
        // Delete all records for the job codes in a single query
        const existingRecords = await bqitCollection
            .query(Q.where('JOB', Q.oneOf(jobCodes)))
            .fetch();

        if (existingRecords.length > 0) {
            await Promise.all(existingRecords.map(record => record.destroyPermanently()));
        }

        // Add new records
        // await Promise.all(bqitData.map(item => {
        //     bqitCollection.create(record => {
        //         record.job = item.JOB;
        //         record.tdpWbsCode = item.TDP_WBS_CODE;
        //         record.tdpDeliverableCode = item.TDP_DELIVERABLE_CODE;
        //         record.insertedBy = item.INSERTED_BY;
        //         record.insertedOn = item.Inserted_On;
        //         record.qty = item.QTY;
        //         record.man = item.MAN;
        //         record.musgdUserId = item.MUSGD_User_ID;
        //         record.fullName = item.Full_Name;
        //         record.referenceId = item.Reference_ID;
        //         record.muomShortDescription = item.MUOM_Short_Description;
        //     });
        // }));

        for (let i = 0; i < bqitData.length; i += CHUNK_SIZE) {
            const chunk = bqitData.slice(i, i + CHUNK_SIZE);
      
            const recordsToInsert = chunk.map(item =>
                bqitCollection.prepareCreate(record => {
                    record.job = item.JOB;
                    record.tdpWbsCode = item.TDP_WBS_CODE;
                    record.tdpDeliverableCode = item.TDP_DELIVERABLE_CODE;
                    record.insertedBy = item.INSERTED_BY;
                    record.insertedOn = item.Inserted_On;
                    record.qty = item.QTY;
                    record.man = item.MAN;
                    record.musgdUserId = item.MUSGD_User_ID;
                    record.fullName = item.Full_Name;
                    record.referenceId = item.Reference_ID;
                    record.muomShortDescription = item.MUOM_Short_Description;
              })
            );
      
            await database.batch(...recordsToInsert);
          }
    });
}; 