import { database } from '../index';
import { Q } from '@nozbe/watermelondb';
import LatLongHierarchy from '../model/LatLongHierarchy';

interface LatLongHierarchyItem {
    PRCLLH_Job_Code: string;
    PRCLLH_WBS_Code: string;
    PRCLLH_Latitude: number;
    PRCLLH_Longitude: number;
    PRCLLH_Hierarchy_Level: string;
    PRCLLH_IsActive: string;
    JOB_DESC: string;
    IL: string;
    IL_DESC: string;
    WP: string;
    WP_DESC: string;
    SWP: string;
    CWP_DESC: string;
    DELIVERABLE_CODE: number;
    DELIVERABLE_CODE_DESC: string;
}

export const StoreLatLongHierarchyData = async (latLongData: LatLongHierarchyItem[]) => {
    const latLongCollection = database.collections.get<LatLongHierarchy>('LatLongHierarchy');
    const jobCodes = [...new Set(latLongData.map(item => item.PRCLLH_Job_Code))];

    try {
        await database.write(async () => {
            // Delete existing records for the job codes
            const existingRecords = await latLongCollection
                .query(Q.where('PRCLLH_Job_Code', Q.oneOf(jobCodes)))
                .fetch();

            if (existingRecords.length > 0) {
                await Promise.all(existingRecords.map(record => record.destroyPermanently()));
            }

            // Add new records
            await Promise.all(latLongData.map(item => {
                latLongCollection.create(record => {
                    record.jobCode = item.PRCLLH_Job_Code;
                    record.wbsCode = item.PRCLLH_WBS_Code;
                    record.latitude = item.PRCLLH_Latitude;
                    record.longitude = item.PRCLLH_Longitude;
                    record.hierarchyLevel = item.PRCLLH_Hierarchy_Level;
                    record.isActive = item.PRCLLH_IsActive;
                    record.jobDesc = item.JOB_DESC;
                    record.il = item.IL;
                    record.ilDesc = item.IL_DESC;
                    record.wp = item.WP;
                    record.wpDesc = item.WP_DESC;
                    record.swp = item.SWP;
                    record.cwpDesc = item.CWP_DESC;
                    record.deliverableCode = item.DELIVERABLE_CODE;
                    record.deliverableCodeDesc = item.DELIVERABLE_CODE_DESC;
                });
            }));
        });
    } catch (error) {
        throw error;
    }
}; 