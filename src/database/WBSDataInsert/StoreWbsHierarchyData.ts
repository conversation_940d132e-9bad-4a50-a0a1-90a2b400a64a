import { database } from '../index';
import { Q } from '@nozbe/watermelondb';
import WBSJob from '../model/WBSJob';
import { WbsHierarchyItem, WbsJobItem } from '../../model/Wbs/WbsData';
import { EntityConstants } from '../../model/DailyProgress/DailyProgressData';

// export const StoreWbsHierarchyData = async (hierarchyData: WbsHierarchyItem[], objJoblist: WbsJobItem[]) => {
//     const wbsJobCollection = database.collections.get<WBSJob>('WBSJob');
//     const jobCodes = objJoblist.map(job => job.jobCode);

//     await database.write(async () => {
//         // Delete all records for the job codes in a single query
//         const existingRecords = await wbsJobCollection
//             .query(Q.where('Job_Code', Q.oneOf(jobCodes)))
//             .fetch();

//         if (existingRecords.length > 0) {
//             await Promise.all(existingRecords.map(record => record.destroyPermanently()));
//         }

//         // Add new records
//         await Promise.all(hierarchyData.map(item => {
//             wbsJobCollection.create(record => {
//                 record.Job_Code = item.Job_Code;
//                 record.WBS_Code = item.WBS_Code;
//                 record.WBS_Description = item.WBS_Description;
//                 record.Parent_WBS_Code = item.Parent_WBS_Code;
//                 record.ET_Code = item.ET_Code;
//                 record.Leaf_Node_Tag = item.Leaf_Node_Tag || '';
//                 record.Sort_Order = item.Sort_Order;
//                 record.Is_Active = item.Is_Active;
//             });
//         }));
//     });
// }; 

const CHUNK_SIZE = 500;

export const StoreWbsHierarchyData = async (
  hierarchyData: WbsHierarchyItem[],
  objJoblist: WbsJobItem[]
) => {
  const wbsJobCollection = database.collections.get<WBSJob>('WBSJob');
  const jobCodes = objJoblist.map(job => job.jobCode);

  await database.write(async () => {
    // Delete all matching records first
    const existingRecords = await wbsJobCollection
      .query(Q.where('Job_Code', Q.oneOf(jobCodes)))
      .fetch();

    // if (existingRecords.length > 0) {
    //   await database.batch(...existingRecords.map(record => record.prepareDestroyPermanently()));
    // }

    // Insert in chunks
    for (let i = 0; i < hierarchyData.length; i += CHUNK_SIZE) {
      const chunk = hierarchyData.slice(i, i + CHUNK_SIZE);

      const recordsToInsert = chunk.map(item =>
        wbsJobCollection.prepareCreate(record => {
          record.Job_Code = item.Job_Code;
          record.WBS_Code = item.WBS_Code;
          record.WBS_Description = item.WBS_Description;
          record.Parent_WBS_Code = item.Parent_WBS_Code;
          record.ET_Code = item.ET_Code;
          record.Leaf_Node_Tag = item.Leaf_Node_Tag || '';
          record.Sort_Order = item.Sort_Order;
          record.Is_Active = item.Is_Active;
          record.entity_Type = EntityConstants.WBSJob 
        })
      );

      await database.batch(...recordsToInsert);
    }
  });
};
