import { database } from '../index';
import { Q } from '@nozbe/watermelondb';
import ProgressDetailsConsolidated from '../model/ProgressDetailsConsolidated';
import { ProgressConsolidatedItem, WbsJobItem } from '../../model/Wbs/WbsData';

const CHUNK_SIZE=500;
export const StoreProgressConsolidatedData = async (consolidatedData: ProgressConsolidatedItem[], objJoblist: WbsJobItem[]) => {
    const progressConsolidatedCollection = database.collections.get<ProgressDetailsConsolidated>('ProgressUpdateConsolidated');
    const jobCodes = objJoblist.map(job => job.jobCode);

    await database.write(async () => {
        // Delete all records for the job codes in a single query
        const existingRecords = await progressConsolidatedCollection
            .query(Q.where('TDD_Job_Code', Q.oneOf(jobCodes)))
            .fetch();

        if (existingRecords.length > 0) {
            await Promise.all(existingRecords.map(record => record.destroyPermanently()));
        }

        // Add new records
        // await Promise.all(consolidatedData.map(item => {
        //     progressConsolidatedCollection.create(record => {
        //         record.tddJobCode = item.TDD_Job_Code;
        //         record.tddWbsCode = item.TDD_WBS_Code;
        //         record.tddDeliverableCode = item.TDD_Deliverable_Code;
        //         record.delivscope = item.Delivscope;
        //         record.cumProg = item.CumProg;
        //         record.cumManday = item.CumManday;
        //         record.ftmProgress = item.FTMProgress;
        //         record.ftmManday = item.FTMManday;
        //         record.scScopeE = item.ScScopeE;
        //         record.scProgress = item.ScProgress;
        //         record.scManday = item.ScManday;
        //         record.cumPlanningQuantity = item.CumPlanningQuantity;
        //         record.ftmPlanningQuantity = item.FTMPlanningQuantity;
        //     });
        // }));

        for (let i = 0; i < consolidatedData.length; i += CHUNK_SIZE) {
            const chunk = consolidatedData.slice(i, i + CHUNK_SIZE);
      
            const recordsToInsert = chunk.map(item =>
                progressConsolidatedCollection.prepareCreate(record => {
                    record.tddJobCode = item.TDD_Job_Code;
                    record.tddWbsCode = item.TDD_WBS_Code;
                    record.tddDeliverableCode = item.TDD_Deliverable_Code;
                    record.delivscope = item.Delivscope;
                    record.cumProg = item.CumProg;
                    record.cumManday = item.CumManday;
                    record.ftmProgress = item.FTMProgress;
                    record.ftmManday = item.FTMManday;
                    record.scScopeE = item.ScScopeE;
                    record.scProgress = item.ScProgress;
                    record.scManday = item.ScManday;
                    record.cumPlanningQuantity = item.CumPlanningQuantity;
                    record.ftmPlanningQuantity = item.FTMPlanningQuantity;
              })
            );
      
            await database.batch(...recordsToInsert);
          }
    });
}; 