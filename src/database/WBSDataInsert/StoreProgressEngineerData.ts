import { database } from '../index';
import { Q } from '@nozbe/watermelondb';
import ProgressUpdateEngineer from '../model/ProgressUpdateEngineer';
import { DeliverableProgressDetailsEngineerItem, WbsJobItem } from '../../model/Wbs/WbsData';

 const CHUNK_SIZE = 500;
export const StoreProgressEngineerData = async (engineerData: DeliverableProgressDetailsEngineerItem[], objJoblist: WbsJobItem[]) => {
    const progressEngineerCollection = database.collections.get<ProgressUpdateEngineer>('ProgressUpdateEngineer');
    const jobCodes = objJoblist.map(job => job.jobCode);

    await database.write(async () => {
        // Delete all records for the job codes in a single query
        const existingRecords = await progressEngineerCollection
            .query(Q.where('TDD_Job_Code', Q.oneOf(jobCodes)))
            .fetch();

        if (existingRecords.length > 0) {
            await Promise.all(existingRecords.map(record => record.destroyPermanently()));
        }

        // Add new records
        // await Promise.all(engineerData.map(item => {
        //     progressEngineerCollection.create(record => {
        //         record.tddJobCode = item.TDD_Job_Code;
        //         record.tddWbsCode = item.TDD_WBS_Code;
        //         record.tddDeliverableCode = item.TDD_Deliverable_Code;
        //         record.delivscope = item.Delivscope;
        //         record.cumProg = item.CumProg;
        //         record.cumManday = item.CumManday;
        //         record.ftmProgress = item.FTMProgress;
        //         record.ftmManday = item.FTMManday;
        //         record.cumTargetPlanQty = item.CumTargetPlanQty;
        //         record.ftmCumTargetPlanQty = item.FTMCumTargetPlanQty;
        //         record.targetScope = item.TargetScope;
        //     });
        // }));

        for (let i = 0; i < engineerData.length; i += CHUNK_SIZE) {
            const chunk = engineerData.slice(i, i + CHUNK_SIZE);
      
            const recordsToInsert = chunk.map(item =>
                progressEngineerCollection.prepareCreate(record => {
                    record.tddJobCode = item.TDD_Job_Code;
                    record.tddWbsCode = item.TDD_WBS_Code;
                    record.tddDeliverableCode = item.TDD_Deliverable_Code;
                    record.delivscope = item.Delivscope;
                    record.cumProg = item.CumProg;
                    record.cumManday = item.CumManday;
                    record.ftmProgress = item.FTMProgress;
                    record.ftmManday = item.FTMManday;
                    record.cumTargetPlanQty = item.CumTargetPlanQty;
                    record.ftmCumTargetPlanQty = item.FTMCumTargetPlanQty;
                    record.targetScope = item.TargetScope;
              })
            );
      
            await database.batch(...recordsToInsert);
          }
    });
}; 