import { Store } from 'redux';
import { database } from '../index';
import { Q } from '@nozbe/watermelondb';
// import WBSTask from '../model/WBSTask';
// import { WbsTaskItem, WbsJobItem } from '../../model/Wbs/WbsData';
// import { EntityConstants } from '../../model/DailyProgress/DailyProgressData';
import EpragatiGisPipe from '../model/EpragatiGisPipe';
import { GisPragatiItem } from '../../model/Wbs/WbsData';

const CHUNK_SIZE = 500;
export const StoreEpragatiGisPipesUtil = {
  // StoreEpragatiGisPipesUtil: async (pragatiList: EpragatiGisPipe[], objJoblist: WbsJobItem[]) => {
  StoreEpragatiGisPipesUtil: async (pragatiList: GisPragatiItem[]) => {

    const wbsTaskCollection = database.collections.get<EpragatiGisPipe>('EpragatiGisPipes');
    // const jobCodes = objJoblist.map(job => job.jobCode);

    await database.write(async () => {
      // Delete all records for the job codes in a single query
      // const existingRecords = await wbsTaskCollection
      //     .query(Q.where('Job_Code', Q.oneOf(jobCodes)))
      //     .fetch();

      // if (existingRecords.length > 0) {
      //     await Promise.all(existingRecords.map(record => record.destroyPermanently()));
      // }

      // Insert in chunks
      console.log('StoreEpragatiGisPipesUtil -- pragatiList.length: ', pragatiList.length);
      for (let i = 0; i < pragatiList.length; i += CHUNK_SIZE) {

        const chunk = pragatiList.slice(i, i + CHUNK_SIZE);

        const recordsToInsert = chunk.map(item =>
          wbsTaskCollection.prepareCreate(pipe => {
            pipe.ZONE = item.ZONE;
            pipe.DMA = item.DMA;
            pipe.Sl_No = item.Sl_No;
            pipe.PRAGATI_WBS_Dia = item.PRAGATI_WBS_Dia;
            pipe.Design_Dia = item.Design_Dia;
            pipe.Material = item.Material;
            pipe.Pipe_ID = item.Pipe_ID;
            pipe.Start_Node_ID = item.Start_Node_ID;
            pipe.Stop_Node_ID = item.Stop_Node_ID;
            pipe.Design_Length = item.Design_Length;
            pipe.Deliverable_Code = item.Deliverable_Code;
            pipe.Boq_Code = item.Boq_Code;
          })
        );

        await database.batch(...recordsToInsert);
      }

      await StoreEpragatiGisPipesUtil.logAllPragatiPipeIds();
    });
  },

  logAllPragatiPipeIds: async () => {
    try {
      const collection = database.get<EpragatiGisPipe>('EpragatiGisPipes');
      const records = await collection.query().fetch();

      if (records.length === 0) {
        console.log('No records found in pragati_pipes');
      }

      // records.forEach((pipe, i) => {
      //   console.log(`Record ${i + 1}:`, pipe.Pipe_ID);
      // });

      const firstFive = records.slice(0, 5);
      firstFive.forEach(pipe => console.log('Pipe:', pipe.Pipe_ID));

    } catch (err) {
      console.error('Error fetching records:', err);
    }
  }
}
