import { database } from '../index';
import { Q } from '@nozbe/watermelondb';
import WBSGISDetails from '../model/WBSGISDetails';
import { WbsGISDetailsItem, WbsJobItem } from '../../model/Wbs/WbsData';

const CHUNK_SIZE = 500;
export const StoreWbsGISDetailsData = async (gisDetailsData: WbsGISDetailsItem[], objJoblist: WbsJobItem[]) => {
    const wbsGISDetailsCollection = database.collections.get<WBSGISDetails>('WBSGISDetails');
    const jobCodes = objJoblist.map(job => job.jobCode);

    await database.write(async () => {
        // Delete all records for the job codes in a single query
        const existingRecords = await wbsGISDetailsCollection
            .query(Q.where('jobCode', Q.oneOf(jobCodes)))
            .fetch();

        if (existingRecords.length > 0) {
            await Promise.all(existingRecords.map(record => record.destroyPermanently()));
        }

        // Add new records
        // await Promise.all(gisDetailsData.map(item => {
        //     wbsGISDetailsCollection.create(record => {
        //         record.jobCode = item.JobCode;
        //         record.wbs = item.WBS;
        //         record.task = item.Task;
        //         record.deliverable = item.Deliverable;
        //         record.remarks = item.Remarks;
        //         record.boq = item.Boq;
        //         record.actualDate = item.ActualDate;
        //         record.nodeId = item.NodeId;
        //         record.nodeRefCode = item.NodeRefCode;
        //         record.fromLength = item.FromLength;
        //         record.toLength = item.ToLength;
        //         record.totalLength = item.TotalLength;
        //         record.manpower = item.Manpower;
        //         record.distanceFCenter = item.DistanceFCenter;
        //         record.alignment = item.Alignment;
        //         record.taskType = item.TaskType;
        //         record.scope = item.Scope;
        //         record.monthwisePlanQty = item.MonthwisePlanQty;
        //         record.etCode = item.Et_Code;
        //         record.deliverableTypeEtCode = item.DeliverableType_Et_Code;
        //         record.isHindrance = item.Is_Hindrance;
        //         record.detAssignedScopeQuantity = item.DET_Assigned_Scope_Quantity;
        //         record.detYearMonth = item.DET_Year_Month;
        //         record.tddIsEngineerTargetMandatory = item.TDD_Is_Engineer_Target_Mandatory;
        //         record.planedSDate = item.PlanedSDate;
        //         record.planedEDate = item.PlanedEDate;
        //         record.actualSDate = item.ActualSDate;
        //         record.actualEDate = item.ActualEDate;
        //     });
        // }));

        for (let i = 0; i < gisDetailsData.length; i += CHUNK_SIZE) {
            const chunk = gisDetailsData.slice(i, i + CHUNK_SIZE);
      
            const recordsToInsert = chunk.map(item =>
                wbsGISDetailsCollection.prepareCreate(record => {
                record.jobCode = item.JobCode;
                record.wbs = item.WBS;
                record.task = item.Task;
                record.deliverable = item.Deliverable;
                record.remarks = item.Remarks;
                record.boq = item.Boq;
                record.actualDate = item.ActualDate;
                record.nodeId = item.NodeId;
                record.nodeRefCode = item.NodeRefCode;
                record.fromLength = item.FromLength;
                record.toLength = item.ToLength;
                record.totalLength = item.TotalLength;
                record.manpower = item.Manpower;
                record.distanceFCenter = item.DistanceFCenter;
                record.alignment = item.Alignment;
                record.taskType = item.TaskType;
                record.scope = item.Scope;
                record.monthwisePlanQty = item.MonthwisePlanQty;
                record.etCode = item.Et_Code;
                record.deliverableTypeEtCode = item.DeliverableType_Et_Code;
                record.isHindrance = item.Is_Hindrance;
                record.detAssignedScopeQuantity = item.DET_Assigned_Scope_Quantity;
                record.detYearMonth = item.DET_Year_Month;
                record.tddIsEngineerTargetMandatory = item.TDD_Is_Engineer_Target_Mandatory;
                record.planedSDate = item.PlanedSDate;
                record.planedEDate = item.PlanedEDate;
                record.actualSDate = item.ActualSDate;
                record.actualEDate = item.ActualEDate;
              })
            );
      
            await database.batch(...recordsToInsert);
          }
    });
}; 