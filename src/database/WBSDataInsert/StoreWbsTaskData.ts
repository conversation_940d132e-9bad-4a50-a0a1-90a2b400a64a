import { Store } from 'redux';
import { database } from '../index';
import { Q } from '@nozbe/watermelondb';
import WBSTask from '../model/WBSTask';
import { WbsTaskItem, WbsJobItem } from '../../model/Wbs/WbsData';
import { EntityConstants } from '../../model/DailyProgress/DailyProgressData';

const CHUNK_SIZE = 500;
export const StoreWbsTaskDataUtil = {
    StoreWbsTaskData: async (taskData: WbsTaskItem[], objJoblist: WbsJobItem[]) => {
        const wbsTaskCollection = database.collections.get<WBSTask>('WBSTask');
        const jobCodes = objJoblist.map(job => job.jobCode);

        await database.write(async () => {
            // Delete all records for the job codes in a single query
            // const existingRecords = await wbsTaskCollection
            //     .query(Q.where('Job_Code', Q.oneOf(jobCodes)))
            //     .fetch();

            // if (existingRecords.length > 0) {
            //     await Promise.all(existingRecords.map(record => record.destroyPermanently()));
            // }

            // Add new records
            // await Promise.all(taskData.map(item => {
            //     wbsTaskCollection.create(record => {
            //         record.jobCode = item.Job_Code;
            //         record.taskCode = item.Task_Code;
            //         record.taskDescription = item.Task_Description;
            //         record.parentTaskCode = item.Parent_Task_Code;
            //         record.etCode = item.ET_Code;
            //         record.uomSymbol = item.UOM_Symbol || '';
            //         record.isActive = item.Is_Active;
            //         record.gisTag = item.Gis_Tag || '';
            //         record.parentWbs = item.Parent_WBS || '';
            //         record.parentTask = item.Parent_Task || '';
            //     });
            // }));

            // Insert in chunks
            console.log('StoreWbsTaskData -- taskData.length: ', taskData.length);
            for (let i = 0; i < taskData.length; i += CHUNK_SIZE) {

                const chunk = taskData.slice(i, i + CHUNK_SIZE);

                const recordsToInsert = chunk.map(item =>
                    wbsTaskCollection.prepareCreate(record => {
                        record.jobCode = item.Job_Code;
                        record.taskCode = item.Task_Code;
                        record.taskDescription = item.Task_Description;
                        record.parentTaskCode = item.Parent_Task_Code;
                        record.etCode = item.ET_Code;
                        record.uomSymbol = item.UOM_Symbol || '';
                        record.isActive = item.Is_Active;
                        record.gisTag = item.Gis_Tag || '';
                        record.parentWbs = item.Parent_WBS || '';
                        record.parentTask = item.Parent_Task || '';
                        record.entity_Type = EntityConstants.WBSTask;
                        // record.Parent_WBS_Code = item.Parent_WBS_Code;
                    })
                );

                await database.batch(...recordsToInsert);
            }
        });
    }
}
