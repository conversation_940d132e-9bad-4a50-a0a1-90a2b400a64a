import { database } from '../index';
import { Q } from '@nozbe/watermelondb';
import BookMarkList from '../model/BookMarkList';

// The input 'bookmarkData' is raw data from the API, not BookMarkList instances.
// Changing the type to 'any[]' makes it clear we're handling raw objects.
export const StoreBookMarkListData = async (bookmarkData: any[]) => {
    const bookmarkCollection = database.collections.get<BookMarkList>('BookMarkList');
    
    // Get job codes directly from the raw API data using the correct key.
    const jobCodes = [...new Set(bookmarkData.map(item => item.PRCGB_Job_Code))];

    try {
        await database.write(async () => {
            // Delete existing records for the job codes.
            const existingRecords = await bookmarkCollection
                // Use the actual database column name in the query.
                .query(Q.where('PRCGB_Job_Code', Q.oneOf(jobCodes)))
                .fetch();

            if (existingRecords.length > 0) {
                await Promise.all(existingRecords.map(record => record.destroyPermanently()));
            }
            console.log('bookmarkData.length:', bookmarkData.length, ' -- bookmarkData:', bookmarkData);

            // Create new records by mapping the raw API data to the model fields.
            await Promise.all(bookmarkData.map(item => {
                // Log the data to be inserted
                console.log('Preparing to insert:', {
                    jobCode: item.PRCGB_Job_Code,
                    wbsCode: item.PRCGB_WBS_Code,
                    userId: item.PRCGB_User_ID,
                    hierarchyLevel: item.PRCGB_Hierarchy_Level,
                    isActive: item.PRCGB_IsActive,
                    jobDescription: item.JOB_DESC,
                    ilCode: item.IL,
                    ilDescription: item.IL_DESC,
                    wpCode: item.WP,
                    wpDescription: item.WP_DESC,
                    childWorkCode: item.SWP,
                    childWorkDescription: item.CWP_DESC,
                    deliverableCode: item.DELIVERABLE_CODE,
                    deliverableCodeDesc: item.DELIVERABLE_CODE_DESC,
                    etCode: item.ET_CODE,
                });
                return bookmarkCollection.create(record => {
                    record.jobCode = item.PRCGB_Job_Code;
                    record.wbsCode = item.PRCGB_WBS_Code;
                    record.userId = item.PRCGB_User_ID;
                    record.hierarchyLevel = item.PRCGB_Hierarchy_Level;
                    record.isActive = item.PRCGB_IsActive;
                    record.jobDescription = item.JOB_DESC;
                    record.ilCode = item.IL;
                    record.ilDescription = item.IL_DESC;
                    record.wpCode = item.WP;
                    record.wpDescription = item.WP_DESC;
                    record.childWorkCode = item.SWP;
                    record.childWorkDescription = item.CWP_DESC;
                    record.deliverableCode = item.DELIVERABLE_CODE;
                    record.deliverableCodeDesc = item.DELIVERABLE_CODE_DESC;
                    record.etCode = item.ET_CODE;
                });
            }));

            // After all records are created
            const allRecords = await bookmarkCollection.query().fetch();
            console.log('All BookMarkList records after insert:', allRecords, 'Count:', allRecords.length);
        });
    } catch (error) {
        console.error("Error storing bookmark data:", error);
        throw error;
    }
};