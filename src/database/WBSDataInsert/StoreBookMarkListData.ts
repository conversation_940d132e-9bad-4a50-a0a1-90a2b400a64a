import { database } from '../index';
import { Q } from '@nozbe/watermelondb';
import BookMarkList from '../model/BookMarkList';

interface BookMarkListItem {
    PRCGB_Job_Code: string;
    PRCGB_WBS_Code: string;
    PRCGB_User_ID: number;
    PRCGB_Hierarchy_Level: string;
    PRCGB_IsActive: string;
}

export const StoreBookMarkListData = async (bookmarkData: BookMarkListItem[]) => {
    const bookmarkCollection = database.collections.get<BookMarkList>('BookMarkList');
    const jobCodes = [...new Set(bookmarkData.map(item => item.PRCGB_Job_Code))];

    try {
        await database.write(async () => {
            // Delete existing records for the job codes
            const existingRecords = await bookmarkCollection
                .query(Q.where('PRCGB_Job_Code', Q.oneOf(jobCodes)))
                .fetch();

            if (existingRecords.length > 0) {
                await Promise.all(existingRecords.map(record => record.destroyPermanently()));
            }

            // Add new records
            await Promise.all(bookmarkData.map(item => {
                bookmarkCollection.create(record => {
                    record.jobCode = item.PRCGB_Job_Code;
                    record.wbsCode = item.PRCGB_WBS_Code;
                    record.userId = item.PRCGB_User_ID;
                    record.hierarchyLevel = item.PRCGB_Hierarchy_Level;
                    record.isActive = item.PRCGB_IsActive;
                });
            }));
        });
    } catch (error) {
        throw error;
    }
}; 