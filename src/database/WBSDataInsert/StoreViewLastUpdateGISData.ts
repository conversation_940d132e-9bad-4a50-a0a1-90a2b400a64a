import { database } from '../index';
import { Q } from '@nozbe/watermelondb';
import ViewLastUpdateGIS from '../model/ViewLastUpdateGIS';

interface ViewLastUpdateGISItem {
    JOB: string;
    TDP_WBS_CODE: string;
    TDP_DELIVERABLE_CODE: number;
    NodeID: string;
    INSERTED_BY: number;
    Inserted_On: string;
    QTY: number;
    MAN: number;
    MUSGD_User_ID: number;
    Full_Name: string;
    Reference_ID: string;
    MUOM_Short_Description: string;
}

const CHUNK_SIZE = 500;
export const StoreViewLastUpdateGISData = async (gisData: ViewLastUpdateGISItem[]) => {
    const gisCollection = database.collections.get<ViewLastUpdateGIS>('ViewLastUpdateGIS');
    const jobCodes = [...new Set(gisData.map(item => item.JOB))];

    await database.write(async () => {
        // Delete all records for the job codes in a single query
        // const existingRecords = await gisCollection
        //     .query(Q.where('JOB', Q.oneOf(jobCodes)))
        //     .fetch();

        // if (existingRecords.length > 0) {
        //     await Promise.all(existingRecords.map(record => record.destroyPermanently()));
        // }

        // Add new records
        // await Promise.all(gisData.map(item => {
        //     gisCollection.create(record => {
        //         record.job = item.JOB;
        //         record.tdpWbsCode = item.TDP_WBS_CODE;
        //         record.tdpDeliverableCode = item.TDP_DELIVERABLE_CODE;
        //         record.nodeId = item.NodeID;
        //         record.insertedBy = item.INSERTED_BY;
        //         record.insertedOn = item.Inserted_On;
        //         record.qty = item.QTY;
        //         record.man = item.MAN;
        //         record.musgdUserId = item.MUSGD_User_ID;
        //         record.fullName = item.Full_Name;
        //         record.referenceId = item.Reference_ID;
        //         record.muomShortDescription = item.MUOM_Short_Description;
        //     });
        // }));

        for (let i = 0; i < gisData.length; i += CHUNK_SIZE) {
            const chunk = gisData.slice(i, i + CHUNK_SIZE);
      
            const recordsToInsert = chunk.map(item =>
                gisCollection.prepareCreate(record => {
                    record.job = item.JOB;
                    record.tdpWbsCode = item.TDP_WBS_CODE;
                    record.tdpDeliverableCode = item.TDP_DELIVERABLE_CODE;
                    record.nodeId = item.NodeID;
                    record.insertedBy = item.INSERTED_BY;
                    record.insertedOn = item.Inserted_On;
                    record.qty = item.QTY;
                    record.man = item.MAN;
                    record.musgdUserId = item.MUSGD_User_ID;
                    record.fullName = item.Full_Name;
                    record.referenceId = item.Reference_ID;
                    record.muomShortDescription = item.MUOM_Short_Description;
              })
            );
      
            await database.batch(...recordsToInsert);
          }
    });
}; 