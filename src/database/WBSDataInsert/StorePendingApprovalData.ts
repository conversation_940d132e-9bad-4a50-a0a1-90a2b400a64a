import { database } from '../index';
import { Q } from '@nozbe/watermelondb';
import PendingForApproval from '../model/PendingForApprovalBQIT';
import { WbsJobItem, WbsPendingApprovalModal } from '../../model/Wbs/WbsData';

const CHUNK_SIZE = 500;

export const StorePendingApproval = async (
  pendingApprovalData: WbsPendingApprovalModal[],
  objJoblist: WbsJobItem[]
) => {
  const wbsPendingApprovalCollection = database.collections.get<PendingForApproval>('PendingForApprovalBQIT');
  const jobCodes = objJoblist.map(job => job.jobCode);

  await database.write(async () => {
    // Delete existing records for given jobs
    const existingRecords = await wbsPendingApprovalCollection
      .query(Q.where('JOB', Q.oneOf(jobCodes)))
      .fetch();

    if (existingRecords.length > 0) {
      await database.batch(...existingRecords.map(record => record.prepareDestroyPermanently()));
    }

    // Insert in chunks
    for (let i = 0; i < pendingApprovalData.length; i += CHUNK_SIZE) {
      const chunk = pendingApprovalData.slice(i, i + CHUNK_SIZE);

      const recordsToInsert = chunk.map(item =>
        wbsPendingApprovalCollection.prepareCreate(record => {
          record.job = item.JOB;
          record.wbs = item.WBS;
          record.task = item.Task;
          record.tDate = item.TDate;
          record.userId = item.UserID;
          record.userName = item.UserName;
          record.quantity = item.Quantity;
          record.manpower = item.Manpower;
          record.remarks = item.Remarks;
          record.taskType = item.TaskType;
          record.latitude = item.Latitude;
          record.longitude = item.Longitude;
          record.totalQuantity = item.TotalQuantity;
          record.actualQuantity = item.ActualQuantity;
          record.wbsDescription = item.WBS_Description;
          record.wbsCustomDescription = item.WBS_Custom_Description;
          record.taskDescription = item.Task_Description;
          record.taskCustomDescription = item.Task_Custom_Description;
          record.imageId = item.Image_ID;
          record.imageUrl = item.Image_URL;
          record.uom = item.UOM;
          record.nodeId = item.NodeId;
          record.referanceNodeId = item.ReferanceNodeId;
          record.fromLength = item.From_Length;
          record.progressLength = item.Progress_Length;
          record.totalLength = item.Total_Length;
          record.distanceFromCenter = item.Distance_From_Center;
          record.alignment = item.Alignment;
          record.isCompleted = item.IS_Completed;
          record.isHindrance = item.Is_Hindrance;
        })
      );

      await database.batch(...recordsToInsert);
    }
  });
};
