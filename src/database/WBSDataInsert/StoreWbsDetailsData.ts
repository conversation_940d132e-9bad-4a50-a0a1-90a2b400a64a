import { database } from '../index';
import { Q } from '@nozbe/watermelondb';
import WBSDetails from '../model/WBSDetails';
import { WbsDetailsItem, WbsJobItem } from '../../model/Wbs/WbsData';

const CHUNK_SIZE = 500;
export const StoreWbsDetailsData = async (detailsData: WbsDetailsItem[], objJoblist: WbsJobItem[]) => {
    const wbsDetailsCollection = database.collections.get<WBSDetails>('WBSDetails');
    const jobCodes = objJoblist.map(job => job.jobCode);

    await database.write(async () => {
        // Delete all records for the job codes in a single query
        const existingRecords = await wbsDetailsCollection
            .query(Q.where('jobCode', Q.oneOf(jobCodes)))
            .fetch();

        // if (existingRecords.length > 0) {
        //     await Promise.all(existingRecords.map(record => record.destroyPermanently()));
        // }

        // Add new records
        // await Promise.all(detailsData.map(item => {
        //     wbsDetailsCollection.create(record => {
        //         record.jobCode = item.JobCode;
        //         record.wbs = item.WBS;
        //         record.task = item.Task;
        //         record.planedQty = item.PlanedQty;
        //         record.planedSDate = item.PlanedSDate;
        //         record.planedEDate = item.PlanedEDate;
        //         record.planedLabour = item.PlanedLabour;
        //         record.actualQty = item.ActualQty;
        //         record.actualSDate = item.ActualSDate;
        //         record.actualEDate = item.ActualEDate;
        //         record.actualLabour = item.ActualLabour;
        //         record.lastUpdatedDate = item.LastUpdatedDate;
        //         record.lastUpdatedQty = item.LastUpdatedQty;
        //         record.lastUpdatedLabour = item.LastUpdatedLabour;
        //         record.remarks = item.Remarks;
        //         record.taskType = item.TaskType;
        //         record.minProductivityRange = item.Min_Productivity_Range;
        //         record.maxProductivityRange = item.Max_Productivity_Range;
        //         record.scope = item.Scope;
        //         record.monthwisePlanQty = item.MonthwisePlanQty;
        //         record.etCode = item.Et_Code;
        //         record.deliverableTypeEtCode = item.DeliverableType_Et_Code;
        //         record.detAssignedScopeQuantity = item.DET_Assigned_Scope_Quantity;
        //         record.detYearMonth = item.DET_Year_Month;
        //         record.tddIsEngineerTargetMandatory = item.TDD_Is_Engineer_Target_Mandatory;
        //     });
        // }));
        for (let i = 0; i < detailsData.length; i += CHUNK_SIZE) {
            const chunk = detailsData.slice(i, i + CHUNK_SIZE);
      
            const recordsToInsert = chunk.map(item =>
                wbsDetailsCollection.prepareCreate(record => {
                    record.jobCode = item.JobCode;
                    record.wbs = item.WBS;
                    record.task = item.Task;
                    record.planedQty = item.PlanedQty;
                    record.planedSDate = item.PlanedSDate;
                    record.planedEDate = item.PlanedEDate;
                    record.planedLabour = item.PlanedLabour;
                    record.actualQty = item.ActualQty;
                    record.actualSDate = item.ActualSDate;
                    record.actualEDate = item.ActualEDate;
                    record.actualLabour = item.ActualLabour;
                    record.lastUpdatedDate = item.LastUpdatedDate;
                    record.lastUpdatedQty = item.LastUpdatedQty;
                    record.lastUpdatedLabour = item.LastUpdatedLabour;
                    record.remarks = item.Remarks;
                    record.taskType = item.TaskType;
                    record.minProductivityRange = item.Min_Productivity_Range;
                    record.maxProductivityRange = item.Max_Productivity_Range;
                    record.scope = item.Scope;
                    record.monthwisePlanQty = item.MonthwisePlanQty;
                    record.etCode = item.Et_Code;
                    record.deliverableTypeEtCode = item.DeliverableType_Et_Code;
                    record.detAssignedScopeQuantity = item.DET_Assigned_Scope_Quantity;
                    record.detYearMonth = item.DET_Year_Month;
                    record.tddIsEngineerTargetMandatory = item.TDD_Is_Engineer_Target_Mandatory;
              })
            );
      
            await database.batch(...recordsToInsert);
          }
        
    });
}; 