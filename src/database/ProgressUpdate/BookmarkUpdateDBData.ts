import { Q } from '@nozbe/watermelondb';
import { database } from '../index';
import BookMarkList from '../model/BookmarkList';
import UnsyncedBookmarkList from '../model/UnsyncedBookmarkList';

// // This function was incomplete, causing the export error. I've given it a body.
// export const InsertProgressUpdateDetails = async (data: any) => {
//     // TODO: Implement this function's logic
// };

export interface UnsyncedBookmarkData {
    jobCode: string,
    wbsCode: string,
    userId: number,
    hierarchyLevel: string, // or derive as needed
    isActive: string,
    jobDescription: string,
    ilCode: string,
    ilDescription: string,
    wpCode: string,
    wpDescription: string,
    childWorkCode: string,
    childWorkDescription: string,
    deliverableCode: number,
    deliverableCodeDesc: string,
    etCode: string,
    type: string,
    wpCodeForSync: string,
}

export const saveBookmarkToLocalDB = async (bookmarkData: UnsyncedBookmarkData) => {
    try {
        await database.write(async () => {
            await database.get('UnsyncedBookmarkList').create((record) => {
                const bookmarkRecord = record as UnsyncedBookmarkList;
                bookmarkRecord.jobCode = bookmarkData.jobCode;
                bookmarkRecord.wbsCode = bookmarkData.wbsCode;
                bookmarkRecord.userId = bookmarkData.userId;
                bookmarkRecord.hierarchyLevel = '';
                bookmarkRecord.isActive = bookmarkData.isActive;
                bookmarkRecord.jobDescription = bookmarkData.jobDescription;
                bookmarkRecord.ilCode = bookmarkData.ilCode;
                bookmarkRecord.ilDescription = bookmarkData.ilDescription;
                bookmarkRecord.wpCode = bookmarkData.wpCode;
                bookmarkRecord.wpDescription = bookmarkData.wpDescription;
                bookmarkRecord.childWorkCode = bookmarkData.childWorkCode;
                bookmarkRecord.childWorkDescription = bookmarkData.childWorkDescription;
                bookmarkRecord.deliverableCode = bookmarkData.deliverableCode;
                bookmarkRecord.deliverableCodeDesc = bookmarkData.deliverableCodeDesc;
                bookmarkRecord.etCode = bookmarkData.etCode;
                bookmarkRecord.type = bookmarkData.type;
                bookmarkRecord.wpCodeForSync = bookmarkData.wpCodeForSync;
            });
        });
        console.log('Bookmark saved to local database for offline sync');
        return true;
    } catch (error) {
        console.error('Error saving bookmark to local DB:', error);
        return false;
    }
};

export const getPendingBookmarks = async () => {
    try {
        const pendingBookmarks = await database
            .get('BookMarkList')
            .query(Q.where('PRCGB_IsActive', 'Y'))
            .fetch();
        return pendingBookmarks;
    } catch (error) {
        console.error('Error fetching pending bookmarks:', error);
        return [];
    }
};

// export const markBookmarkAsSynced = async (bookmarkId: string) => {
//     try {
//         await database.write(async () => {
//             const bookmark = await database.get('BookMarkList').find(bookmarkId);
//             await bookmark.destroyPermanently();
//         });
//     } catch (error) {
//         console.error('Error marking bookmark as synced:', error);
//     }
// };

export const updateBookmarkStatus = async (bookmarkId: string, isActive: boolean) => {
  try {
      await database.write(async () => {
          const bookmarkToUpdate = await database.get<BookMarkList>('BookMarkList').find(bookmarkId);
          if (bookmarkToUpdate) {
              await bookmarkToUpdate.update(record => {
                  record.isActive = isActive ? 'Y' : 'N';
              });
              console.log(`Bookmark ${bookmarkId} status updated to ${isActive ? 'Y' : 'N'}`);
          }
      });
  } catch (error) {
      console.error('Error updating bookmark status:', error);
  }
};