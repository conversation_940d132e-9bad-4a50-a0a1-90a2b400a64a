import { database } from '../index'

export const InsertProgressUpdateDetails = async (data: any) => {
  // Validate data structure
  if (!data || !data.inputDetails) {
    throw new Error('Invalid data structure: inputDetails is required');
  }

  if (!data.parentItems || !Array.isArray(data.parentItems)) {
    throw new Error('Invalid data structure: parentItems array is required');
  }

  const inputDetailCollection = database.get('InputDetails')
  const parentItemsCollection = database.get('ParentItems')

  try {
    await database.write(async () => {
      const inputDetail = await inputDetailCollection.create((record: any) => {
        const d = data.inputDetails;

        // Use inputDetails fields as-is with null safety
        record.Job_Code = d?.JobCode || '';
        record.Wbs = d?.WBS || '';
        record.Task = d?.Task || '';
        record.Planned_Qty = d?.PlanedQty || '';
        record.Planned_SDate = d?.PlanedSDate || '';
        record.Planned_EDate = d?.PlanedEDate || '';
        record.Planned_Labour = d?.PlanedLabour || '';
        record.Actual_Qty = d?.ActualQty || '';
        record.Actual_SDate = d?.ActualSDate || '';
        record.Actual_EDate = d?.ActualEDate || '';
        record.Actual_Labour = d?.ActualLabour || '';
        record.Last_Updated_Date = d?.LastUpdatedDate || '';
        record.Last_Updated_Qty = d?.LastUpdatedQty || '';
        record.Last_Updated_Labour = d?.LastUpdatedLabour || '';
        record.Remarks = d?.Remarks || '';
        record.Task_Type = d?.TaskType || '';
        record.Min_Productivity_Range = d?.Min_Productivity_Range || '';
        record.Max_Productivity_Range = d?.Max_Productivity_Range || '';
        record.Scope = d?.Scope || '';
        record.Monthwise_Plan_Qty = d?.MonthwisePlanQty || '';
        record.Et_Code = d?.Et_Code || '';
        record.Deliverable_Type_Et_Code = d?.DeliverableType_Et_Code || '';
        record.Det_Assigned_Scope_Quantity = d?.DET_Assigned_Scope_Quantity || '';
        record.Det_Year_Month = d?.DET_Year_Month || '';
        record.Tdd_Is_Engineer_Target_Mandatory = d?.TDD_Is_Engineer_Target_Mandatory || '';
        record.Full_Desc = d?.Full_Desc || '';
        record.Wbs_Path = d?.wbsPath || '';
        record.Selected_Item = data.selectedItem ? JSON.stringify(data.selectedItem) : '';
        record.Man_Days = d?.ManDays || '';
        record.Date = d?.date || '';

      });

      for (const item of data.parentItems) {
        if (item) {
          await parentItemsCollection.create((parent: any) => {
            parent.Wbs_Code = item.WBS_Code || '';
            parent.Wbs_Description = item.WBS_Description || '';
            parent.Et_Code = item.ET_Code || '';
            // Comment out the relationship setting that's causing issues
            // parent.inputDetail.set(inputDetail);
            // ISSUE: parent.inputDetail is undefined - the ParentItems model doesn't have 
            // the inputDetail relationship field defined in the database schema.
            // This causes "TypeError: Cannot read property 'type' of undefined" error.
            // To fix: Add the relationship field to ParentItems model in database schema.
          });
        }
      }

    });

  } catch (error) {
    throw error; // Re-throw to let the calling function know there was an error
  }
};


export const logInputDetails = async () => {
  try {
    console.log('Fetching InputDetails...');
    const inputDetailCollection = database.get('InputDetails');
    const records = await inputDetailCollection.query().fetch();

    if (records.length === 0) {
      console.log('No records found in InputDetails');
    } else {
      console.log('InputDetails records:', records);
    }
  } catch (error) {
    console.error('Error fetching InputDetails:', error);
  }
};

export const logParentItems = async () => {
  const parentItemCollection = database.get('ParentItems');
  const parentRecords = await parentItemCollection.query().fetch();

  console.log('All ParentItems:', JSON.stringify(parentRecords, null, 2));
};