import { getUserInfo } from '../../utils/DataStorage/Storage';
import { database } from '../index'

export const InsertProgressUpdateDetails = async (data: any) => {
  const inputDetailCollection = database.get('InputDetails')
  const parentItemsCollection = database.get('ParentItems')
  const pendingApprovalCollection = database.get('PendingForApprovalBQIT')
  const user = getUserInfo();

  const d = data.inputDetails;
console.log('InsertProgressUpdateDetails -- data -- d: ', d);
  await database.write(async () => {

    // First, check if a record with the same WBS value exists
    const existingRecords = await inputDetailCollection
      .query()
      .fetch();

    const existingRecord = existingRecords.find(record => record.Wbs === d.WBS);

    // If record exists, delete related ParentItems first, then delete the InputDetails record
    if (existingRecord) {
      // Delete related ParentItems records
      const relatedParentItems = await parentItemsCollection
        .query()
        .fetch();

      const parentItemsToDelete = relatedParentItems.filter(parentItem =>
        (parentItem as any).Input_Detail_Id === existingRecord.id
      );

      if (parentItemsToDelete.length > 0) {
        for (const parentItem of parentItemsToDelete) {
          await parentItem.destroyPermanently();
        }
      }

      // Delete the InputDetails record
      await existingRecord.destroyPermanently();
    }

    // Create the new record
    const inputDetail = await inputDetailCollection.create((record: any) => {
      record.Job_Code = d.JobCode;
      record.Wbs = d.WBS;
      record.Task = d.Task;
      record.Planned_Qty = d.PlanedQty;
      record.Planned_SDate = d.PlanedSDate;
      record.Planned_EDate = d.PlanedEDate;
      record.Planned_Labour = d.PlanedLabour;
      record.Actual_Qty = d.ActualQty;
      record.Actual_SDate = d.ActualSDate;
      record.Actual_EDate = d.ActualEDate;
      record.Actual_Labour = d.ActualLabour;
      record.Last_Updated_Date = d.LastUpdatedDate;
      record.Last_Updated_Qty = d.LastUpdatedQty;
      record.Last_Updated_Labour = d.LastUpdatedLabour;
      record.Remarks = d.Remarks;
      record.Task_Type = d.TaskType;
      record.Min_Productivity_Range = d.Min_Productivity_Range;
      record.Max_Productivity_Range = d.Max_Productivity_Range;
      record.Scope = d.Scope;
      record.Monthwise_Plan_Qty = d.MonthwisePlanQty;
      record.Et_Code = d.Et_Code;
      record.Deliverable_Type_Et_Code = d.DeliverableType_Et_Code;
      record.Det_Assigned_Scope_Quantity = d.DET_Assigned_Scope_Quantity;
      record.Det_Year_Month = d.DET_Year_Month;
      record.Tdd_Is_Engineer_Target_Mandatory = d.TDD_Is_Engineer_Target_Mandatory;
      record.Full_Desc = d.Full_Desc;
      record.Wbs_Path = data.wbsPath;
      record.Selected_Item = JSON.stringify(data.selectedItem);
      record.Man_Days = d.ManDays;
      record.Date = d.date;
    });

    // Create all parent items concurrently
    const parentItemPromises = data.parentItems.map(async (item: any) => {
      return await parentItemsCollection.create((parent: any) => {
        parent.Wbs_Code = item.WBS_Code;
        parent.Wbs_Description = item.WBS_Description;
        parent.Et_Code = item.ET_Code;
        parent.Input_Detail_Id = inputDetail.id; // Set the foreign key directly
      });
    });

    await Promise.all(parentItemPromises);
  });

  // Second separate async write operation
  await database.write(async () => {
    // Check and delete existing PendingForApprovalBQIT records with the same WBS
    const existingPendingRecords = await pendingApprovalCollection
      .query()
      .fetch();

    const existingPendingRecord = existingPendingRecords.find((record: any) => record.wbs === d.WBS);

    if (existingPendingRecord) {
      await existingPendingRecord.destroyPermanently();
    }

    await pendingApprovalCollection.create((pendingRecord: any) => {
      pendingRecord.job = d.JobCode;
      pendingRecord.wbs = d.WBS;
      pendingRecord.task = d.Task;
      pendingRecord.tDate = d.date;
      pendingRecord.userId = user?.UID;
      pendingRecord.userName = user?.UserName;
      pendingRecord.quantity = d.PlanedQty;
      pendingRecord.manpower = d.ManDays;
      pendingRecord.remarks = d.Remarks;
      pendingRecord.taskType = d.TaskType;
      pendingRecord.latitude = d.Latitude || 13.02042;
      pendingRecord.longitude = d.Longitude || 80.17819;
      pendingRecord.totalQuantity = d.TotalQuantity;
      pendingRecord.actualQuantity = d.ActualQty;
      pendingRecord.wbsDescription = d.WBS_Description;
      pendingRecord.wbsCustomDescription = d.WBS_Custom_Description;
      pendingRecord.taskDescription = d.Task_Description;
      pendingRecord.taskCustomDescription = d.Task_Custom_Description;
      pendingRecord.imageId = d.Image_ID || null;
      pendingRecord.imageUrl = d.Image_URL || null;
      pendingRecord.uom = d.UOM || '';
      pendingRecord.nodeId = d.NodeId || null;
      pendingRecord.referanceNodeId = d.ReferanceNodeId || null;
      pendingRecord.fromLength = d.From_Length || null;
      pendingRecord.progressLength = d.Progress_Length || null;
      pendingRecord.totalLength = d.Total_Length || null;
      pendingRecord.distanceFromCenter = d.Distance_From_Center || null;
      pendingRecord.alignment = d.Alignment || null;
      pendingRecord.isCompleted = d.IS_Completed || null;
      pendingRecord.isHindrance = d.Is_Hindrance || null;
    });
  });
};


export const logInputDetails = async () => {
  try {
    console.log('Fetching InputDetails...');
    const inputDetailCollection = database.get('InputDetails');
    const records = await inputDetailCollection.query().fetch();

    if (records.length === 0) {
      console.log('No records found in InputDetails');
    } else {
      console.log('InputDetails records:', records);
    }
  } catch (error) {
    console.error('Error fetching InputDetails:', error);
  }
};

export const logParentItems = async () => {
  const parentItemCollection = database.get('ParentItems');
  const parentRecords = await parentItemCollection.query().fetch();

  console.log('All ParentItems:', parentRecords);
};