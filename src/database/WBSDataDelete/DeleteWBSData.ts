import { database } from '../index';
import { Q } from '@nozbe/watermelondb';
import WBSJob from '../model/WBSJob';
import WBSTask from '../model/WBSTask';
import WBSDetails from '../model/WBSDetails';
import WBSGISDetails from '../model/WBSGISDetails';
import BookMarkList from '../model/BookMarkList';
import LatLongHierarchy from '../model/LatLongHierarchy';
import ViewLastUpdateBQIT from '../model/ViewLastUpdateBQIT';
import ViewLastUpdateGIS from '../model/ViewLastUpdateGIS';
import ProgressDetailsConsolidated from '../model/ProgressDetailsConsolidated';
import ProgressUpdateEngineer from '../model/ProgressUpdateEngineer';

export const deleteWBSDataForJob = async (jobCode: string): Promise<boolean> => {
    try {
        await database.write(async () => {
            // Fetch all data in parallel
            const [
                wbsJobs,
                wbsTasks,
                wbsDetails,
                wbsGisDetails,
                bookmarks,
                latLongs,
                bqitUpdates,
                gisUpdates,
                progressConsolidated,
                progressEngineer
            ] = await Promise.all([
                database.get<WBSJob>('WBSJob').query(Q.where('Job_Code', jobCode)).fetch(),
                database.get<WBSTask>('WBSTask').query(Q.where('Job_Code', jobCode)).fetch(),
                database.get<WBSDetails>('WBSDetails').query(Q.where('JobCode', jobCode)).fetch(),
                database.get<WBSGISDetails>('WBSGISDetails').query(Q.where('JobCode', jobCode)).fetch(),
                database.get<BookMarkList>('BookMarkList').query(Q.where('PRCGB_Job_Code', jobCode)).fetch(),
                database.get<LatLongHierarchy>('LatLongHierarchy').query(Q.where('PRCLLH_Job_Code', jobCode)).fetch(),
                database.get<ViewLastUpdateBQIT>('ViewLastUpdateBQIT').query(Q.where('JOB', jobCode)).fetch(),
                database.get<ViewLastUpdateGIS>('ViewLastUpdateGIS').query(Q.where('JOB', jobCode)).fetch(),
                database.get<ProgressDetailsConsolidated>('ProgressUpdateConsolidated').query(Q.where('TDD_Job_Code', jobCode)).fetch(),
                database.get<ProgressUpdateEngineer>('ProgressUpdateEngineer').query(Q.where('TDD_Job_Code', jobCode)).fetch()
            ]);

            // Combine all records to delete
            const allRecordsToDelete = [
                ...wbsJobs,
                ...wbsTasks,
                ...wbsDetails,
                ...wbsGisDetails,
                ...bookmarks,
                ...latLongs,
                ...bqitUpdates,
                ...gisUpdates,
                ...progressConsolidated,
                ...progressEngineer
            ];

            // Use batch operation for better performance
            await database.batch(
                ...allRecordsToDelete.map(record => record.prepareDestroyPermanently())
            );
        });

        return true;
    } catch (error) {
        return false;
    }
}; 