export interface SyncDataHeaderProps {
    id: number;
    isActive: boolean;
    tabName: string;
    onPress: any;
};
export interface subCategory {
    catergory: string;
    data: string;
};
export interface DayPlanningCategoryDetails {
    id: number;
    name: string;
    subCategory?: subCategory[];
};

export interface AssetDetailsProps {
    id: number;
    name: string;
    fromDate: string;
    startTime: string;
    toDate: string;
    endTime: string;
    usage: string;
    reason: string;
};

export interface ProgressUpdateData {
    id: number;
    name: string;
    data: string;
}