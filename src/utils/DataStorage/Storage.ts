import { MMKV } from 'react-native-mmkv';
import { LoginResponseItem } from '../../model/Auth/LoginData';
import { RolesResponseData } from '../../model/Auth/RolesData';

export const storage = new MMKV();

export const TOKEN_KEY = 'TOKEN_KEY';
export const saveToken = (token: string) => storage.set(TOKEN_KEY, token);
export const getToken = () => storage.getString(TOKEN_KEY);
export const removeToken = () => {
    storage.delete(TOKEN_KEY);
}

export const USER_INFO_KEY = 'USER_INFO_KEY';
export const saveUserInfo = (userInfo: LoginResponseItem) => {
    storage.set(USER_INFO_KEY, JSON.stringify(userInfo));
};
export const getUserInfo = (): LoginResponseItem | null => {
    const data = storage.getString(USER_INFO_KEY);
    return data ? JSON.parse(data) as LoginResponseItem : null;
};
export const removeUserInfo = () => {
    storage.delete(USER_INFO_KEY);
}

export const USER_ROLES_INFO_KEY = 'USER_ROLES_INFO_KEY';
export const saveUserRolesInfo = (userRolesInfo: RolesResponseData) => {
    storage.set(USER_ROLES_INFO_KEY, JSON.stringify(userRolesInfo));
};
export const getUserRolesInfo = ():RolesResponseData | null => {
    const data = storage.getString(USER_ROLES_INFO_KEY);
    return data ? JSON.parse(data) as RolesResponseData : null;
};
export const removeUserRolesInfo = () => {
    storage.delete(USER_ROLES_INFO_KEY);
}

export const USER_LAST_LOGGED_IN_DATE = 'USER_LAST_LOGGED_IN_DATE';

export const setUserLastLoggedInDate = (lastLoggedInDate: string) => {
    storage.set(USER_LAST_LOGGED_IN_DATE, lastLoggedInDate);
};
export const getUserLastLoggedInDate = () => storage.getString(USER_LAST_LOGGED_IN_DATE);

export const clearAllData = () => {
    storage.clearAll();
    console.log('Data after clear:', {
      token: getToken(),
      userInfo: getUserInfo(),
      roles: getUserRolesInfo(),
    });
  };