type FormattedDate = {
    shortDate: string | null;
    fullDate: string | null;
    time: string | null;
  };
  
  export function FormatDate(isoString: string): FormattedDate {
    const date = new Date(isoString);
  
    if (isNaN(date.getTime())) {
      return {
        shortDate: null,
        fullDate: null,
        time: null,
      };
    }
  
    const pad = (n: number): string => String(n).padStart(2, '0');
  
    const day = pad(date.getDate());
    const month = pad(date.getMonth() + 1);
    const year = date.getFullYear();
    const shortYear = String(year).slice(-2);
  
    const time = new Intl.DateTimeFormat('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    }).format(date);
  
    return {
      shortDate: `${day}/${month}/${shortYear}`,     // DD/MM/YY
      fullDate: `${day}/${month}/${year}`,           // DD/MM/YYYY
      time,                                          // HH:MM AM/PM
    };
  };

  export function FormatMonthDate(isoString: string): FormattedDate {
    const date = new Date(isoString);
  
    if (isNaN(date.getTime())) {
      return {
        shortDate: null,
        fullDate: null,
        time: null,
      };
    }
  
    const pad = (n: number): string => String(n).padStart(2, '0');
  
    const day = pad(date.getDate());
    const year = date.getFullYear();
    const shortYear = String(year).slice(-2);
  
    const monthShort = date.toLocaleString('en-US', { month: 'short' }); // e.g. Jun
  
    const time = new Intl.DateTimeFormat('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    }).format(date);
  
    return {
      shortDate: `${day}/${monthShort}/${shortYear}`,   // e.g. 19/Jun/25
      fullDate: `${day}/${monthShort}/${year}`,         // e.g. 19/Jun/2025
      time,                                             // e.g. 02:45 PM
    };
  }
  


  