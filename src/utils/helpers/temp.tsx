
export const hindranceData = [
    {
        id: '1',
        name: '<PERSON><PERSON>',
        details: 'Package I / OHSR / 100KL / 18M / Jawada / Plinth Protection',
        progressQty: '25',
        manDays: '10',
        date: '11/05/2025',
        latitude: 28.6139,
        longitude: 77.209,
        Open_Active: "N",
        New_Gap: "Y",
        Progress_Update: "Y",
        GAP_Length: 649.0,
        GapLength_Updated: 0.0,
        Remarks: "Along Railway Track",
        LGD_Latitude_End: null,
        LGD_Longitude_End: null,
        LGD_Start_End_Node: null,
        LGD_Gap_Reason: null,
        Classification_Type_Detail_Code: null,
        Classification_Type_Detail_Description: null,
    },
    {
        id: '2',
        name: '<PERSON><PERSON>',
        details: 'Package II / OHSR / 200KL / 25M / Samira / Concrete Works',
        progressQty: '30',
        manDays: '20',
        date: '12/15/2025',
        latitude: 28.7041,
        longitude: 77.1025,
        Open_Active: "N",
        New_Gap: "N",
        Progress_Update: "N",
        GAP_Length: 649.0,
        GapLength_Updated: 0.0,
        Remarks: "Along Railway Track",
        LGD_Latitude_End: null,
        LGD_Longitude_End: null,
        LGD_Start_End_Node: null,
        LGD_Gap_Reason: null,
        Classification_Type_Detail_Code: null,
        Classification_Type_Detail_Description: null,
    },
    {
        id: '3',
        name: 'Deepak Mehta',
        details: 'Package IV / OHSR / 120KL / 10M / Anita / Pavement Works',
        progressQty: '15',
        manDays: '12',
        date: '12/01/2025',
        latitude: 28.4595,
        longitude: 77.0266,
        Open_Active: "N",
        New_Gap: "Y",
        Progress_Update: "Y",
        GAP_Length: 649.0,
        GapLength_Updated: 0.0,
        Remarks: "Along Railway Track",
        LGD_Latitude_End: null,
        LGD_Longitude_End: null,
        LGD_Start_End_Node: null,
        LGD_Gap_Reason: null,
        Classification_Type_Detail_Code: null,
        Classification_Type_Detail_Description: null,
    },
    {
        id: '4',
        name: 'Suresh Patel',
        details: 'Package III / Pipeline / 150M / 30M / Pipeline Installation',
        progressQty: '45',
        manDays: '25',
        date: '12/20/2025',
        latitude: 28.5895,
        longitude: 77.0895,
        Open_Active: "N",
        New_Gap: "Y",
        Progress_Update: "Y",
        GAP_Length: 649.0,
        GapLength_Updated: 200.0,
        Remarks: "Along Railway Track",
        LGD_Latitude_End: 28.5995,
        LGD_Longitude_End: 77.0995,
        LGD_Start_End_Node: null,
        LGD_Gap_Reason: null,
        Classification_Type_Detail_Code: null,
        Classification_Type_Detail_Description: null,
    },
    {
        id: '5',
        name: 'Rahul Sharma',
        details: 'Package V / Road / 200M / 40M / Road Construction',
        progressQty: '60',
        manDays: '35',
        date: '12/25/2025',
        latitude: 28.5245,
        longitude: 77.0566,
        Open_Active: "N",
        New_Gap: "N",
        Progress_Update: "Y",
        GAP_Length: 649.0,
        GapLength_Updated: 80.4,
        Remarks: "Along Railway Track",
        LGD_Latitude_End: 28.5345,
        LGD_Longitude_End: 77.0666,
        LGD_Start_End_Node: null,
        LGD_Gap_Reason: null,
        Classification_Type_Detail_Code: null,
        Classification_Type_Detail_Description: null,
    },
    {
        id: '6',
        name: 'Vikram Singh',
        details: 'Package VI / Bridge / 100M / 20M / Bridge Construction',
        progressQty: '35',
        manDays: '28',
        date: '12/30/2025',
        latitude: 28.4745,
        longitude: 77.0166,
        Open_Active: "Y",
        New_Gap: "Y",
        Progress_Update: "N",
        GAP_Length: 649.0,
        GapLength_Updated: 400.0,
        Remarks: "Along Railway Track",
        LGD_Latitude_End: 28.4845,
        LGD_Longitude_End: 77.0266,
        LGD_Start_End_Node: null,
        LGD_Gap_Reason: null,
        Classification_Type_Detail_Code: null,
        Classification_Type_Detail_Description: null,
    }
];
