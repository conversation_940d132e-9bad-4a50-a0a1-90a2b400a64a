# Design Token System Documentation

## Overview

This document provides comprehensive documentation for the ePragati application's design token system, focusing on color constants and their implementation across the application ecosystem.

## Color System Architecture

### 1. Brand Colors
Core brand identity colors that establish the visual foundation of the application.

| Color Name | Hex Value | Usage Context | Accessibility Notes |
|------------|-----------|---------------|-------------------|
| `primary` | `#33485E` | Main actions, headers, key UI elements | High contrast, meets WCAG AA |
| `secondary` | `#052669` | Secondary actions and accents | Good contrast for interactive elements |
| `brandBlue` | `#0775CE` | Interactive elements and links | Excellent contrast ratio |
| `darkBlue` | `#373D4B` | Gradients and emphasis | Used in gradients for depth |
| `appTitleBackground` | `#17408B` | App header branding | Strong brand presence |
| `forgotPinBlue` | `#007AFF` | Password recovery flows | iOS-style blue for familiarity |

### 2. Neutral Colors
Grays and basic colors for structural elements and subtle UI components.

| Color Name | Hex Value | Usage Context | Accessibility Notes |
|------------|-----------|---------------|-------------------|
| `white` | `#FFFFFF` | Backgrounds and text on dark surfaces | Pure white for maximum contrast |
| `black` | `#000000` | Text and icons on light surfaces | Pure black for maximum contrast |
| `offWhite` | `#FAF9F6` | Subtle backgrounds | Warm white for better UX |
| `grey` | `#E1E1ED` | Borders and dividers | Light gray for subtle separation |
| `labelBlue` | `#6B7280` | Secondary text and icons | Medium gray for hierarchy |
| `lightBlack` | `#747980` | Tertiary text | Dark gray for muted content |
| `circleGrey` | `#CDD8EC` | Circular indicators | Light blue-gray for indicators |
| `verticalLineColor` | `#D1D5DB` | Dividers | Neutral gray for visual separation |

### 3. Text Colors
Typography color hierarchy for maintaining readable content hierarchy.

| Color Name | Hex Value | Usage Context | Accessibility Notes |
|------------|-----------|---------------|-------------------|
| `textPrimary` | `#03101F` | Main content and headings | Very dark blue for excellent readability |
| `textSecondary` | `#7B8D9E` | Descriptions and captions | Medium gray for secondary content |
| `textLightGray` | `rgba(3,16,31, 1)` | Disabled or muted content | High opacity for accessibility |
| `textInputBlack` | `#858585` | Form input text | Medium gray for input fields |
| `searchTextBlack` | `#999999` | Search placeholder text | Light gray for placeholders |
| `pipeIdTextBlack` | `#697DA5` | Specific data labels | Blue-gray for technical data |
| `textBg` | `#F2F2F7` | Text containers | Very light gray for text backgrounds |

### 4. Status Colors
System status and feedback colors for user communication.

| Color Name | Hex Value | Usage Context | Accessibility Notes |
|------------|-----------|---------------|-------------------|
| `onlineGreen` | `#028D10` | Positive states and online indicators | High contrast green for success |
| `offlineRed` | `#F24822` | Negative states and offline indicators | High contrast red for errors |
| `error` | `#FF3B30` | Error messages and critical alerts | Standard error red |
| `red` | `#FF0000` | Warnings and caution states | Pure red for critical warnings |
| `bgLightRed` | `rgba(242, 72, 34, 0.1)` | Error state backgrounds | Transparent red for subtle errors |
| `palePink` | `#FADDD6` | Subtle error indicators | Very light pink for gentle alerts |

### 5. Background Colors
Surface and container colors for layout and visual hierarchy.

| Color Name | Hex Value | Usage Context | Accessibility Notes |
|------------|-----------|---------------|-------------------|
| `background` | `#F6F8FB` | Main app background | Very light blue-gray |
| `containerligetBlue` | `#F5FAFF` | Card and container backgrounds | Light blue for containers |
| `dailyProgressBg` | `#ECECEC` | Progress screens | Light gray for progress areas |
| `dailyProgressItemBg` | `#F0F0F0` | List items | Slightly darker gray for items |
| `lightBlueSidebar` | `#E3F0FF` | Sidebar backgrounds | Light blue for navigation |
| `inputBgColr` | `#f5f5f5` | Form input backgrounds | Light gray for inputs |
| `bgSecondaryLightBlue` | `rgba(245, 250, 255, 1)` | Secondary containers | Transparent light blue |
| `mediumBlueBg` | `rgba(7, 117, 206, 0.05)` | Subtle blue backgrounds | Very transparent blue |
| `iconBg` | `#DDE2EB` | Icon containers | Light blue-gray for icons |
| `badgeContainerColor` | `#697DA51A` | Badge backgrounds | Transparent blue-gray |
| `tabBgColoor` | `#4F66810F` | Tab containers | Transparent dark blue |

### 6. Interactive Colors
Buttons, links, and interactive elements for user engagement.

| Color Name | Hex Value | Usage Context | Accessibility Notes |
|------------|-----------|---------------|-------------------|
| `blue` | `#0775CE` | Primary buttons and links | High contrast blue for actions |
| `borderColor` | `#0775CE` | Button and input borders | Consistent with brand blue |
| `updateTextLightBlue` | `#E2EFFF` | Update action text | Light blue for secondary actions |
| `onlineBackground` | `#CCFFD1` | Online status backgrounds | Light green for positive states |
| `offlineBackground` | `#FFC7BA` | Offline status backgrounds | Light red for negative states |

### 7. Form Colors
Input fields and form elements for data entry.

| Color Name | Hex Value | Usage Context | Accessibility Notes |
|------------|-----------|---------------|-------------------|
| `inputBorder` | `#C7D3E0` | Form input borders | Light blue-gray for inputs |
| `searchBorderGrey` | `#E0E0E0` | Search input borders | Light gray for search fields |
| `placeholder` | `rgba(51, 72, 94, 0.4)` | Input placeholder text | Transparent dark blue |

### 8. Map Colors
Geographic and mapping elements for spatial data visualization.

| Color Name | Hex Value | Usage Context | Accessibility Notes |
|------------|-----------|---------------|-------------------|
| `mapStrokeLine` | `#8797B7` | Map polylines and routes | Medium blue-gray for map elements |

### 9. Overlay Colors
Modals, overlays, and shadow effects for depth and focus.

| Color Name | Hex Value | Usage Context | Accessibility Notes |
|------------|-----------|---------------|-------------------|
| `modelOverlay` | `rgba(0,0,0,0.3)` | Modal backgrounds | 30% black for modals |
| `overlay` | `#00000080` | General overlay backgrounds | 50% black for overlays |
| `shadow` | `rgba(0, 0, 0, 0.1)` | Drop shadows and elevation | 10% black for subtle shadows |

### 10. Progress Colors
Progress indicators and loading states for user feedback.

| Color Name | Hex Value | Usage Context | Accessibility Notes |
|------------|-----------|---------------|-------------------|
| `progressAsh` | `#E1E5ED` | Progress bar backgrounds | Light gray for progress tracks |

## Usage Guidelines

### 1. Semantic Naming Convention
- **Brand Colors**: Use for primary actions and brand identity
- **Status Colors**: Use for system feedback and state indication
- **Text Colors**: Use for typography hierarchy
- **Background Colors**: Use for layout and visual separation
- **Interactive Colors**: Use for buttons and clickable elements

### 2. Accessibility Standards
- **WCAG AA Compliance**: All color combinations meet 4.5:1 contrast ratio
- **Color Independence**: Information is not conveyed by color alone
- **High Contrast**: Critical information uses high contrast colors
- **Reduced Motion**: Consider users with motion sensitivity

### 3. Component-Specific Usage

#### Buttons
```typescript
// Primary Button
backgroundColor: Colors.primary
color: Colors.white

// Secondary Button
backgroundColor: Colors.white
borderColor: Colors.blue
color: Colors.blue

// Success Button
backgroundColor: Colors.onlineGreen
color: Colors.white
```

#### Cards
```typescript
// Card Container
backgroundColor: Colors.white
borderColor: Colors.grey

// Card Background
backgroundColor: Colors.containerligetBlue
```

#### Forms
```typescript
// Input Field
backgroundColor: Colors.inputBgColr
borderColor: Colors.inputBorder
color: Colors.textPrimary

// Placeholder
color: Colors.placeholder
```

#### Status Indicators
```typescript
// Online Status
backgroundColor: Colors.onlineBackground
color: Colors.onlineGreen

// Offline Status
backgroundColor: Colors.offlineBackground
color: Colors.offlineRed
```

## Future-Proofing Strategies

### 1. Color System Evolution
- **Semantic Organization**: Colors are grouped by meaning, not by hue
- **Extensible Structure**: New colors can be added to appropriate categories
- **Type Safety**: TypeScript interfaces ensure compile-time validation
- **Documentation**: Comprehensive comments explain usage context

### 2. Design System Scalability
- **Component-Based**: Colors are designed for component reuse
- **Theme Support**: Structure supports future theming capabilities
- **Dark Mode Ready**: Color system can be extended for dark mode
- **Internationalization**: Colors support multiple cultural contexts

### 3. Maintenance Considerations
- **Single Source of Truth**: All colors defined in one location
- **Version Control**: Changes tracked through Git history
- **Testing Strategy**: Automated tests for color consistency
- **Design Handoff**: Clear documentation for design team collaboration

## Implementation Best Practices

### 1. Import and Usage
```typescript
// Correct usage
import Colors from '../utils/Colors/Colors';

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background,
    borderColor: Colors.borderColor,
  },
  text: {
    color: Colors.textPrimary,
  },
});
```

### 2. Type Safety
```typescript
// Use type-safe color keys
import { ColorKey, getColorByKey } from '../utils/Colors/Colors';

const getColor = (key: ColorKey): string => getColorByKey(key);
```

### 3. Accessibility Utilities
```typescript
// Check color accessibility
import { isAccessible } from '../utils/Colors/Colors';

const isTextReadable = isAccessible(Colors.textPrimary, Colors.background);
```

## Migration Guide

### 1. Breaking Changes
- **Renamed Colors**: Some colors have been renamed for clarity
- **New Organization**: Colors are now grouped by semantic meaning
- **Type Exports**: New TypeScript types for type safety

### 2. Update Process
1. **Review Current Usage**: Audit existing color usage
2. **Update Imports**: Ensure all components use the new structure
3. **Test Accessibility**: Verify contrast ratios meet standards
4. **Document Changes**: Update component documentation

### 3. Backward Compatibility
- **Existing Colors**: All existing color values remain unchanged
- **Gradual Migration**: Components can be updated incrementally
- **Fallback Support**: Old color names supported during transition

## Quality Assurance

### 1. Automated Testing
- **Color Validation**: Ensure all colors are valid hex values
- **Contrast Testing**: Verify accessibility compliance
- **Type Checking**: TypeScript validation for color usage
- **Component Testing**: Visual regression testing for color changes

### 2. Manual Testing
- **Cross-Platform**: Test colors on iOS and Android
- **Accessibility**: Test with screen readers and high contrast mode
- **Performance**: Ensure color changes don't impact performance
- **User Testing**: Validate color choices with target users

## Conclusion

This design token system provides a robust, scalable foundation for the ePragati application's visual design. The semantic organization, comprehensive documentation, and type safety ensure consistent implementation across the entire application ecosystem while maintaining flexibility for future design evolution.

The color system is designed to support the application's growth while maintaining accessibility standards and providing clear guidance for developers and designers working on the project. 