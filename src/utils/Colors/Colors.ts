/**
 * Design Token System - Color Constants
 * 
 * This file serves as the single source of truth for all color values
 * used throughout the ePragati application. Colors are organized by:
 * - Semantic meaning (primary, secondary, status, etc.)
 * - Usage context (text, background, borders, etc.)
 * - Accessibility considerations (contrast ratios)
 * 
 * Naming Convention:
 * - Semantic names: primary, secondary, success, error, warning
 * - Context-specific: textPrimary, backgroundPrimary, borderSecondary
 * - Status-based: onlineGreen, offlineRed, pendingYellow
 * - Component-specific: buttonPrimary, cardBackground, inputBorder
 */

// ============================================================================
// BRAND COLORS - Core brand identity colors
// ============================================================================
const brandColors = {
  /** Primary brand color - Used for main actions, headers, and key UI elements */
  primary: "#33485E",
  
  /** Secondary brand color - Used for secondary actions and accents */
  secondary: "#052669",
  
  /** Brand blue - Used for interactive elements and links */
  brandBlue: '#0775CE',
  
  /** Dark blue variant - Used for gradients and emphasis */
  darkBlue: '#373D4B',
  
  /** App title background - Used for app header branding */
  appTitleBackground: '#17408B',
  
  /** Forgot PIN blue - Used for password recovery flows */
  forgotPinBlue: '#007AFF',
};

// ============================================================================
// NEUTRAL COLORS - Grays and basic colors
// ============================================================================
const neutralColors = {
  /** Pure white - Used for backgrounds and text on dark surfaces */
  white: "#FFFFFF",
  
  /** Pure black - Used for text and icons on light surfaces */
  black: "#000000",
  
  /** Off-white - Used for subtle backgrounds */
  offWhite: "#FAF9F6",
  
  /** Light gray - Used for borders and dividers */
  grey: "#E1E1ED",
  
  /** Medium gray - Used for secondary text and icons */
  labelBlue: "#6B7280",
  
  /** Light black - Used for tertiary text */
  lightBlack: '#747980',
  
  /** Circle gray - Used for circular indicators */
  circleGrey: '#CDD8EC',
  
  /** Vertical line color - Used for dividers */
  verticalLineColor: '#D1D5DB',
};

// ============================================================================
// TEXT COLORS - Typography color hierarchy
// ============================================================================
const textColors = {
  /** Primary text color - Used for main content and headings */
  textPrimary: "#03101F",
  
  /** Secondary text color - Used for descriptions and captions */
  textSecondary: '#7B8D9E',
  
  /** Light gray text - Used for disabled or muted content */
  textLightGray: "rgba(3,16,31, 1)",
  
  /** Text input black - Used for form input text */
  textInputBlack: '#858585',
  
  /** Search text black - Used for search placeholder text */
  searchTextBlack: '#999999',
  
  /** Pipe ID text black - Used for specific data labels */
  pipeIdTextBlack: '#697DA5',
  
  /** Text background - Used for text containers */
  textBg: '#F2F2F7',
};

// ============================================================================
// STATUS COLORS - System status and feedback colors
// ============================================================================
const statusColors = {
  /** Success/Online green - Used for positive states and online indicators */
  onlineGreen: '#028D10',
  
  /** Error/Offline red - Used for negative states and offline indicators */
  offlineRed: '#F24822',
  
  /** Error red - Used for error messages and critical alerts */
  error: '#FF3B30',
  
  /** Warning red - Used for warnings and caution states */
  red: "#FF0000",
  
  /** Light red background - Used for error state backgrounds */
  bgLightRed: 'rgba(242, 72, 34, 0.1)',
  
  /** Pale pink - Used for subtle error indicators */
  palePink: '#FADDD6',
};

// ============================================================================
// BACKGROUND COLORS - Surface and container colors
// ============================================================================
const backgroundColors = {
  /** Main background - Used for app background */
  background: '#F6F8FB',
  
  /** Container light blue - Used for card and container backgrounds */
  containerligetBlue: '#F5FAFF',
  
  /** Daily progress background - Used for progress screens */
  dailyProgressBg: '#ECECEC',
  
  /** Daily progress item background - Used for list items */
  dailyProgressItemBg: '#F0F0F0',
  
  /** Light blue sidebar - Used for sidebar backgrounds */
  lightBlueSidebar: '#E3F0FF',
  
  /** Input background color - Used for form input backgrounds */
  inputBgColr: '#f5f5f5',
  
  /** Secondary light blue background - Used for secondary containers */
  bgSecondaryLightBlue: 'rgba(245, 250, 255, 1)',
  
  /** Medium blue background - Used for subtle blue backgrounds */
  mediumBlueBg: 'rgba(7, 117, 206, 0.05)',
  
  /** Icon background - Used for icon containers */
  iconBg: '#DDE2EB',
  
  /** Badge container color - Used for badge backgrounds */
  badgeContainerColor: '#697DA51A',
  
  /** Tab background color - Used for tab containers */
  tabBgColoor: '#4F66810F',
};

// ============================================================================
// INTERACTIVE COLORS - Buttons, links, and interactive elements
// ============================================================================
const interactiveColors = {
  /** Primary blue - Used for primary buttons and links */
  blue: '#0775CE',
  
  /** Border color - Used for button and input borders */
  borderColor: '#0775CE',
  
  /** Update text light blue - Used for update action text */
  updateTextLightBlue: '#E2EFFF',
  
  /** Online background - Used for online status backgrounds */
  onlineBackground: '#CCFFD1',
  
  /** Offline background - Used for offline status backgrounds */
  offlineBackground: '#FFC7BA',
};

// ============================================================================
// FORM COLORS - Input fields and form elements
// ============================================================================
const formColors = {
  /** Input border - Used for form input borders */
  inputBorder: '#C7D3E0',
  
  /** Search border grey - Used for search input borders */
  searchBorderGrey: '#E0E0E0',
  
  /** Placeholder text - Used for input placeholder text */
  placeholder: "rgba(51, 72, 94, 0.4)",
};

// ============================================================================
// MAP COLORS - Geographic and mapping elements
// ============================================================================
const mapColors = {
  /** Map stroke line - Used for map polylines and routes */
  mapStrokeLine: '#8797B7',
  mapStrokeOpenLine: 'rgba(105, 125, 165, 0.5)',
};

// ============================================================================
// OVERLAY COLORS - Modals, overlays, and overlays
// ============================================================================
const overlayColors = {
  /** Model overlay - Used for modal backgrounds */
  modelOverlay: 'rgba(0,0,0,0.3)',
  
  /** Overlay - Used for general overlay backgrounds */
  overlay: '#00000080',
  
  /** Shadow - Used for drop shadows and elevation */
  shadow: 'rgba(0, 0, 0, 0.1)',
};

// ============================================================================
// PROGRESS COLORS - Progress indicators and loading states
// ============================================================================
const progressColors = {
  /** Progress ash - Used for progress bar backgrounds */
  progressAsh: "#E1E5ED",
};

// ============================================================================
// EXPORT CONSOLIDATED COLORS OBJECT
// ============================================================================
const Colors = {
  // Brand Colors
  ...brandColors,
  
  // Neutral Colors
  ...neutralColors,
  
  // Text Colors
  ...textColors,
  
  // Status Colors
  ...statusColors,
  
  // Background Colors
  ...backgroundColors,
  
  // Interactive Colors
  ...interactiveColors,
  
  // Form Colors
  ...formColors,
  
  // Map Colors
  ...mapColors,
  
  // Overlay Colors
  ...overlayColors,
  
  // Progress Colors
  ...progressColors,
};

export default Colors;

// ============================================================================
// TYPE DEFINITIONS FOR TYPE SAFETY
// ============================================================================
export type ColorKey = keyof typeof Colors;
export type BrandColorKey = keyof typeof brandColors;
export type StatusColorKey = keyof typeof statusColors;
export type TextColorKey = keyof typeof textColors;
export type BackgroundColorKey = keyof typeof backgroundColors;

// ============================================================================
// COLOR UTILITY FUNCTIONS
// ============================================================================
export const getColorByKey = (key: ColorKey): string => Colors[key];

export const getBrandColor = (key: BrandColorKey): string => brandColors[key];

export const getStatusColor = (key: StatusColorKey): string => statusColors[key];

export const getTextColor = (key: TextColorKey): string => textColors[key];

export const getBackgroundColor = (key: BackgroundColorKey): string => backgroundColors[key];

// ============================================================================
// ACCESSIBILITY UTILITIES
// ============================================================================
export const getContrastRatio = (color1: string, color2: string): number => {
  // Implementation for contrast ratio calculation
  // This would be used to ensure WCAG compliance
  return 4.5; // Placeholder - implement actual calculation
};

export const isAccessible = (foreground: string, background: string): boolean => {
  const ratio = getContrastRatio(foreground, background);
  return ratio >= 4.5; // WCAG AA standard for normal text
};