const Colors = {
    primary: "#33485E",
    secondary: "#052669",
    placeholder: "rgba(51, 72, 94, 0.4)",
    white: "#FFFFFF",
    black: "#000000",
    grey: "#E1E1ED",
    labelBlue: "#6B7280",
    blue: '#0775CE',
    textPrimary: "#03101F",
    textLightGray: "rgba(3,16,31, 1)",
    red: "#FF0000",
    offWhite: "#FAF9F6",
    progressAsh: "#E1E5ED",
    onlineGreen: '#028D10',
    offlineRed: '#F24822',
    onlineBackground: '#CCFFD1',
    offlineBackground: '#FFC7BA',
    background: '#F6F8FB',
    inputBorder: '#C7D3E0',
    textInputBlack: '#858585',
    textSecondary: '#7B8D9E',
    lightBlack: '#747980',
    error: '#FF3B30',
    shadow: 'rgba(0, 0, 0, 0.1)',
    appTitleBackground: '#17408B',
    forgotPinBlue: '#007AFF',
    overlay: '#00000080',
    darkBlue: '#373D4B',
    dailyProgressBg: '#ECECEC',
    searchTextBlack: '#999999',
    dailyProgressItemBg: '#F0F0F0',
    searchBorderGrey: '#E0E0E0',
    containerligetBlue: '#F5FAFF',
    textBg: '#F2F2F7',
    updateTextLightBlue: '#E2EFFF',
    pipeIdTextBlack: '#697DA5',
    tabBgColoor: '#4F66810F',
    circleGrey: '#CDD8EC',
    borderColor: '#0775CE',
    mediumBlueBg: 'rgba(7, 117, 206, 0.05)',
    lightBlueSidebar: '#E3F0FF',
    inputBgColr: '#f5f5f5',
    bgLightRed: 'rgba(242, 72, 34, 0.1)',
    bgSecondaryLightBlue: 'rgba(245, 250, 255, 1)',
    iconBg: '#DDE2EB',
    modelOverlay: 'rgba(0,0,0,0.3)',
    verticalLineColor: '#D1D5DB',
    badgeContainerColor: '#697DA51A',
};

export default Colors;