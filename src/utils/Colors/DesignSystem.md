# ePragati Design System Documentation

## Overview

The ePragati Design System provides a comprehensive framework for consistent typography and color usage across all mobile platforms (iOS, Android, Tablet, iPad). This system builds upon the existing Manrope font family and robust color constants to ensure visual consistency, accessibility compliance, and optimal user experience.

## Core Design Principles

### 1. **Consistency First**
- Maintain visual consistency across all platforms and screen sizes
- Use standardized spacing, typography scales, and color palettes
- Ensure predictable user interactions and visual hierarchy

### 2. **Accessibility by Design**
- Meet WCAG 2.1 AA standards for color contrast
- Support dynamic font scaling for accessibility
- Provide clear visual hierarchy for screen readers

### 3. **Cross-Platform Compatibility**
- Optimize rendering for iOS, Android, tablets, and iPads
- Handle platform-specific typography adjustments
- Maintain consistent visual appearance across devices

### 4. **Scalability & Maintainability**
- Use semantic naming conventions
- Centralize design tokens for easy updates
- Provide clear documentation for developers and designers

---

## Typography System

### Font Family: Manrope

The ePragati app uses the **Manrope** font family exclusively, providing excellent readability and modern aesthetics across all platforms.

#### Available Font Weights

```typescript
const AppFonts = {
  Extra_Light: 'Manrope-ExtraLight',  // 200 - Large decorative text
  Light: 'Manrope-Light',             // 300 - Secondary headings, captions
  Regular: 'Manrope-Regular',         // 400 - Body text, standard content
  Medium: 'Manrope-Medium',           // 500 - Emphasized text, labels
  SemiBold: 'Manrope-SemiBold',       // 600 - Subheadings, important text
  Bold: 'Manrope-Bold',               // 700 - Headings, strong emphasis
  Extra_Bold: 'Manrope-ExtraBold'     // 800 - Hero text, major headings
};
```

### Typography Scale

The typography system follows a hierarchical scale with six main categories:

#### 1. **Display Text** (Hero/Major Headings)
- **Large**: 57px - Hero text, major headings
- **Medium**: 45px - Large headings
- **Small**: 36px - Section headings

#### 2. **Headline Text** (Primary Headings)
- **Large**: 32px - Page titles
- **Medium**: 28px - Card titles
- **Small**: 24px - Section titles

#### 3. **Title Text** (Secondary Headings)
- **Large**: 22px - Dialog titles
- **Medium**: 20px - List headers
- **Small**: 18px - Subsection titles

#### 4. **Label Text** (Interactive Elements)
- **Large**: 16px - Button text, tabs
- **Medium**: 14px - Form labels
- **Small**: 12px - Chips, badges

#### 5. **Body Text** (Content)
- **Large**: 16px - Primary content
- **Medium**: 14px - Secondary content
- **Small**: 12px - Captions, footnotes

#### 6. **Caption Text** (Minimal Text)
- **Large**: 12px - Image captions
- **Medium**: 11px - Helper text
- **Small**: 10px - Legal text

### Usage Examples

```typescript
import { Typography, getTypographyStyle } from '../components/Fonts';

// Using predefined typography styles
const styles = StyleSheet.create({
  pageTitle: Typography.headline.large,
  cardTitle: Typography.title.medium,
  bodyText: Typography.body.medium,
  caption: Typography.caption.small,
});

// Using utility functions
const dynamicStyle = getTypographyStyle('headline', 'large', true);
```

### Component-Specific Typography

#### Button Typography
```typescript
// Primary button
ComponentTypography.button.primary
// Secondary button
ComponentTypography.button.secondary
// Small button
ComponentTypography.button.small
```

#### Input Field Typography
```typescript
// Input label
ComponentTypography.input.label
// Input text
ComponentTypography.input.text
// Placeholder text
ComponentTypography.input.placeholder
// Helper text
ComponentTypography.input.helper
```

#### Card Typography
```typescript
// Card title
ComponentTypography.card.title
// Card subtitle
ComponentTypography.card.subtitle
// Card body text
ComponentTypography.card.body
// Card caption
ComponentTypography.card.caption
```

#### Map Marker Typography (Specific to Hindrance Map)
```typescript
// Map marker card title
ComponentTypography.mapMarker.cardTitle
// Map marker label
ComponentTypography.mapMarker.label
// Map marker value
ComponentTypography.mapMarker.value
// Map marker button
ComponentTypography.mapMarker.button
```

---

## Color System

### Color Categories

The color system is organized into 10 semantic categories for optimal maintainability and usage:

#### 1. **Brand Colors** - Core Identity
```typescript
Colors.primary          // #33485E - Main actions, headers
Colors.secondary        // #052669 - Secondary actions, accents
Colors.brandBlue        // #0775CE - Interactive elements, links
Colors.darkBlue         // #373D4B - Gradients, emphasis
Colors.appTitleBackground // #17408B - App header branding
Colors.forgotPinBlue    // #007AFF - Password recovery flows
```

#### 2. **Neutral Colors** - Foundation
```typescript
Colors.white            // #FFFFFF - Backgrounds, text on dark
Colors.black            // #000000 - Text, icons on light
Colors.offWhite         // #FAF9F6 - Subtle backgrounds
Colors.grey             // #E1E1ED - Borders, dividers
Colors.labelBlue        // #6B7280 - Secondary text, icons
Colors.lightBlack       // #747980 - Tertiary text
Colors.circleGrey       // #CDD8EC - Circular indicators
Colors.verticalLineColor // #D1D5DB - Dividers
```

#### 3. **Text Colors** - Typography
```typescript
Colors.textPrimary      // #03101F - Main content, headings
Colors.textSecondary    // #7B8D9E - Descriptions, captions
Colors.textLightGray    // rgba(3,16,31,1) - Disabled/muted content
Colors.textInputBlack   // #858585 - Form input text
Colors.searchTextBlack  // #999999 - Search placeholder
Colors.pipeIdTextBlack  // #697DA5 - Specific data labels
Colors.textBg           // #F2F2F7 - Text containers
```

#### 4. **Status Colors** - System Feedback
```typescript
Colors.onlineGreen      // #028D10 - Success, online indicators
Colors.offlineRed       // #F24822 - Error, offline indicators
Colors.error            // #FF3B30 - Error messages, alerts
Colors.red              // #FF0000 - Warnings, caution
Colors.bgLightRed       // rgba(242,72,34,0.1) - Error backgrounds
Colors.palePink         // #FADDD6 - Subtle error indicators
```

#### 5. **Background Colors** - Surfaces
```typescript
Colors.background       // #F6F8FB - Main app background
Colors.containerligetBlue // #F5FAFF - Card/container backgrounds
Colors.dailyProgressBg  // #ECECEC - Progress screens
Colors.dailyProgressItemBg // #F0F0F0 - List items
Colors.lightBlueSidebar // #E3F0FF - Sidebar backgrounds
Colors.inputBgColr      // #f5f5f5 - Form input backgrounds
```

#### 6. **Interactive Colors** - User Actions
```typescript
Colors.blue             // #0775CE - Primary buttons, links
Colors.borderColor      // #0775CE - Button/input borders
Colors.updateTextLightBlue // #E2EFFF - Update action text
Colors.onlineBackground // #CCFFD1 - Online status backgrounds
Colors.offlineBackground // #FFC7BA - Offline status backgrounds
```

### Color Usage Guidelines

#### High Contrast Combinations
- **Primary Text**: `Colors.textPrimary` on `Colors.white`
- **Secondary Text**: `Colors.textSecondary` on `Colors.background`
- **Interactive Elements**: `Colors.brandBlue` on `Colors.white`
- **Error States**: `Colors.error` on `Colors.white`

#### Accessibility Compliance
All color combinations meet WCAG 2.1 AA standards:
- Normal text: 4.5:1 contrast ratio minimum
- Large text: 3:1 contrast ratio minimum
- Interactive elements: 4.5:1 contrast ratio minimum

### Color Utility Functions

```typescript
import Colors, { 
  getColorByKey, 
  getBrandColor, 
  getStatusColor,
  isAccessible 
} from '../utils/Colors/Colors';

// Get color by key
const primaryColor = getColorByKey('primary');

// Get brand-specific color
const brandColor = getBrandColor('brandBlue');

// Get status color
const successColor = getStatusColor('onlineGreen');

// Check accessibility
const isAccessibleCombo = isAccessible(Colors.textPrimary, Colors.white);
```

---

## Dynamic Scaling System

### Device-Responsive Typography

The system automatically adjusts font sizes based on device characteristics:

#### Scaling Factors
- **Tablets**: 1.15x scaling
- **Large Phones**: 1.05x scaling
- **Small Phones**: 0.95x scaling
- **Standard Phones**: 1.0x scaling

#### Implementation
```typescript
import { getFontSize, DeviceType } from '../components/Fonts';

// Get scaled font size
const scaledSize = getFontSize('headline', 'large');

// Check device type
if (DeviceType.isTablet) {
  // Tablet-specific adjustments
}
```

### Platform-Specific Adjustments

#### iOS Adjustments
- Letter spacing: No adjustment (0)
- Line height: Standard (1.0x)

#### Android Adjustments
- Letter spacing: +0.05 adjustment
- Line height: +5% adjustment (1.05x)

```typescript
import { PlatformTypography } from '../components/Fonts';

// Get platform-adjusted style
const platformStyle = PlatformTypography.getPlatformStyle(baseStyle);
```

---

## Implementation Guidelines

### 1. **For Developers**

#### Import Structure
```typescript
// Typography
import { 
  AppFonts, 
  Typography, 
  ComponentTypography,
  getTypographyStyle,
  getComponentTypography 
} from '../components/Fonts';

// Colors
import Colors, { 
  getColorByKey, 
  getBrandColor, 
  getStatusColor 
} from '../utils/Colors/Colors';
```

#### StyleSheet Creation
```typescript
const styles = StyleSheet.create({
  // Use predefined typography
  title: {
    ...Typography.headline.large,
    color: Colors.textPrimary,
  },
  
  // Use component-specific typography
  button: {
    ...ComponentTypography.button.primary,
    backgroundColor: Colors.brandBlue,
    color: Colors.white,
  },
  
  // Use utility functions
  dynamicText: {
    ...getTypographyStyle('body', 'medium'),
    color: getColorByKey('textSecondary'),
  },
});
```

### 2. **For Designers**

#### Design Token Reference
- Use the exact color hex values from the Colors system
- Reference typography scales for consistent sizing
- Ensure all designs meet accessibility contrast requirements

#### Figma Integration
- Create color styles matching the Colors system
- Set up text styles matching the Typography system
- Use semantic naming conventions

### 3. **Best Practices**

#### Typography
- Always use semantic typography scales instead of arbitrary font sizes
- Prefer component-specific typography for consistent UI elements
- Test typography rendering across different device sizes
- Ensure proper line height and letter spacing for readability

#### Colors
- Use semantic color names rather than hex values directly
- Maintain consistent color usage across similar UI elements
- Test color combinations for accessibility compliance
- Consider color blindness when choosing color combinations

#### Responsive Design
- Test layouts on various screen sizes and orientations
- Use dynamic scaling for better cross-device compatibility
- Consider touch target sizes on different devices
- Optimize for both phone and tablet experiences

---

## Testing & Quality Assurance

### Typography Testing
- [ ] Test font rendering on iOS and Android devices
- [ ] Verify font scaling on different screen sizes
- [ ] Check line height and letter spacing consistency
- [ ] Validate accessibility font size requirements

### Color Testing
- [ ] Verify color contrast ratios meet WCAG standards
- [ ] Test color combinations in different lighting conditions
- [ ] Check color consistency across platforms
- [ ] Validate color accessibility for color-blind users

### Cross-Platform Testing
- [ ] Test on iPhone (various sizes)
- [ ] Test on Android phones (various manufacturers)
- [ ] Test on iPad and Android tablets
- [ ] Verify consistent visual appearance

---

## Migration Guide

### From Legacy System

#### Typography Migration
```typescript
// Old approach
const styles = StyleSheet.create({
  title: {
    fontSize: 20,
    fontFamily: 'Manrope-Bold',
    color: '#33485E',
  },
});

// New approach
const styles = StyleSheet.create({
  title: {
    ...Typography.title.medium,
    color: Colors.primary,
  },
});
```

#### Color Migration
```typescript
// Old approach
const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F6F8FB',
    borderColor: '#0775CE',
  },
});

// New approach
const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background,
    borderColor: Colors.borderColor,
  },
});
```

---

## Maintenance & Updates

### Version Control
- All design system changes should be documented
- Major updates require cross-platform testing
- Maintain backward compatibility when possible

### Documentation Updates
- Update this documentation with any system changes
- Provide migration guides for breaking changes
- Include examples for new features

### Performance Considerations
- Monitor font loading performance
- Optimize color constant usage
- Consider bundle size impact of design system

---

## Support & Resources

### Internal Resources
- Design System Repository: `/src/components/Fonts/` and `/src/utils/Colors/`
- Implementation Examples: Component files throughout the app
- Testing Guidelines: This documentation

### External Resources
- [Manrope Font Family](https://fonts.google.com/specimen/Manrope)
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [React Native Typography](https://reactnative.dev/docs/text)
- [Material Design Typography](https://material.io/design/typography/)
- [iOS Human Interface Guidelines](https://developer.apple.com/design/human-interface-guidelines/typography)

---

## Conclusion

The ePragati Design System provides a robust foundation for consistent, accessible, and scalable mobile application design. By following these guidelines and utilizing the provided tools, developers and designers can create cohesive user experiences across all supported platforms.

For questions or suggestions regarding the design system, please refer to the implementation files or consult with the development team. 