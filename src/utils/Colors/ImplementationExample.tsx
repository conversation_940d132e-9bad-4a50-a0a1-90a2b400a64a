/**
 * ePragati Design System - Implementation Examples
 * 
 * This file demonstrates practical usage of the enhanced typography and color system
 * across various common UI components in the ePragati application.
 */

import React from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet } from 'react-native';

// Import the enhanced design system
import { 
  Typography, 
  ComponentTypography, 
  getTypographyStyle, 
  getComponentTypography,
  DeviceType 
} from '../components/Fonts';
import Colors, { getColorByKey, getBrandColor, getStatusColor } from './Colors';
import { ms } from '../Scale/Scaling';

// ============================================================================
// EXAMPLE 1: Header Component with Dynamic Typography
// ============================================================================

interface HeaderProps {
  title: string;
  subtitle?: string;
  showBackButton?: boolean;
}

const ExampleHeader: React.FC<HeaderProps> = ({ title, subtitle, showBackButton }) => {
  return (
    <View style={styles.headerContainer}>
      {showBackButton && (
        <TouchableOpacity style={styles.backButton}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
      )}
      
      <View style={styles.headerContent}>
        <Text style={styles.headerTitle}>{title}</Text>
        {subtitle && <Text style={styles.headerSubtitle}>{subtitle}</Text>}
      </View>
    </View>
  );
};

// ============================================================================
// EXAMPLE 2: Card Component with Component-Specific Typography
// ============================================================================

interface CardProps {
  title: string;
  description: string;
  status: 'online' | 'offline' | 'pending';
  onPress?: () => void;
}

const ExampleCard: React.FC<CardProps> = ({ title, description, status, onPress }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return Colors.onlineGreen;
      case 'offline':
        return Colors.offlineRed;
      case 'pending':
        return Colors.labelBlue;
      default:
        return Colors.textSecondary;
    }
  };

  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <View style={styles.cardHeader}>
        <Text style={styles.cardTitle}>{title}</Text>
        <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(status) }]} />
      </View>
      
      <Text style={styles.cardDescription}>{description}</Text>
      
      <View style={styles.cardFooter}>
        <Text style={[styles.statusText, { color: getStatusColor(status) }]}>
          {status.toUpperCase()}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

// ============================================================================
// EXAMPLE 3: Form Component with Input Typography
// ============================================================================

interface FormFieldProps {
  label: string;
  placeholder: string;
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  helperText?: string;
}

const ExampleFormField: React.FC<FormFieldProps> = ({
  label,
  placeholder,
  value,
  onChangeText,
  error,
  helperText
}) => {
  return (
    <View style={styles.formField}>
      <Text style={styles.inputLabel}>{label}</Text>
      
      <TextInput
        style={[styles.textInput, error && styles.textInputError]}
        placeholder={placeholder}
        placeholderTextColor={Colors.searchTextBlack}
        value={value}
        onChangeText={onChangeText}
      />
      
      {error && <Text style={styles.errorText}>{error}</Text>}
      {helperText && !error && <Text style={styles.helperText}>{helperText}</Text>}
    </View>
  );
};

// ============================================================================
// EXAMPLE 4: Button Component with Dynamic Sizing
// ============================================================================

interface ButtonProps {
  title: string;
  variant: 'primary' | 'secondary' | 'small';
  onPress: () => void;
  disabled?: boolean;
}

const ExampleButton: React.FC<ButtonProps> = ({ title, variant, onPress, disabled }) => {
  const getButtonStyle = () => {
    switch (variant) {
      case 'primary':
        return [styles.button, styles.buttonPrimary];
      case 'secondary':
        return [styles.button, styles.buttonSecondary];
      case 'small':
        return [styles.button, styles.buttonSmall];
      default:
        return [styles.button, styles.buttonPrimary];
    }
  };

  const getButtonTextStyle = () => {
    switch (variant) {
      case 'primary':
        return styles.buttonTextPrimary;
      case 'secondary':
        return styles.buttonTextSecondary;
      case 'small':
        return styles.buttonTextSmall;
      default:
        return styles.buttonTextPrimary;
    }
  };

  return (
    <TouchableOpacity
      style={[...getButtonStyle(), disabled && styles.buttonDisabled]}
      onPress={onPress}
      disabled={disabled}
    >
      <Text style={[getButtonTextStyle(), disabled && styles.buttonTextDisabled]}>
        {title}
      </Text>
    </TouchableOpacity>
  );
};

// ============================================================================
// EXAMPLE 5: Map Marker Info Card (Specific to Hindrance Map)
// ============================================================================

interface MapMarkerInfoProps {
  jobCode: string;
  serialNo: string;
  gapLength: number;
  latitude: number;
  longitude: number;
  onUpdatePress: () => void;
}

const ExampleMapMarkerInfo: React.FC<MapMarkerInfoProps> = ({
  jobCode,
  serialNo,
  gapLength,
  latitude,
  longitude,
  onUpdatePress
}) => {
  return (
    <View style={styles.mapMarkerCard}>
      <Text style={styles.mapMarkerTitle}>Hindrance Details</Text>
      
      <View style={styles.mapMarkerRow}>
        <Text style={styles.mapMarkerLabel}>Job Code:</Text>
        <Text style={styles.mapMarkerValue}>{jobCode}</Text>
      </View>
      
      <View style={styles.mapMarkerRow}>
        <Text style={styles.mapMarkerLabel}>Serial No:</Text>
        <Text style={styles.mapMarkerValue}>{serialNo}</Text>
      </View>
      
      <View style={styles.mapMarkerRow}>
        <Text style={styles.mapMarkerLabel}>Gap Length:</Text>
        <Text style={styles.mapMarkerValue}>{gapLength}m</Text>
      </View>
      
      <View style={styles.mapMarkerRow}>
        <Text style={styles.mapMarkerLabel}>Coordinates:</Text>
        <Text style={styles.mapMarkerValue}>
          {latitude.toFixed(6)}, {longitude.toFixed(6)}
        </Text>
      </View>
      
      <TouchableOpacity style={styles.mapMarkerButton} onPress={onUpdatePress}>
        <Text style={styles.mapMarkerButtonText}>Update Details</Text>
      </TouchableOpacity>
    </View>
  );
};

// ============================================================================
// EXAMPLE 6: Responsive Layout Example
// ============================================================================

const ExampleResponsiveLayout: React.FC = () => {
  const isTablet = DeviceType.isTablet;
  
  return (
    <View style={[styles.responsiveContainer, isTablet && styles.tabletContainer]}>
      <Text style={[styles.responsiveTitle, isTablet && styles.tabletTitle]}>
        Responsive Layout Example
      </Text>
      
      <Text style={styles.responsiveDescription}>
        This layout adapts to different screen sizes using the DeviceType utility.
        On tablets, the content has increased spacing and larger typography.
      </Text>
      
      <View style={[styles.responsiveGrid, isTablet && styles.tabletGrid]}>
        <View style={styles.gridItem}>
          <Text style={styles.gridItemText}>Item 1</Text>
        </View>
        <View style={styles.gridItem}>
          <Text style={styles.gridItemText}>Item 2</Text>
        </View>
        <View style={styles.gridItem}>
          <Text style={styles.gridItemText}>Item 3</Text>
        </View>
      </View>
    </View>
  );
};

// ============================================================================
// STYLESHEET DEFINITIONS
// ============================================================================

const styles = StyleSheet.create({
  // Header Styles
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: ms(16),
    paddingVertical: ms(12),
    paddingTop: ms(44), // Account for status bar
  },
  backButton: {
    marginRight: ms(16),
  },
  backButtonText: {
    ...ComponentTypography.button.primary,
    color: Colors.white,
    fontSize: ms(18),
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    ...Typography.headline.medium,
    color: Colors.white,
  },
  headerSubtitle: {
    ...Typography.body.medium,
    color: Colors.offWhite,
    marginTop: ms(4),
  },

  // Card Styles
  card: {
    backgroundColor: Colors.white,
    borderRadius: ms(12),
    padding: ms(16),
    marginHorizontal: ms(16),
    marginVertical: ms(8),
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: ms(8),
  },
  cardTitle: {
    ...ComponentTypography.card.title,
    color: Colors.textPrimary,
    flex: 1,
  },
  statusIndicator: {
    width: ms(8),
    height: ms(8),
    borderRadius: ms(4),
  },
  cardDescription: {
    ...ComponentTypography.card.body,
    color: Colors.textSecondary,
    marginBottom: ms(12),
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  statusText: {
    ...ComponentTypography.card.caption,
    fontWeight: '600',
  },

  // Form Styles
  formField: {
    marginBottom: ms(16),
  },
  inputLabel: {
    ...ComponentTypography.input.label,
    color: Colors.textPrimary,
    marginBottom: ms(8),
  },
  textInput: {
    ...ComponentTypography.input.text,
    backgroundColor: Colors.inputBgColr,
    borderColor: Colors.inputBorder,
    borderWidth: 1,
    borderRadius: ms(8),
    paddingHorizontal: ms(16),
    paddingVertical: ms(12),
    color: Colors.textPrimary,
  },
  textInputError: {
    borderColor: Colors.error,
  },
  errorText: {
    ...ComponentTypography.input.helper,
    color: Colors.error,
    marginTop: ms(4),
  },
  helperText: {
    ...ComponentTypography.input.helper,
    color: Colors.textSecondary,
    marginTop: ms(4),
  },

  // Button Styles
  button: {
    borderRadius: ms(8),
    paddingHorizontal: ms(24),
    paddingVertical: ms(12),
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonPrimary: {
    backgroundColor: Colors.brandBlue,
  },
  buttonSecondary: {
    backgroundColor: Colors.white,
    borderWidth: 1,
    borderColor: Colors.brandBlue,
  },
  buttonSmall: {
    backgroundColor: Colors.brandBlue,
    paddingHorizontal: ms(16),
    paddingVertical: ms(8),
  },
  buttonDisabled: {
    backgroundColor: Colors.grey,
  },
  buttonTextPrimary: {
    ...ComponentTypography.button.primary,
    color: Colors.white,
  },
  buttonTextSecondary: {
    ...ComponentTypography.button.secondary,
    color: Colors.brandBlue,
  },
  buttonTextSmall: {
    ...ComponentTypography.button.small,
    color: Colors.white,
  },
  buttonTextDisabled: {
    color: Colors.textSecondary,
  },

  // Map Marker Card Styles
  mapMarkerCard: {
    backgroundColor: Colors.white,
    borderRadius: ms(16),
    padding: ms(20),
    margin: ms(16),
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    minWidth: ms(320),
  },
  mapMarkerTitle: {
    ...ComponentTypography.mapMarker.cardTitle,
    color: Colors.textPrimary,
    marginBottom: ms(16),
    textAlign: 'center',
  },
  mapMarkerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: ms(12),
  },
  mapMarkerLabel: {
    ...ComponentTypography.mapMarker.label,
    color: Colors.textSecondary,
    flex: 1,
  },
  mapMarkerValue: {
    ...ComponentTypography.mapMarker.value,
    color: Colors.textPrimary,
    flex: 1,
    textAlign: 'right',
  },
  mapMarkerButton: {
    backgroundColor: Colors.brandBlue,
    borderRadius: ms(8),
    paddingVertical: ms(12),
    paddingHorizontal: ms(24),
    alignItems: 'center',
    marginTop: ms(16),
  },
  mapMarkerButtonText: {
    ...ComponentTypography.mapMarker.button,
    color: Colors.white,
  },

  // Responsive Layout Styles
  responsiveContainer: {
    flex: 1,
    backgroundColor: Colors.background,
    padding: ms(16),
  },
  tabletContainer: {
    padding: ms(32),
  },
  responsiveTitle: {
    ...Typography.headline.large,
    color: Colors.textPrimary,
    marginBottom: ms(16),
    textAlign: 'center',
  },
  tabletTitle: {
    ...Typography.display.small,
    marginBottom: ms(24),
  },
  responsiveDescription: {
    ...Typography.body.medium,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: ms(24),
    lineHeight: ms(22),
  },
  responsiveGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  tabletGrid: {
    justifyContent: 'space-around',
  },
  gridItem: {
    backgroundColor: Colors.containerligetBlue,
    borderRadius: ms(8),
    padding: ms(16),
    marginBottom: ms(16),
    width: '48%',
    alignItems: 'center',
  },
  gridItemText: {
    ...Typography.label.medium,
    color: Colors.textPrimary,
  },
});

// ============================================================================
// EXPORT EXAMPLES FOR REFERENCE
// ============================================================================

export {
  ExampleHeader,
  ExampleCard,
  ExampleFormField,
  ExampleButton,
  ExampleMapMarkerInfo,
  ExampleResponsiveLayout,
};

// ============================================================================
// USAGE DEMONSTRATION
// ============================================================================

/*
// Example of how to use these components in your application:

import React from 'react';
import { ScrollView } from 'react-native';
import {
  ExampleHeader,
  ExampleCard,
  ExampleFormField,
  ExampleButton,
  ExampleMapMarkerInfo,
  ExampleResponsiveLayout
} from './ImplementationExample';

const MyScreen = () => {
  const [inputValue, setInputValue] = React.useState('');

  return (
    <ScrollView>
      <ExampleHeader 
        title="ePragati Dashboard" 
        subtitle="Hindrance Management System"
        showBackButton={true}
      />
      
      <ExampleCard
        title="Hindrance Report #1234"
        description="Gap found in pipeline section A-45"
        status="online"
        onPress={() => console.log('Card pressed')}
      />
      
      <ExampleFormField
        label="Enter Description"
        placeholder="Describe the hindrance..."
        value={inputValue}
        onChangeText={setInputValue}
        helperText="Please provide detailed information"
      />
      
      <ExampleButton
        title="Submit Report"
        variant="primary"
        onPress={() => console.log('Button pressed')}
      />
      
      <ExampleMapMarkerInfo
        jobCode="JOB-001"
        serialNo="SN-12345"
        gapLength={2.5}
        latitude={12.9716}
        longitude={77.5946}
        onUpdatePress={() => console.log('Update pressed')}
      />
      
      <ExampleResponsiveLayout />
    </ScrollView>
  );
};
*/ 