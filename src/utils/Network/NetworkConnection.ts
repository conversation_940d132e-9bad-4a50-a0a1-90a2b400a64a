import NetInfo from '@react-native-community/netinfo';
import { customAlertWithOK } from '../../components/CustomAlert';
import { t } from 'i18next';

export const isNetworkConnected = async (
  showAlert = true,
): Promise<boolean> => {
  const state = await NetInfo.fetch();

  // Prefer isInternetReachable if available
  const connected = state.isInternetReachable !== null && state.isInternetReachable !== undefined
    ? state.isInternetReachable
    : state.isConnected;

  if (!connected) {
    if (showAlert) {
      customAlertWithOK(
        t('alertMessageStrings.networkAlertTitle'),
        t('alertMessageStrings.noNetworkConnection'),
        [{ text: t('commonStrings.ok') }],
        false,
      );
    }
    return false;
  }
  return true;
};

// export const isNetworkConnected = async (
//   showAlert = true,
// ): Promise<boolean> => {
//   const state = await NetInfo.fetch();

//   if (!state.isConnected) {
//     if (showAlert) {
//       customAlertWithOK(
//         t('alertMessageStrings.networkAlertTitle'),
//         t('alertMessageStrings.noNetworkConnection'),
//         [{ text: t('commonStrings.ok') }],
//         false,
//       );
//     }
//     return false;
//   }
//   return true;
// };

// export const isNetworkConnected = async (
//   showAlert = true,
// ): Promise<boolean> => {
//   return new Promise((resolve) => {
//     const unsubscribe = NetInfo.addEventListener((state) => {
//       if (state.isConnected !== null) {
//         unsubscribe();
//         if (!state.isConnected) {
//           if (showAlert) {
//             customAlertWithOK(
//               t('alertMessageStrings.networkAlertTitle'),
//               t('alertMessageStrings.noNetworkConnection'),
//               [{ text: t('commonStrings.ok') }],
//               false,
//             );
//           }
//           resolve(false);
//         } else {
//           resolve(true);
//         }
//       }
//     });
//   });
// };

// export const isNetworkConnected = async (showAlert = true): Promise<boolean> => {
//   return new Promise((resolve) => {
//     const unsubscribe = NetInfo.addEventListener((state) => {
//       unsubscribe(); // One-time listener
//       if (state.isConnected) {
//         resolve(true);
//       } else {
//         if (showAlert) {
//           customAlertWithOK(
//             t('alertMessageStrings.networkAlertTitle'),
//             t('alertMessageStrings.noNetworkConnection'),
//             [{ text: t('commonStrings.ok') }],
//             false,
//           );
//         }
//         resolve(false);
//       }
//     });
//   });
// };
