import { MMKV } from 'react-native-mmkv';

const storage = new MMKV();

interface Job {
    id: string;
    name: string;
    role: string;
    isCurrentJob?: boolean;
}

export const setSelectedJobs = (jobs: Job[]) => {
    const existingJobs = getSelectedJobs();
    const existingJobIds = new Set(existingJobs.map(job => job.id));

    // Filter out jobs that already exist
    const newJobs = jobs.filter(job => !existingJobIds.has(job.id));

    // Combine existing and new jobs
    const updatedJobs = [...existingJobs, ...newJobs];

    storage.set('selected_jobs', JSON.stringify(updatedJobs));
};

export const getSelectedJobs = (): Job[] => {
    const jobs = storage.getString('selected_jobs');
    return jobs ? JSON.parse(jobs) : [];
};

export const setJobAsCurrent = (jobId: string) => {
    const jobs = getSelectedJobs();
    const updatedJobs = jobs.map(job => ({
        ...job,
        isCurrentJob: job.id === jobId
    }));
    storage.set('selected_jobs', JSON.stringify(updatedJobs));
    return updatedJobs;
};

export const deleteJobFromStorage = (jobId: string) => {
    const jobs = getSelectedJobs();
    const updatedJobs = jobs.filter(job => job.id !== jobId);
    storage.set('selected_jobs', JSON.stringify(updatedJobs));
    return updatedJobs;
}; 