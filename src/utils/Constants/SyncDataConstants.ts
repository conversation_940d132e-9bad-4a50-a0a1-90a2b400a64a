import { AssetDetailsProps, DayPlanningCategoryDetails, ProgressUpdateData, SyncDataHeaderProps } from "../types/SyncData";

export const tabs: SyncDataHeaderProps[] = [
    {
      id: 0,
      isActive: false,
      tabName: 'Progress Update',
      onPress: () => console.log('0')
    },
    {
      id: 1,
      isActive: false,
      tabName: 'Hindrance',
      onPress: () => console.log('0')
    },
    {
      id: 2,
      isActive: false,
      tabName: 'Day Plan',
      onPress: () => console.log('0')
    },
    {
      id: 3,
      isActive: false,
      tabName: 'Asset',
      onPress: () => console.log('0')
    },
  ];

  export const DayPlanningCategory: SyncDataHeaderProps[] = [
    {
      id: 0,
      isActive: false,
      tabName: 'SyncData.manpower',
      onPress: () => console.log('0')
    },
    {
      id: 1,
      isActive: false,
      tabName: 'SyncData.material',
      onPress: () => console.log('0')
    },
    {
      id: 2,
      isActive: false,
      tabName: 'SyncData.pm',
      onPress: () => console.log('0')
    },
    {
      id: 3,
      isActive: false,
      tabName: 'SyncData.safety',
      onPress: () => console.log('0')
    },
  ];

  export const ManPowerCategory: DayPlanningCategoryDetails[] = [
    {
      id: 0,
      name: 'Helper',
      subCategory: [
        {
          catergory: 'Available',
          data: '5',
        },
        {
          catergory: 'Required',
          data: '2'
        },
      ]
    },
    {
      id: 1,
      name: 'Assistant',
      subCategory: [
        {
          catergory: 'Available',
          data: '0',
        },
        {
          catergory: 'Required',
          data: '1',
        },
      ]
    },
    {
      id: 2,
      name: 'Moderator',
      subCategory: [
        {
          catergory: 'Available',
          data: '5',
        },
        {
          catergory: 'Required',
          data: '3',
        },
      ]
    },
    {
      id: 3,
      name: 'Adviser',
      subCategory: [
        {
          catergory: 'Available',
          data: '4',
        },
        {
          catergory: 'Required',
          data: '2',
        },
      ]
    },
  ];

  export const MaterialCategory: DayPlanningCategoryDetails[] = [
    {
      id: 0,
      name: '90 Deg Bend - 200 mm',
      subCategory: [
        {
          catergory: 'UOM',
          data: 'M',
        },
        {
          catergory: 'Available',
          data: '2'
        },
        {
          catergory: 'Req Qty',
          data: '4'
        },
      ]
    },
    {
      id: 1,
      name: '45 Deg Bend - 150 mm',
      subCategory: [
        {
          catergory: 'UOM',
          data: 'M',
        },
        {
          catergory: 'Available',
          data: '2'
        },
        {
          catergory: 'Req Qty',
          data: '4'
        },
      ]
    },
    {
      id: 2,
      name: 'Tee Joint - 100 mm',
      subCategory: [
        {
          catergory: 'UOM',
          data: 'M',
        },
        {
          catergory: 'Available',
          data: '10'
        },
        {
          catergory: 'Req Qty',
          data: '4'
        },
      ]
    },
    {
      id: 3,
      name: 'Cross Joint - 75 mm',
      subCategory: [
        {
          catergory: 'UOM',
          data: 'M',
        },
        {
          catergory: 'Available',
          data: '8'
        },
        {
          catergory: 'Req Qty',
          data: '1'
        },
      ]
    },
  ];

  export const PMCategory: DayPlanningCategoryDetails[] = [
    {
      id: 0,
      name: 'Boom Placer',
      subCategory: [
        {
          catergory: 'From Time',
          data: '09:00 am',
        },
        {
          catergory: 'To Time',
          data: '05:00 am'
        },
        {
          catergory: 'Avaialable',
          data: '2'
        },
        {
          catergory: 'Req Qty',
          data: '2'
        },
      ]
    },
    {
      id: 1,
      name: '45 Deg Bend - 150 mm',
      subCategory: [
        {
          catergory: 'From Time',
          data: '10:00 am',
        },
        {
          catergory: 'To Time',
          data: '06:00 am'
        },
        {
          catergory: 'Unavailable',
          data: '2'
        },
        {
          catergory: 'Req Qty',
          data: '2'
        },
      ]
    },
    {
      id: 2,
      name: 'Tee Joint - 100 mm',
      subCategory: [
        {
          catergory: 'From Time',
          data: '08:30 am',
        },
        {
          catergory: 'To Time',
          data: '04:30 am'
        },
        {
          catergory: 'Avaialable',
          data: '2'
        },
        {
          catergory: 'Req Qty',
          data: '2'
        },
      ]
    },
    {
      id: 3,
      name: 'Cross Joint - 75 mm',
      subCategory: [
        {
          catergory: 'From Time',
          data: '08:30 am',
        },
        {
          catergory: 'To Time',
          data: '07:00 am'
        },
        {
          catergory: 'Avaialable',
          data: '4'
        },
        {
          catergory: 'Req Qty',
          data: '2'
        },
      ]
    },
  ];

  export const SafetyCategory: DayPlanningCategoryDetails[] = [
    {
      id: 0,
      name: 'Sub Activity Name 1',
      subCategory: []
    },
    {
      id: 1,
      name: 'Sub Activity Name 2',
      subCategory: []
    },
    {
      id: 2,
      name: 'Sub Activity Name 3',
      subCategory: []
    },
    {
      id: 3,
      name: 'Sub Activity Name 4',
      subCategory: []
    },
    {
      id: 4,
      name: 'Sub Activity Name 5',
      subCategory: []
    },
  ];

  export const AssetsDetails: AssetDetailsProps[] = [
    {
      id: 0,
      name: '01223645H - Backhoe loader',
      fromDate: new Date().toISOString(),
      startTime: '10:00 AM',
      toDate: new Date().toISOString(),
      endTime: '06:00 PM',
      usage: '90%',
      reason: 'Pipe Laying'
    },
  ];

  export const ProgressUpdateDetailProps: ProgressUpdateData[] = [
    {
      id: 0,
      name: 'SyncData.totalquantity',
      data: '5'
    },
    {
      id: 0,
      name: 'SyncData.progressquantity',
      data: '13'
    },
    {
      id: 2,
      name: 'SyncData.progresstilldate',
      data: '10'
    },
    {
      id: 3,
      name: 'SyncData.mandays',
      data: '1'
    },
  ];

  export const constructionAssets = [
    {
      id: 1,
      name: "Excavator",
      onPress: () => console.log("Excavator pressed"),
    },
    {
      id: 2,
      name: "Bulldozer",
      onPress: () => console.log("Bulldozer pressed"),
    },
    {
      id: 3,
      name: "Crane",
      onPress: () => console.log("Crane pressed"),
    },
    {
      id: 4,
      name: "Concrete Mixer",
      onPress: () => console.log("Concrete Mixer pressed"),
    },
    {
      id: 5,
      name: "Dump Truck",
      onPress: () => console.log("Dump Truck pressed"),
    },
    {
      id: 6,
      name: "Loader",
      onPress: () => console.log("Loader pressed"),
    },
    {
      id: 7,
      name: "Backhoe",
      onPress: () => console.log("Backhoe pressed"),
    },
    {
      id: 8,
      name: "Tower Crane",
      onPress: () => console.log("Tower Crane pressed"),
    },
    {
      id: 9,
      name: "Pile Driver",
      onPress: () => console.log("Pile Driver pressed"),
    },
    {
      id: 10,
      name: "Grader",
      onPress: () => console.log("Grader pressed"),
    },
    {
      id: 11,
      name: "Scaffolding",
      onPress: () => console.log("Scaffolding pressed"),
    },
    {
      id: 12,
      name: "Jackhammer",
      onPress: () => console.log("Jackhammer pressed"),
    },
    {
      id: 13,
      name: "Wheelbarrow",
      onPress: () => console.log("Wheelbarrow pressed"),
    },
    {
      id: 14,
      name: "Welding Machine",
      onPress: () => console.log("Welding Machine pressed"),
    },
    {
      id: 15,
      name: "Compressor",
      onPress: () => console.log("Compressor pressed"),
    },
    {
      id: 16,
      name: "Skid Steer Loader",
      onPress: () => console.log("Skid Steer Loader pressed"),
    },
    {
      id: 17,
      name: "Asphalt Paver",
      onPress: () => console.log("Asphalt Paver pressed"),
    },
    {
      id: 18,
      name: "Vibratory Roller",
      onPress: () => console.log("Vibratory Roller pressed"),
    },
    {
      id: 19,
      name: "Water Tanker",
      onPress: () => console.log("Water Tanker pressed"),
    },
    {
      id: 20,
      name: "Surveying Equipment",
      onPress: () => console.log("Surveying Equipment pressed"),
    },
  ];
  