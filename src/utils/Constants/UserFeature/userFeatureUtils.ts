import DocDownload from '../../../assets/svg/DocDownload.svg';
import UserManual from '../../../assets/svg/UserManual.svg';
import Notification from '../../../assets/svg/Notification.svg';
import Profile from '../../../assets/svg/Profile.svg';
import Report from '../../../assets/svg/Report.svg';
import Strings from '../../Strings/Strings';
import { getUserRolesInfo } from '../../DataStorage/Storage';
import { getSelectedJobs } from '../../Storage/Storage';
import { setJobsDownloaded } from '../../../redux/HomeRedux/HomeActions';

const roles = getUserRolesInfo();

export const COMMON_ITEMS = {
  report: { label: Strings.bottomTabs.reports, icon: Report },
  userManual: { label: Strings.homeScreen.userManual, icon: UserManual },
  notification: { label: Strings.homeScreen.notification, icon: Notification },
  profile: { label: Strings.bottomTabs.profile, icon: Profile },
};

export const getDownloadItem = (
  labelKey: keyof typeof Strings.homeScreen
) => {
  const hasMultipleRoles = Array.isArray(roles?.RolesList) && roles.RolesList.length > 1;
  const selectedJobs = getSelectedJobs();
  const hasSelectedJobs = selectedJobs && selectedJobs.length > 0;

  setJobsDownloaded(hasSelectedJobs);

  return hasMultipleRoles
    ? { label: Strings.homeScreen[labelKey], icon: DocDownload, screen: 'DownloadWBS' }
    : null;
};