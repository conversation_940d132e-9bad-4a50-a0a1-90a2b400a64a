import Receipt from '../../../assets/svg/Receipt.svg';
import Map from '../../../assets/svg/Map.svg';
import MapView from '../../../assets/svg/MapView.svg';
import Message from '../../../assets/svg/Message.svg';
import DayPlan from '../../../assets/svg/DayPlan.svg';
import Asset from '../../../assets/svg/Asset.svg';
import PipeStockYard from '../../../assets/svg/pipe_stock_yard.svg';
import SyncData from '../../../assets/svg/sync_data.svg';
import Strings from '../../Strings/Strings';
import { COMMON_ITEMS, getDownloadItem } from './userFeatureUtils';

export const USER_FEATURES = {
    'Site Engineer': [
      { label: Strings.homeScreen.progressUpdate, icon: Message },
      { label: Strings.homeScreen.hindrance, icon: Map },
      { label: Strings.homeScreen.dayPlanning, icon: DayPlan },
      { label: Strings.homeScreen.assetAllocation, icon: Asset },
      { label: Strings.homeScreen.pipeStockYard, icon: PipeStockYard },
      { label: Strings.homeScreen.syncData, icon: SyncData },
      COMMON_ITEMS.userManual,
      getDownloadItem('downloadWBS'),
    ].filter(Boolean),
  
    'Quality Incharge': [
      { label: Strings.homeScreen.hindrance, icon: Map },
      { label: Strings.homeScreen.pipeTracking, icon: Receipt },
      getDownloadItem('downloadProject'),
      COMMON_ITEMS.userManual,
      COMMON_ITEMS.notification,
      COMMON_ITEMS.profile,
    ].filter(Boolean),
  
    'Store Incharge': [
      { label: Strings.homeScreen.pipeTracking, icon: Receipt, map: true, mapComponent: MapView },
      getDownloadItem('downloadProject'),
      { label: Strings.homeScreen.syncData, icon: Map },
      COMMON_ITEMS.userManual,
      COMMON_ITEMS.notification,
      COMMON_ITEMS.profile,
    ].filter(Boolean),
  
    'Section Incharge': [
      { label: Strings.homeScreen.pipeTrackingApproval, icon: Receipt, badge: 'Pending Request : 10' },
      getDownloadItem('downloadProject'),
      COMMON_ITEMS.userManual,
      COMMON_ITEMS.notification,
      COMMON_ITEMS.profile,
    ].filter(Boolean),
  
    'Accountant': [
      { label: Strings.homeScreen.pipeTrackingApproval, icon: Receipt, badge: 'Pending Request : 10' },
      getDownloadItem('downloadProject'),
      COMMON_ITEMS.userManual,
      COMMON_ITEMS.notification,
      COMMON_ITEMS.profile,
    ].filter(Boolean),
  
    'P&M Incharge': [
      {
        label: Strings.homeScreen.assetAllocation,
        icon: Receipt,
        assetButtons: [
          Strings.homeScreen.requestApproval,
          Strings.homeScreen.assetStatus,
          Strings.homeScreen.directAllocation,
        ],
      },
      getDownloadItem('downloadProject'),
      COMMON_ITEMS.userManual,
      COMMON_ITEMS.notification,
      COMMON_ITEMS.profile,
    ].filter(Boolean),
  
    'Approver': [
      { label: Strings.homeScreen.progressUpdate, icon: Message },
      { label: Strings.homeScreen.hindrance, icon: Map },
      { label: Strings.homeScreen.pipeTracking, icon: Receipt },
      { label: Strings.homeScreen.dayPlan, icon: DayPlan },
      { label: Strings.homeScreen.assetStatus, icon: Asset },
      getDownloadItem('downloadProject'),
      COMMON_ITEMS.report,
      COMMON_ITEMS.userManual,
      COMMON_ITEMS.profile,
    ].filter(Boolean),
  
    'Master Admin': [
      { label: Strings.homeScreen.progressUpdate, icon: Message },
      { label: Strings.homeScreen.hindrance, icon: Map },
      { label: Strings.homeScreen.dayPlan, icon: DayPlan },
      COMMON_ITEMS.report,
      { label: Strings.homeScreen.assetAllocation, icon: Asset },
      COMMON_ITEMS.userManual,
      getDownloadItem('downloadProject'),
      Strings.homeScreen.push_notification,
      COMMON_ITEMS.profile,
    ].filter(Boolean),
  
    'Project Manager': [
      { label: Strings.homeScreen.hindrance, icon: Map },
      { label: Strings.homeScreen.pipeTracking, icon: Receipt },
      { label: Strings.homeScreen.assetStatus, icon: Asset },
      COMMON_ITEMS.report,
      COMMON_ITEMS.userManual,
      getDownloadItem('downloadProject'),
      COMMON_ITEMS.profile,
    ].filter(Boolean),
  };
  