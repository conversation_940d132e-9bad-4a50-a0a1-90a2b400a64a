export const API = {
  login: 'EPragathiLogin',
  MPin: 'EPragathiMPINValidate',
  tokenEndpoint: 'GetGenerateToken',
  approverNavigation: 'GetJobListAPIDetails',
  roles: 'roles',
  WBSDownload: 'WBSDownload',
  masterList: 'MasterList',
  getPendingAppovals: 'PendingForApproval_Progress_NodeProgress',
  getNodeApprovals: 'GetNodeActualsForApproval',
  ProgressUpdateInsert: 'DailyProgress',
};

export const OPERATION_TYPES = {
  INSERT: 'insert',
};
