export const API = {
  login: 'EPragathiLogin',
  MPin: 'EPragathiMPINValidate',
  tokenEndpoint: 'GetGenerateToken',
  approverNavigation: 'GetJobListAPIDetails',
  roles: 'roles',
  WBSDownload: 'WBSDownload',
  masterList: 'MasterList',
  getPendingAppovals: 'PendingForApproval_Progress_NodeProgress',
  getNodeApprovals: 'PendingForApproval_NodeProgress',
  ProgressUpdateInsert: 'DailyProgress',
  userMannual: 'MasterList',
  DownloadImaage: 'DownloadImaage',
  getAuthTokenForNode: 'GetAuthToken',
  getPragatiData: 'GetPragatiData',
  getHindranceGapDetails: 'hinderance',
  insertHindranceUpdate: 'GetGislengthDetails'
};

export const OPERATION_TYPES = {
  INSERT: 'insert',
};
