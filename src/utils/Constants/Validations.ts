import { CommonActions, NavigationProp } from "@react-navigation/native";
import { clearAllData } from "../DataStorage/Storage";
import { VALIDATE_TOKEN_LOGOUT } from "../../redux/AuthRedux/Token/TokenActionTypes";
import { LOGIN_LOGOUT } from "../../redux/AuthRedux/Login/LoginActionTypes";

export const isUserIdValid = (userId: string) => {
    // Only allow numbers
    return /^\d+$/.test(userId) && userId.length > 0;
};
export const removeEmojis = (text: string) => {
    // Remove emoji characters
    return text.replace(/[\u{1F600}-\u{1F6FF}\u{1F300}-\u{1F5FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu, '');
};

export const formatDateWithTwoDigitYear = (dateStr: string | null | undefined) => {
  if (!dateStr) return undefined;
  const date = new Date(dateStr);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = String(date.getFullYear()).slice(-2); // 2-digit year
  return `${day}/${month}/${year}`;
};

export const formatDateWithFullYear = (date: Date) => {
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
};

export const formatDateRangeWithStartEndDate = (start?: string, end?: string) => {
  if (start && end) {
      return `${start} - ${end}`;
  }
  if (start) {
      return start;
  }
  if (end) {
      return end;
  }
  return '-';
};

