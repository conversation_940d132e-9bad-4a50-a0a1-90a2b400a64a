import type React from "react"
import { render, type RenderOptions } from "@testing-library/react-native"
import { Provider } from "react-redux"
import { NavigationContainer } from "@react-navigation/native"
import { configureStore, type EnhancedStore } from "@reduxjs/toolkit"
import axios, { type AxiosResponse, type AxiosError } from "axios"
import { expect } from "@jest/globals"

// Mock Axios instance
const mockedAxios = axios as jest.Mocked<typeof axios>

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================
interface MockStoreState {
  auth?: {
    user?: any
    loading?: boolean
    error?: string | null
    token?: string | null
  }
  home?: {
    currentJobId?: string
    isJobsDownloaded?: boolean
    loading?: boolean
  }
  wbs?: {
    response?: any
    loading?: boolean
    error?: string | null
  }
  dailyProgress?: {
    bookmarks?: any[]
    loading?: boolean
    error?: string | null
  }
  approval?: {
    pending?: any[]
    loading?: boolean
    error?: string | null
  }
}

interface CustomRenderOptions extends Omit<RenderOptions, "wrapper"> {
  initialState?: MockStoreState
  store?: EnhancedStore
  navigation?: any
}

interface MockApiResponse<T = any> {
  status: "success" | "error"
  data: T
  message: string
  timestamp: string
}

interface MockImageAsset {
  uri: string
  type: string
  fileName: string
  fileSize: number
  width: number
  height: number
}

interface MockImageResponse {
  assets?: MockImageAsset[]
  didCancel?: boolean
  errorMessage?: string
}

interface MockNetworkState {
  isConnected: boolean
  isInternetReachable: boolean
  type: string
  details: any
}

// ============================================================================
// MOCK STORE CONFIGURATION
// ============================================================================
const createMockStore = (initialState: MockStoreState = {}): EnhancedStore => {
  const defaultState: Required<MockStoreState> = {
    auth: {
      user: null,
      loading: false,
      error: null,
      token: null,
      ...initialState.auth,
    },
    home: {
      currentJobId: "TEST_JOB_001",
      isJobsDownloaded: true,
      loading: false,
      ...initialState.home,
    },
    wbs: {
      response: null,
      loading: false,
      error: null,
      ...initialState.wbs,
    },
    dailyProgress: {
      bookmarks: [],
      loading: false,
      error: null,
      ...initialState.dailyProgress,
    },
    approval: {
      pending: [],
      loading: false,
      error: null,
      ...initialState.approval,
    },
  }

  return configureStore({
    reducer: {
      auth: (state = defaultState.auth, action: any) => {
        switch (action.type) {
          case "LOGIN_REQUEST":
            return { ...state, loading: true, error: null }
          case "LOGIN_SUCCESS":
            return { ...state, loading: false, user: action.payload, error: null }
          case "LOGIN_FAILURE":
            return { ...state, loading: false, error: action.payload }
          case "LOGOUT":
            return { ...state, user: null, token: null }
          case "RESET_AUTH_STATE":
            return defaultState.auth
          default:
            return state
        }
      },
      home: (state = defaultState.home, action: any) => {
        switch (action.type) {
          case "SET_CURRENT_JOB":
            return { ...state, currentJobId: action.payload }
          case "SET_JOBS_DOWNLOADED":
            return { ...state, isJobsDownloaded: action.payload }
          case "SET_HOME_LOADING":
            return { ...state, loading: action.payload }
          case "RESET_HOME_STATE":
            return defaultState.home
          default:
            return state
        }
      },
      wbs: (state = defaultState.wbs, action: any) => {
        switch (action.type) {
          case "WBS_REQUEST":
            return { ...state, loading: true, error: null }
          case "WBS_SUCCESS":
            return { ...state, loading: false, response: action.payload, error: null }
          case "WBS_FAILURE":
            return { ...state, loading: false, error: action.payload }
          case "RESET_WBS_STATE":
            return defaultState.wbs
          default:
            return state
        }
      },
      dailyProgress: (state = defaultState.dailyProgress, action: any) => {
        switch (action.type) {
          case "BOOKMARK_REQUEST":
            return { ...state, loading: true, error: null }
          case "BOOKMARK_SUCCESS":
            return { ...state, loading: false, bookmarks: action.payload, error: null }
          case "BOOKMARK_FAILURE":
            return { ...state, loading: false, error: action.payload }
          case "ADD_BOOKMARK":
            return { ...state, bookmarks: [...state.bookmarks, action.payload] }
          case "REMOVE_BOOKMARK":
            return {
              ...state,
              bookmarks: state.bookmarks.filter((bookmark: any) => bookmark.id !== action.payload),
            }
          case "RESET_DAILY_PROGRESS_STATE":
            return defaultState.dailyProgress
          default:
            return state
        }
      },
      approval: (state = defaultState.approval, action: any) => {
        switch (action.type) {
          case "APPROVAL_REQUEST":
            return { ...state, loading: true, error: null }
          case "APPROVAL_SUCCESS":
            return { ...state, loading: false, pending: action.payload, error: null }
          case "APPROVAL_FAILURE":
            return { ...state, loading: false, error: action.payload }
          case "APPROVE_ITEM":
            return {
              ...state,
              pending: state.pending.filter((item: any) => item.id !== action.payload),
            }
          case "REJECT_ITEM":
            return {
              ...state,
              pending: state.pending.map((item: any) =>
                item.id === action.payload.id ? { ...item, status: "rejected", reason: action.payload.reason } : item,
              ),
            }
          case "RESET_APPROVAL_STATE":
            return defaultState.approval
          default:
            return state
        }
      },
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
        },
        immutableCheck: {
          warnAfter: 128,
        },
      }),
    devTools: process.env.NODE_ENV !== "production",
  })
}

// ============================================================================
// RENDER WITH PROVIDERS
// ============================================================================
const renderWithProviders = (
  ui: React.ReactElement,
  {
    initialState = {},
    store = createMockStore(initialState),
    navigation = {},
    ...renderOptions
  }: CustomRenderOptions = {},
) => {
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <Provider store={store}>
      <NavigationContainer>{children}</NavigationContainer>
    </Provider>
  )

  return {
    store,
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  }
}

// ============================================================================
// AXIOS MOCK HELPERS
// ============================================================================
export const mockAxiosSuccess = <T = any>(data: T, status = 200): void => {
  const response: Partial<AxiosResponse<T>> = {
    data,
    status,
    statusText: "OK",
    headers: {},
    config: {} as any,
  }

  mockedAxios.request.mockResolvedValueOnce(response as AxiosResponse<T>)
  mockedAxios.get?.mockResolvedValueOnce?.(response as AxiosResponse<T>)
  mockedAxios.post?.mockResolvedValueOnce?.(response as AxiosResponse<T>)
  mockedAxios.put?.mockResolvedValueOnce?.(response as AxiosResponse<T>)
  mockedAxios.delete?.mockResolvedValueOnce?.(response as AxiosResponse<T>)
}

export const mockAxiosError = (error: any, status = 500): void => {
  const errorResponse: Partial<AxiosError> = {
    response: {
      status,
      data: error,
      statusText: "Error",
      headers: {},
      config: {} as any,
    },
    message: typeof error === "string" ? error : "Request failed",
    isAxiosError: true,
    name: "AxiosError",
    config: {} as any,
    toJSON: () => ({}),
  }

  mockedAxios.request.mockRejectedValueOnce(errorResponse)
  mockedAxios.get?.mockRejectedValueOnce?.(errorResponse)
  mockedAxios.post?.mockRejectedValueOnce?.(errorResponse)
  mockedAxios.put?.mockRejectedValueOnce?.(errorResponse)
  mockedAxios.delete?.mockRejectedValueOnce?.(errorResponse)
}

export const mockAxiosNetworkError = (): void => {
  const networkError: Partial<AxiosError> = {
    message: "Network Error",
    code: "ECONNABORTED",
    isAxiosError: true,
    name: "AxiosError",
    config: {} as any,
    toJSON: () => ({}),
  }

  mockedAxios.request.mockRejectedValueOnce(networkError)
  mockedAxios.get?.mockRejectedValueOnce?.(networkError)
  mockedAxios.post?.mockRejectedValueOnce?.(networkError)
  mockedAxios.put?.mockRejectedValueOnce?.(networkError)
  mockedAxios.delete?.mockRejectedValueOnce?.(networkError)
}

export const mockAxiosTimeout = (): void => {
  const timeoutError: Partial<AxiosError> = {
    message: "timeout of 5000ms exceeded",
    code: "ECONNABORTED",
    isAxiosError: true,
    name: "AxiosError",
    config: {} as any,
    toJSON: () => ({}),
  }

  mockedAxios.request.mockRejectedValueOnce(timeoutError)
}

export const resetAxiosMocks = (): void => {
  if (mockedAxios.request?.mockClear) {
    mockedAxios.request.mockClear()
  }
  if (mockedAxios.get?.mockClear) {
    mockedAxios.get.mockClear()
  }
  if (mockedAxios.post?.mockClear) {
    mockedAxios.post.mockClear()
  }
  if (mockedAxios.put?.mockClear) {
    mockedAxios.put.mockClear()
  }
  if (mockedAxios.delete?.mockClear) {
    mockedAxios.delete.mockClear()
  }
  if (mockedAxios.patch?.mockClear) {
    mockedAxios.patch.mockClear()
  }
}

// ============================================================================
// MOCK DATA GENERATORS
// ============================================================================
export const mockUserData = (overrides: Partial<any> = {}) => ({
  id: "user_123",
  username: "test_user",
  email: "<EMAIL>",
  role: "engineer",
  permissions: ["read", "write"],
  profile: {
    firstName: "John",
    lastName: "Doe",
    phone: "+1234567890",
    avatar: null,
    department: "Engineering",
    position: "Senior Engineer",
  },
  preferences: {
    language: "en",
    theme: "light",
    notifications: true,
    timezone: "UTC",
  },
  lastLogin: new Date().toISOString(),
  isActive: true,
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: new Date().toISOString(),
  ...overrides,
})

export const mockJobData = (overrides: Partial<any> = {}) => ({
  id: "JOB_001",
  code: "TEST_JOB_001",
  name: "Test Project",
  description: "Test project for unit testing",
  status: "active",
  priority: "high",
  startDate: "2024-01-01",
  endDate: "2024-12-31",
  actualStartDate: "2024-01-01",
  actualEndDate: null,
  progress: 25,
  budget: 1000000,
  spent: 250000,
  location: {
    latitude: 12.9716,
    longitude: 77.5946,
    address: "Test Location, Test City",
    country: "India",
    state: "Karnataka",
    city: "Bangalore",
  },
  team: {
    projectManager: "user_123",
    engineers: ["user_124", "user_125"],
    supervisors: ["user_126"],
  },
  client: {
    name: "Test Client",
    contact: "<EMAIL>",
    phone: "+1234567890",
  },
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: new Date().toISOString(),
  ...overrides,
})

export const mockWBSData = (overrides: Partial<any> = {}) => ({
  id: "WBS_001",
  code: "WBS_TEST_001",
  name: "Test WBS Item",
  description: "Test WBS item for unit testing",
  parentId: null,
  level: 1,
  status: "active",
  type: "work_package",
  plannedStartDate: "2024-01-01",
  plannedEndDate: "2024-01-31",
  actualStartDate: null,
  actualEndDate: null,
  progress: 0,
  plannedProgress: 0,
  budget: 50000,
  spent: 0,
  resources: [],
  dependencies: [],
  milestones: [],
  risks: [],
  issues: [],
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: new Date().toISOString(),
  ...overrides,
})

export const mockBookmarkData = (overrides: Partial<any> = {}) => ({
  id: "BOOKMARK_001",
  wbsCode: "WBS_TEST_001",
  jobCode: "TEST_JOB_001",
  description: "Test bookmark",
  notes: "Test bookmark notes",
  latitude: 12.9716,
  longitude: 77.5946,
  accuracy: 5,
  altitude: 920,
  heading: 0,
  speed: 0,
  timestamp: new Date().toISOString(),
  photos: [],
  attachments: [],
  tags: ["test", "bookmark"],
  isActive: true,
  isSynced: false,
  createdBy: "user_123",
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: new Date().toISOString(),
  status: "active",
  ...overrides,
})

export const mockProgressData = (overrides: Partial<any> = {}) => ({
  id: "PROGRESS_001",
  wbsCode: "WBS_TEST_001",
  jobCode: "TEST_JOB_001",
  date: "2024-01-01",
  actualProgress: 25,
  plannedProgress: 20,
  description: "Test progress entry",
  notes: "Progress notes",
  issues: [],
  risks: [],
  attachments: [],
  photos: [],
  weather: {
    condition: "sunny",
    temperature: 25,
    humidity: 60,
    windSpeed: 10,
  },
  resources: {
    manpower: 10,
    equipment: 2,
    materials: ["cement", "steel"],
  },
  quality: {
    inspections: [],
    tests: [],
    defects: [],
  },
  safety: {
    incidents: [],
    nearMisses: [],
    observations: [],
  },
  createdBy: "test_user",
  approvedBy: null,
  status: "submitted",
  isSubmitted: true,
  isSynced: false,
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: new Date().toISOString(),
  ...overrides,
})

export const mockApiResponse = <T = any>(data: T, status: "success" | "error" = "success"): MockApiResponse<T> => ({
  status,
  data,
  message: status === "success" ? "Operation successful" : "Operation failed",
  timestamp: new Date().toISOString(),
})

// ============================================================================
// PERMISSION MOCK HELPERS
// ============================================================================
export const mockPermissionGranted = (): void => {
  const { check, request, PERMISSIONS, RESULTS } = require("react-native-permissions")
  ;(check as jest.Mock).mockResolvedValue(RESULTS.GRANTED)
  ;(request as jest.Mock).mockResolvedValue(RESULTS.GRANTED)
}

export const mockPermissionDenied = (): void => {
  const { check, request, PERMISSIONS, RESULTS } = require("react-native-permissions")
  ;(check as jest.Mock).mockResolvedValue(RESULTS.DENIED)
  ;(request as jest.Mock).mockResolvedValue(RESULTS.DENIED)
}

export const mockPermissionBlocked = (): void => {
  const { check, request, PERMISSIONS, RESULTS } = require("react-native-permissions")
  ;(check as jest.Mock).mockResolvedValue(RESULTS.BLOCKED)
  ;(request as jest.Mock).mockResolvedValue(RESULTS.BLOCKED)
}

export const mockPermissionUnavailable = (): void => {
  const { check, request, PERMISSIONS, RESULTS } = require("react-native-permissions")
  ;(check as jest.Mock).mockResolvedValue(RESULTS.UNAVAILABLE)
  ;(request as jest.Mock).mockResolvedValue(RESULTS.UNAVAILABLE)
}

export const mockPermissionLimited = (): void => {
  const { check, request, PERMISSIONS, RESULTS } = require("react-native-permissions")
  ;(check as jest.Mock).mockResolvedValue(RESULTS.LIMITED)
  ;(request as jest.Mock).mockResolvedValue(RESULTS.LIMITED)
}

// ============================================================================
// PLATFORM MOCK HELPERS
// ============================================================================
export const mockPlatformIOS = (version = "14.0"): void => {
  const Platform = require("react-native/Libraries/Utilities/Platform")
  Platform.OS = "ios"
  Platform.Version = version
  Platform.select = jest.fn((obj) => obj.ios)
  Platform.isPad = false
  Platform.isTVOS = false
}

export const mockPlatformAndroid = (version = 30): void => {
  const Platform = require("react-native/Libraries/Utilities/Platform")
  Platform.OS = "android"
  Platform.Version = version
  Platform.select = jest.fn((obj) => obj.android)
  Platform.constants = {
    reactNativeVersion: { major: 0, minor: 70, patch: 0 },
    Version: version,
    Release: "11",
    Serial: "unknown",
    Fingerprint: "test-fingerprint",
    Model: "Test Device",
    Brand: "Test Brand",
    Manufacturer: "Test Manufacturer",
  }
}

export const mockPlatformWeb = (): void => {
  const Platform = require("react-native/Libraries/Utilities/Platform")
  Platform.OS = "web"
  Platform.select = jest.fn((obj) => obj.web || obj.default)
}

// ============================================================================
// NETWORK MOCK HELPERS
// ============================================================================
export const mockNetworkConnected = (type = "wifi"): void => {
  const NetInfo = require("@react-native-community/netinfo")
  const connectedState: MockNetworkState = {
    isConnected: true,
    isInternetReachable: true,
    type,
    details: {
      isConnectionExpensive: false,
      ssid: type === "wifi" ? "Test_WiFi" : null,
      strength: type === "wifi" ? 100 : 4,
      ipAddress: "*************",
      subnet: "*************",
      frequency: type === "wifi" ? 2400 : null,
    },
  }

  NetInfo.fetch.mockResolvedValue(connectedState)
  NetInfo.addEventListener.mockImplementation((listener: (state: MockNetworkState) => void) => {
    listener(connectedState)
    return jest.fn() // unsubscribe function
  })
}

export const mockNetworkDisconnected = (): void => {
  const NetInfo = require("@react-native-community/netinfo")
  const disconnectedState: MockNetworkState = {
    isConnected: false,
    isInternetReachable: false,
    type: "none",
    details: null,
  }

  NetInfo.fetch.mockResolvedValue(disconnectedState)
  NetInfo.addEventListener.mockImplementation((listener: (state: MockNetworkState) => void) => {
    listener(disconnectedState)
    return jest.fn() // unsubscribe function
  })
}

export const mockNetworkSlow = (): void => {
  const NetInfo = require("@react-native-community/netinfo")
  const slowState: MockNetworkState = {
    isConnected: true,
    isInternetReachable: true,
    type: "cellular",
    details: {
      isConnectionExpensive: true,
      cellularGeneration: "3g",
      carrier: "Test Carrier",
    },
  }

  NetInfo.fetch.mockResolvedValue(slowState)
}

// ============================================================================
// IMAGE PICKER MOCK HELPERS
// ============================================================================
export const mockImagePickerSuccess = (imageUri = "file://test-image.jpg"): void => {
  const { launchCamera, launchImageLibrary } = require("react-native-image-picker")
  const successResponse: MockImageResponse = {
    assets: [
      {
        uri: imageUri,
        type: "image/jpeg",
        fileName: "test-image.jpg",
        fileSize: 1024,
        width: 800,
        height: 600,
      },
    ],
  }

  launchCamera.mockImplementation((options: any, callback: (response: MockImageResponse) => void) => {
    callback(successResponse)
  })

  launchImageLibrary.mockImplementation((options: any, callback: (response: MockImageResponse) => void) => {
    callback(successResponse)
  })
}

export const mockImagePickerMultipleSuccess = (
  imageUris: string[] = ["file://test-image1.jpg", "file://test-image2.jpg"],
): void => {
  const { launchImageLibrary } = require("react-native-image-picker")
  const successResponse: MockImageResponse = {
    assets: imageUris.map((uri, index) => ({
      uri,
      type: "image/jpeg",
      fileName: `test-image${index + 1}.jpg`,
      fileSize: 1024 * (index + 1),
      width: 800,
      height: 600,
    })),
  }

  launchImageLibrary.mockImplementation((options: any, callback: (response: MockImageResponse) => void) => {
    callback(successResponse)
  })
}

export const mockImagePickerCancel = (): void => {
  const { launchCamera, launchImageLibrary } = require("react-native-image-picker")
  const cancelResponse: MockImageResponse = {
    didCancel: true,
  }

  launchCamera.mockImplementation((options: any, callback: (response: MockImageResponse) => void) => {
    callback(cancelResponse)
  })

  launchImageLibrary.mockImplementation((options: any, callback: (response: MockImageResponse) => void) => {
    callback(cancelResponse)
  })
}

export const mockImagePickerError = (errorMessage = "Image picker error"): void => {
  const { launchCamera, launchImageLibrary } = require("react-native-image-picker")
  const errorResponse: MockImageResponse = {
    errorMessage,
  }

  launchCamera.mockImplementation((options: any, callback: (response: MockImageResponse) => void) => {
    callback(errorResponse)
  })

  launchImageLibrary.mockImplementation((options: any, callback: (response: MockImageResponse) => void) => {
    callback(errorResponse)
  })
}

// ============================================================================
// DATABASE MOCK HELPERS
// ============================================================================
export const mockDatabaseQuery = (mockData: any[]) => {
  const mockQuery = {
    fetch: jest.fn().mockResolvedValue(
      mockData.map((item) => ({
        id: item.id,
        _raw: item,
        ...item,
      })),
    ),
    fetchCount: jest.fn().mockResolvedValue(mockData.length),
    observe: jest.fn().mockReturnValue({
      subscribe: jest.fn(),
      unsubscribe: jest.fn(),
    }),
    observeCount: jest.fn().mockReturnValue({
      subscribe: jest.fn(),
      unsubscribe: jest.fn(),
    }),
  }
  return mockQuery
}

export const mockDatabaseError = (errorMessage = "Database error") => {
  const error = new Error(errorMessage)
  const mockQuery = {
    fetch: jest.fn().mockRejectedValue(error),
    fetchCount: jest.fn().mockRejectedValue(error),
    observe: jest.fn().mockReturnValue({
      subscribe: jest.fn(),
      unsubscribe: jest.fn(),
    }),
    observeCount: jest.fn().mockReturnValue({
      subscribe: jest.fn(),
      unsubscribe: jest.fn(),
    }),
  }
  return mockQuery
}

export const mockDatabaseTransaction = (shouldSucceed = true) => {
  const mockDatabase = {
    write: jest.fn().mockImplementation(async (callback: () => Promise<any>) => {
      if (shouldSucceed) {
        return await callback()
      } else {
        throw new Error("Transaction failed")
      }
    }),
    read: jest.fn().mockImplementation(async (callback: () => Promise<any>) => {
      return await callback()
    }),
  }
  return mockDatabase
}

// ============================================================================
// GEOLOCATION MOCK HELPERS
// ============================================================================
export const mockGeolocationSuccess = (coords = { latitude: 12.9716, longitude: 77.5946 }): void => {
  const mockGeolocation = {
    getCurrentPosition: jest.fn().mockImplementation((success: (position: any) => void) => {
      success({
        coords: {
          ...coords,
          accuracy: 5,
          altitude: 920,
          altitudeAccuracy: 10,
          heading: 0,
          speed: 0,
        },
        timestamp: Date.now(),
      })
    }),
    watchPosition: jest.fn().mockImplementation((success: (position: any) => void) => {
      success({
        coords: {
          ...coords,
          accuracy: 5,
          altitude: 920,
          altitudeAccuracy: 10,
          heading: 0,
          speed: 0,
        },
        timestamp: Date.now(),
      })
      return 1 // watchId
    }),
    clearWatch: jest.fn(),
  }
  ;(global as any).navigator = {
    geolocation: mockGeolocation,
  }
}

export const mockGeolocationError = (code = 1, message = "Location access denied"): void => {
  const mockGeolocation = {
    getCurrentPosition: jest.fn().mockImplementation((success: any, error: (error: any) => void) => {
      error({
        code,
        message,
        PERMISSION_DENIED: 1,
        POSITION_UNAVAILABLE: 2,
        TIMEOUT: 3,
      })
    }),
    watchPosition: jest.fn().mockImplementation((success: any, error: (error: any) => void) => {
      error({
        code,
        message,
        PERMISSION_DENIED: 1,
        POSITION_UNAVAILABLE: 2,
        TIMEOUT: 3,
      })
      return 1 // watchId
    }),
    clearWatch: jest.fn(),
  }
  ;(global as any).navigator = {
    geolocation: mockGeolocation,
  }
}

// ============================================================================
// ASYNC STORAGE MOCK HELPERS
// ============================================================================
export const mockAsyncStorage = () => {
  const AsyncStorage = require("@react-native-async-storage/async-storage")
  const storage: { [key: string]: string } = {}

  AsyncStorage.setItem.mockImplementation(async (key: string, value: string) => {
    storage[key] = value
    return Promise.resolve()
  })

  AsyncStorage.getItem.mockImplementation(async (key: string) => {
    return Promise.resolve(storage[key] || null)
  })

  AsyncStorage.removeItem.mockImplementation(async (key: string) => {
    delete storage[key]
    return Promise.resolve()
  })

  AsyncStorage.clear.mockImplementation(async () => {
    Object.keys(storage).forEach((key) => delete storage[key])
    return Promise.resolve()
  })

  AsyncStorage.getAllKeys.mockImplementation(async () => {
    return Promise.resolve(Object.keys(storage))
  })

  AsyncStorage.multiGet.mockImplementation(async (keys: string[]) => {
    return Promise.resolve(keys.map((key) => [key, storage[key] || null]))
  })

  AsyncStorage.multiSet.mockImplementation(async (keyValuePairs: [string, string][]) => {
    keyValuePairs.forEach(([key, value]) => {
      storage[key] = value
    })
    return Promise.resolve()
  })

  AsyncStorage.multiRemove.mockImplementation(async (keys: string[]) => {
    keys.forEach((key) => delete storage[key])
    return Promise.resolve()
  })

  return storage
}

// ============================================================================
// ASYNC TESTING HELPERS
// ============================================================================
export const waitForAsync = (ms = 0): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

export const flushPromises = (): Promise<void> => {
  return new Promise((resolve) => setImmediate(resolve))
}

export const waitForCondition = async (condition: () => boolean, timeout = 5000, interval = 100): Promise<void> => {
  const startTime = Date.now()

  while (!condition() && Date.now() - startTime < timeout) {
    await waitForAsync(interval)
  }

  if (!condition()) {
    throw new Error(`Condition not met within ${timeout}ms`)
  }
}

export const mockTimers = () => {
  jest.useFakeTimers()
  return {
    advanceTimersByTime: jest.advanceTimersByTime,
    runAllTimers: jest.runAllTimers,
    runOnlyPendingTimers: jest.runOnlyPendingTimers,
    clearAllTimers: jest.clearAllTimers,
    restoreTimers: jest.useRealTimers,
  }
}

// ============================================================================
// SETUP AND TEARDOWN HELPERS
// ============================================================================
export const setupTest = (): void => {
  // Clear all mocks before each test
  jest.clearAllMocks()

  // Reset console mocks
  if (global.console.warn && typeof global.console.warn === "function") {
    ;(global.console.warn as jest.Mock).mockClear?.()
  }
  if (global.console.error && typeof global.console.error === "function") {
    ;(global.console.error as jest.Mock).mockClear?.()
  }
  if (global.console.log && typeof global.console.log === "function") {
    ;(global.console.log as jest.Mock).mockClear?.()
  }

  // Reset axios mocks
  resetAxiosMocks()

  // Mock console methods to avoid noise in tests
  jest.spyOn(console, "warn").mockImplementation(() => {})
  jest.spyOn(console, "error").mockImplementation(() => {})
  jest.spyOn(console, "log").mockImplementation(() => {})
}

export const teardownTest = (): void => {
  // Clean up any remaining timers
  jest.clearAllTimers()

  // Use real timers
  jest.useRealTimers()

  // Reset all mocks to initial state
  jest.restoreAllMocks()

  // Clear all mock calls and instances
  jest.clearAllMocks()
}

export const setupGlobalMocks = (): void => {
  // Mock Alert
  jest.mock("react-native", () => ({
    ...jest.requireActual("react-native"),
    Alert: {
      alert: jest.fn(),
    },
    Dimensions: {
      get: jest.fn(() => ({ width: 375, height: 812 })),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
    Platform: {
      OS: "ios",
      select: jest.fn((obj) => obj.ios),
      Version: 25,
    },
  }))

  // Mock react-navigation
  jest.mock("@react-navigation/native", () => ({
    useNavigation: () => ({
      navigate: jest.fn(),
      dispatch: jest.fn(),
      goBack: jest.fn(),
      canGoBack: jest.fn(() => true),
    }),
    useRoute: () => ({
      params: {},
    }),
    useFocusEffect: jest.fn(),
    NavigationContainer: ({ children }: { children: React.ReactNode }) => children,
    StackActions: {
      replace: jest.fn(),
      push: jest.fn(),
      pop: jest.fn(),
      popToTop: jest.fn(),
    },
  }))

  // Mock i18next
  jest.mock("i18next", () => ({
    t: (key: string, options?: any) => {
      if (options && typeof options === "object") {
        let result = key
        Object.keys(options).forEach((optionKey) => {
          result = result.replace(`{{${optionKey}}}`, options[optionKey])
        })
        return result
      }
      return key
    },
    changeLanguage: jest.fn(),
    language: "en",
  }))
}

// ============================================================================
// CUSTOM MATCHERS
// ============================================================================
declare global {
  namespace jest {
    interface Matchers<R> {
      toHaveBeenCalledWithError(error: any): R
      toHaveBeenCalledWithSuccess(data: any): R
      toBeWithinRange(floor: number, ceiling: number): R
      toHaveValidTimestamp(): R
    }
  }
}

// Add custom matchers
expect.extend({
  toHaveBeenCalledWithError(received: jest.Mock, error: any) {
    const calls = received.mock.calls
    const hasErrorCall = calls.some(
      (call) =>
        call.length > 0 &&
        (call[0] === error ||
          (typeof call[0] === "object" && call[0]?.message === error) ||
          (typeof call[0] === "string" && call[0].includes(error))),
    )

    return {
      message: () => `expected ${received.getMockName() || "mock function"} to have been called with error: ${error}`,
      pass: hasErrorCall,
    }
  },

  toHaveBeenCalledWithSuccess(received: jest.Mock, data: any) {
    const calls = received.mock.calls
    const hasSuccessCall = calls.some((call) => call.length > 0 && JSON.stringify(call[0]) === JSON.stringify(data))

    return {
      message: () =>
        `expected ${received.getMockName() || "mock function"} to have been called with success data: ${JSON.stringify(data)}`,
      pass: hasSuccessCall,
    }
  },

  toBeWithinRange(received: number, floor: number, ceiling: number) {
    const pass = received >= floor && received <= ceiling
    return {
      message: () => `expected ${received} to be within range ${floor} - ${ceiling}`,
      pass,
    }
  },

  toHaveValidTimestamp(received: string) {
    const timestamp = new Date(received)
    const pass = !isNaN(timestamp.getTime())
    return {
      message: () => `expected ${received} to be a valid timestamp`,
      pass,
    }
  },
})

// ============================================================================
// EXPORTS
// ============================================================================
export { renderWithProviders, createMockStore, type MockStoreState, type CustomRenderOptions, type MockApiResponse }
