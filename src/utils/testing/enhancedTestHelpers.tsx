import React, { ReactElement } from 'react';
import { render, RenderOptions, fireEvent, waitFor } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { configureStore, Store } from '@reduxjs/toolkit';
import { runSaga, Saga } from 'redux-saga';
import { Alert, Dimensions } from 'react-native';

// ============================================================================
// ENHANCED MOCK STORE CONFIGURATION
// ============================================================================

interface EnhancedMockStoreState {
  auth?: {
    user?: any;
    loading?: boolean;
    error?: string | null;
    token?: string | null;
    isAuthenticated?: boolean;
  };
  home?: {
    currentJobId?: string;
    isJobsDownloaded?: boolean;
    loading?: boolean;
    jobs?: any[];
    selectedJob?: any;
  };
  wbs?: {
    response?: any;
    loading?: boolean;
    error?: string | null;
    pendingApprovalResponse?: any[];
    masterWBSData?: any[];
  };
  dailyProgress?: {
    bookmarks?: any[];
    loading?: boolean;
    error?: string | null;
    progressData?: any[];
    selectedProgress?: any;
    updateLoading?: boolean;
  };
  approval?: {
    pending?: any[];
    loading?: boolean;
    error?: string | null;
    approveLoading?: boolean;
    rejectLoading?: boolean;
  };
  hindrance?: {
    hindrances?: any[];
    loading?: boolean;
    error?: string | null;
    createLoading?: boolean;
    updateLoading?: boolean;
    mapData?: any[];
  };
  sync?: {
    syncing?: boolean;
    lastSyncTime?: string | null;
    syncProgress?: number;
    syncError?: string | null;
  };
}

const createEnhancedMockStore = (initialState: EnhancedMockStoreState = {}) => {
  const defaultState: EnhancedMockStoreState = {
    auth: {
      user: null,
      loading: false,
      error: null,
      token: null,
      isAuthenticated: false,
      ...initialState.auth,
    },
    home: {
      currentJobId: null,
      isJobsDownloaded: false,
      loading: false,
      jobs: [],
      selectedJob: null,
      ...initialState.home,
    },
    wbs: {
      response: null,
      loading: false,
      error: null,
      pendingApprovalResponse: [],
      masterWBSData: [],
      ...initialState.wbs,
    },
    dailyProgress: {
      bookmarks: [],
      loading: false,
      error: null,
      progressData: [],
      selectedProgress: null,
      updateLoading: false,
      ...initialState.dailyProgress,
    },
    approval: {
      pending: [],
      loading: false,
      error: null,
      approveLoading: false,
      rejectLoading: false,
      ...initialState.approval,
    },
    hindrance: {
      hindrances: [],
      loading: false,
      error: null,
      createLoading: false,
      updateLoading: false,
      mapData: [],
      ...initialState.hindrance,
    },
    sync: {
      syncing: false,
      lastSyncTime: null,
      syncProgress: 0,
      syncError: null,
      ...initialState.sync,
    },
  };

  return configureStore({
    reducer: {
      auth: (state = defaultState.auth, action: any) => state,
      home: (state = defaultState.home, action: any) => state,
      wbs: (state = defaultState.wbs, action: any) => state,
      dailyProgress: (state = defaultState.dailyProgress, action: any) => state,
      approval: (state = defaultState.approval, action: any) => state,
      hindrance: (state = defaultState.hindrance, action: any) => state,
      sync: (state = defaultState.sync, action: any) => state,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
        thunk: true,
      }),
  });
};

// ============================================================================
// NAVIGATION MOCKING
// ============================================================================

const createMockNavigation = (overrides = {}) => ({
  navigate: jest.fn(),
  goBack: jest.fn(),
  dispatch: jest.fn(),
  setOptions: jest.fn(),
  addListener: jest.fn(),
  removeListener: jest.fn(),
  canGoBack: jest.fn(() => true),
  reset: jest.fn(),
  push: jest.fn(),
  pop: jest.fn(),
  popToTop: jest.fn(),
  replace: jest.fn(),
  setParams: jest.fn(),
  getState: jest.fn(() => ({ routes: [], index: 0 })),
  isFocused: jest.fn(() => true),
  getId: jest.fn(() => 'test-screen'),
  ...overrides,
});

const createMockRoute = (params = {}, overrides = {}) => ({
  key: 'test-route-key',
  name: 'TestScreen',
  params,
  path: undefined,
  ...overrides,
});

// ============================================================================
// API MOCKING UTILITIES
// ============================================================================

interface MockApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

const createMockApiResponse = (
  data: any = null,
  success: boolean = true,
  error: string | null = null
): MockApiResponse => ({
  success,
  data,
  error,
  message: success ? 'Success' : error || 'Unknown error',
});

const mockApiService = {
  login: jest.fn(),
  logout: jest.fn(),
  fetchWBSData: jest.fn(),
  fetchBookmarks: jest.fn(),
  updateProgress: jest.fn(),
  createHindrance: jest.fn(),
  approveProgress: jest.fn(),
  rejectProgress: jest.fn(),
  syncData: jest.fn(),
  uploadImage: jest.fn(),
  downloadFile: jest.fn(),
};

// ============================================================================
// DATABASE MOCKING
// ============================================================================

const mockDatabase = {
  insertProgress: jest.fn(),
  updateProgress: jest.fn(),
  deleteProgress: jest.fn(),
  getProgress: jest.fn(),
  getAllProgress: jest.fn(),
  insertHindrance: jest.fn(),
  updateHindrance: jest.fn(),
  getHindrances: jest.fn(),
  insertWBSData: jest.fn(),
  getWBSData: jest.fn(),
  clearAllData: jest.fn(),
};

// ============================================================================
// ASYNC TESTING UTILITIES
// ============================================================================

const waitForAsync = async (callback: () => Promise<void> | void) => {
  await waitFor(async () => {
    await callback();
  });
};

const flushPromises = () => new Promise(setImmediate);

// ============================================================================
// REDUX SAGA TESTING UTILITIES
// ============================================================================

const runSagaTest = async (saga: Saga, action: any, mockState: any = {}) => {
  const dispatched: any[] = [];
  const sagaEnvironment = {
    dispatch: (action: any) => dispatched.push(action),
    getState: () => mockState,
  };

  await runSaga(sagaEnvironment, saga, action).toPromise();
  return dispatched;
};

// ============================================================================
// CUSTOM RENDER FUNCTIONS
// ============================================================================

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialState?: EnhancedMockStoreState;
  store?: Store;
  navigation?: any;
  route?: any;
  withNavigation?: boolean;
}

const Stack = createNativeStackNavigator();

const customRender = (
  ui: ReactElement,
  {
    initialState = {},
    store,
    navigation,
    route,
    withNavigation = true,
    ...renderOptions
  }: CustomRenderOptions = {}
) => {
  const mockStore = store || createEnhancedMockStore(initialState);
  
  const mockNavigation = navigation || createMockNavigation();
  const mockRoute = route || createMockRoute();

  const TestComponent = () => ui;

  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    const content = (
      <Provider store={mockStore}>
        {children}
      </Provider>
    );

    if (withNavigation) {
      return (
        <NavigationContainer>
          <Stack.Navigator initialRouteName="Test">
            <Stack.Screen 
              name="Test" 
              component={() => content}
              options={{ headerShown: false }}
            />
          </Stack.Navigator>
        </NavigationContainer>
      );
    }

    return content;
  };

  // Inject navigation and route props if component expects them
  const componentWithProps = React.cloneElement(ui, {
    navigation: mockNavigation,
    route: mockRoute,
  });

  return {
    ...render(componentWithProps, { wrapper: Wrapper, ...renderOptions }),
    store: mockStore,
    navigation: mockNavigation,
    route: mockRoute,
  };
};

// ============================================================================
// INTERACTION TESTING UTILITIES
// ============================================================================

const userInteractions = {
  tapButton: async (getByTestId: any, testId: string) => {
    const button = getByTestId(testId);
    fireEvent.press(button);
    await flushPromises();
  },

  enterText: async (getByTestId: any, testId: string, text: string) => {
    const input = getByTestId(testId);
    fireEvent.changeText(input, text);
    await flushPromises();
  },

  scrollToEnd: async (getByTestId: any, testId: string) => {
    const scrollView = getByTestId(testId);
    fireEvent.scroll(scrollView, {
      nativeEvent: {
        contentOffset: { y: 1000, x: 0 },
        contentSize: { height: 2000, width: 400 },
        layoutMeasurement: { height: 800, width: 400 },
      },
    });
    await flushPromises();
  },

  swipeLeft: async (getByTestId: any, testId: string) => {
    const element = getByTestId(testId);
    fireEvent(element, 'onSwipeLeft');
    await flushPromises();
  },

  longPress: async (getByTestId: any, testId: string) => {
    const element = getByTestId(testId);
    fireEvent(element, 'onLongPress');
    await flushPromises();
  },
};

// ============================================================================
// ERROR SIMULATION UTILITIES
// ============================================================================

const errorSimulation = {
  networkError: () => {
    throw new Error('Network request failed');
  },

  timeoutError: () => {
    throw new Error('Request timeout');
  },

  serverError: () => ({
    success: false,
    error: 'Internal server error',
    status: 500,
  }),

  authError: () => ({
    success: false,
    error: 'Authentication failed',
    status: 401,
  }),

  validationError: (field: string) => ({
    success: false,
    error: `Validation failed for ${field}`,
    status: 400,
  }),
};

// ============================================================================
// SNAPSHOT TESTING UTILITIES
// ============================================================================

const snapshotTest = (Component: ReactElement, props: any = {}) => {
  const { toJSON } = customRender(React.cloneElement(Component, props));
  expect(toJSON()).toMatchSnapshot();
};

// ============================================================================
// PERFORMANCE TESTING UTILITIES
// ============================================================================

const performanceTest = {
  measureRenderTime: async (Component: ReactElement) => {
    const start = performance.now();
    customRender(Component);
    const end = performance.now();
    return end - start;
  },

  measureMemoryUsage: () => {
    // Mock implementation for React Native environment
    return {
      usedJSHeapSize: 0,
      totalJSHeapSize: 0,
      jsHeapSizeLimit: 0,
    };
  },
};

// ============================================================================
// DEVICE MOCKING UTILITIES
// ============================================================================

const deviceMocking = {
  mockSmallScreen: () => {
    jest.spyOn(Dimensions, 'get').mockReturnValue({
      width: 320,
      height: 568,
      scale: 2,
      fontScale: 1,
    });
  },

  mockLargeScreen: () => {
    jest.spyOn(Dimensions, 'get').mockReturnValue({
      width: 414,
      height: 896,
      scale: 3,
      fontScale: 1,
    });
  },

  mockLandscape: () => {
    jest.spyOn(Dimensions, 'get').mockReturnValue({
      width: 896,
      height: 414,
      scale: 3,
      fontScale: 1,
    });
  },

  restoreScreen: () => {
    jest.restoreAllMocks();
  },
};

// ============================================================================
// MOCK DATA GENERATORS
// ============================================================================

const mockDataGenerators = {
  user: (overrides = {}) => ({
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'user',
    token: 'mock-token',
    ...overrides,
  }),

  wbsItem: (overrides = {}) => ({
    id: '1',
    name: 'Test WBS Item',
    description: 'Test description',
    status: 'active',
    progress: 50,
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    ...overrides,
  }),

  dailyProgress: (overrides = {}) => ({
    id: '1',
    wbsId: '1',
    date: '2024-01-01',
    progress: 25,
    remarks: 'Test progress',
    images: [],
    location: { latitude: 0, longitude: 0 },
    ...overrides,
  }),

  hindrance: (overrides = {}) => ({
    id: '1',
    title: 'Test Hindrance',
    description: 'Test hindrance description',
    severity: 'medium',
    status: 'open',
    createdDate: '2024-01-01',
    ...overrides,
  }),
};

// ============================================================================
// EXPORTS
// ============================================================================

export {
  customRender,
  createEnhancedMockStore,
  createMockNavigation,
  createMockRoute,
  createMockApiResponse,
  mockApiService,
  mockDatabase,
  userInteractions,
  errorSimulation,
  snapshotTest,
  performanceTest,
  deviceMocking,
  mockDataGenerators,
  waitForAsync,
  flushPromises,
  runSagaTest,
};

// Default export for convenience
export default {
  render: customRender,
  mockStore: createEnhancedMockStore,
  mockNavigation: createMockNavigation,
  mockRoute: createMockRoute,
  mockApi: mockApiService,
  mockDb: mockDatabase,
  interactions: userInteractions,
  errors: errorSimulation,
  snapshot: snapshotTest,
  performance: performanceTest,
  device: deviceMocking,
  mockData: mockDataGenerators,
  async: { waitFor: waitForAsync, flush: flushPromises },
  saga: runSagaTest,
}; 