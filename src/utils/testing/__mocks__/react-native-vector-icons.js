// Mock for react-native-vector-icons
import React from 'react';

const createIconSet = () => {
  return React.forwardRef((props, ref) => {
    return React.createElement('Text', {
      ...props,
      ref,
      testID: props.testID || 'icon',
    });
  });
};

const createIconSetFromFontello = createIconSet;
const createIconSetFromIcoMoon = createIconSet;

// Default export for individual icon sets
const MockIcon = React.forwardRef((props, ref) => {
  return React.createElement('Text', {
    ...props,
    ref,
    testID: props.testID || 'icon',
  });
});

MockIcon.Button = React.forwardRef((props, ref) => {
  return React.createElement('TouchableOpacity', {
    ...props,
    ref,
    testID: props.testID || 'icon-button',
  });
});

MockIcon.getImageSource = jest.fn(() => Promise.resolve({}));
MockIcon.getImageSourceSync = jest.fn(() => ({}));
MockIcon.getRawGlyphMap = jest.fn(() => ({}));
MockIcon.getFontFamily = jest.fn(() => 'MockFont');

export default MockIcon;
export { createIconSet, createIconSetFromFontello, createIconSetFromIcoMoon }; 