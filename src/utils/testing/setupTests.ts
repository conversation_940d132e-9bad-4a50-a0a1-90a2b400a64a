// Mock utilities that use React Native APIs
jest.mock('../../utils/Scale/Scaling', () => ({
  ms: jest.fn((size, factor = 0.5) => Math.round(size + (size - size) * factor)),
  s: jest.fn((size) => size * 1.2),
  vs: jest.fn((size) => size * 1.2),
  mvs: jest.fn((size, factor = 0.5) => Math.round(size + (size - size) * factor)),
}));

// Mock PixelRatio at the component level - must be before any imports
jest.mock('../../components/Fonts', () => ({
  AppFonts: {
    Extra_Light: 'Manrope-ExtraLight',
    Light: 'Manrope-Light',
    Regular: 'Manrope-Regular',
    Medium: 'Manrope-Medium',
    SemiBold: 'Manrope-SemiBold',
    Bold: 'Manrope-Bold',
    Extra_Bold: 'Manrope-ExtraBold',
  },
  DeviceType: {
    isTablet: false,
    isSmallPhone: false,
    isLargePhone: true,
    isLandscape: false,
    pixelDensity: 2,
    screenWidth: 375,
    screenHeight: 812,
  },
  Typography: {
    display: { large: { fontSize: 57, fontFamily: 'Manrope-ExtraBold', lineHeight: 62.7, letterSpacing: -0.5 } },
    headline: { large: { fontSize: 32, fontFamily: 'Manrope-Bold', lineHeight: 40, letterSpacing: 0 } },
    title: { large: { fontSize: 22, fontFamily: 'Manrope-SemiBold', lineHeight: 30.8, letterSpacing: 0.1 } },
    label: { large: { fontSize: 16, fontFamily: 'Manrope-SemiBold', lineHeight: 24, letterSpacing: 0.1 } },
    body: { large: { fontSize: 16, fontFamily: 'Manrope-Regular', lineHeight: 25.6, letterSpacing: 0.15 } },
    caption: { large: { fontSize: 12, fontFamily: 'Manrope-Regular', lineHeight: 20.4, letterSpacing: 0.3 } },
  },
  ComponentTypography: {
    button: { primary: { fontSize: 16, fontFamily: 'Manrope-SemiBold', lineHeight: 19.2, letterSpacing: 0.1 } },
    input: { label: { fontSize: 14, fontFamily: 'Manrope-Medium', lineHeight: 18.2, letterSpacing: 0.1 } },
    card: { title: { fontSize: 20, fontFamily: 'Manrope-SemiBold', lineHeight: 26, letterSpacing: 0.1 } },
    navigation: { title: { fontSize: 20, fontFamily: 'Manrope-SemiBold', lineHeight: 24, letterSpacing: 0.1 } },
    mapMarker: { cardTitle: { fontSize: 18, fontFamily: 'Manrope-SemiBold', lineHeight: 23.4, letterSpacing: 0.1 } },
  },
  getFontSize: jest.fn((category, size) => 16),
  getTypographyStyle: jest.fn(() => ({ fontSize: 16, fontFamily: 'Manrope-Regular' })),
  getComponentTypography: jest.fn(() => ({ fontSize: 16, fontFamily: 'Manrope-Regular' })),
  createTypographyStyle: jest.fn(() => ({ fontSize: 16, fontFamily: 'Manrope-Regular' })),
  getAccessibleFontSize: jest.fn((size) => size),
  isAccessibleFontSize: jest.fn(() => true),
  PlatformTypography: {
    getPlatformStyle: jest.fn((style) => style),
  },
}));

import '@testing-library/jest-native/extend-expect';

// Mock React Native core modules
jest.mock('react-native/Libraries/Utilities/Platform', () => ({
  OS: 'ios',
  select: (obj: any) => obj.ios,
  Version: 25,
}));



jest.mock('react-native/Libraries/Utilities/Dimensions', () => {
  const mockDimensions = {
    get: jest.fn((dimension) => {
      if (dimension === 'window') {
        return { width: 375, height: 812 };
      }
      return { width: 375, height: 812 };
    }),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
  };
  return {
    ...mockDimensions,
    default: mockDimensions, // For default import compatibility
  };
});


// Mock StyleSheet
jest.mock('react-native/Libraries/StyleSheet/StyleSheet', () => ({
  create: (styles: any) => styles,
  flatten: (style: any) => style,
  compose: (style1: any, style2: any) => [style1, style2],
}));

// Mock React Native Text Input
jest.mock('react-native/Libraries/Components/TextInput/TextInput', () => 'TextInput');

// Mock React Native components
jest.mock('react-native/Libraries/Components/Touchable/TouchableOpacity', () => 'TouchableOpacity');
jest.mock('react-native/Libraries/Components/Touchable/TouchableHighlight', () => 'TouchableHighlight');
jest.mock('react-native/Libraries/Components/Touchable/TouchableWithoutFeedback', () => 'TouchableWithoutFeedback');
jest.mock('react-native/Libraries/Components/ScrollView/ScrollView', () => 'ScrollView');
jest.mock('react-native/Libraries/Lists/FlatList', () => 'FlatList');
jest.mock('react-native/Libraries/Lists/SectionList', () => 'SectionList');
jest.mock('react-native/Libraries/Components/ActivityIndicator/ActivityIndicator', () => 'ActivityIndicator');

// Mock React Navigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
    dispatch: jest.fn(),
    setOptions: jest.fn(),
    addListener: jest.fn(),
    removeListener: jest.fn(),
    canGoBack: jest.fn(() => true),
    reset: jest.fn(),
    setParams: jest.fn(),
  }),
  useRoute: () => ({
    params: {},
    name: 'MockRoute',
    key: 'mock-route-key',
  }),
  useFocusEffect: jest.fn(),
  useIsFocused: jest.fn(() => true),
  NavigationContainer: ({ children }: any) => children,
  CommonActions: {
    reset: jest.fn(),
    navigate: jest.fn(),
    goBack: jest.fn(),
  },
  StackActions: {
    replace: jest.fn(),
    push: jest.fn(),
    pop: jest.fn(),
  },
}));

jest.mock('@react-navigation/native-stack', () => ({
  createNativeStackNavigator: () => ({
    Navigator: ({ children }: any) => children,
    Screen: ({ children }: any) => children,
  }),
}));

// Vector Icons are handled by manual mock file

// Mock WatermelonDB
jest.mock('@nozbe/watermelondb', () => ({
  Database: jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    collections: {
      get: jest.fn(() => ({
        query: jest.fn(() => ({
          fetch: jest.fn(() => Promise.resolve([])),
          observe: jest.fn(() => ({
            subscribe: jest.fn(),
          })),
        })),
        create: jest.fn(() => Promise.resolve({})),
        find: jest.fn(() => Promise.resolve({})),
      })),
    },
    write: jest.fn((fn) => fn()),
    batch: jest.fn(() => Promise.resolve()),
    adapter: {
      schema: {},
      migrations: {},
    },
  })),
  Model: class MockModel {
    static table = 'mock_table';
    static associations = {};
    constructor() {}
    prepareCreate = jest.fn();
    prepareUpdate = jest.fn();
    prepareDestroy = jest.fn();
    observe = jest.fn(() => ({ subscribe: jest.fn() }));
  },
  appSchema: jest.fn(() => ({})),
  tableSchema: jest.fn(() => ({})),
  Q: {
    where: jest.fn(),
    oneOf: jest.fn(),
    and: jest.fn(),
    or: jest.fn(),
    like: jest.fn(),
    eq: jest.fn(),
    notEq: jest.fn(),
    gt: jest.fn(),
    gte: jest.fn(),
    lt: jest.fn(),
    lte: jest.fn(),
    between: jest.fn(),
    in: jest.fn(),
    notIn: jest.fn(),
    includes: jest.fn(),
    sortBy: jest.fn(),
  },
  field: () => (target: any, propertyKey: string) => {},
  readonly: () => (target: any, propertyKey: string) => {},
  date: () => (target: any, propertyKey: string) => {},
  children: () => (target: any, propertyKey: string) => {},
  lazy: () => (target: any, propertyKey: string) => {},
  relation: () => (target: any, propertyKey: string) => {},
  action: () => (target: any, propertyKey: string) => {},
  text: () => (target: any, propertyKey: string) => {},
  json: () => (target: any, propertyKey: string) => {},
  writer: () => (target: any, propertyKey: string) => {},
}));

jest.mock('@nozbe/watermelondb/adapters/sqlite', () => {
  class MockSQLiteAdapter {
    constructor() {}
  }
  return {
    __esModule: true,
    default: MockSQLiteAdapter
  };
});

jest.mock('@nozbe/watermelondb/decorators', () => ({
  field: () => (target: any, propertyKey: string) => {},
  readonly: () => (target: any, propertyKey: string) => {},
  date: () => (target: any, propertyKey: string) => {},
  children: () => (target: any, propertyKey: string) => {},
  lazy: () => (target: any, propertyKey: string) => {},
  text: () => (target: any, propertyKey: string) => {},
  json: () => (target: any, propertyKey: string) => {},
  relation: () => (target: any, propertyKey: string) => {},
  action: () => (target: any, propertyKey: string) => {},
  writer: () => (target: any, propertyKey: string) => {},
}));

// Mock Axios
jest.mock('axios', () => ({
  default: {
    defaults: {
      baseURL: '',
      timeout: 5000,
      headers: {},
    },
    request: jest.fn(() => Promise.resolve({ data: {} })),
    get: jest.fn(() => Promise.resolve({ data: {} })),
    post: jest.fn(() => Promise.resolve({ data: {} })),
    put: jest.fn(() => Promise.resolve({ data: {} })),
    delete: jest.fn(() => Promise.resolve({ data: {} })),
    interceptors: {
      request: {
        use: jest.fn(),
        eject: jest.fn(),
      },
      response: {
        use: jest.fn(),
        eject: jest.fn(),
      },
    },
  },
  create: jest.fn(() => ({
    defaults: {
      baseURL: '',
      timeout: 5000,
      headers: {},
    },
    request: jest.fn(() => Promise.resolve({ data: {} })),
    get: jest.fn(() => Promise.resolve({ data: {} })),
    post: jest.fn(() => Promise.resolve({ data: {} })),
    put: jest.fn(() => Promise.resolve({ data: {} })),
    delete: jest.fn(() => Promise.resolve({ data: {} })),
    interceptors: {
      request: {
        use: jest.fn(),
        eject: jest.fn(),
      },
      response: {
        use: jest.fn(),
        eject: jest.fn(),
      },
    },
  })),
}));

// Mock React Redux
jest.mock('react-redux', () => ({
  useSelector: jest.fn((selector) => selector({
    auth: { user: null, loading: false, error: null, token: null },
    home: { currentJobId: 'TEST_JOB', isJobsDownloaded: true, loading: false },
    wbs: { response: null, loading: false, error: null },
    bookmark: { bookmarks: [], loading: false, error: null },
    rolesData: { roles: [], loading: false, error: null },
    progressUpdate: { data: null, loading: false, error: null },
    approveReducer: { pending: [], loading: false, error: null },
    hindranceMap: { data: [], loading: false, error: null },
    userMannual: { pdfUrl: null, loading: false, error: null },
    uploadImageReducer: { response: null, loading: false, error: null },
  })),
  useDispatch: () => jest.fn(),
  Provider: ({ children }: any) => children,
  connect: () => (component: any) => component,
}));

// Mock Redux Saga
jest.mock('redux-saga/effects', () => ({
  call: jest.fn(),
  put: jest.fn(),
  take: jest.fn(),
  fork: jest.fn(),
  spawn: jest.fn(),
  select: jest.fn(),
  takeEvery: jest.fn(),
  takeLatest: jest.fn(),
  takeLeading: jest.fn(),
  delay: jest.fn(),
  race: jest.fn(),
  all: jest.fn(),
  cancel: jest.fn(),
  cancelled: jest.fn(),
  flush: jest.fn(),
  getContext: jest.fn(),
  setContext: jest.fn(),
  cps: jest.fn(),
  apply: jest.fn(),
  actionChannel: jest.fn(),
  eventChannel: jest.fn(),
}));

// Mock i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, params?: any) => {
      if (params) {
        return `${key}_with_params`;
      }
      return key;
    },
    i18n: {
      changeLanguage: jest.fn(),
      language: 'en',
      isInitialized: true,
    },
  }),
  Trans: ({ children }: any) => children,
  initReactI18next: {
    type: '3rdParty',
    init: jest.fn(),
  },
}));

// Mock NetInfo
jest.mock('@react-native-community/netinfo', () => ({
  fetch: jest.fn(() => Promise.resolve({
    isConnected: true,
    isInternetReachable: true,
    type: 'wifi',
    details: {
      isConnectionExpensive: false,
      ssid: 'Test_WiFi',
      strength: 100,
    },
  })),
  addEventListener: jest.fn(),
  useNetInfo: jest.fn(() => ({
    isConnected: true,
    isInternetReachable: true,
    type: 'wifi',
  })),
}));

// Mock Permissions
jest.mock('react-native-permissions', () => ({
  PERMISSIONS: {
    IOS: {
      CAMERA: 'ios.permission.CAMERA',
      PHOTO_LIBRARY: 'ios.permission.PHOTO_LIBRARY',
      LOCATION_WHEN_IN_USE: 'ios.permission.LOCATION_WHEN_IN_USE',
    },
    ANDROID: {
      CAMERA: 'android.permission.CAMERA',
      READ_EXTERNAL_STORAGE: 'android.permission.READ_EXTERNAL_STORAGE',
      WRITE_EXTERNAL_STORAGE: 'android.permission.WRITE_EXTERNAL_STORAGE',
      ACCESS_FINE_LOCATION: 'android.permission.ACCESS_FINE_LOCATION',
    },
  },
  RESULTS: {
    GRANTED: 'granted',
    DENIED: 'denied',
    BLOCKED: 'blocked',
    LIMITED: 'limited',
    UNAVAILABLE: 'unavailable',
  },
  request: jest.fn(() => Promise.resolve('granted')),
  check: jest.fn(() => Promise.resolve('granted')),
  checkMultiple: jest.fn(() => Promise.resolve({})),
  requestMultiple: jest.fn(() => Promise.resolve({})),
  openSettings: jest.fn(() => Promise.resolve()),
}));

// Mock Image Picker
jest.mock('react-native-image-picker', () => ({
  ImagePicker: {
    showImagePicker: jest.fn(),
    launchCamera: jest.fn(),
    launchImageLibrary: jest.fn(),
  },
  launchCamera: jest.fn((options, callback) => {
    if (callback) {
      callback({
        assets: [{
          uri: 'file://test-image.jpg',
          type: 'image/jpeg',
          fileName: 'test-image.jpg',
          fileSize: 1024,
          width: 800,
          height: 600,
        }]
      });
    }
  }),
  launchImageLibrary: jest.fn((options, callback) => {
    if (callback) {
      callback({
        assets: [{
          uri: 'file://test-image.jpg',
          type: 'image/jpeg',
          fileName: 'test-image.jpg',
          fileSize: 1024,
          width: 800,
          height: 600,
        }]
      });
    }
  }),
}));

// Mock DateTimePicker
jest.mock('@react-native-community/datetimepicker', () => ({
  default: 'MockDateTimePicker',
}));

// Mock Device Info
jest.mock('react-native-device-info', () => ({
  getVersion: jest.fn(() => '1.0.0'),
  getBuildNumber: jest.fn(() => '1'),
  getSystemVersion: jest.fn(() => '14.0'),
  getModel: jest.fn(() => 'iPhone'),
  getDeviceId: jest.fn(() => 'test-device-id'),
  getUniqueId: jest.fn(() => Promise.resolve('test-unique-id')),
  isTablet: jest.fn(() => false),
  hasNotch: jest.fn(() => false),
}));

// Mock Blob Util
jest.mock('react-native-blob-util', () => ({
  fs: {
    dirs: {
      DocumentDir: '/mock/documents',
      CacheDir: '/mock/cache',
    },
    writeFile: jest.fn(() => Promise.resolve()),
    readFile: jest.fn(() => Promise.resolve('')),
    exists: jest.fn(() => Promise.resolve(true)),
    unlink: jest.fn(() => Promise.resolve()),
    mkdir: jest.fn(() => Promise.resolve()),
  },
  config: jest.fn(() => ({
    fetch: jest.fn(() => Promise.resolve({
      data: 'mock-data',
      path: jest.fn(() => '/mock/path'),
    })),
  })),
}));

// Mock Alert
jest.mock('react-native/Libraries/Alert/Alert', () => ({
  alert: jest.fn(),
}));

// Mock Linking
jest.mock('react-native/Libraries/Linking/Linking', () => ({
  openURL: jest.fn(() => Promise.resolve()),
  canOpenURL: jest.fn(() => Promise.resolve(true)),
  getInitialURL: jest.fn(() => Promise.resolve(null)),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
}));

// Mock Toast Message
jest.mock('react-native-toast-message', () => ({
  show: jest.fn(),
  hide: jest.fn(),
  success: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  BaseToast: 'BaseToast',
  ErrorToast: 'ErrorToast',
  InfoToast: 'InfoToast',
}));

// Mock CryptoJS
jest.mock('crypto-js', () => ({
  AES: {
    encrypt: jest.fn(() => ({ toString: () => 'encrypted_data' })),
    decrypt: jest.fn(() => ({ toString: () => 'decrypted_data' })),
  },
  enc: {
    Utf16LE: {
      stringify: jest.fn(() => 'utf16_string'),
      parse: jest.fn(() => ({})),
    },
    Base64: {
      parse: jest.fn(() => ({})),
      stringify: jest.fn(() => 'base64_string'),
    },
    Hex: {
      parse: jest.fn(() => ({})),
      stringify: jest.fn(() => 'hex_string'),
    },
    Utf8: {
      parse: jest.fn(() => ({})),
      stringify: jest.fn(() => 'utf8_string'),
    },
  },
  PBKDF2: jest.fn(() => ({
    words: new Array(12).fill(0),
    toString: jest.fn(() => 'pbkdf2_string'),
  })),
  lib: {
    WordArray: {
      create: jest.fn(() => ({})),
      random: jest.fn(() => ({ toString: () => 'random_string' })),
    },
    CipherParams: {
      create: jest.fn(() => ({})),
    },
  },
  algo: {
    SHA1: 'sha1',
    SHA256: 'sha256',
  },
  mode: {
    CBC: 'cbc',
  },
  pad: {
    Pkcs7: 'pkcs7',
  },
  HmacSHA256: jest.fn(() => ({ toString: () => 'hmac_signature' })),
}));

// Mock React Native Config
jest.mock('react-native-config', () => ({
  API_URL: 'https://test-api.com',
  API_BASE_URL: 'https://test-api.com',
  API_SUBSCRIPTION_KEY: 'test-subscription-key',
  DEBUG: true,
  ENVIRONMENT: 'test',
}));

// Mock React Native SVG
jest.mock('react-native-svg', () => ({
  Svg: 'MockSvg',
  Circle: 'MockCircle',
  Path: 'MockPath',
  G: 'MockG',
  Text: 'MockText',
  Line: 'MockLine',
  Rect: 'MockRect',
  Defs: 'MockDefs',
  LinearGradient: 'MockLinearGradient',
  Stop: 'MockStop',
}));

// Mock React Native PDF
jest.mock('react-native-pdf', () => ({
  default: 'MockPDF',
}));

// Mock React Native Maps
jest.mock('react-native-maps', () => ({
  default: 'MockMapView',
  Marker: 'MockMarker',
  Callout: 'MockCallout',
  Circle: 'MockCircle',
  Polygon: 'MockPolygon',
  Polyline: 'MockPolyline',
  UrlTile: 'MockUrlTile',
  PROVIDER_GOOGLE: 'google',
  PROVIDER_DEFAULT: 'default',
}));

// Mock React Native Geolocation
jest.mock('@react-native-community/geolocation', () => ({
  getCurrentPosition: jest.fn((success, error, options) => {
    if (success) {
      success({
        coords: {
          latitude: 37.7749,
          longitude: -122.4194,
          altitude: 0,
          accuracy: 5,
          altitudeAccuracy: 5,
          heading: 0,
          speed: 0,
        },
        timestamp: Date.now(),
      });
    }
  }),
  watchPosition: jest.fn(() => 1),
  clearWatch: jest.fn(),
  stopObserving: jest.fn(),
  requestAuthorization: jest.fn(() => Promise.resolve()),
  setRNConfiguration: jest.fn(),
}));

// Mock React Native Calendars
jest.mock('react-native-calendars', () => ({
  Calendar: 'MockCalendar',
  CalendarList: 'MockCalendarList',
  Agenda: 'MockAgenda',
}));

// Mock React Native Element Dropdown
jest.mock('react-native-element-dropdown', () => ({
  Dropdown: 'MockDropdown',
  SelectCountry: 'MockSelectCountry',
  MultiSelect: 'MockMultiSelect',
}));

// Mock React Native Picker
jest.mock('@react-native-picker/picker', () => ({
  Picker: 'MockPicker',
  PickerItem: 'MockPickerItem',
}));

// Mock React Native Circular Progress
jest.mock('react-native-circular-progress', () => ({
  CircularProgress: 'MockCircularProgress',
  AnimatedCircularProgress: 'MockAnimatedCircularProgress',
}));

// Mock React Native Safe Area Context
jest.mock('react-native-safe-area-context', () => ({
  SafeAreaProvider: ({ children }: any) => children,
  SafeAreaView: ({ children }: any) => children,
  useSafeAreaInsets: () => ({ top: 0, bottom: 0, left: 0, right: 0 }),
  useSafeAreaFrame: () => ({ x: 0, y: 0, width: 375, height: 812 }),
}));

// Mock React Native Screens
jest.mock('react-native-screens', () => ({
  Screen: 'MockScreen',
  ScreenContainer: 'MockScreenContainer',
}));

// Global test utilities
global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
};

// Mock performance API
global.performance = {
  now: jest.fn(() => Date.now()),
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByName: jest.fn(() => []),
  getEntriesByType: jest.fn(() => []),
  clearMarks: jest.fn(),
  clearMeasures: jest.fn(),
} as any;

// Mock requestAnimationFrame
global.requestAnimationFrame = jest.fn((cb) => setTimeout(cb, 0));
global.cancelAnimationFrame = jest.fn();

// Mock InteractionManager
jest.mock('react-native/Libraries/Interaction/InteractionManager', () => ({
  runAfterInteractions: jest.fn((callback) => Promise.resolve(callback())),
  createInteractionHandle: jest.fn(() => 1),
  clearInteractionHandle: jest.fn(),
}));

// Mock BackHandler
jest.mock('react-native/Libraries/Utilities/BackHandler', () => ({
  addEventListener: jest.fn(() => ({
    remove: jest.fn(),
  })),
  removeEventListener: jest.fn(),
  exitApp: jest.fn(),
}));

// Mock AppState
jest.mock('react-native/Libraries/AppState/AppState', () => ({
  currentState: 'active',
  addEventListener: jest.fn(() => ({
    remove: jest.fn(),
  })),
  removeEventListener: jest.fn(),
}));

// Mock Keyboard
jest.mock('react-native/Libraries/Components/Keyboard/Keyboard', () => ({
  addListener: jest.fn(() => ({
    remove: jest.fn(),
  })),
  removeListener: jest.fn(),
  removeAllListeners: jest.fn(),
  dismiss: jest.fn(),
}));

// Mock PixelRatio
jest.mock('react-native/Libraries/Utilities/PixelRatio', () => ({
  get: jest.fn(() => 2),
  getFontScale: jest.fn(() => 1),
  getPixelSizeForLayoutSize: jest.fn((size) => size * 2),
  roundToNearestPixel: jest.fn((size) => size),
}));

// Mock Vibration
jest.mock('react-native/Libraries/Vibration/Vibration', () => ({
  vibrate: jest.fn(),
  cancel: jest.fn(),
}));

// Suppress console warnings during tests
const originalWarn = console.warn;
const originalError = console.error;

beforeEach(() => {
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterEach(() => {
  console.warn = originalWarn;
  console.error = originalError;
  jest.clearAllMocks();
}); 