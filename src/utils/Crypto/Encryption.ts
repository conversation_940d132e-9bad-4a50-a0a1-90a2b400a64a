import CryptoJS from 'crypto-js';

export const encryptString = (text: string): string => {
  const encryptionKey = 'lntecceipphoenix';
  const salt = CryptoJS.enc.Hex.parse('4976616e204d65647665646576'); // same as .NET salt

  // Derive key and IV using PBKDF2 like Rfc2898DeriveBytes
  const key256Bits = CryptoJS.PBKDF2(encryptionKey, salt, {
    keySize: 256 / 32 + 128 / 32, // 32 bytes = 256-bit key, 16 bytes = 128-bit IV
    iterations: 1000,
    hasher: CryptoJS.algo.SHA1,
  });

  const key = CryptoJS.lib.WordArray.create(key256Bits.words.slice(0, 8)); // 32 bytes
  const iv = CryptoJS.lib.WordArray.create(key256Bits.words.slice(8, 12)); // 16 bytes

  const encrypted = CryptoJS.AES.encrypt(CryptoJS.enc.Utf16LE.parse(text), key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });

  return encrypted.toString(); // Base64 encoded string
};
