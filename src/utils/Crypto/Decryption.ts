import CryptoJS from 'crypto-js';
import PrintLog from '../Logger/PrintLog';

export const decryptString = (cipherText: string): string => {
  try {
    const encryptionKey = 'lntecceipphoenix';
    const salt = CryptoJS.enc.Hex.parse('4976616e204d65647665646576'); // same as .NET

    // Derive key (32 bytes) and IV (16 bytes) from PBKDF2
    const fullKey = CryptoJS.PBKDF2(encryptionKey, salt, {
      keySize: (32 + 16) / 4, // 48 bytes total, /4 because keySize is in words
      iterations: 1000,
      hasher: CryptoJS.algo.SHA1,
    });

    const key = CryptoJS.lib.WordArray.create(fullKey.words.slice(0, 8));  // 32 bytes
    const iv = CryptoJS.lib.WordArray.create(fullKey.words.slice(8, 12));  // 16 bytes

    const cleanedCipherText = cipherText
      .replace(/ /g, '+')
      .replace(/^["\\]+|["\\]+$/g, ''); // NOSONAR


    const encrypted = CryptoJS.enc.Base64.parse(cleanedCipherText);

    const decrypted = CryptoJS.AES.decrypt(
      CryptoJS.lib.CipherParams.create({ ciphertext: encrypted }),
      key,
      {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      }
    );

    // .NET uses Encoding.Unicode => UTF-16LE
    return CryptoJS.enc.Utf16LE.stringify(decrypted);
  } catch (error) {
    PrintLog.error('Decryption error:', error);
    return '';
  }
};
