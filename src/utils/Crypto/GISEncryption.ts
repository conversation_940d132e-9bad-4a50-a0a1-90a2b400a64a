// import CryptoJS from 'crypto-js';
// import { pbkdf2Sync } from 'pbkdf2';

// // Helper to convert a word array to a Uint8Array
// const wordArrayToUint8Array = (wordArray: CryptoJS.lib.WordArray): Uint8Array => {
//   const words = wordArray.words;
//   const sigBytes = wordArray.sigBytes;
//   const u8 = new Uint8Array(sigBytes);
//   for (let i = 0; i < sigBytes; i++) {
//     u8[i] = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
//   }
//   return u8;
// };

// export const encryptGis = (clearText: string): string => {
//   const encryptionKey = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1';
//   const salt = Buffer.from([
//     0x49, 0x76, 0x61, 0x6e,
//     0x20, 0x4d, 0x65, 0x64,
//     0x76, 0x65, 0x64, 0x65,
//     0x76,
//   ]);

//   // Derive key and IV from encryptionKey and salt
//   const derivedBytes = pbkdf2Sync(encryptionKey, salt, 1000, 48, 'sha1');
//   const key = derivedBytes.slice(0, 32);
//   const iv = derivedBytes.slice(32, 48);

//   // Encrypt using AES with key and IV
//   const encrypted = CryptoJS.AES.encrypt(
//     CryptoJS.enc.Utf16LE.parse(clearText), // Matches .NET Unicode
//     CryptoJS.enc.Hex.parse(Buffer.from(key).toString('hex')),
//     {
//       iv: CryptoJS.enc.Hex.parse(Buffer.from(iv).toString('hex')),
//       mode: CryptoJS.mode.CBC,
//       padding: CryptoJS.pad.Pkcs7,
//     }
//   );

//   // URL encode the Base64 result (as .NET returns base64)
//   return encodeURIComponent(encrypted.toString());
// };
