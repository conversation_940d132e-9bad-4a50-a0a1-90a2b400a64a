import { t } from "i18next";

const Strings = {
  commonStrings: {
    logoutConfirmationMessage: 'Are you sure want to Logout?',
  },
  loginStrings: {
    login: () => t('loginStrings.login'),
  },
  welcomeMessageStrings: {
    welcome: () => t('welcomeMessageStrings.welcome'),
  },
  apiHeaderStrings: {
    PLAIN: 'plain',
    MULTIPART: 'multi-part',
  },
  validationStrings: {
    validToken: 'Generated Token is still valid',
    invalidToken: 'ClientId or ClientSecretKey does not match/exist',
    successStatusCode: 1,
    failureStatusCode: 0,
  },
  loginScreen: {
    title: 'Login',
    usernamePlaceholder: 'Username',
    passwordPlaceholder: 'Password',
    appTitle: 'ePragati',
    credtsCommonErrorMsg: 'Username and Password is required',
    userIdErrorMsg: 'Username is required',
    passwordErrorMsg: 'Password is required',
    credsErrorMsg: 'Invalid username or password',
    approver: 'approver',
  },
  homeScreen: {
    hello: 'Hello',
    viewMore: 'View More Details',
    downloadWBSMsg: 'Looking your not yet download WBS job !',
    viewAndDownloadWBS: 'View & Download WBS',
    refresh: 'Refresh',
    lastUpdated: 'Last Update: ',
    progressUpdate: 'Progress Update',
    hindrance: 'Hindrance',
    dayPlanning: 'Day Planning',
    assetAllocation: 'Asset Allocation',
    pipeStockYard: 'Pipe stock yard',
    syncData: 'Sync Data',
    userManual: 'User Manual',
    downloadWBS: 'Download WBS',
    pipeTracking: 'Pipe Tracking',
    downloadProject: 'Download Project',
    notification: 'Notification',
    push_notification: 'Push Notification',
    pipeTrackingApproval: 'Pipe Tracking Approval',
    dayPlan: 'Day Plan',
    assetStatus: 'Asset Status',
    requestApproval: 'Request Approval',
    directAllocation: 'Direct Allocation',
  },
  bottomTabs: {
    home: 'Home',
    reports: 'Reports',
    gisMap: 'GIS MAP',
    profile: 'Profile'
  },
  DailyProgress: {
    newProgressUpdate: 'New Progress Update',
    progressUpdate: 'Progress Update',
    search: 'Search',
    viewLastUpdate: 'View Last Update',
    uploadImage: 'Upload Image',
    update: 'Update',
    uploadImageFrom: 'Upload Image from',
    camera: 'Camera',
    gallery: 'Gallery',
    lastUpdate: 'Last Update',
    selectDate: 'Select Date',
    fromDate: 'From Date',
    endDate: 'End Date',
    dummyDate: '5/Aug/2025',
    updateDate: 'Update Date',
    manDays: 'Man Days',
    quantity: 'Quantity',
    uom: 'UOM',
    history: 'History',
    upload: 'Upload',
    noData: 'No Data',
    gisNodeDetails: 'GIS Node Details',
    hindarnce: 'Hindrance',
    dailyProgress: 'Daily Progress',
    date: 'Date *',
    fromLength: 'From Length',
    actualLength: 'Actual Length',
    jobPath: 'Job Path',
    edit: 'Edit',
    jobDetails: 'Job Details',
    del: 'Del',
    engineer: 'Engineer',
    sc: 'S/C',
    scope: 'Scope',
    cumPlanQty: 'Cum.Plan Qty',
    cumProgress: 'Cum.Progress',
    cumManday: 'Cum.Manday',
    ftmPlanQty: 'FTM Plan Qty',
    ftmProgress: 'FTM Progress',
    ftmManday: 'FTM Manday',
    nodeDetails: 'Node Details',
    bookmark: 'Bookmark',
  },

  SyncData: {
    syncDataHeader: 'Sync Data',
    progressUpdate: 'Progress Update',
    hindrance: 'Hindrance',
    dayPlan: 'Day Plan',
    assets: 'Assets',
    sync: 'Sync',
    date: 'Date',
    gapLength: 'Gap Length',
    lat: 'Latitude',
    long: 'Longitude'
  }
};
export default Strings;
