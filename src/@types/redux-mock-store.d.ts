// src/@types/redux-mock-store.d.ts
declare module 'redux-mock-store' {
    import { Store, AnyAction, Middleware } from 'redux';
  
    interface MockStore<TState = any, TAction extends AnyAction = AnyAction> extends Store<TState, TAction> {
      clearActions(): void;
      getActions(): TAction[];
      dispatch(action: TAction): TAction;
    }
  
    type ConfigureMockStore = (
      middlewares?: Middleware[]
    ) => <TState = any, TAction extends AnyAction = AnyAction>(
      state?: Partial<TState>
    ) => MockStore<TState, TAction>;
  
    const configureMockStore: ConfigureMockStore;
  
    export default configureMockStore;
  }
  