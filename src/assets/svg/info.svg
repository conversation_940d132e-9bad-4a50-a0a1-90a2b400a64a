<svg width="40" height="40" viewBox="0 0 60 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_457_11584)">
<rect x="8" y="5" width="24" height="24" rx="4" fill="white"/>
<g clip-path="url(#clip0_457_11584)">
<path d="M20.0001 20.3332V16.9998M20.0001 13.6665H20.0084M28.3334 16.9998C28.3334 21.6022 24.6025 25.3332 20.0001 25.3332C15.3977 25.3332 11.6667 21.6022 11.6667 16.9998C11.6667 12.3975 15.3977 8.6665 20.0001 8.6665C24.6025 8.6665 28.3334 12.3975 28.3334 16.9998Z" stroke="#052669" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
<defs>
<filter id="filter0_d_457_11584" x="0" y="0" width="40" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_457_11584"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_457_11584" result="shape"/>
</filter>
<clipPath id="clip0_457_11584">
<rect width="20" height="20" fill="white" transform="translate(10 7)"/>
</clipPath>
</defs>
</svg>
