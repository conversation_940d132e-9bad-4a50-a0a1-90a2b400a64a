<svg width="42" height="42" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_4892_1287)">
<rect x="7" y="7" width="28" height="28" rx="4" fill="white"/>
<g clip-path="url(#clip0_4892_1287)">
<path d="M23.4998 15.1668L26.8331 18.5002M28.6448 16.6768C29.0854 16.2363 29.3329 15.6389 29.333 15.0159C29.3331 14.3929 29.0857 13.7953 28.6452 13.3547C28.2047 12.9141 27.6073 12.6666 26.9842 12.6665C26.3612 12.6664 25.7637 12.9138 25.3231 13.3543L14.2014 24.4785C14.008 24.6714 13.8649 24.9089 13.7848 25.1702L12.6839 28.7968C12.6624 28.8689 12.6608 28.9454 12.6792 29.0184C12.6977 29.0913 12.7355 29.1578 12.7888 29.211C12.842 29.2641 12.9086 29.3019 12.9816 29.3202C13.0545 29.3386 13.1311 29.3368 13.2031 29.3152L16.8306 28.2152C17.0916 28.1358 17.3291 27.9936 17.5223 27.801L28.6448 16.6768Z" stroke="#052669" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
<defs>
<filter id="filter0_d_4892_1287" x="0" y="0" width="42" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4892_1287"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4892_1287" result="shape"/>
</filter>
<clipPath id="clip0_4892_1287">
<rect width="20" height="20" fill="white" transform="translate(11 11)"/>
</clipPath>
</defs>
</svg>
