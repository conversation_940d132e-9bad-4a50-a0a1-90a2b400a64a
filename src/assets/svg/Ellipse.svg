<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_789_30152)">
<circle cx="8" cy="8" r="6" fill="#E1E5ED" shape-rendering="crispEdges"/>
<circle cx="8" cy="8" r="7" stroke="#052669" stroke-opacity="0.5" stroke-width="2" shape-rendering="crispEdges"/>
</g>
<circle cx="8" cy="8" r="4" fill="#052669"/>
<defs>
<filter id="filter0_d_789_30152" x="0" y="0" width="16" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_789_30152"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_789_30152" result="shape"/>
</filter>
</defs>
</svg>
