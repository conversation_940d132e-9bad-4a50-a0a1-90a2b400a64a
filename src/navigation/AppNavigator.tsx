import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import SplashScreen from '../screens/Splash/SplashScreen';
import LoginScreen from '../screens/Auth/LoginScreen';
import Toast from 'react-native-toast-message';
import MainScreen from '../screens/Main/MainScreen';
import DownloadMasterWBSScreen from '../screens/WBS/DownloadMasterWBSScreen';
import DailyProgressScreen from '../screens/DailyProgress/DailyProgressScreen';
import DailyProgressDetailsScreen from '../screens/DailyProgress/DailyProgressDetailsScreen';
import BookmarksScreen from '../screens/DailyProgress/BookmarksScreen';
import LastUpdateScreen from '../screens/DailyProgress/LastUpdateScreen';
import PipeIdHistoryScreen from '../screens/DailyProgress/PipeIdHistoryScreen';
import ProgressUpdateScreen from '../screens/DailyProgress/ProgressUpdateScreen';
import HindranceMapView from '../screens/Hindrance/HindranceMapView';
import HindranceUpdate from '../screens/Hindrance/HindranceUpdate';
import CreateHindranceDetails from '../screens/Hindrance/CreateHindranceDetails';
import CreateHindranceUpdate from '../screens/Hindrance/CreateHindranceUpdate';
import SyncDataScreen from '../screens/SyncData/SyncDataScreen';
import SyncProgressUpdateDetails from '../screens/SyncData/components/SyncProgressUpdate/SyncProgressUpdateDetails';
import SyncHindranceUpdateDetails from '../screens/SyncData/components/SyncHindrance/SyncHindranceUpdateDetails';
import SyncDayPlanListScreen from '../screens/SyncData/components/SyncDayPlan/SyncDayPlanList';
import RequestAssetsScreen from '../screens/SyncData/components/RequestAssets.tsx/RequestAssets';
import MapViewScreen from '../screens/DailyProgress/MapViewScreen';
import GapReportScreen from '../screens/Hindrance/GapReportScreen';
import UserMannualScreen from '../screens/UserMannual/UserMannualScreen';
import { DailyProgressApprove } from '../screens/DailyProgressApprover';
import DailyProgressApproverUpdate from '../screens/DailyProgressApprover/DailyProgressApproverUpdate';
import DailyProgressApproverMapView from '../screens/DailyProgressApprover/DailyProgressApproverMapView';

const Stack = createNativeStackNavigator();

export type RootStackParamList = {
  Splash: undefined;
  Login: undefined;
  Home: undefined;
  DownloadWBS: undefined;
  DailyProgressView: undefined;
  SyncData: undefined;
  DailyProgressDetailsView: undefined;
  BookmarksView: undefined;
  LastUpdateView: undefined;
  ProgressUpdateView: undefined;
  PipeIdHistoryView: undefined;
  SyncProgressUpdateScreen: undefined;
  SyncHindranceUpdateScreen: undefined;
  SyncDayPlanListScreen: undefined;
  RequestAssetsScreen: undefined;
  HindranceMapView: {
    latitude: number;
    longitude: number;
    endLatitude?: number;
    endLongitude?: number;
    isSinglePointer: boolean;
  };
  HindranceUpdate: { selectedItem: any };
  HindranceCreation: { isSinglePointer: boolean; selectedLocation: any };
  CreateHindranceUpdate: { isSinglePointer: boolean; selectedLocation: any };
  GapReportScreen: { fromDate: string; toDate: string };
  DailyProgressApprove: undefined;
  DailyProgressApproverUpdate: { [key: string]: any };
  DailyProgressApproverMapView: { pendingItems: any[] };
};

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Splash"
        screenOptions={{ headerShown: false }}
      >
        <Stack.Screen name="Splash" component={SplashScreen} />
        <Stack.Screen name="Login" component={LoginScreen} />
        <Stack.Screen name="Home" component={MainScreen} />
        <Stack.Screen name="DownloadWBS" component={DownloadMasterWBSScreen} />
        <Stack.Screen name="DailyProgressView" component={DailyProgressScreen} />
        <Stack.Screen name="DailyProgressMapView" component={MapViewScreen} />
        <Stack.Screen name="DailyProgressDetailsView" component={DailyProgressDetailsScreen} />
        <Stack.Screen name="BookmarksView" component={BookmarksScreen} />
        <Stack.Screen name="LastUpdateView" component={LastUpdateScreen} />
        <Stack.Screen name="ProgressUpdateView" component={ProgressUpdateScreen} />
        <Stack.Screen name="PipeIdHistoryView" component={PipeIdHistoryScreen} />
        <Stack.Screen name="HindranceMapView" component={HindranceMapView} />
        <Stack.Screen name="HindranceUpdate" component={HindranceUpdate} />
        <Stack.Screen name="HindranceCreation" component={CreateHindranceDetails} />
        <Stack.Screen name="CreateHindranceUpdate" component={CreateHindranceUpdate} />
        <Stack.Screen name="SyncData" component={SyncDataScreen} />

        <Stack.Screen name="SyncProgressUpdateScreen" component={SyncProgressUpdateDetails} />
        <Stack.Screen name="SyncHindranceUpdateScreen" component={SyncHindranceUpdateDetails} />
        <Stack.Screen name="SyncDayPlanListScreen" component={SyncDayPlanListScreen} />
        <Stack.Screen name="RequestAssetsScreen" component={RequestAssetsScreen} />
        <Stack.Screen name="GapReportScreen" component={GapReportScreen} />
        <Stack.Screen name="UserMannualScreen" component={UserMannualScreen} />
        <Stack.Screen name="DailyProgressApprove" component={DailyProgressApprove} />
        <Stack.Screen name="DailyProgressApproverUpdate" component={DailyProgressApproverUpdate} />
        <Stack.Screen name="DailyProgressApproverMapView" component={DailyProgressApproverMapView} />
      </Stack.Navigator>
      <Toast />
    </NavigationContainer >
  );
};

export default AppNavigator;
