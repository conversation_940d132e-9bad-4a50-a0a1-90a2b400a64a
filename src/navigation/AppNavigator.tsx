import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import SplashScreen from '../screens/Splash/SplashScreen';
import LoginScreen from '../screens/Auth/LoginScreen';
import Toast from 'react-native-toast-message';
import MainScreen from '../screens/Main/MainScreen';
import DownloadMasterWBSScreen from '../screens/WBS/DownloadMasterWBSScreen';
import DailyProgressScreen from '../screens/DailyProgress/DailyProgressScreen';
import DailyProgressDetailsScreen from '../screens/DailyProgress/DailyProgressDetailsScreen';
import BookmarksScreen from '../screens/DailyProgress/BookmarksScreen';
import LastUpdateScreen from '../screens/DailyProgress/LastUpdateScreen';
import PipeIdHistoryScreen from '../screens/DailyProgress/PipeIdHistoryScreen';
import ProgressUpdateScreen from '../screens/DailyProgress/ProgressUpdateScreen';
import SyncDataScreen from '../screens/SyncData/SyncDataScreen';
import SyncProgressUpdateDetails from '../screens/SyncData/components/SyncProgressUpdate/SyncProgressUpdateDetails';
import SyncHindranceUpdateDetails from '../screens/SyncData/components/SyncHindrance/SyncHindranceUpdateDetails';
import SyncDayPlanListScreen from '../screens/SyncData/components/SyncDayPlan/SyncDayPlanList';
import RequestAssetsScreen from '../screens/SyncData/components/RequestAssets.tsx/RequestAssets';
import MapViewScreen from '../screens/DailyProgress/MapViewScreen';

const Stack = createNativeStackNavigator();

export type RootStackParamList = {
  Splash: undefined;
  Login: undefined;
  Home: undefined;
  DownloadWBS: undefined;
  DailyProgressView: undefined;
  SyncData: undefined;
  DailyProgressDetailsView: undefined;
  BookmarksView: undefined;
  LastUpdateView: undefined;
  ProgressUpdateView: undefined;
  PipeIdHistoryView: undefined;
  SyncProgressUpdateScreen: undefined;
  SyncHindranceUpdateScreen: undefined;
  SyncDayPlanListScreen: undefined;
  RequestAssetsScreen: undefined;
};

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Splash"
        screenOptions={{ headerShown: false }}
      >
        <Stack.Screen name="Splash" component={SplashScreen} />
        <Stack.Screen name="Login" component={LoginScreen} />
        <Stack.Screen name="Home" component={MainScreen} />
        <Stack.Screen name="DownloadWBS" component={DownloadMasterWBSScreen} />
        <Stack.Screen name="DailyProgressView" component={DailyProgressScreen} />
        <Stack.Screen name="DailyProgressMapView" component={MapViewScreen} />
        <Stack.Screen name="DailyProgressDetailsView" component={DailyProgressDetailsScreen} />
        <Stack.Screen name="BookmarksView" component={BookmarksScreen} />
        <Stack.Screen name="LastUpdateView" component={LastUpdateScreen} />
        <Stack.Screen name="ProgressUpdateView" component={ProgressUpdateScreen} />
        <Stack.Screen name="PipeIdHistoryView" component={PipeIdHistoryScreen} />
        <Stack.Screen name="SyncData" component={SyncDataScreen} />

        <Stack.Screen name="SyncProgressUpdateScreen" component={SyncProgressUpdateDetails} />
        <Stack.Screen name="SyncHindranceUpdateScreen" component={SyncHindranceUpdateDetails} />
        <Stack.Screen name="SyncDayPlanListScreen" component={SyncDayPlanListScreen} />
        <Stack.Screen name="RequestAssetsScreen" component={RequestAssetsScreen} />
      </Stack.Navigator>
      <Toast />
    </NavigationContainer >
  );
};

export default AppNavigator;
