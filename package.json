{"name": "ePragati", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "sonar": "sonar-scanner", "clean": "watchman watch-del-all &&  rm -rf node_modules android/app/build ios/Pods ios/Podfile.lock ~/Library/Developer/Xcode/DerivedData", "android-clean": "cd android && ./gradlew clean && cd .."}, "dependencies": {"@nozbe/watermelondb": "^0.28.0", "@react-native-community/datetimepicker": "^8.4.1", "@react-native-community/netinfo": "^11.4.1", "@react-native-picker/picker": "^2.11.0", "@react-navigation/elements": "^2.4.3", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@reduxjs/toolkit": "^2.8.2", "@sonar/scan": "^4.3.0", "axios": "^1.9.0", "crypto-js": "^4.2.0", "i18next": "^25.2.1", "react": "19.0.0", "react-i18next": "^15.5.2", "react-native": "0.78.2", "react-native-calendars": "^1.1312.1", "react-native-circular-progress": "^1.4.1", "react-native-config": "^1.5.5", "react-native-device-info": "^14.0.4", "react-native-element-dropdown": "^2.12.4", "react-native-image-picker": "^8.2.1", "react-native-maps": "^1.24.3", "react-native-mmkv": "^3.2.0", "react-native-permissions": "^5.4.1", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-toast-message": "^2.3.0", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "redux-saga": "^1.3.0", "sonarqube-scanner": "^4.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.2", "@react-native/eslint-config": "0.78.2", "@react-native/metro-config": "0.78.2", "@react-native/typescript-config": "0.78.2", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-svg-transformer": "^1.5.1", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}