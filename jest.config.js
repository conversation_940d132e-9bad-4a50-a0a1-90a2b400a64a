module.exports = {
  preset: 'react-native',

  setupFilesAfterEnv: [
    '<rootDir>/src/utils/testing/setupTests.ts',
  ],

  transformIgnorePatterns: [
    'node_modules/(?!(react-native' +
      '|@react-native' +
      '|react-native-vector-icons' +
      '|@react-navigation' +
      '|react-redux' +
      '|@reduxjs' +
      '|redux-saga' +
      '|@react-native-community' +
      '|@react-native-vector-icons' +
      '|react-native-svg' +
      '|react-native-mmkv' +
      '|react-native-permissions' +
      '|react-native-image-picker' +
      '|@nozbe' +
      '|crypto-js' +
    ')/)',
  ],

  moduleNameMapper: {
    // Mock crypto-js manually to avoid path issues
    'crypto-js': '<rootDir>/__mocks__/crypto-js.js',

    // ✅ Proper SVG mock setup
    '\\.svg$': '<rootDir>/__mocks__/svgMock.js',

    // ✅ Mock for image/font/etc. (identity-obj-proxy is usually only for CSS)
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|ttf|woff|woff2)$': '<rootDir>/__mocks__/fileMock.js',

    // ✅ Custom path alias for "@/..."
    '^@/(.*)$': '<rootDir>/src/$1',

    // ✅ Mock vector icons
    '^react-native-vector-icons/(.*)$': '<rootDir>/src/utils/testing/__mocks__/react-native-vector-icons.js',
  },

  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': 'babel-jest',
  },

  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],

  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/index.ts',
    '!src/assets/**',
    '!src/utils/testing/**',
    '!src/**/__tests__/**',
    '!src/**/*.test.{ts,tsx}',
    '!src/database/schema.ts',
    '!src/database/migrations.ts',
    '!src/model/**',  // Exclude model files (interfaces only)
  ],

  coverageReporters: ['text', 'lcov', 'html'],
  coverageDirectory: 'coverage',
  collectCoverage: true, // Enabled for coverage reporting

  coverageThreshold: {
    global: {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85,
    },
  },

  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/src/assets/',
  ],

  testEnvironment: 'node',

  testTimeout: 10000,
  verbose: true,
  bail: false,

  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,

  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.(test|spec).{js,jsx,ts,tsx}',
    '<rootDir>/__tests__/**/*.{js,jsx,ts,tsx}',
  ],

  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/android/',
    '<rootDir>/ios/',
  ],
};
